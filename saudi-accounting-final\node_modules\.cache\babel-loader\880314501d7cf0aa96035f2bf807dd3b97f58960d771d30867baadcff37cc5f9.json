{"ast": null, "code": "// نظام قاعدة البيانات المحلية للنظام المحاسبي السعودي\n// Local Database System for Saudi Accounting System\nclass LocalDatabase{constructor(){this.initializeDatabase();}// تهيئة قاعدة البيانات\ninitializeDatabase(){// إنشاء الجداول الأساسية إذا لم تكن موجودة\nif(!localStorage.getItem('saudi_accounting_invoices')){localStorage.setItem('saudi_accounting_invoices',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_products')){localStorage.setItem('saudi_accounting_products',JSON.stringify(this.getDefaultProducts()));}if(!localStorage.getItem('saudi_accounting_customers')){localStorage.setItem('saudi_accounting_customers',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_settings')){localStorage.setItem('saudi_accounting_settings',JSON.stringify(this.getDefaultSettings()));}if(!localStorage.getItem('saudi_accounting_payments')){localStorage.setItem('saudi_accounting_payments',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_inventory_movements')){localStorage.setItem('saudi_accounting_inventory_movements',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_employees')){localStorage.setItem('saudi_accounting_employees',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_returns')){localStorage.setItem('saudi_accounting_returns',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_quotations')){localStorage.setItem('saudi_accounting_quotations',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_purchase_invoices')){localStorage.setItem('saudi_accounting_purchase_invoices',JSON.stringify([]));}if(!localStorage.getItem('saudi_accounting_vouchers')){localStorage.setItem('saudi_accounting_vouchers',JSON.stringify([]));}}// المنتجات الافتراضية\ngetDefaultProducts(){return[{id:1,name:'لابتوب Dell',price:2500,stock:10,category:'إلكترونيات',barcode:'*************',description:'لابتوب Dell Inspiron 15',vatRate:0.15,createdAt:new Date().toISOString()},{id:2,name:'ماوس لاسلكي',price:85,stock:25,category:'إلكترونيات',barcode:'*************',description:'ماوس لاسلكي عالي الجودة',vatRate:0.15,createdAt:new Date().toISOString()},{id:3,name:'كيبورد ميكانيكي',price:150,stock:15,category:'إلكترونيات',barcode:'*************',description:'كيبورد ميكانيكي للألعاب',vatRate:0.15,createdAt:new Date().toISOString()},{id:4,name:'شاشة 24 بوصة',price:800,stock:8,category:'إلكترونيات',barcode:'*************',description:'شاشة LED 24 بوصة Full HD',vatRate:0.15,createdAt:new Date().toISOString()},{id:5,name:'طابعة HP',price:450,stock:5,category:'مكتبية',barcode:'1234567890127',description:'طابعة HP LaserJet',vatRate:0.15,createdAt:new Date().toISOString()},{id:6,name:'كاميرا ويب',price:120,stock:20,category:'إلكترونيات',barcode:'1234567890128',description:'كاميرا ويب HD 1080p',vatRate:0.15,createdAt:new Date().toISOString()},{id:7,name:'سماعات بلوتوث',price:200,stock:12,category:'إلكترونيات',barcode:'1234567890129',description:'سماعات بلوتوث لاسلكية',vatRate:0.15,createdAt:new Date().toISOString()},{id:8,name:'قلم رقمي',price:75,stock:30,category:'مكتبية',barcode:'1234567890130',description:'قلم رقمي للرسم والكتابة',vatRate:0.15,createdAt:new Date().toISOString()}];}// الإعدادات الافتراضية\ngetDefaultSettings(){return{companyName:'شركة نظام المحاسبة السعودي المحدودة',vatNumber:'310122393500003',address:'الرياض، المملكة العربية السعودية',phone:'+966 11 123 4567',email:'<EMAIL>',vatRate:0.15,currency:'ريال سعودي',invoicePrefix:'INV',nextInvoiceNumber:1001,createdAt:new Date().toISOString()};}// === إدارة الفواتير ===\n// حفظ فاتورة جديدة\nsaveInvoice(invoice){try{const invoices=this.getInvoices();const newInvoice={...invoice,id:this.generateInvoiceId(),createdAt:new Date().toISOString(),status:'paid'};invoices.push(newInvoice);localStorage.setItem('saudi_accounting_invoices',JSON.stringify(invoices));// تحديث المخزون\nthis.updateStockAfterSale(invoice.items);// تحديث رقم الفاتورة التالي\nthis.incrementInvoiceNumber();return newInvoice;}catch(error){console.error('خطأ في حفظ الفاتورة:',error);throw new Error('فشل في حفظ الفاتورة');}}// جلب جميع الفواتير\ngetInvoices(){try{const invoices=localStorage.getItem('saudi_accounting_invoices');return invoices?JSON.parse(invoices):[];}catch(error){console.error('خطأ في جلب الفواتير:',error);return[];}}// جلب فاتورة بالرقم\ngetInvoiceById(id){const invoices=this.getInvoices();return invoices.find(invoice=>invoice.id===id);}// جلب فواتير بتاريخ محدد\ngetInvoicesByDate(date){const invoices=this.getInvoices();return invoices.filter(invoice=>{const invoiceDate=new Date(invoice.createdAt).toDateString();const searchDate=new Date(date).toDateString();return invoiceDate===searchDate;});}// === إدارة المنتجات ===\n// جلب جميع المنتجات\ngetProducts(){try{const products=localStorage.getItem('saudi_accounting_products');return products?JSON.parse(products):this.getDefaultProducts();}catch(error){console.error('خطأ في جلب المنتجات:',error);return this.getDefaultProducts();}}// إضافة منتج جديد\naddProduct(product){try{const products=this.getProducts();const newProduct={...product,id:this.generateProductId(),createdAt:new Date().toISOString()};products.push(newProduct);localStorage.setItem('saudi_accounting_products',JSON.stringify(products));return newProduct;}catch(error){console.error('خطأ في إضافة المنتج:',error);throw new Error('فشل في إضافة المنتج');}}// تحديث منتج\nupdateProduct(id,updates){try{const products=this.getProducts();const index=products.findIndex(product=>product.id===id);if(index!==-1){products[index]={...products[index],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_products',JSON.stringify(products));return products[index];}throw new Error('المنتج غير موجود');}catch(error){console.error('خطأ في تحديث المنتج:',error);throw new Error('فشل في تحديث المنتج');}}// حذف منتج\ndeleteProduct(id){try{const products=this.getProducts();const filteredProducts=products.filter(product=>product.id!==id);localStorage.setItem('saudi_accounting_products',JSON.stringify(filteredProducts));return true;}catch(error){console.error('خطأ في حذف المنتج:',error);throw new Error('فشل في حذف المنتج');}}// تحديث المخزون بعد البيع\nupdateStockAfterSale(items){try{const products=this.getProducts();items.forEach(item=>{const productIndex=products.findIndex(product=>product.id===item.id);if(productIndex!==-1){products[productIndex].stock-=item.quantity;if(products[productIndex].stock<0){products[productIndex].stock=0;}}});localStorage.setItem('saudi_accounting_products',JSON.stringify(products));}catch(error){console.error('خطأ في تحديث المخزون:',error);}}// === إدارة العملاء ===\n// جلب جميع العملاء\ngetCustomers(){try{const customers=localStorage.getItem('saudi_accounting_customers');return customers?JSON.parse(customers):[];}catch(error){console.error('خطأ في جلب العملاء:',error);return[];}}// إضافة عميل جديد\naddCustomer(customer){try{const customers=this.getCustomers();const newCustomer={...customer,id:this.generateCustomerId(),createdAt:new Date().toISOString()};customers.push(newCustomer);localStorage.setItem('saudi_accounting_customers',JSON.stringify(customers));return newCustomer;}catch(error){console.error('خطأ في إضافة العميل:',error);throw new Error('فشل في إضافة العميل');}}// === مولدات الأرقام ===\ngenerateInvoiceId(){const settings=this.getSettings();return`${settings.invoicePrefix}-${settings.nextInvoiceNumber}`;}generateProductId(){const products=this.getProducts();return products.length>0?Math.max(...products.map(p=>p.id))+1:1;}generateCustomerId(){const customers=this.getCustomers();return customers.length>0?Math.max(...customers.map(c=>c.id))+1:1;}// === إدارة الموردين ===\n// جلب جميع الموردين\ngetSuppliers(){try{const suppliers=localStorage.getItem('saudi_accounting_suppliers');return suppliers?JSON.parse(suppliers):[];}catch(error){console.error('خطأ في جلب الموردين:',error);return[];}}// إضافة مورد جديد\naddSupplier(supplier){try{const suppliers=this.getSuppliers();const newSupplier={...supplier,id:this.generateSupplierId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};suppliers.push(newSupplier);localStorage.setItem('saudi_accounting_suppliers',JSON.stringify(suppliers));return newSupplier;}catch(error){console.error('خطأ في إضافة المورد:',error);throw new Error('فشل في إضافة المورد');}}// تحديث مورد\nupdateSupplier(id,updates){try{const suppliers=this.getSuppliers();const index=suppliers.findIndex(supplier=>supplier.id===id);if(index!==-1){suppliers[index]={...suppliers[index],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_suppliers',JSON.stringify(suppliers));return suppliers[index];}throw new Error('المورد غير موجود');}catch(error){console.error('خطأ في تحديث المورد:',error);throw new Error('فشل في تحديث المورد');}}// حذف مورد\ndeleteSupplier(id){try{const suppliers=this.getSuppliers();const filteredSuppliers=suppliers.filter(supplier=>supplier.id!==id);localStorage.setItem('saudi_accounting_suppliers',JSON.stringify(filteredSuppliers));return true;}catch(error){console.error('خطأ في حذف المورد:',error);throw new Error('فشل في حذف المورد');}}// توليد معرف مورد\ngenerateSupplierId(){const suppliers=this.getSuppliers();return suppliers.length>0?Math.max(...suppliers.map(s=>s.id))+1:1;}// === إدارة المدفوعات ===\n// جلب جميع المدفوعات\ngetPayments(){try{const payments=localStorage.getItem('saudi_accounting_payments');return payments?JSON.parse(payments):[];}catch(error){console.error('خطأ في جلب المدفوعات:',error);return[];}}// إضافة دفعة جديدة\naddPayment(payment){try{const payments=this.getPayments();const newPayment={...payment,id:this.generatePaymentId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};payments.push(newPayment);localStorage.setItem('saudi_accounting_payments',JSON.stringify(payments));// تحديث حالة الفاتورة إذا كانت الدفعة مكتملة\nif(payment.status==='completed'){this.updateInvoicePaymentStatus(payment.invoiceId,payment.amount);}return newPayment;}catch(error){console.error('خطأ في إضافة الدفعة:',error);throw new Error('فشل في إضافة الدفعة');}}// تحديث دفعة\nupdatePayment(id,updates){try{const payments=this.getPayments();const paymentIndex=payments.findIndex(p=>p.id===id);if(paymentIndex!==-1){payments[paymentIndex]={...payments[paymentIndex],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_payments',JSON.stringify(payments));return payments[paymentIndex];}throw new Error('الدفعة غير موجودة');}catch(error){console.error('خطأ في تحديث الدفعة:',error);throw new Error('فشل في تحديث الدفعة');}}// حذف دفعة\ndeletePayment(id){try{const payments=this.getPayments();const filteredPayments=payments.filter(p=>p.id!==id);localStorage.setItem('saudi_accounting_payments',JSON.stringify(filteredPayments));return true;}catch(error){console.error('خطأ في حذف الدفعة:',error);throw new Error('فشل في حذف الدفعة');}}// توليد معرف دفعة\ngeneratePaymentId(){const payments=this.getPayments();return payments.length>0?Math.max(...payments.map(p=>p.id))+1:1;}// تحديث حالة دفع الفاتورة\nupdateInvoicePaymentStatus(invoiceId,paidAmount){try{const invoices=this.getInvoices();const invoiceIndex=invoices.findIndex(inv=>inv.id===invoiceId);if(invoiceIndex!==-1){const invoice=invoices[invoiceIndex];const totalPaid=(invoice.paidAmount||0)+paidAmount;let paymentStatus='unpaid';if(totalPaid>=invoice.total){paymentStatus='paid';}else if(totalPaid>0){paymentStatus='partial';}invoices[invoiceIndex]={...invoice,paidAmount:totalPaid,paymentStatus:paymentStatus,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_invoices',JSON.stringify(invoices));}}catch(error){console.error('خطأ في تحديث حالة دفع الفاتورة:',error);}}// جلب مدفوعات عميل معين\ngetCustomerPayments(customerId){try{const payments=this.getPayments();return payments.filter(payment=>payment.customerId===customerId);}catch(error){console.error('خطأ في جلب مدفوعات العميل:',error);return[];}}// جلب مدفوعات فاتورة معينة\ngetInvoicePayments(invoiceId){try{const payments=this.getPayments();return payments.filter(payment=>payment.invoiceId===invoiceId);}catch(error){console.error('خطأ في جلب مدفوعات الفاتورة:',error);return[];}}// === إدارة حركات المخزون ===\n// جلب جميع حركات المخزون\ngetInventoryMovements(){try{const movements=localStorage.getItem('saudi_accounting_inventory_movements');return movements?JSON.parse(movements):[];}catch(error){console.error('خطأ في جلب حركات المخزون:',error);return[];}}// إضافة حركة مخزون جديدة\naddInventoryMovement(movement){try{const movements=this.getInventoryMovements();const newMovement={...movement,id:this.generateMovementId(),createdAt:new Date().toISOString()};movements.push(newMovement);localStorage.setItem('saudi_accounting_inventory_movements',JSON.stringify(movements));// تحديث مخزون المنتج\nthis.updateProductStock(movement.productId,movement.quantity,movement.type);return newMovement;}catch(error){console.error('خطأ في إضافة حركة المخزون:',error);throw new Error('فشل في إضافة حركة المخزون');}}// تحديث مخزون المنتج\nupdateProductStock(productId,quantity,movementType){try{const products=this.getProducts();const productIndex=products.findIndex(p=>p.id===productId);if(productIndex!==-1){const currentStock=products[productIndex].stock||0;let newStock=currentStock;if(movementType==='in'){newStock=currentStock+quantity;}else if(movementType==='out'||movementType==='damaged'||movementType==='expired'){newStock=Math.max(0,currentStock-quantity);}else if(movementType==='adjustment'){newStock=quantity;// التعديل يحدد الكمية الجديدة مباشرة\n}products[productIndex]={...products[productIndex],stock:newStock,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_products',JSON.stringify(products));}}catch(error){console.error('خطأ في تحديث مخزون المنتج:',error);}}// توليد معرف حركة مخزون\ngenerateMovementId(){const movements=this.getInventoryMovements();return movements.length>0?Math.max(...movements.map(m=>m.id))+1:1;}// جلب حركات مخزون منتج معين\ngetProductMovements(productId){try{const movements=this.getInventoryMovements();return movements.filter(movement=>movement.productId===productId);}catch(error){console.error('خطأ في جلب حركات المنتج:',error);return[];}}// جلب تقرير حركات المخزون لفترة معينة\ngetInventoryMovementsByDateRange(startDate,endDate){try{const movements=this.getInventoryMovements();return movements.filter(movement=>{const movementDate=new Date(movement.date);return movementDate>=new Date(startDate)&&movementDate<=new Date(endDate);});}catch(error){console.error('خطأ في جلب حركات المخزون للفترة:',error);return[];}}// حساب قيمة المخزون الإجمالية\ncalculateTotalInventoryValue(){try{const products=this.getProducts();return products.reduce((total,product)=>{return total+product.stock*product.price;},0);}catch(error){console.error('خطأ في حساب قيمة المخزون:',error);return 0;}}// جلب المنتجات منخفضة المخزون\ngetLowStockProducts(){try{const products=this.getProducts();return products.filter(product=>product.stock<=(product.minStock||5));}catch(error){console.error('خطأ في جلب المنتجات منخفضة المخزون:',error);return[];}}// جلب المنتجات نافدة المخزون\ngetOutOfStockProducts(){try{const products=this.getProducts();return products.filter(product=>product.stock===0);}catch(error){console.error('خطأ في جلب المنتجات نافدة المخزون:',error);return[];}}// === إدارة الموظفين ===\n// جلب جميع الموظفين\ngetEmployees(){try{const employees=localStorage.getItem('saudi_accounting_employees');return employees?JSON.parse(employees):[];}catch(error){console.error('خطأ في جلب الموظفين:',error);return[];}}// إضافة موظف جديد\naddEmployee(employee){try{const employees=this.getEmployees();const newEmployee={...employee,id:this.generateEmployeeId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};employees.push(newEmployee);localStorage.setItem('saudi_accounting_employees',JSON.stringify(employees));return newEmployee;}catch(error){console.error('خطأ في إضافة الموظف:',error);throw new Error('فشل في إضافة الموظف');}}// تحديث موظف\nupdateEmployee(id,updates){try{const employees=this.getEmployees();const employeeIndex=employees.findIndex(emp=>emp.id===id);if(employeeIndex!==-1){employees[employeeIndex]={...employees[employeeIndex],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_employees',JSON.stringify(employees));return employees[employeeIndex];}throw new Error('الموظف غير موجود');}catch(error){console.error('خطأ في تحديث الموظف:',error);throw new Error('فشل في تحديث الموظف');}}// حذف موظف\ndeleteEmployee(id){try{const employees=this.getEmployees();const filteredEmployees=employees.filter(emp=>emp.id!==id);localStorage.setItem('saudi_accounting_employees',JSON.stringify(filteredEmployees));return true;}catch(error){console.error('خطأ في حذف الموظف:',error);throw new Error('فشل في حذف الموظف');}}// توليد معرف موظف\ngenerateEmployeeId(){const employees=this.getEmployees();return employees.length>0?Math.max(...employees.map(emp=>emp.id))+1:1;}// جلب الموظفين النشطين\ngetActiveEmployees(){try{const employees=this.getEmployees();return employees.filter(employee=>employee.status==='active');}catch(error){console.error('خطأ في جلب الموظفين النشطين:',error);return[];}}// جلب موظفين حسب القسم\ngetEmployeesByDepartment(department){try{const employees=this.getEmployees();return employees.filter(employee=>employee.department===department);}catch(error){console.error('خطأ في جلب موظفين القسم:',error);return[];}}// حساب إجمالي الرواتب\ncalculateTotalSalaries(){try{const activeEmployees=this.getActiveEmployees();return activeEmployees.reduce((total,employee)=>{return total+(employee.salary||0);},0);}catch(error){console.error('خطأ في حساب إجمالي الرواتب:',error);return 0;}}// === إدارة الإعدادات المحسنة ===\n// جلب الإعدادات الافتراضية المحسنة\ngetDefaultSettings(){return{// بيانات الشركة\ncompanyName:'شركة المحاسبة السعودية',vatNumber:'300000000000003',address:'الرياض، المملكة العربية السعودية',phone:'+966 11 123 4567',email:'<EMAIL>',website:'www.company.com',// إعدادات الفواتير\ninvoicePrefix:'INV',nextInvoiceNumber:1,vatRate:15,currency:'SAR',defaultPaymentTerms:'الدفع خلال 30 يوم من تاريخ الفاتورة',autoSaveInvoices:true,// إعدادات النظام\nlanguage:'ar',timezone:'Asia/Riyadh',dateFormat:'DD/MM/YYYY',enableNotifications:true,enableDarkMode:false,enableAutoBackup:false,// إعدادات إضافية\nfiscalYearStart:'01/01',backupFrequency:'weekly',maxInvoiceItems:50,lowStockThreshold:5,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};}// تحديث الإعدادات\nupdateSettings(newSettings){try{const currentSettings=this.getSettings();const updatedSettings={...currentSettings,...newSettings,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_settings',JSON.stringify(updatedSettings));return updatedSettings;}catch(error){console.error('خطأ في تحديث الإعدادات:',error);throw new Error('فشل في تحديث الإعدادات');}}// إعادة تعيين الإعدادات للقيم الافتراضية\nresetSettings(){try{const defaultSettings=this.getDefaultSettings();localStorage.setItem('saudi_accounting_settings',JSON.stringify(defaultSettings));return defaultSettings;}catch(error){console.error('خطأ في إعادة تعيين الإعدادات:',error);throw new Error('فشل في إعادة تعيين الإعدادات');}}// تصدير جميع البيانات\nexportAllData(){try{const data={settings:this.getSettings(),invoices:this.getInvoices(),products:this.getProducts(),customers:this.getCustomers(),suppliers:this.getSuppliers(),accounts:this.getAccounts(),journalEntries:this.getJournalEntries(),payments:this.getPayments(),inventoryMovements:this.getInventoryMovements(),employees:this.getEmployees(),exportDate:new Date().toISOString(),version:'1.0.0'};return data;}catch(error){console.error('خطأ في تصدير البيانات:',error);throw new Error('فشل في تصدير البيانات');}}// استيراد جميع البيانات\nimportAllData(data){try{if(data.settings){localStorage.setItem('saudi_accounting_settings',JSON.stringify(data.settings));}if(data.invoices){localStorage.setItem('saudi_accounting_invoices',JSON.stringify(data.invoices));}if(data.products){localStorage.setItem('saudi_accounting_products',JSON.stringify(data.products));}if(data.customers){localStorage.setItem('saudi_accounting_customers',JSON.stringify(data.customers));}if(data.suppliers){localStorage.setItem('saudi_accounting_suppliers',JSON.stringify(data.suppliers));}if(data.accounts){localStorage.setItem('saudi_accounting_accounts',JSON.stringify(data.accounts));}if(data.journalEntries){localStorage.setItem('saudi_accounting_journal_entries',JSON.stringify(data.journalEntries));}if(data.payments){localStorage.setItem('saudi_accounting_payments',JSON.stringify(data.payments));}if(data.inventoryMovements){localStorage.setItem('saudi_accounting_inventory_movements',JSON.stringify(data.inventoryMovements));}if(data.employees){localStorage.setItem('saudi_accounting_employees',JSON.stringify(data.employees));}if(data.returns){localStorage.setItem('saudi_accounting_returns',JSON.stringify(data.returns));}if(data.quotations){localStorage.setItem('saudi_accounting_quotations',JSON.stringify(data.quotations));}if(data.purchaseInvoices){localStorage.setItem('saudi_accounting_purchase_invoices',JSON.stringify(data.purchaseInvoices));}if(data.vouchers){localStorage.setItem('saudi_accounting_vouchers',JSON.stringify(data.vouchers));}return true;}catch(error){console.error('خطأ في استيراد البيانات:',error);throw new Error('فشل في استيراد البيانات');}}// === إدارة المرتجعات ===\n// جلب جميع المرتجعات\ngetReturns(){try{const returns=localStorage.getItem('saudi_accounting_returns');return returns?JSON.parse(returns):[];}catch(error){console.error('خطأ في جلب المرتجعات:',error);return[];}}// إضافة مرتجع جديد\naddReturn(returnData){try{const returns=this.getReturns();const newReturn={...returnData,id:this.generateReturnId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};returns.push(newReturn);localStorage.setItem('saudi_accounting_returns',JSON.stringify(returns));// تحديث المخزون إذا كان المرتجع معتمد\nif(returnData.status==='approved'||returnData.status==='processed'){this.updateInventoryForReturn(newReturn);}return newReturn;}catch(error){console.error('خطأ في إضافة المرتجع:',error);throw new Error('فشل في إضافة المرتجع');}}// تحديث مرتجع\nupdateReturn(id,updates){try{const returns=this.getReturns();const returnIndex=returns.findIndex(ret=>ret.id===id);if(returnIndex!==-1){const oldReturn=returns[returnIndex];returns[returnIndex]={...oldReturn,...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_returns',JSON.stringify(returns));// تحديث المخزون إذا تغيرت الحالة\nif(oldReturn.status!==updates.status&&(updates.status==='approved'||updates.status==='processed')){this.updateInventoryForReturn(returns[returnIndex]);}return returns[returnIndex];}throw new Error('المرتجع غير موجود');}catch(error){console.error('خطأ في تحديث المرتجع:',error);throw new Error('فشل في تحديث المرتجع');}}// حذف مرتجع\ndeleteReturn(id){try{const returns=this.getReturns();const filteredReturns=returns.filter(ret=>ret.id!==id);localStorage.setItem('saudi_accounting_returns',JSON.stringify(filteredReturns));return true;}catch(error){console.error('خطأ في حذف المرتجع:',error);throw new Error('فشل في حذف المرتجع');}}// معالجة مرتجع\nprocessReturn(id){try{return this.updateReturn(id,{status:'processed'});}catch(error){console.error('خطأ في معالجة المرتجع:',error);throw new Error('فشل في معالجة المرتجع');}}// توليد معرف مرتجع\ngenerateReturnId(){const returns=this.getReturns();return returns.length>0?Math.max(...returns.map(ret=>ret.id))+1:1;}// تحديث المخزون للمرتجع\nupdateInventoryForReturn(returnData){try{if(returnData.items&&returnData.items.length>0){returnData.items.forEach(item=>{if(returnData.type==='sales_return'){// إضافة الكمية للمخزون في حالة مرتجع المبيعات\nthis.addInventoryMovement({productId:item.productId,type:'in',quantity:item.quantity,reason:`مرتجع مبيعات - ${returnData.returnNumber}`,reference:returnData.returnNumber,date:returnData.returnDate});}else if(returnData.type==='purchase_return'){// خصم الكمية من المخزون في حالة مرتجع المشتريات\nthis.addInventoryMovement({productId:item.productId,type:'out',quantity:item.quantity,reason:`مرتجع مشتريات - ${returnData.returnNumber}`,reference:returnData.returnNumber,date:returnData.returnDate});}});}}catch(error){console.error('خطأ في تحديث المخزون للمرتجع:',error);}}// === إدارة عروض الأسعار ===\n// جلب جميع عروض الأسعار\ngetQuotations(){try{const quotations=localStorage.getItem('saudi_accounting_quotations');return quotations?JSON.parse(quotations):[];}catch(error){console.error('خطأ في جلب عروض الأسعار:',error);return[];}}// إضافة عرض سعر جديد\naddQuotation(quotationData){try{const quotations=this.getQuotations();const newQuotation={...quotationData,id:this.generateQuotationId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};quotations.push(newQuotation);localStorage.setItem('saudi_accounting_quotations',JSON.stringify(quotations));return newQuotation;}catch(error){console.error('خطأ في إضافة عرض السعر:',error);throw new Error('فشل في إضافة عرض السعر');}}// تحديث عرض سعر\nupdateQuotation(id,updates){try{const quotations=this.getQuotations();const quotationIndex=quotations.findIndex(quot=>quot.id===id);if(quotationIndex!==-1){quotations[quotationIndex]={...quotations[quotationIndex],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_quotations',JSON.stringify(quotations));return quotations[quotationIndex];}throw new Error('عرض السعر غير موجود');}catch(error){console.error('خطأ في تحديث عرض السعر:',error);throw new Error('فشل في تحديث عرض السعر');}}// حذف عرض سعر\ndeleteQuotation(id){try{const quotations=this.getQuotations();const filteredQuotations=quotations.filter(quot=>quot.id!==id);localStorage.setItem('saudi_accounting_quotations',JSON.stringify(filteredQuotations));return true;}catch(error){console.error('خطأ في حذف عرض السعر:',error);throw new Error('فشل في حذف عرض السعر');}}// توليد معرف عرض سعر\ngenerateQuotationId(){const quotations=this.getQuotations();return quotations.length>0?Math.max(...quotations.map(quot=>quot.id))+1:1;}// === إدارة فواتير المشتريات ===\n// جلب جميع فواتير المشتريات\ngetPurchaseInvoices(){try{const purchaseInvoices=localStorage.getItem('saudi_accounting_purchase_invoices');return purchaseInvoices?JSON.parse(purchaseInvoices):[];}catch(error){console.error('خطأ في جلب فواتير المشتريات:',error);return[];}}// إضافة فاتورة مشتريات جديدة\naddPurchaseInvoice(invoiceData){try{const purchaseInvoices=this.getPurchaseInvoices();const newInvoice={...invoiceData,id:this.generatePurchaseInvoiceId(),invoiceNumber:this.generatePurchaseInvoiceNumber(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};purchaseInvoices.push(newInvoice);localStorage.setItem('saudi_accounting_purchase_invoices',JSON.stringify(purchaseInvoices));// تحديث المخزون\nif(newInvoice.items&&newInvoice.items.length>0){newInvoice.items.forEach(item=>{this.addInventoryMovement({productId:item.productId,type:'in',quantity:item.quantity,reason:`فاتورة مشتريات - ${newInvoice.invoiceNumber}`,reference:newInvoice.invoiceNumber,date:newInvoice.invoiceDate});});}return newInvoice;}catch(error){console.error('خطأ في إضافة فاتورة المشتريات:',error);throw new Error('فشل في إضافة فاتورة المشتريات');}}// توليد معرف فاتورة مشتريات\ngeneratePurchaseInvoiceId(){const purchaseInvoices=this.getPurchaseInvoices();return purchaseInvoices.length>0?Math.max(...purchaseInvoices.map(inv=>inv.id))+1:1;}// توليد رقم فاتورة مشتريات\ngeneratePurchaseInvoiceNumber(){const prefix='PUR';const nextNumber=this.getPurchaseInvoices().length+1;return`${prefix}-${nextNumber.toString().padStart(6,'0')}`;}// === إدارة السندات ===\n// جلب جميع السندات\ngetVouchers(){try{const vouchers=localStorage.getItem('saudi_accounting_vouchers');return vouchers?JSON.parse(vouchers):[];}catch(error){console.error('خطأ في جلب السندات:',error);return[];}}// إضافة سند جديد\naddVoucher(voucherData){try{const vouchers=this.getVouchers();const newVoucher={...voucherData,id:this.generateVoucherId(),voucherNumber:this.generateVoucherNumber(voucherData.type),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};vouchers.push(newVoucher);localStorage.setItem('saudi_accounting_vouchers',JSON.stringify(vouchers));// إنشاء قيد محاسبي للسند\nthis.createVoucherJournalEntry(newVoucher);return newVoucher;}catch(error){console.error('خطأ في إضافة السند:',error);throw new Error('فشل في إضافة السند');}}// تحديث سند\nupdateVoucher(id,updates){try{const vouchers=this.getVouchers();const voucherIndex=vouchers.findIndex(voucher=>voucher.id===id);if(voucherIndex!==-1){vouchers[voucherIndex]={...vouchers[voucherIndex],...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_vouchers',JSON.stringify(vouchers));return vouchers[voucherIndex];}throw new Error('السند غير موجود');}catch(error){console.error('خطأ في تحديث السند:',error);throw new Error('فشل في تحديث السند');}}// حذف سند\ndeleteVoucher(id){try{const vouchers=this.getVouchers();const filteredVouchers=vouchers.filter(voucher=>voucher.id!==id);localStorage.setItem('saudi_accounting_vouchers',JSON.stringify(filteredVouchers));return true;}catch(error){console.error('خطأ في حذف السند:',error);throw new Error('فشل في حذف السند');}}// توليد معرف سند\ngenerateVoucherId(){const vouchers=this.getVouchers();return vouchers.length>0?Math.max(...vouchers.map(voucher=>voucher.id))+1:1;}// توليد رقم سند\ngenerateVoucherNumber(type){const vouchers=this.getVouchers().filter(v=>v.type===type);const prefix=type==='receipt'?'REC':'PAY';const nextNumber=vouchers.length+1;return`${prefix}-${nextNumber.toString().padStart(6,'0')}`;}// إنشاء قيد محاسبي للسند\ncreateVoucherJournalEntry(voucher){try{const journalEntry={date:voucher.voucherDate,reference:voucher.voucherNumber,description:`${voucher.type==='receipt'?'سند قبض':'سند صرف'} - ${voucher.description}`,type:voucher.type==='receipt'?'receipt':'payment',status:'posted'};if(voucher.type==='receipt'){// سند قبض: مدين النقدية، دائن العميل أو الإيراد\njournalEntry.debitEntries=[{accountId:voucher.accountId,// حساب النقدية أو البنك\namount:voucher.amount,description:voucher.description}];journalEntry.creditEntries=[{accountId:voucher.fromAccountId||voucher.customerId,// حساب العميل أو الإيراد\namount:voucher.amount,description:voucher.description}];}else{// سند صرف: مدين المصروف أو المورد، دائن النقدية\njournalEntry.debitEntries=[{accountId:voucher.toAccountId||voucher.supplierId,// حساب المصروف أو المورد\namount:voucher.amount,description:voucher.description}];journalEntry.creditEntries=[{accountId:voucher.accountId,// حساب النقدية أو البنك\namount:voucher.amount,description:voucher.description}];}journalEntry.totalAmount=voucher.amount;this.addJournalEntry(journalEntry);}catch(error){console.error('خطأ في إنشاء قيد السند:',error);}}// === وظائف القيود المحاسبية المحسنة ===\n// ترحيل قيد محاسبي\npostJournalEntry(id){try{const entries=this.getJournalEntries();const entryIndex=entries.findIndex(entry=>entry.id===id);if(entryIndex!==-1){entries[entryIndex]={...entries[entryIndex],status:'posted',postedAt:new Date().toISOString(),updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_journal_entries',JSON.stringify(entries));// تحديث أرصدة الحسابات\nthis.updateAccountBalances(entries[entryIndex]);return entries[entryIndex];}throw new Error('القيد غير موجود');}catch(error){console.error('خطأ في ترحيل القيد:',error);throw new Error('فشل في ترحيل القيد');}}// تحديث أرصدة الحسابات\nupdateAccountBalances(journalEntry){try{const accounts=this.getAccounts();// تحديث الحسابات المدينة\nif(journalEntry.debitEntries){journalEntry.debitEntries.forEach(debitEntry=>{const accountIndex=accounts.findIndex(acc=>acc.id===parseInt(debitEntry.accountId));if(accountIndex!==-1){accounts[accountIndex].balance=(accounts[accountIndex].balance||0)+parseFloat(debitEntry.amount);accounts[accountIndex].updatedAt=new Date().toISOString();}});}// تحديث الحسابات الدائنة\nif(journalEntry.creditEntries){journalEntry.creditEntries.forEach(creditEntry=>{const accountIndex=accounts.findIndex(acc=>acc.id===parseInt(creditEntry.accountId));if(accountIndex!==-1){accounts[accountIndex].balance=(accounts[accountIndex].balance||0)-parseFloat(creditEntry.amount);accounts[accountIndex].updatedAt=new Date().toISOString();}});}localStorage.setItem('saudi_accounting_accounts',JSON.stringify(accounts));}catch(error){console.error('خطأ في تحديث أرصدة الحسابات:',error);}}// === المحاسبة المالية ===\n// جلب جميع الحسابات\ngetAccounts(){try{const accounts=localStorage.getItem('saudi_accounting_accounts');return accounts?JSON.parse(accounts):this.getDefaultAccounts();}catch(error){console.error('خطأ في جلب الحسابات:',error);return this.getDefaultAccounts();}}// الحسابات الافتراضية\ngetDefaultAccounts(){return[{id:1,code:'1001',name:'النقدية',type:'asset',balance:0,description:'النقدية في الصندوق'},{id:2,code:'1002',name:'البنك',type:'asset',balance:0,description:'الحساب الجاري في البنك'},{id:3,code:'1101',name:'العملاء',type:'asset',balance:0,description:'مدينو العملاء'},{id:4,code:'1201',name:'المخزون',type:'asset',balance:0,description:'مخزون البضائع'},{id:5,code:'2001',name:'الموردون',type:'liability',balance:0,description:'دائنو الموردين'},{id:6,code:'2101',name:'ضريبة القيمة المضافة',type:'liability',balance:0,description:'ضريبة القيمة المضافة المستحقة'},{id:7,code:'3001',name:'رأس المال',type:'equity',balance:0,description:'رأس مال المالك'},{id:8,code:'4001',name:'المبيعات',type:'revenue',balance:0,description:'إيرادات المبيعات'},{id:9,code:'5001',name:'تكلفة البضاعة المباعة',type:'expense',balance:0,description:'تكلفة البضائع المباعة'},{id:10,code:'5101',name:'مصاريف التشغيل',type:'expense',balance:0,description:'المصاريف التشغيلية'}];}// إضافة حساب جديد\naddAccount(account){try{const accounts=this.getAccounts();const newAccount={...account,id:this.generateAccountId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};accounts.push(newAccount);localStorage.setItem('saudi_accounting_accounts',JSON.stringify(accounts));return newAccount;}catch(error){console.error('خطأ في إضافة الحساب:',error);throw new Error('فشل في إضافة الحساب');}}// توليد معرف حساب\ngenerateAccountId(){const accounts=this.getAccounts();return accounts.length>0?Math.max(...accounts.map(a=>a.id))+1:1;}// جلب جميع القيود المحاسبية\ngetJournalEntries(){try{const entries=localStorage.getItem('saudi_accounting_journal_entries');return entries?JSON.parse(entries):[];}catch(error){console.error('خطأ في جلب القيود:',error);return[];}}// إضافة قيد محاسبي\naddJournalEntry(entry){try{const entries=this.getJournalEntries();const newEntry={...entry,id:this.generateJournalEntryId(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};entries.push(newEntry);localStorage.setItem('saudi_accounting_journal_entries',JSON.stringify(entries));// تحديث أرصدة الحسابات\nthis.updateAccountBalances(newEntry);return newEntry;}catch(error){console.error('خطأ في إضافة القيد:',error);throw new Error('فشل في إضافة القيد');}}// تحديث أرصدة الحسابات\nupdateAccountBalances(entry){try{const accounts=this.getAccounts();entry.entries.forEach(line=>{const accountIndex=accounts.findIndex(a=>a.id===parseInt(line.accountId));if(accountIndex!==-1){// للأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص\n// للخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص\nif(accounts[accountIndex].type==='asset'||accounts[accountIndex].type==='expense'){accounts[accountIndex].balance+=line.debit-line.credit;}else{accounts[accountIndex].balance+=line.credit-line.debit;}accounts[accountIndex].updatedAt=new Date().toISOString();}});localStorage.setItem('saudi_accounting_accounts',JSON.stringify(accounts));}catch(error){console.error('خطأ في تحديث أرصدة الحسابات:',error);}}// توليد معرف قيد محاسبي\ngenerateJournalEntryId(){const entries=this.getJournalEntries();return entries.length>0?Math.max(...entries.map(e=>e.id))+1:1;}incrementInvoiceNumber(){try{const settings=this.getSettings();settings.nextInvoiceNumber+=1;localStorage.setItem('saudi_accounting_settings',JSON.stringify(settings));}catch(error){console.error('خطأ في تحديث رقم الفاتورة:',error);}}// === إدارة الإعدادات ===\ngetSettings(){try{const settings=localStorage.getItem('saudi_accounting_settings');return settings?JSON.parse(settings):this.getDefaultSettings();}catch(error){console.error('خطأ في جلب الإعدادات:',error);return this.getDefaultSettings();}}updateSettings(updates){try{const settings=this.getSettings();const newSettings={...settings,...updates,updatedAt:new Date().toISOString()};localStorage.setItem('saudi_accounting_settings',JSON.stringify(newSettings));return newSettings;}catch(error){console.error('خطأ في تحديث الإعدادات:',error);throw new Error('فشل في تحديث الإعدادات');}}// === التقارير والإحصائيات ===\n// إحصائيات اليوم\ngetTodayStats(){const today=new Date().toDateString();const todayInvoices=this.getInvoicesByDate(today);const totalSales=todayInvoices.reduce((sum,invoice)=>sum+invoice.total,0);const totalVAT=todayInvoices.reduce((sum,invoice)=>sum+invoice.vat,0);const totalInvoices=todayInvoices.length;return{totalSales,totalVAT,totalInvoices,averageInvoice:totalInvoices>0?totalSales/totalInvoices:0};}// إحصائيات الشهر\ngetMonthStats(){const currentMonth=new Date().getMonth();const currentYear=new Date().getFullYear();const monthInvoices=this.getInvoices().filter(invoice=>{const invoiceDate=new Date(invoice.createdAt);return invoiceDate.getMonth()===currentMonth&&invoiceDate.getFullYear()===currentYear;});const totalSales=monthInvoices.reduce((sum,invoice)=>sum+invoice.total,0);const totalVAT=monthInvoices.reduce((sum,invoice)=>sum+invoice.vat,0);const totalInvoices=monthInvoices.length;return{totalSales,totalVAT,totalInvoices,averageInvoice:totalInvoices>0?totalSales/totalInvoices:0};}// منتجات منخفضة المخزون\ngetLowStockProducts(){let threshold=arguments.length>0&&arguments[0]!==undefined?arguments[0]:5;const products=this.getProducts();return products.filter(product=>product.stock<=threshold);}// أفضل المنتجات مبيعاً\ngetTopSellingProducts(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:5;const invoices=this.getInvoices();const productSales={};invoices.forEach(invoice=>{invoice.items.forEach(item=>{if(productSales[item.id]){productSales[item.id].quantity+=item.quantity;productSales[item.id].revenue+=item.price*item.quantity;}else{productSales[item.id]={id:item.id,name:item.name,quantity:item.quantity,revenue:item.price*item.quantity};}});});return Object.values(productSales).sort((a,b)=>b.quantity-a.quantity).slice(0,limit);}// تصدير البيانات\nexportData(){return{invoices:this.getInvoices(),products:this.getProducts(),customers:this.getCustomers(),suppliers:this.getSuppliers(),accounts:this.getAccounts(),journalEntries:this.getJournalEntries(),settings:this.getSettings(),exportDate:new Date().toISOString()};}// استيراد البيانات\nimportData(data){try{if(data.invoices){localStorage.setItem('saudi_accounting_invoices',JSON.stringify(data.invoices));}if(data.products){localStorage.setItem('saudi_accounting_products',JSON.stringify(data.products));}if(data.customers){localStorage.setItem('saudi_accounting_customers',JSON.stringify(data.customers));}if(data.settings){localStorage.setItem('saudi_accounting_settings',JSON.stringify(data.settings));}return true;}catch(error){console.error('خطأ في استيراد البيانات:',error);throw new Error('فشل في استيراد البيانات');}}// مسح جميع البيانات\nclearAllData(){try{localStorage.removeItem('saudi_accounting_invoices');localStorage.removeItem('saudi_accounting_products');localStorage.removeItem('saudi_accounting_customers');localStorage.removeItem('saudi_accounting_settings');this.initializeDatabase();return true;}catch(error){console.error('خطأ في مسح البيانات:',error);throw new Error('فشل في مسح البيانات');}}// تسجيل الأنشطة\nlogActivity(activity){try{const activities=this.getActivities();const newActivity={id:Date.now(),...activity,timestamp:activity.timestamp||new Date().toISOString()};activities.push(newActivity);localStorage.setItem('activities',JSON.stringify(activities));return newActivity;}catch(error){console.error('خطأ في تسجيل النشاط:',error);return null;}}// الحصول على الأنشطة\ngetActivities(){try{return JSON.parse(localStorage.getItem('activities')||'[]');}catch(error){console.error('خطأ في قراءة الأنشطة:',error);return[];}}}// إنشاء مثيل واحد من قاعدة البيانات\nconst database=new LocalDatabase();export default database;", "map": {"version": 3, "names": ["LocalDatabase", "constructor", "initializeDatabase", "localStorage", "getItem", "setItem", "JSON", "stringify", "getDefaultProducts", "getDefaultSettings", "id", "name", "price", "stock", "category", "barcode", "description", "vatRate", "createdAt", "Date", "toISOString", "companyName", "vatNumber", "address", "phone", "email", "currency", "invoicePrefix", "nextInvoiceNumber", "saveInvoice", "invoice", "invoices", "getInvoices", "newInvoice", "generateInvoiceId", "status", "push", "updateStockAfterSale", "items", "incrementInvoiceNumber", "error", "console", "Error", "parse", "getInvoiceById", "find", "getInvoicesByDate", "date", "filter", "invoiceDate", "toDateString", "searchDate", "getProducts", "products", "addProduct", "product", "newProduct", "generateProductId", "updateProduct", "updates", "index", "findIndex", "updatedAt", "deleteProduct", "filteredProducts", "for<PERSON>ach", "item", "productIndex", "quantity", "getCustomers", "customers", "addCustomer", "customer", "newCustomer", "generateCustomerId", "settings", "getSettings", "length", "Math", "max", "map", "p", "c", "getSuppliers", "suppliers", "addSupplier", "supplier", "newSupplier", "generateSupplierId", "updateSupplier", "deleteSupplier", "filteredSuppliers", "s", "getPayments", "payments", "addPayment", "payment", "newPayment", "generatePaymentId", "updateInvoicePaymentStatus", "invoiceId", "amount", "updatePayment", "paymentIndex", "deletePayment", "filteredPayments", "paidAmount", "invoiceIndex", "inv", "totalPaid", "paymentStatus", "total", "getCustomerPayments", "customerId", "getInvoicePayments", "getInventoryMovements", "movements", "addInventoryMovement", "movement", "newMovement", "generateMovementId", "updateProductStock", "productId", "type", "movementType", "currentStock", "newStock", "m", "getProductMovements", "getInventoryMovementsByDateRange", "startDate", "endDate", "movementDate", "calculateTotalInventoryValue", "reduce", "getLowStockProducts", "minStock", "getOutOfStockProducts", "getEmployees", "employees", "addEmployee", "employee", "newEmployee", "generateEmployeeId", "updateEmployee", "employeeIndex", "emp", "deleteEmployee", "filteredEmployees", "getActiveEmployees", "getEmployeesByDepartment", "department", "calculateTotalSalaries", "activeEmployees", "salary", "website", "defaultPaymentTerms", "autoSaveInvoices", "language", "timezone", "dateFormat", "enableNotifications", "enableDarkMode", "enableAutoBackup", "fiscalYearStart", "backupFrequency", "maxInvoiceItems", "lowStockThreshold", "updateSettings", "newSettings", "currentSettings", "updatedSettings", "resetSettings", "defaultSettings", "exportAllData", "data", "accounts", "getAccounts", "journalEntries", "getJournalEntries", "inventoryMovements", "exportDate", "version", "importAllData", "returns", "quotations", "purchaseInvoices", "vouchers", "getReturns", "addReturn", "returnData", "newReturn", "generateReturnId", "updateInventoryForReturn", "updateReturn", "returnIndex", "ret", "oldReturn", "deleteReturn", "filteredReturns", "processReturn", "reason", "returnNumber", "reference", "returnDate", "getQuotations", "addQuotation", "quotationData", "newQuotation", "generateQuotationId", "updateQuotation", "quotationIndex", "quot", "deleteQuotation", "filteredQuotations", "getPurchaseInvoices", "addPurchaseInvoice", "invoiceData", "generatePurchaseInvoiceId", "invoiceNumber", "generatePurchaseInvoiceNumber", "prefix", "nextNumber", "toString", "padStart", "getVouchers", "addVoucher", "voucherData", "newVoucher", "generateVoucherId", "voucherNumber", "generateVoucherNumber", "createVoucherJournalEntry", "updateVoucher", "voucherIndex", "voucher", "deleteVoucher", "filteredVouchers", "v", "journalEntry", "voucherDate", "debitEntries", "accountId", "creditEntries", "fromAccountId", "toAccountId", "supplierId", "totalAmount", "addJournalEntry", "postJournalEntry", "entries", "entryIndex", "entry", "postedAt", "updateAccountBalances", "debitEntry", "accountIndex", "acc", "parseInt", "balance", "parseFloat", "creditEntry", "getDefaultAccounts", "code", "addAccount", "account", "newAccount", "generateAccountId", "a", "newEntry", "generateJournalEntryId", "line", "debit", "credit", "e", "getTodayStats", "today", "todayInvoices", "totalSales", "sum", "totalVAT", "vat", "totalInvoices", "averageInvoice", "getMonthStats", "currentMonth", "getMonth", "currentYear", "getFullYear", "monthInvoices", "threshold", "arguments", "undefined", "getTopSellingProducts", "limit", "productSales", "revenue", "Object", "values", "sort", "b", "slice", "exportData", "importData", "clearAllData", "removeItem", "logActivity", "activity", "activities", "getActivities", "newActivity", "now", "timestamp", "database"], "sources": ["D:/aronim/saudi-accounting-final/src/utils/database.js"], "sourcesContent": ["// نظام قاعدة البيانات المحلية للنظام المحاسبي السعودي\n// Local Database System for Saudi Accounting System\n\nclass LocalDatabase {\n  constructor() {\n    this.initializeDatabase();\n  }\n\n  // تهيئة قاعدة البيانات\n  initializeDatabase() {\n    // إنشاء الجداول الأساسية إذا لم تكن موجودة\n    if (!localStorage.getItem('saudi_accounting_invoices')) {\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify([]));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_products')) {\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(this.getDefaultProducts()));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_customers')) {\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify([]));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_settings')) {\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(this.getDefaultSettings()));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_payments')) {\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_inventory_movements')) {\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_employees')) {\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_returns')) {\n      localStorage.setItem('saudi_accounting_returns', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_quotations')) {\n      localStorage.setItem('saudi_accounting_quotations', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_purchase_invoices')) {\n      localStorage.setItem('saudi_accounting_purchase_invoices', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_vouchers')) {\n      localStorage.setItem('saudi_accounting_vouchers', JSON.stringify([]));\n    }\n  }\n\n  // المنتجات الافتراضية\n  getDefaultProducts() {\n    return [\n      {\n        id: 1,\n        name: 'لابتوب Dell',\n        price: 2500,\n        stock: 10,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'لابتوب Dell Inspiron 15',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 2,\n        name: 'ماوس لاسلكي',\n        price: 85,\n        stock: 25,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'ماوس لاسلكي عالي الجودة',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 3,\n        name: 'كيبورد ميكانيكي',\n        price: 150,\n        stock: 15,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'كيبورد ميكانيكي للألعاب',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 4,\n        name: 'شاشة 24 بوصة',\n        price: 800,\n        stock: 8,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'شاشة LED 24 بوصة Full HD',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 5,\n        name: 'طابعة HP',\n        price: 450,\n        stock: 5,\n        category: 'مكتبية',\n        barcode: '1234567890127',\n        description: 'طابعة HP LaserJet',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 6,\n        name: 'كاميرا ويب',\n        price: 120,\n        stock: 20,\n        category: 'إلكترونيات',\n        barcode: '1234567890128',\n        description: 'كاميرا ويب HD 1080p',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 7,\n        name: 'سماعات بلوتوث',\n        price: 200,\n        stock: 12,\n        category: 'إلكترونيات',\n        barcode: '1234567890129',\n        description: 'سماعات بلوتوث لاسلكية',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 8,\n        name: 'قلم رقمي',\n        price: 75,\n        stock: 30,\n        category: 'مكتبية',\n        barcode: '1234567890130',\n        description: 'قلم رقمي للرسم والكتابة',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      companyName: 'شركة نظام المحاسبة السعودي المحدودة',\n      vatNumber: '310122393500003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      vatRate: 0.15,\n      currency: 'ريال سعودي',\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1001,\n      createdAt: new Date().toISOString()\n    };\n  }\n\n  // === إدارة الفواتير ===\n  \n  // حفظ فاتورة جديدة\n  saveInvoice(invoice) {\n    try {\n      const invoices = this.getInvoices();\n      const newInvoice = {\n        ...invoice,\n        id: this.generateInvoiceId(),\n        createdAt: new Date().toISOString(),\n        status: 'paid'\n      };\n      \n      invoices.push(newInvoice);\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n      \n      // تحديث المخزون\n      this.updateStockAfterSale(invoice.items);\n      \n      // تحديث رقم الفاتورة التالي\n      this.incrementInvoiceNumber();\n      \n      return newInvoice;\n    } catch (error) {\n      console.error('خطأ في حفظ الفاتورة:', error);\n      throw new Error('فشل في حفظ الفاتورة');\n    }\n  }\n\n  // جلب جميع الفواتير\n  getInvoices() {\n    try {\n      const invoices = localStorage.getItem('saudi_accounting_invoices');\n      return invoices ? JSON.parse(invoices) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الفواتير:', error);\n      return [];\n    }\n  }\n\n  // جلب فاتورة بالرقم\n  getInvoiceById(id) {\n    const invoices = this.getInvoices();\n    return invoices.find(invoice => invoice.id === id);\n  }\n\n  // جلب فواتير بتاريخ محدد\n  getInvoicesByDate(date) {\n    const invoices = this.getInvoices();\n    return invoices.filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt).toDateString();\n      const searchDate = new Date(date).toDateString();\n      return invoiceDate === searchDate;\n    });\n  }\n\n  // === إدارة المنتجات ===\n  \n  // جلب جميع المنتجات\n  getProducts() {\n    try {\n      const products = localStorage.getItem('saudi_accounting_products');\n      return products ? JSON.parse(products) : this.getDefaultProducts();\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات:', error);\n      return this.getDefaultProducts();\n    }\n  }\n\n  // إضافة منتج جديد\n  addProduct(product) {\n    try {\n      const products = this.getProducts();\n      const newProduct = {\n        ...product,\n        id: this.generateProductId(),\n        createdAt: new Date().toISOString()\n      };\n      \n      products.push(newProduct);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      return newProduct;\n    } catch (error) {\n      console.error('خطأ في إضافة المنتج:', error);\n      throw new Error('فشل في إضافة المنتج');\n    }\n  }\n\n  // تحديث منتج\n  updateProduct(id, updates) {\n    try {\n      const products = this.getProducts();\n      const index = products.findIndex(product => product.id === id);\n      \n      if (index !== -1) {\n        products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n        return products[index];\n      }\n      \n      throw new Error('المنتج غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المنتج:', error);\n      throw new Error('فشل في تحديث المنتج');\n    }\n  }\n\n  // حذف منتج\n  deleteProduct(id) {\n    try {\n      const products = this.getProducts();\n      const filteredProducts = products.filter(product => product.id !== id);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(filteredProducts));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المنتج:', error);\n      throw new Error('فشل في حذف المنتج');\n    }\n  }\n\n  // تحديث المخزون بعد البيع\n  updateStockAfterSale(items) {\n    try {\n      const products = this.getProducts();\n      \n      items.forEach(item => {\n        const productIndex = products.findIndex(product => product.id === item.id);\n        if (productIndex !== -1) {\n          products[productIndex].stock -= item.quantity;\n          if (products[productIndex].stock < 0) {\n            products[productIndex].stock = 0;\n          }\n        }\n      });\n      \n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n    } catch (error) {\n      console.error('خطأ في تحديث المخزون:', error);\n    }\n  }\n\n  // === إدارة العملاء ===\n  \n  // جلب جميع العملاء\n  getCustomers() {\n    try {\n      const customers = localStorage.getItem('saudi_accounting_customers');\n      return customers ? JSON.parse(customers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب العملاء:', error);\n      return [];\n    }\n  }\n\n  // إضافة عميل جديد\n  addCustomer(customer) {\n    try {\n      const customers = this.getCustomers();\n      const newCustomer = {\n        ...customer,\n        id: this.generateCustomerId(),\n        createdAt: new Date().toISOString()\n      };\n      \n      customers.push(newCustomer);\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify(customers));\n      return newCustomer;\n    } catch (error) {\n      console.error('خطأ في إضافة العميل:', error);\n      throw new Error('فشل في إضافة العميل');\n    }\n  }\n\n  // === مولدات الأرقام ===\n  \n  generateInvoiceId() {\n    const settings = this.getSettings();\n    return `${settings.invoicePrefix}-${settings.nextInvoiceNumber}`;\n  }\n\n  generateProductId() {\n    const products = this.getProducts();\n    return products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;\n  }\n\n  generateCustomerId() {\n    const customers = this.getCustomers();\n    return customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;\n  }\n\n  // === إدارة الموردين ===\n\n  // جلب جميع الموردين\n  getSuppliers() {\n    try {\n      const suppliers = localStorage.getItem('saudi_accounting_suppliers');\n      return suppliers ? JSON.parse(suppliers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموردين:', error);\n      return [];\n    }\n  }\n\n  // إضافة مورد جديد\n  addSupplier(supplier) {\n    try {\n      const suppliers = this.getSuppliers();\n      const newSupplier = {\n        ...supplier,\n        id: this.generateSupplierId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      suppliers.push(newSupplier);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n      return newSupplier;\n    } catch (error) {\n      console.error('خطأ في إضافة المورد:', error);\n      throw new Error('فشل في إضافة المورد');\n    }\n  }\n\n  // تحديث مورد\n  updateSupplier(id, updates) {\n    try {\n      const suppliers = this.getSuppliers();\n      const index = suppliers.findIndex(supplier => supplier.id === id);\n\n      if (index !== -1) {\n        suppliers[index] = {\n          ...suppliers[index],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n        return suppliers[index];\n      }\n\n      throw new Error('المورد غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المورد:', error);\n      throw new Error('فشل في تحديث المورد');\n    }\n  }\n\n  // حذف مورد\n  deleteSupplier(id) {\n    try {\n      const suppliers = this.getSuppliers();\n      const filteredSuppliers = suppliers.filter(supplier => supplier.id !== id);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(filteredSuppliers));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المورد:', error);\n      throw new Error('فشل في حذف المورد');\n    }\n  }\n\n  // توليد معرف مورد\n  generateSupplierId() {\n    const suppliers = this.getSuppliers();\n    return suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 1;\n  }\n\n  // === إدارة المدفوعات ===\n\n  // جلب جميع المدفوعات\n  getPayments() {\n    try {\n      const payments = localStorage.getItem('saudi_accounting_payments');\n      return payments ? JSON.parse(payments) : [];\n    } catch (error) {\n      console.error('خطأ في جلب المدفوعات:', error);\n      return [];\n    }\n  }\n\n  // إضافة دفعة جديدة\n  addPayment(payment) {\n    try {\n      const payments = this.getPayments();\n      const newPayment = {\n        ...payment,\n        id: this.generatePaymentId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      payments.push(newPayment);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n\n      // تحديث حالة الفاتورة إذا كانت الدفعة مكتملة\n      if (payment.status === 'completed') {\n        this.updateInvoicePaymentStatus(payment.invoiceId, payment.amount);\n      }\n\n      return newPayment;\n    } catch (error) {\n      console.error('خطأ في إضافة الدفعة:', error);\n      throw new Error('فشل في إضافة الدفعة');\n    }\n  }\n\n  // تحديث دفعة\n  updatePayment(id, updates) {\n    try {\n      const payments = this.getPayments();\n      const paymentIndex = payments.findIndex(p => p.id === id);\n\n      if (paymentIndex !== -1) {\n        payments[paymentIndex] = {\n          ...payments[paymentIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n        return payments[paymentIndex];\n      }\n\n      throw new Error('الدفعة غير موجودة');\n    } catch (error) {\n      console.error('خطأ في تحديث الدفعة:', error);\n      throw new Error('فشل في تحديث الدفعة');\n    }\n  }\n\n  // حذف دفعة\n  deletePayment(id) {\n    try {\n      const payments = this.getPayments();\n      const filteredPayments = payments.filter(p => p.id !== id);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(filteredPayments));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الدفعة:', error);\n      throw new Error('فشل في حذف الدفعة');\n    }\n  }\n\n  // توليد معرف دفعة\n  generatePaymentId() {\n    const payments = this.getPayments();\n    return payments.length > 0 ? Math.max(...payments.map(p => p.id)) + 1 : 1;\n  }\n\n  // تحديث حالة دفع الفاتورة\n  updateInvoicePaymentStatus(invoiceId, paidAmount) {\n    try {\n      const invoices = this.getInvoices();\n      const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);\n\n      if (invoiceIndex !== -1) {\n        const invoice = invoices[invoiceIndex];\n        const totalPaid = (invoice.paidAmount || 0) + paidAmount;\n\n        let paymentStatus = 'unpaid';\n        if (totalPaid >= invoice.total) {\n          paymentStatus = 'paid';\n        } else if (totalPaid > 0) {\n          paymentStatus = 'partial';\n        }\n\n        invoices[invoiceIndex] = {\n          ...invoice,\n          paidAmount: totalPaid,\n          paymentStatus: paymentStatus,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث حالة دفع الفاتورة:', error);\n    }\n  }\n\n  // جلب مدفوعات عميل معين\n  getCustomerPayments(customerId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.customerId === customerId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات العميل:', error);\n      return [];\n    }\n  }\n\n  // جلب مدفوعات فاتورة معينة\n  getInvoicePayments(invoiceId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.invoiceId === invoiceId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات الفاتورة:', error);\n      return [];\n    }\n  }\n\n  // === إدارة حركات المخزون ===\n\n  // جلب جميع حركات المخزون\n  getInventoryMovements() {\n    try {\n      const movements = localStorage.getItem('saudi_accounting_inventory_movements');\n      return movements ? JSON.parse(movements) : [];\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون:', error);\n      return [];\n    }\n  }\n\n  // إضافة حركة مخزون جديدة\n  addInventoryMovement(movement) {\n    try {\n      const movements = this.getInventoryMovements();\n      const newMovement = {\n        ...movement,\n        id: this.generateMovementId(),\n        createdAt: new Date().toISOString()\n      };\n\n      movements.push(newMovement);\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(movements));\n\n      // تحديث مخزون المنتج\n      this.updateProductStock(movement.productId, movement.quantity, movement.type);\n\n      return newMovement;\n    } catch (error) {\n      console.error('خطأ في إضافة حركة المخزون:', error);\n      throw new Error('فشل في إضافة حركة المخزون');\n    }\n  }\n\n  // تحديث مخزون المنتج\n  updateProductStock(productId, quantity, movementType) {\n    try {\n      const products = this.getProducts();\n      const productIndex = products.findIndex(p => p.id === productId);\n\n      if (productIndex !== -1) {\n        const currentStock = products[productIndex].stock || 0;\n        let newStock = currentStock;\n\n        if (movementType === 'in') {\n          newStock = currentStock + quantity;\n        } else if (movementType === 'out' || movementType === 'damaged' || movementType === 'expired') {\n          newStock = Math.max(0, currentStock - quantity);\n        } else if (movementType === 'adjustment') {\n          newStock = quantity; // التعديل يحدد الكمية الجديدة مباشرة\n        }\n\n        products[productIndex] = {\n          ...products[productIndex],\n          stock: newStock,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث مخزون المنتج:', error);\n    }\n  }\n\n  // توليد معرف حركة مخزون\n  generateMovementId() {\n    const movements = this.getInventoryMovements();\n    return movements.length > 0 ? Math.max(...movements.map(m => m.id)) + 1 : 1;\n  }\n\n  // جلب حركات مخزون منتج معين\n  getProductMovements(productId) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => movement.productId === productId);\n    } catch (error) {\n      console.error('خطأ في جلب حركات المنتج:', error);\n      return [];\n    }\n  }\n\n  // جلب تقرير حركات المخزون لفترة معينة\n  getInventoryMovementsByDateRange(startDate, endDate) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => {\n        const movementDate = new Date(movement.date);\n        return movementDate >= new Date(startDate) && movementDate <= new Date(endDate);\n      });\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون للفترة:', error);\n      return [];\n    }\n  }\n\n  // حساب قيمة المخزون الإجمالية\n  calculateTotalInventoryValue() {\n    try {\n      const products = this.getProducts();\n      return products.reduce((total, product) => {\n        return total + (product.stock * product.price);\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب قيمة المخزون:', error);\n      return 0;\n    }\n  }\n\n  // جلب المنتجات منخفضة المخزون\n  getLowStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock <= (product.minStock || 5));\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);\n      return [];\n    }\n  }\n\n  // جلب المنتجات نافدة المخزون\n  getOutOfStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock === 0);\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات نافدة المخزون:', error);\n      return [];\n    }\n  }\n\n  // === إدارة الموظفين ===\n\n  // جلب جميع الموظفين\n  getEmployees() {\n    try {\n      const employees = localStorage.getItem('saudi_accounting_employees');\n      return employees ? JSON.parse(employees) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين:', error);\n      return [];\n    }\n  }\n\n  // إضافة موظف جديد\n  addEmployee(employee) {\n    try {\n      const employees = this.getEmployees();\n      const newEmployee = {\n        ...employee,\n        id: this.generateEmployeeId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      employees.push(newEmployee);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n\n      return newEmployee;\n    } catch (error) {\n      console.error('خطأ في إضافة الموظف:', error);\n      throw new Error('فشل في إضافة الموظف');\n    }\n  }\n\n  // تحديث موظف\n  updateEmployee(id, updates) {\n    try {\n      const employees = this.getEmployees();\n      const employeeIndex = employees.findIndex(emp => emp.id === id);\n\n      if (employeeIndex !== -1) {\n        employees[employeeIndex] = {\n          ...employees[employeeIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n        return employees[employeeIndex];\n      }\n\n      throw new Error('الموظف غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث الموظف:', error);\n      throw new Error('فشل في تحديث الموظف');\n    }\n  }\n\n  // حذف موظف\n  deleteEmployee(id) {\n    try {\n      const employees = this.getEmployees();\n      const filteredEmployees = employees.filter(emp => emp.id !== id);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(filteredEmployees));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الموظف:', error);\n      throw new Error('فشل في حذف الموظف');\n    }\n  }\n\n  // توليد معرف موظف\n  generateEmployeeId() {\n    const employees = this.getEmployees();\n    return employees.length > 0 ? Math.max(...employees.map(emp => emp.id)) + 1 : 1;\n  }\n\n  // جلب الموظفين النشطين\n  getActiveEmployees() {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.status === 'active');\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين النشطين:', error);\n      return [];\n    }\n  }\n\n  // جلب موظفين حسب القسم\n  getEmployeesByDepartment(department) {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.department === department);\n    } catch (error) {\n      console.error('خطأ في جلب موظفين القسم:', error);\n      return [];\n    }\n  }\n\n  // حساب إجمالي الرواتب\n  calculateTotalSalaries() {\n    try {\n      const activeEmployees = this.getActiveEmployees();\n      return activeEmployees.reduce((total, employee) => {\n        return total + (employee.salary || 0);\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب إجمالي الرواتب:', error);\n      return 0;\n    }\n  }\n\n  // === إدارة الإعدادات المحسنة ===\n\n  // جلب الإعدادات الافتراضية المحسنة\n  getDefaultSettings() {\n    return {\n      // بيانات الشركة\n      companyName: 'شركة المحاسبة السعودية',\n      vatNumber: '300000000000003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      website: 'www.company.com',\n\n      // إعدادات الفواتير\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1,\n      vatRate: 15,\n      currency: 'SAR',\n      defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n      autoSaveInvoices: true,\n\n      // إعدادات النظام\n      language: 'ar',\n      timezone: 'Asia/Riyadh',\n      dateFormat: 'DD/MM/YYYY',\n      enableNotifications: true,\n      enableDarkMode: false,\n      enableAutoBackup: false,\n\n      // إعدادات إضافية\n      fiscalYearStart: '01/01',\n      backupFrequency: 'weekly',\n      maxInvoiceItems: 50,\n      lowStockThreshold: 5,\n\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n  }\n\n  // تحديث الإعدادات\n  updateSettings(newSettings) {\n    try {\n      const currentSettings = this.getSettings();\n      const updatedSettings = {\n        ...currentSettings,\n        ...newSettings,\n        updatedAt: new Date().toISOString()\n      };\n\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(updatedSettings));\n      return updatedSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // إعادة تعيين الإعدادات للقيم الافتراضية\n  resetSettings() {\n    try {\n      const defaultSettings = this.getDefaultSettings();\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(defaultSettings));\n      return defaultSettings;\n    } catch (error) {\n      console.error('خطأ في إعادة تعيين الإعدادات:', error);\n      throw new Error('فشل في إعادة تعيين الإعدادات');\n    }\n  }\n\n  // تصدير جميع البيانات\n  exportAllData() {\n    try {\n      const data = {\n        settings: this.getSettings(),\n        invoices: this.getInvoices(),\n        products: this.getProducts(),\n        customers: this.getCustomers(),\n        suppliers: this.getSuppliers(),\n        accounts: this.getAccounts(),\n        journalEntries: this.getJournalEntries(),\n        payments: this.getPayments(),\n        inventoryMovements: this.getInventoryMovements(),\n        employees: this.getEmployees(),\n        exportDate: new Date().toISOString(),\n        version: '1.0.0'\n      };\n\n      return data;\n    } catch (error) {\n      console.error('خطأ في تصدير البيانات:', error);\n      throw new Error('فشل في تصدير البيانات');\n    }\n  }\n\n  // استيراد جميع البيانات\n  importAllData(data) {\n    try {\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.suppliers) {\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(data.suppliers));\n      }\n      if (data.accounts) {\n        localStorage.setItem('saudi_accounting_accounts', JSON.stringify(data.accounts));\n      }\n      if (data.journalEntries) {\n        localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(data.journalEntries));\n      }\n      if (data.payments) {\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(data.payments));\n      }\n      if (data.inventoryMovements) {\n        localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(data.inventoryMovements));\n      }\n      if (data.employees) {\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(data.employees));\n      }\n      if (data.returns) {\n        localStorage.setItem('saudi_accounting_returns', JSON.stringify(data.returns));\n      }\n      if (data.quotations) {\n        localStorage.setItem('saudi_accounting_quotations', JSON.stringify(data.quotations));\n      }\n      if (data.purchaseInvoices) {\n        localStorage.setItem('saudi_accounting_purchase_invoices', JSON.stringify(data.purchaseInvoices));\n      }\n      if (data.vouchers) {\n        localStorage.setItem('saudi_accounting_vouchers', JSON.stringify(data.vouchers));\n      }\n\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // === إدارة المرتجعات ===\n\n  // جلب جميع المرتجعات\n  getReturns() {\n    try {\n      const returns = localStorage.getItem('saudi_accounting_returns');\n      return returns ? JSON.parse(returns) : [];\n    } catch (error) {\n      console.error('خطأ في جلب المرتجعات:', error);\n      return [];\n    }\n  }\n\n  // إضافة مرتجع جديد\n  addReturn(returnData) {\n    try {\n      const returns = this.getReturns();\n      const newReturn = {\n        ...returnData,\n        id: this.generateReturnId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      returns.push(newReturn);\n      localStorage.setItem('saudi_accounting_returns', JSON.stringify(returns));\n\n      // تحديث المخزون إذا كان المرتجع معتمد\n      if (returnData.status === 'approved' || returnData.status === 'processed') {\n        this.updateInventoryForReturn(newReturn);\n      }\n\n      return newReturn;\n    } catch (error) {\n      console.error('خطأ في إضافة المرتجع:', error);\n      throw new Error('فشل في إضافة المرتجع');\n    }\n  }\n\n  // تحديث مرتجع\n  updateReturn(id, updates) {\n    try {\n      const returns = this.getReturns();\n      const returnIndex = returns.findIndex(ret => ret.id === id);\n\n      if (returnIndex !== -1) {\n        const oldReturn = returns[returnIndex];\n        returns[returnIndex] = {\n          ...oldReturn,\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_returns', JSON.stringify(returns));\n\n        // تحديث المخزون إذا تغيرت الحالة\n        if (oldReturn.status !== updates.status &&\n            (updates.status === 'approved' || updates.status === 'processed')) {\n          this.updateInventoryForReturn(returns[returnIndex]);\n        }\n\n        return returns[returnIndex];\n      }\n\n      throw new Error('المرتجع غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المرتجع:', error);\n      throw new Error('فشل في تحديث المرتجع');\n    }\n  }\n\n  // حذف مرتجع\n  deleteReturn(id) {\n    try {\n      const returns = this.getReturns();\n      const filteredReturns = returns.filter(ret => ret.id !== id);\n      localStorage.setItem('saudi_accounting_returns', JSON.stringify(filteredReturns));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المرتجع:', error);\n      throw new Error('فشل في حذف المرتجع');\n    }\n  }\n\n  // معالجة مرتجع\n  processReturn(id) {\n    try {\n      return this.updateReturn(id, { status: 'processed' });\n    } catch (error) {\n      console.error('خطأ في معالجة المرتجع:', error);\n      throw new Error('فشل في معالجة المرتجع');\n    }\n  }\n\n  // توليد معرف مرتجع\n  generateReturnId() {\n    const returns = this.getReturns();\n    return returns.length > 0 ? Math.max(...returns.map(ret => ret.id)) + 1 : 1;\n  }\n\n  // تحديث المخزون للمرتجع\n  updateInventoryForReturn(returnData) {\n    try {\n      if (returnData.items && returnData.items.length > 0) {\n        returnData.items.forEach(item => {\n          if (returnData.type === 'sales_return') {\n            // إضافة الكمية للمخزون في حالة مرتجع المبيعات\n            this.addInventoryMovement({\n              productId: item.productId,\n              type: 'in',\n              quantity: item.quantity,\n              reason: `مرتجع مبيعات - ${returnData.returnNumber}`,\n              reference: returnData.returnNumber,\n              date: returnData.returnDate\n            });\n          } else if (returnData.type === 'purchase_return') {\n            // خصم الكمية من المخزون في حالة مرتجع المشتريات\n            this.addInventoryMovement({\n              productId: item.productId,\n              type: 'out',\n              quantity: item.quantity,\n              reason: `مرتجع مشتريات - ${returnData.returnNumber}`,\n              reference: returnData.returnNumber,\n              date: returnData.returnDate\n            });\n          }\n        });\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث المخزون للمرتجع:', error);\n    }\n  }\n\n  // === إدارة عروض الأسعار ===\n\n  // جلب جميع عروض الأسعار\n  getQuotations() {\n    try {\n      const quotations = localStorage.getItem('saudi_accounting_quotations');\n      return quotations ? JSON.parse(quotations) : [];\n    } catch (error) {\n      console.error('خطأ في جلب عروض الأسعار:', error);\n      return [];\n    }\n  }\n\n  // إضافة عرض سعر جديد\n  addQuotation(quotationData) {\n    try {\n      const quotations = this.getQuotations();\n      const newQuotation = {\n        ...quotationData,\n        id: this.generateQuotationId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      quotations.push(newQuotation);\n      localStorage.setItem('saudi_accounting_quotations', JSON.stringify(quotations));\n\n      return newQuotation;\n    } catch (error) {\n      console.error('خطأ في إضافة عرض السعر:', error);\n      throw new Error('فشل في إضافة عرض السعر');\n    }\n  }\n\n  // تحديث عرض سعر\n  updateQuotation(id, updates) {\n    try {\n      const quotations = this.getQuotations();\n      const quotationIndex = quotations.findIndex(quot => quot.id === id);\n\n      if (quotationIndex !== -1) {\n        quotations[quotationIndex] = {\n          ...quotations[quotationIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_quotations', JSON.stringify(quotations));\n        return quotations[quotationIndex];\n      }\n\n      throw new Error('عرض السعر غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث عرض السعر:', error);\n      throw new Error('فشل في تحديث عرض السعر');\n    }\n  }\n\n  // حذف عرض سعر\n  deleteQuotation(id) {\n    try {\n      const quotations = this.getQuotations();\n      const filteredQuotations = quotations.filter(quot => quot.id !== id);\n      localStorage.setItem('saudi_accounting_quotations', JSON.stringify(filteredQuotations));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف عرض السعر:', error);\n      throw new Error('فشل في حذف عرض السعر');\n    }\n  }\n\n  // توليد معرف عرض سعر\n  generateQuotationId() {\n    const quotations = this.getQuotations();\n    return quotations.length > 0 ? Math.max(...quotations.map(quot => quot.id)) + 1 : 1;\n  }\n\n  // === إدارة فواتير المشتريات ===\n\n  // جلب جميع فواتير المشتريات\n  getPurchaseInvoices() {\n    try {\n      const purchaseInvoices = localStorage.getItem('saudi_accounting_purchase_invoices');\n      return purchaseInvoices ? JSON.parse(purchaseInvoices) : [];\n    } catch (error) {\n      console.error('خطأ في جلب فواتير المشتريات:', error);\n      return [];\n    }\n  }\n\n  // إضافة فاتورة مشتريات جديدة\n  addPurchaseInvoice(invoiceData) {\n    try {\n      const purchaseInvoices = this.getPurchaseInvoices();\n      const newInvoice = {\n        ...invoiceData,\n        id: this.generatePurchaseInvoiceId(),\n        invoiceNumber: this.generatePurchaseInvoiceNumber(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      purchaseInvoices.push(newInvoice);\n      localStorage.setItem('saudi_accounting_purchase_invoices', JSON.stringify(purchaseInvoices));\n\n      // تحديث المخزون\n      if (newInvoice.items && newInvoice.items.length > 0) {\n        newInvoice.items.forEach(item => {\n          this.addInventoryMovement({\n            productId: item.productId,\n            type: 'in',\n            quantity: item.quantity,\n            reason: `فاتورة مشتريات - ${newInvoice.invoiceNumber}`,\n            reference: newInvoice.invoiceNumber,\n            date: newInvoice.invoiceDate\n          });\n        });\n      }\n\n      return newInvoice;\n    } catch (error) {\n      console.error('خطأ في إضافة فاتورة المشتريات:', error);\n      throw new Error('فشل في إضافة فاتورة المشتريات');\n    }\n  }\n\n  // توليد معرف فاتورة مشتريات\n  generatePurchaseInvoiceId() {\n    const purchaseInvoices = this.getPurchaseInvoices();\n    return purchaseInvoices.length > 0 ? Math.max(...purchaseInvoices.map(inv => inv.id)) + 1 : 1;\n  }\n\n  // توليد رقم فاتورة مشتريات\n  generatePurchaseInvoiceNumber() {\n    const prefix = 'PUR';\n    const nextNumber = this.getPurchaseInvoices().length + 1;\n    return `${prefix}-${nextNumber.toString().padStart(6, '0')}`;\n  }\n\n  // === إدارة السندات ===\n\n  // جلب جميع السندات\n  getVouchers() {\n    try {\n      const vouchers = localStorage.getItem('saudi_accounting_vouchers');\n      return vouchers ? JSON.parse(vouchers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب السندات:', error);\n      return [];\n    }\n  }\n\n  // إضافة سند جديد\n  addVoucher(voucherData) {\n    try {\n      const vouchers = this.getVouchers();\n      const newVoucher = {\n        ...voucherData,\n        id: this.generateVoucherId(),\n        voucherNumber: this.generateVoucherNumber(voucherData.type),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      vouchers.push(newVoucher);\n      localStorage.setItem('saudi_accounting_vouchers', JSON.stringify(vouchers));\n\n      // إنشاء قيد محاسبي للسند\n      this.createVoucherJournalEntry(newVoucher);\n\n      return newVoucher;\n    } catch (error) {\n      console.error('خطأ في إضافة السند:', error);\n      throw new Error('فشل في إضافة السند');\n    }\n  }\n\n  // تحديث سند\n  updateVoucher(id, updates) {\n    try {\n      const vouchers = this.getVouchers();\n      const voucherIndex = vouchers.findIndex(voucher => voucher.id === id);\n\n      if (voucherIndex !== -1) {\n        vouchers[voucherIndex] = {\n          ...vouchers[voucherIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_vouchers', JSON.stringify(vouchers));\n        return vouchers[voucherIndex];\n      }\n\n      throw new Error('السند غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث السند:', error);\n      throw new Error('فشل في تحديث السند');\n    }\n  }\n\n  // حذف سند\n  deleteVoucher(id) {\n    try {\n      const vouchers = this.getVouchers();\n      const filteredVouchers = vouchers.filter(voucher => voucher.id !== id);\n      localStorage.setItem('saudi_accounting_vouchers', JSON.stringify(filteredVouchers));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف السند:', error);\n      throw new Error('فشل في حذف السند');\n    }\n  }\n\n  // توليد معرف سند\n  generateVoucherId() {\n    const vouchers = this.getVouchers();\n    return vouchers.length > 0 ? Math.max(...vouchers.map(voucher => voucher.id)) + 1 : 1;\n  }\n\n  // توليد رقم سند\n  generateVoucherNumber(type) {\n    const vouchers = this.getVouchers().filter(v => v.type === type);\n    const prefix = type === 'receipt' ? 'REC' : 'PAY';\n    const nextNumber = vouchers.length + 1;\n    return `${prefix}-${nextNumber.toString().padStart(6, '0')}`;\n  }\n\n  // إنشاء قيد محاسبي للسند\n  createVoucherJournalEntry(voucher) {\n    try {\n      const journalEntry = {\n        date: voucher.voucherDate,\n        reference: voucher.voucherNumber,\n        description: `${voucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} - ${voucher.description}`,\n        type: voucher.type === 'receipt' ? 'receipt' : 'payment',\n        status: 'posted'\n      };\n\n      if (voucher.type === 'receipt') {\n        // سند قبض: مدين النقدية، دائن العميل أو الإيراد\n        journalEntry.debitEntries = [{\n          accountId: voucher.accountId, // حساب النقدية أو البنك\n          amount: voucher.amount,\n          description: voucher.description\n        }];\n        journalEntry.creditEntries = [{\n          accountId: voucher.fromAccountId || voucher.customerId, // حساب العميل أو الإيراد\n          amount: voucher.amount,\n          description: voucher.description\n        }];\n      } else {\n        // سند صرف: مدين المصروف أو المورد، دائن النقدية\n        journalEntry.debitEntries = [{\n          accountId: voucher.toAccountId || voucher.supplierId, // حساب المصروف أو المورد\n          amount: voucher.amount,\n          description: voucher.description\n        }];\n        journalEntry.creditEntries = [{\n          accountId: voucher.accountId, // حساب النقدية أو البنك\n          amount: voucher.amount,\n          description: voucher.description\n        }];\n      }\n\n      journalEntry.totalAmount = voucher.amount;\n      this.addJournalEntry(journalEntry);\n    } catch (error) {\n      console.error('خطأ في إنشاء قيد السند:', error);\n    }\n  }\n\n  // === وظائف القيود المحاسبية المحسنة ===\n\n  // ترحيل قيد محاسبي\n  postJournalEntry(id) {\n    try {\n      const entries = this.getJournalEntries();\n      const entryIndex = entries.findIndex(entry => entry.id === id);\n\n      if (entryIndex !== -1) {\n        entries[entryIndex] = {\n          ...entries[entryIndex],\n          status: 'posted',\n          postedAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(entries));\n\n        // تحديث أرصدة الحسابات\n        this.updateAccountBalances(entries[entryIndex]);\n\n        return entries[entryIndex];\n      }\n\n      throw new Error('القيد غير موجود');\n    } catch (error) {\n      console.error('خطأ في ترحيل القيد:', error);\n      throw new Error('فشل في ترحيل القيد');\n    }\n  }\n\n  // تحديث أرصدة الحسابات\n  updateAccountBalances(journalEntry) {\n    try {\n      const accounts = this.getAccounts();\n\n      // تحديث الحسابات المدينة\n      if (journalEntry.debitEntries) {\n        journalEntry.debitEntries.forEach(debitEntry => {\n          const accountIndex = accounts.findIndex(acc => acc.id === parseInt(debitEntry.accountId));\n          if (accountIndex !== -1) {\n            accounts[accountIndex].balance = (accounts[accountIndex].balance || 0) + parseFloat(debitEntry.amount);\n            accounts[accountIndex].updatedAt = new Date().toISOString();\n          }\n        });\n      }\n\n      // تحديث الحسابات الدائنة\n      if (journalEntry.creditEntries) {\n        journalEntry.creditEntries.forEach(creditEntry => {\n          const accountIndex = accounts.findIndex(acc => acc.id === parseInt(creditEntry.accountId));\n          if (accountIndex !== -1) {\n            accounts[accountIndex].balance = (accounts[accountIndex].balance || 0) - parseFloat(creditEntry.amount);\n            accounts[accountIndex].updatedAt = new Date().toISOString();\n          }\n        });\n      }\n\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n    } catch (error) {\n      console.error('خطأ في تحديث أرصدة الحسابات:', error);\n    }\n  }\n\n  // === المحاسبة المالية ===\n\n  // جلب جميع الحسابات\n  getAccounts() {\n    try {\n      const accounts = localStorage.getItem('saudi_accounting_accounts');\n      return accounts ? JSON.parse(accounts) : this.getDefaultAccounts();\n    } catch (error) {\n      console.error('خطأ في جلب الحسابات:', error);\n      return this.getDefaultAccounts();\n    }\n  }\n\n  // الحسابات الافتراضية\n  getDefaultAccounts() {\n    return [\n      { id: 1, code: '1001', name: 'النقدية', type: 'asset', balance: 0, description: 'النقدية في الصندوق' },\n      { id: 2, code: '1002', name: 'البنك', type: 'asset', balance: 0, description: 'الحساب الجاري في البنك' },\n      { id: 3, code: '1101', name: 'العملاء', type: 'asset', balance: 0, description: 'مدينو العملاء' },\n      { id: 4, code: '1201', name: 'المخزون', type: 'asset', balance: 0, description: 'مخزون البضائع' },\n      { id: 5, code: '2001', name: 'الموردون', type: 'liability', balance: 0, description: 'دائنو الموردين' },\n      { id: 6, code: '2101', name: 'ضريبة القيمة المضافة', type: 'liability', balance: 0, description: 'ضريبة القيمة المضافة المستحقة' },\n      { id: 7, code: '3001', name: 'رأس المال', type: 'equity', balance: 0, description: 'رأس مال المالك' },\n      { id: 8, code: '4001', name: 'المبيعات', type: 'revenue', balance: 0, description: 'إيرادات المبيعات' },\n      { id: 9, code: '5001', name: 'تكلفة البضاعة المباعة', type: 'expense', balance: 0, description: 'تكلفة البضائع المباعة' },\n      { id: 10, code: '5101', name: 'مصاريف التشغيل', type: 'expense', balance: 0, description: 'المصاريف التشغيلية' }\n    ];\n  }\n\n  // إضافة حساب جديد\n  addAccount(account) {\n    try {\n      const accounts = this.getAccounts();\n      const newAccount = {\n        ...account,\n        id: this.generateAccountId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      accounts.push(newAccount);\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n      return newAccount;\n    } catch (error) {\n      console.error('خطأ في إضافة الحساب:', error);\n      throw new Error('فشل في إضافة الحساب');\n    }\n  }\n\n  // توليد معرف حساب\n  generateAccountId() {\n    const accounts = this.getAccounts();\n    return accounts.length > 0 ? Math.max(...accounts.map(a => a.id)) + 1 : 1;\n  }\n\n  // جلب جميع القيود المحاسبية\n  getJournalEntries() {\n    try {\n      const entries = localStorage.getItem('saudi_accounting_journal_entries');\n      return entries ? JSON.parse(entries) : [];\n    } catch (error) {\n      console.error('خطأ في جلب القيود:', error);\n      return [];\n    }\n  }\n\n  // إضافة قيد محاسبي\n  addJournalEntry(entry) {\n    try {\n      const entries = this.getJournalEntries();\n      const newEntry = {\n        ...entry,\n        id: this.generateJournalEntryId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      entries.push(newEntry);\n      localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(entries));\n\n      // تحديث أرصدة الحسابات\n      this.updateAccountBalances(newEntry);\n\n      return newEntry;\n    } catch (error) {\n      console.error('خطأ في إضافة القيد:', error);\n      throw new Error('فشل في إضافة القيد');\n    }\n  }\n\n  // تحديث أرصدة الحسابات\n  updateAccountBalances(entry) {\n    try {\n      const accounts = this.getAccounts();\n\n      entry.entries.forEach(line => {\n        const accountIndex = accounts.findIndex(a => a.id === parseInt(line.accountId));\n        if (accountIndex !== -1) {\n          // للأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص\n          // للخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص\n          if (accounts[accountIndex].type === 'asset' || accounts[accountIndex].type === 'expense') {\n            accounts[accountIndex].balance += (line.debit - line.credit);\n          } else {\n            accounts[accountIndex].balance += (line.credit - line.debit);\n          }\n          accounts[accountIndex].updatedAt = new Date().toISOString();\n        }\n      });\n\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n    } catch (error) {\n      console.error('خطأ في تحديث أرصدة الحسابات:', error);\n    }\n  }\n\n  // توليد معرف قيد محاسبي\n  generateJournalEntryId() {\n    const entries = this.getJournalEntries();\n    return entries.length > 0 ? Math.max(...entries.map(e => e.id)) + 1 : 1;\n  }\n\n  incrementInvoiceNumber() {\n    try {\n      const settings = this.getSettings();\n      settings.nextInvoiceNumber += 1;\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(settings));\n    } catch (error) {\n      console.error('خطأ في تحديث رقم الفاتورة:', error);\n    }\n  }\n\n  // === إدارة الإعدادات ===\n  \n  getSettings() {\n    try {\n      const settings = localStorage.getItem('saudi_accounting_settings');\n      return settings ? JSON.parse(settings) : this.getDefaultSettings();\n    } catch (error) {\n      console.error('خطأ في جلب الإعدادات:', error);\n      return this.getDefaultSettings();\n    }\n  }\n\n  updateSettings(updates) {\n    try {\n      const settings = this.getSettings();\n      const newSettings = { ...settings, ...updates, updatedAt: new Date().toISOString() };\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(newSettings));\n      return newSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // === التقارير والإحصائيات ===\n  \n  // إحصائيات اليوم\n  getTodayStats() {\n    const today = new Date().toDateString();\n    const todayInvoices = this.getInvoicesByDate(today);\n    \n    const totalSales = todayInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = todayInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = todayInvoices.length;\n    \n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // إحصائيات الشهر\n  getMonthStats() {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    \n    const monthInvoices = this.getInvoices().filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt);\n      return invoiceDate.getMonth() === currentMonth && invoiceDate.getFullYear() === currentYear;\n    });\n    \n    const totalSales = monthInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = monthInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = monthInvoices.length;\n    \n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // منتجات منخفضة المخزون\n  getLowStockProducts(threshold = 5) {\n    const products = this.getProducts();\n    return products.filter(product => product.stock <= threshold);\n  }\n\n  // أفضل المنتجات مبيعاً\n  getTopSellingProducts(limit = 5) {\n    const invoices = this.getInvoices();\n    const productSales = {};\n    \n    invoices.forEach(invoice => {\n      invoice.items.forEach(item => {\n        if (productSales[item.id]) {\n          productSales[item.id].quantity += item.quantity;\n          productSales[item.id].revenue += item.price * item.quantity;\n        } else {\n          productSales[item.id] = {\n            id: item.id,\n            name: item.name,\n            quantity: item.quantity,\n            revenue: item.price * item.quantity\n          };\n        }\n      });\n    });\n    \n    return Object.values(productSales)\n      .sort((a, b) => b.quantity - a.quantity)\n      .slice(0, limit);\n  }\n\n  // تصدير البيانات\n  exportData() {\n    return {\n      invoices: this.getInvoices(),\n      products: this.getProducts(),\n      customers: this.getCustomers(),\n      suppliers: this.getSuppliers(),\n      accounts: this.getAccounts(),\n      journalEntries: this.getJournalEntries(),\n      settings: this.getSettings(),\n      exportDate: new Date().toISOString()\n    };\n  }\n\n  // استيراد البيانات\n  importData(data) {\n    try {\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // مسح جميع البيانات\n  clearAllData() {\n    try {\n      localStorage.removeItem('saudi_accounting_invoices');\n      localStorage.removeItem('saudi_accounting_products');\n      localStorage.removeItem('saudi_accounting_customers');\n      localStorage.removeItem('saudi_accounting_settings');\n      this.initializeDatabase();\n      return true;\n    } catch (error) {\n      console.error('خطأ في مسح البيانات:', error);\n      throw new Error('فشل في مسح البيانات');\n    }\n  }\n\n  // تسجيل الأنشطة\n  logActivity(activity) {\n    try {\n      const activities = this.getActivities();\n      const newActivity = {\n        id: Date.now(),\n        ...activity,\n        timestamp: activity.timestamp || new Date().toISOString()\n      };\n      activities.push(newActivity);\n      localStorage.setItem('activities', JSON.stringify(activities));\n      return newActivity;\n    } catch (error) {\n      console.error('خطأ في تسجيل النشاط:', error);\n      return null;\n    }\n  }\n\n  // الحصول على الأنشطة\n  getActivities() {\n    try {\n      return JSON.parse(localStorage.getItem('activities') || '[]');\n    } catch (error) {\n      console.error('خطأ في قراءة الأنشطة:', error);\n      return [];\n    }\n  }\n}\n\n// إنشاء مثيل واحد من قاعدة البيانات\nconst database = new LocalDatabase();\n\nexport default database;\n"], "mappings": "AAAA;AACA;AAEA,KAAM,CAAAA,aAAc,CAClBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAC3B,CAEA;AACAA,kBAAkBA,CAAA,CAAG,CACnB;AACA,GAAI,CAACC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAE,CACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACvE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAE,CACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAC9F,CAEA,GAAI,CAACL,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAE,CACvDD,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACxE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAE,CACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAC9F,CAEA,GAAI,CAACN,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAE,CACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACvE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAE,CACjED,YAAY,CAACE,OAAO,CAAC,sCAAsC,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CAClF,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,CAAE,CACvDD,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACxE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAE,CACrDD,YAAY,CAACE,OAAO,CAAC,0BAA0B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACtE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAE,CACxDD,YAAY,CAACE,OAAO,CAAC,6BAA6B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACzE,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,oCAAoC,CAAC,CAAE,CAC/DD,YAAY,CAACE,OAAO,CAAC,oCAAoC,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CAChF,CAEA,GAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAE,CACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC,CACvE,CACF,CAEA;AACAC,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CACL,CACEE,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,yBAAyB,CACtCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,yBAAyB,CACtCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,iBAAiB,CACvBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,yBAAyB,CACtCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,CAAC,CACRC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,0BAA0B,CACvCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,CAAC,CACRC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,mBAAmB,CAChCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,qBAAqB,CAClCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,uBAAuB,CACpCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,eAAe,CACxBC,WAAW,CAAE,yBAAyB,CACtCC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACF,CACH,CAEA;AACAX,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CACLY,WAAW,CAAE,qCAAqC,CAClDC,SAAS,CAAE,iBAAiB,CAC5BC,OAAO,CAAE,kCAAkC,CAC3CC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,0BAA0B,CACjCR,OAAO,CAAE,IAAI,CACbS,QAAQ,CAAE,YAAY,CACtBC,aAAa,CAAE,KAAK,CACpBC,iBAAiB,CAAE,IAAI,CACvBV,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACH,CAEA;AAEA;AACAS,WAAWA,CAACC,OAAO,CAAE,CACnB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAC,UAAU,CAAG,CACjB,GAAGH,OAAO,CACVpB,EAAE,CAAE,IAAI,CAACwB,iBAAiB,CAAC,CAAC,CAC5BhB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCe,MAAM,CAAE,MACV,CAAC,CAEDJ,QAAQ,CAACK,IAAI,CAACH,UAAU,CAAC,CACzB9B,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAE3E;AACA,IAAI,CAACM,oBAAoB,CAACP,OAAO,CAACQ,KAAK,CAAC,CAExC;AACA,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAE7B,MAAO,CAAAN,UAAU,CACnB,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAV,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAD,QAAQ,CAAG5B,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAA2B,QAAQ,CAAGzB,IAAI,CAACqC,KAAK,CAACZ,QAAQ,CAAC,CAAG,EAAE,CAC7C,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,EAAE,CACX,CACF,CAEA;AACAI,cAAcA,CAAClC,EAAE,CAAE,CACjB,KAAM,CAAAqB,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAD,QAAQ,CAACc,IAAI,CAACf,OAAO,EAAIA,OAAO,CAACpB,EAAE,GAAKA,EAAE,CAAC,CACpD,CAEA;AACAoC,iBAAiBA,CAACC,IAAI,CAAE,CACtB,KAAM,CAAAhB,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAD,QAAQ,CAACiB,MAAM,CAAClB,OAAO,EAAI,CAChC,KAAM,CAAAmB,WAAW,CAAG,GAAI,CAAA9B,IAAI,CAACW,OAAO,CAACZ,SAAS,CAAC,CAACgC,YAAY,CAAC,CAAC,CAC9D,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAhC,IAAI,CAAC4B,IAAI,CAAC,CAACG,YAAY,CAAC,CAAC,CAChD,MAAO,CAAAD,WAAW,GAAKE,UAAU,CACnC,CAAC,CAAC,CACJ,CAEA;AAEA;AACAC,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAGlD,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAAiD,QAAQ,CAAG/C,IAAI,CAACqC,KAAK,CAACU,QAAQ,CAAC,CAAG,IAAI,CAAC7C,kBAAkB,CAAC,CAAC,CACpE,CAAE,MAAOgC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,KAAI,CAAChC,kBAAkB,CAAC,CAAC,CAClC,CACF,CAEA;AACA8C,UAAUA,CAACC,OAAO,CAAE,CAClB,GAAI,CACF,KAAM,CAAAF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAI,UAAU,CAAG,CACjB,GAAGD,OAAO,CACV7C,EAAE,CAAE,IAAI,CAAC+C,iBAAiB,CAAC,CAAC,CAC5BvC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDiC,QAAQ,CAACjB,IAAI,CAACoB,UAAU,CAAC,CACzBrD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC,CAC3E,MAAO,CAAAG,UAAU,CACnB,CAAE,MAAOhB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAgB,aAAaA,CAAChD,EAAE,CAAEiD,OAAO,CAAE,CACzB,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAQ,KAAK,CAAGP,QAAQ,CAACQ,SAAS,CAACN,OAAO,EAAIA,OAAO,CAAC7C,EAAE,GAAKA,EAAE,CAAC,CAE9D,GAAIkD,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBP,QAAQ,CAACO,KAAK,CAAC,CAAG,CAAE,GAAGP,QAAQ,CAACO,KAAK,CAAC,CAAE,GAAGD,OAAO,CAAEG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE,CAAC,CACzFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC,CAC3E,MAAO,CAAAA,QAAQ,CAACO,KAAK,CAAC,CACxB,CAEA,KAAM,IAAI,CAAAlB,KAAK,CAAC,kBAAkB,CAAC,CACrC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAqB,aAAaA,CAACrD,EAAE,CAAE,CAChB,GAAI,CACF,KAAM,CAAA2C,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAY,gBAAgB,CAAGX,QAAQ,CAACL,MAAM,CAACO,OAAO,EAAIA,OAAO,CAAC7C,EAAE,GAAKA,EAAE,CAAC,CACtEP,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACyD,gBAAgB,CAAC,CAAC,CACnF,MAAO,KAAI,CACb,CAAE,MAAOxB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,IAAI,CAAAE,KAAK,CAAC,mBAAmB,CAAC,CACtC,CACF,CAEA;AACAL,oBAAoBA,CAACC,KAAK,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAe,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CAEnCd,KAAK,CAAC2B,OAAO,CAACC,IAAI,EAAI,CACpB,KAAM,CAAAC,YAAY,CAAGd,QAAQ,CAACQ,SAAS,CAACN,OAAO,EAAIA,OAAO,CAAC7C,EAAE,GAAKwD,IAAI,CAACxD,EAAE,CAAC,CAC1E,GAAIyD,YAAY,GAAK,CAAC,CAAC,CAAE,CACvBd,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,EAAIqD,IAAI,CAACE,QAAQ,CAC7C,GAAIf,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,CAAG,CAAC,CAAE,CACpCwC,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,CAAG,CAAC,CAClC,CACF,CACF,CAAC,CAAC,CAEFV,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC,CAC7E,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAEA;AAEA;AACA6B,YAAYA,CAAA,CAAG,CACb,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGnE,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,CACpE,MAAO,CAAAkE,SAAS,CAAGhE,IAAI,CAACqC,KAAK,CAAC2B,SAAS,CAAC,CAAG,EAAE,CAC/C,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,MAAO,EAAE,CACX,CACF,CAEA;AACA+B,WAAWA,CAACC,QAAQ,CAAE,CACpB,GAAI,CACF,KAAM,CAAAF,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAI,WAAW,CAAG,CAClB,GAAGD,QAAQ,CACX9D,EAAE,CAAE,IAAI,CAACgE,kBAAkB,CAAC,CAAC,CAC7BxD,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDkD,SAAS,CAAClC,IAAI,CAACqC,WAAW,CAAC,CAC3BtE,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC+D,SAAS,CAAC,CAAC,CAC7E,MAAO,CAAAG,WAAW,CACpB,CAAE,MAAOjC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AAEAR,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAyC,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,MAAO,GAAGD,QAAQ,CAAChD,aAAa,IAAIgD,QAAQ,CAAC/C,iBAAiB,EAAE,CAClE,CAEA6B,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAJ,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACwB,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG1B,QAAQ,CAAC2B,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvE,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC3E,CAEAgE,kBAAkBA,CAAA,CAAG,CACnB,KAAM,CAAAJ,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,MAAO,CAAAC,SAAS,CAACO,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGT,SAAS,CAACU,GAAG,CAACE,CAAC,EAAIA,CAAC,CAACxE,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC7E,CAEA;AAEA;AACAyE,YAAYA,CAAA,CAAG,CACb,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGjF,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,CACpE,MAAO,CAAAgF,SAAS,CAAG9E,IAAI,CAACqC,KAAK,CAACyC,SAAS,CAAC,CAAG,EAAE,CAC/C,CAAE,MAAO5C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,EAAE,CACX,CACF,CAEA;AACA6C,WAAWA,CAACC,QAAQ,CAAE,CACpB,GAAI,CACF,KAAM,CAAAF,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAI,WAAW,CAAG,CAClB,GAAGD,QAAQ,CACX5E,EAAE,CAAE,IAAI,CAAC8E,kBAAkB,CAAC,CAAC,CAC7BtE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDgE,SAAS,CAAChD,IAAI,CAACmD,WAAW,CAAC,CAC3BpF,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC6E,SAAS,CAAC,CAAC,CAC7E,MAAO,CAAAG,WAAW,CACpB,CAAE,MAAO/C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACA+C,cAAcA,CAAC/E,EAAE,CAAEiD,OAAO,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAyB,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAvB,KAAK,CAAGwB,SAAS,CAACvB,SAAS,CAACyB,QAAQ,EAAIA,QAAQ,CAAC5E,EAAE,GAAKA,EAAE,CAAC,CAEjE,GAAIkD,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBwB,SAAS,CAACxB,KAAK,CAAC,CAAG,CACjB,GAAGwB,SAAS,CAACxB,KAAK,CAAC,CACnB,GAAGD,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACDjB,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC6E,SAAS,CAAC,CAAC,CAC7E,MAAO,CAAAA,SAAS,CAACxB,KAAK,CAAC,CACzB,CAEA,KAAM,IAAI,CAAAlB,KAAK,CAAC,kBAAkB,CAAC,CACrC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAgD,cAAcA,CAAChF,EAAE,CAAE,CACjB,GAAI,CACF,KAAM,CAAA0E,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAQ,iBAAiB,CAAGP,SAAS,CAACpC,MAAM,CAACsC,QAAQ,EAAIA,QAAQ,CAAC5E,EAAE,GAAKA,EAAE,CAAC,CAC1EP,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACoF,iBAAiB,CAAC,CAAC,CACrF,MAAO,KAAI,CACb,CAAE,MAAOnD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,IAAI,CAAAE,KAAK,CAAC,mBAAmB,CAAC,CACtC,CACF,CAEA;AACA8C,kBAAkBA,CAAA,CAAG,CACnB,KAAM,CAAAJ,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,MAAO,CAAAC,SAAS,CAACP,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGK,SAAS,CAACJ,GAAG,CAACY,CAAC,EAAIA,CAAC,CAAClF,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC7E,CAEA;AAEA;AACAmF,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG3F,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAA0F,QAAQ,CAAGxF,IAAI,CAACqC,KAAK,CAACmD,QAAQ,CAAC,CAAG,EAAE,CAC7C,CAAE,MAAOtD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,EAAE,CACX,CACF,CAEA;AACAuD,UAAUA,CAACC,OAAO,CAAE,CAClB,GAAI,CACF,KAAM,CAAAF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAI,UAAU,CAAG,CACjB,GAAGD,OAAO,CACVtF,EAAE,CAAE,IAAI,CAACwF,iBAAiB,CAAC,CAAC,CAC5BhF,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAED0E,QAAQ,CAAC1D,IAAI,CAAC6D,UAAU,CAAC,CACzB9F,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACuF,QAAQ,CAAC,CAAC,CAE3E;AACA,GAAIE,OAAO,CAAC7D,MAAM,GAAK,WAAW,CAAE,CAClC,IAAI,CAACgE,0BAA0B,CAACH,OAAO,CAACI,SAAS,CAAEJ,OAAO,CAACK,MAAM,CAAC,CACpE,CAEA,MAAO,CAAAJ,UAAU,CACnB,CAAE,MAAOzD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACA4D,aAAaA,CAAC5F,EAAE,CAAEiD,OAAO,CAAE,CACzB,GAAI,CACF,KAAM,CAAAmC,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAU,YAAY,CAAGT,QAAQ,CAACjC,SAAS,CAACoB,CAAC,EAAIA,CAAC,CAACvE,EAAE,GAAKA,EAAE,CAAC,CAEzD,GAAI6F,YAAY,GAAK,CAAC,CAAC,CAAE,CACvBT,QAAQ,CAACS,YAAY,CAAC,CAAG,CACvB,GAAGT,QAAQ,CAACS,YAAY,CAAC,CACzB,GAAG5C,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACuF,QAAQ,CAAC,CAAC,CAC3E,MAAO,CAAAA,QAAQ,CAACS,YAAY,CAAC,CAC/B,CAEA,KAAM,IAAI,CAAA7D,KAAK,CAAC,mBAAmB,CAAC,CACtC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACA8D,aAAaA,CAAC9F,EAAE,CAAE,CAChB,GAAI,CACF,KAAM,CAAAoF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAY,gBAAgB,CAAGX,QAAQ,CAAC9C,MAAM,CAACiC,CAAC,EAAIA,CAAC,CAACvE,EAAE,GAAKA,EAAE,CAAC,CAC1DP,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACkG,gBAAgB,CAAC,CAAC,CACnF,MAAO,KAAI,CACb,CAAE,MAAOjE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,IAAI,CAAAE,KAAK,CAAC,mBAAmB,CAAC,CACtC,CACF,CAEA;AACAwD,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAJ,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACjB,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGe,QAAQ,CAACd,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvE,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC3E,CAEA;AACAyF,0BAA0BA,CAACC,SAAS,CAAEM,UAAU,CAAE,CAChD,GAAI,CACF,KAAM,CAAA3E,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAA2E,YAAY,CAAG5E,QAAQ,CAAC8B,SAAS,CAAC+C,GAAG,EAAIA,GAAG,CAAClG,EAAE,GAAK0F,SAAS,CAAC,CAEpE,GAAIO,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB,KAAM,CAAA7E,OAAO,CAAGC,QAAQ,CAAC4E,YAAY,CAAC,CACtC,KAAM,CAAAE,SAAS,CAAG,CAAC/E,OAAO,CAAC4E,UAAU,EAAI,CAAC,EAAIA,UAAU,CAExD,GAAI,CAAAI,aAAa,CAAG,QAAQ,CAC5B,GAAID,SAAS,EAAI/E,OAAO,CAACiF,KAAK,CAAE,CAC9BD,aAAa,CAAG,MAAM,CACxB,CAAC,IAAM,IAAID,SAAS,CAAG,CAAC,CAAE,CACxBC,aAAa,CAAG,SAAS,CAC3B,CAEA/E,QAAQ,CAAC4E,YAAY,CAAC,CAAG,CACvB,GAAG7E,OAAO,CACV4E,UAAU,CAAEG,SAAS,CACrBC,aAAa,CAAEA,aAAa,CAC5BhD,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAC7E,CACF,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAEA;AACAwE,mBAAmBA,CAACC,UAAU,CAAE,CAC9B,GAAI,CACF,KAAM,CAAAnB,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAAC9C,MAAM,CAACgD,OAAO,EAAIA,OAAO,CAACiB,UAAU,GAAKA,UAAU,CAAC,CACtE,CAAE,MAAOzE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,EAAE,CACX,CACF,CAEA;AACA0E,kBAAkBA,CAACd,SAAS,CAAE,CAC5B,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAAC9C,MAAM,CAACgD,OAAO,EAAIA,OAAO,CAACI,SAAS,GAAKA,SAAS,CAAC,CACpE,CAAE,MAAO5D,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,EAAE,CACX,CACF,CAEA;AAEA;AACA2E,qBAAqBA,CAAA,CAAG,CACtB,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGjH,YAAY,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAC9E,MAAO,CAAAgH,SAAS,CAAG9G,IAAI,CAACqC,KAAK,CAACyE,SAAS,CAAC,CAAG,EAAE,CAC/C,CAAE,MAAO5E,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,EAAE,CACX,CACF,CAEA;AACA6E,oBAAoBA,CAACC,QAAQ,CAAE,CAC7B,GAAI,CACF,KAAM,CAAAF,SAAS,CAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC,CAC9C,KAAM,CAAAI,WAAW,CAAG,CAClB,GAAGD,QAAQ,CACX5G,EAAE,CAAE,IAAI,CAAC8G,kBAAkB,CAAC,CAAC,CAC7BtG,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDgG,SAAS,CAAChF,IAAI,CAACmF,WAAW,CAAC,CAC3BpH,YAAY,CAACE,OAAO,CAAC,sCAAsC,CAAEC,IAAI,CAACC,SAAS,CAAC6G,SAAS,CAAC,CAAC,CAEvF;AACA,IAAI,CAACK,kBAAkB,CAACH,QAAQ,CAACI,SAAS,CAAEJ,QAAQ,CAAClD,QAAQ,CAAEkD,QAAQ,CAACK,IAAI,CAAC,CAE7E,MAAO,CAAAJ,WAAW,CACpB,CAAE,MAAO/E,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,IAAI,CAAAE,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CACF,CAEA;AACA+E,kBAAkBA,CAACC,SAAS,CAAEtD,QAAQ,CAAEwD,YAAY,CAAE,CACpD,GAAI,CACF,KAAM,CAAAvE,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAe,YAAY,CAAGd,QAAQ,CAACQ,SAAS,CAACoB,CAAC,EAAIA,CAAC,CAACvE,EAAE,GAAKgH,SAAS,CAAC,CAEhE,GAAIvD,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB,KAAM,CAAA0D,YAAY,CAAGxE,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,EAAI,CAAC,CACtD,GAAI,CAAAiH,QAAQ,CAAGD,YAAY,CAE3B,GAAID,YAAY,GAAK,IAAI,CAAE,CACzBE,QAAQ,CAAGD,YAAY,CAAGzD,QAAQ,CACpC,CAAC,IAAM,IAAIwD,YAAY,GAAK,KAAK,EAAIA,YAAY,GAAK,SAAS,EAAIA,YAAY,GAAK,SAAS,CAAE,CAC7FE,QAAQ,CAAGhD,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE8C,YAAY,CAAGzD,QAAQ,CAAC,CACjD,CAAC,IAAM,IAAIwD,YAAY,GAAK,YAAY,CAAE,CACxCE,QAAQ,CAAG1D,QAAQ,CAAE;AACvB,CAEAf,QAAQ,CAACc,YAAY,CAAC,CAAG,CACvB,GAAGd,QAAQ,CAACc,YAAY,CAAC,CACzBtD,KAAK,CAAEiH,QAAQ,CACfhE,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC,CAC7E,CACF,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAEA;AACAgF,kBAAkBA,CAAA,CAAG,CACnB,KAAM,CAAAJ,SAAS,CAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC,CAC9C,MAAO,CAAAC,SAAS,CAACvC,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGqC,SAAS,CAACpC,GAAG,CAAC+C,CAAC,EAAIA,CAAC,CAACrH,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC7E,CAEA;AACAsH,mBAAmBA,CAACN,SAAS,CAAE,CAC7B,GAAI,CACF,KAAM,CAAAN,SAAS,CAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC,CAC9C,MAAO,CAAAC,SAAS,CAACpE,MAAM,CAACsE,QAAQ,EAAIA,QAAQ,CAACI,SAAS,GAAKA,SAAS,CAAC,CACvE,CAAE,MAAOlF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,EAAE,CACX,CACF,CAEA;AACAyF,gCAAgCA,CAACC,SAAS,CAAEC,OAAO,CAAE,CACnD,GAAI,CACF,KAAM,CAAAf,SAAS,CAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC,CAC9C,MAAO,CAAAC,SAAS,CAACpE,MAAM,CAACsE,QAAQ,EAAI,CAClC,KAAM,CAAAc,YAAY,CAAG,GAAI,CAAAjH,IAAI,CAACmG,QAAQ,CAACvE,IAAI,CAAC,CAC5C,MAAO,CAAAqF,YAAY,EAAI,GAAI,CAAAjH,IAAI,CAAC+G,SAAS,CAAC,EAAIE,YAAY,EAAI,GAAI,CAAAjH,IAAI,CAACgH,OAAO,CAAC,CACjF,CAAC,CAAC,CACJ,CAAE,MAAO3F,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,MAAO,EAAE,CACX,CACF,CAEA;AACA6F,4BAA4BA,CAAA,CAAG,CAC7B,GAAI,CACF,KAAM,CAAAhF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACiF,MAAM,CAAC,CAACvB,KAAK,CAAExD,OAAO,GAAK,CACzC,MAAO,CAAAwD,KAAK,CAAIxD,OAAO,CAAC1C,KAAK,CAAG0C,OAAO,CAAC3C,KAAM,CAChD,CAAC,CAAE,CAAC,CAAC,CACP,CAAE,MAAO4B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,EAAC,CACV,CACF,CAEA;AACA+F,mBAAmBA,CAAA,CAAG,CACpB,GAAI,CACF,KAAM,CAAAlF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACL,MAAM,CAACO,OAAO,EAAIA,OAAO,CAAC1C,KAAK,GAAK0C,OAAO,CAACiF,QAAQ,EAAI,CAAC,CAAC,CAAC,CAC7E,CAAE,MAAOhG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,MAAO,EAAE,CACX,CACF,CAEA;AACAiG,qBAAqBA,CAAA,CAAG,CACtB,GAAI,CACF,KAAM,CAAApF,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACL,MAAM,CAACO,OAAO,EAAIA,OAAO,CAAC1C,KAAK,GAAK,CAAC,CAAC,CACxD,CAAE,MAAO2B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,EAAE,CACX,CACF,CAEA;AAEA;AACAkG,YAAYA,CAAA,CAAG,CACb,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGxI,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,CACpE,MAAO,CAAAuI,SAAS,CAAGrI,IAAI,CAACqC,KAAK,CAACgG,SAAS,CAAC,CAAG,EAAE,CAC/C,CAAE,MAAOnG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,EAAE,CACX,CACF,CAEA;AACAoG,WAAWA,CAACC,QAAQ,CAAE,CACpB,GAAI,CACF,KAAM,CAAAF,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAI,WAAW,CAAG,CAClB,GAAGD,QAAQ,CACXnI,EAAE,CAAE,IAAI,CAACqI,kBAAkB,CAAC,CAAC,CAC7B7H,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDuH,SAAS,CAACvG,IAAI,CAAC0G,WAAW,CAAC,CAC3B3I,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACoI,SAAS,CAAC,CAAC,CAE7E,MAAO,CAAAG,WAAW,CACpB,CAAE,MAAOtG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAsG,cAAcA,CAACtI,EAAE,CAAEiD,OAAO,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAgF,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAO,aAAa,CAAGN,SAAS,CAAC9E,SAAS,CAACqF,GAAG,EAAIA,GAAG,CAACxI,EAAE,GAAKA,EAAE,CAAC,CAE/D,GAAIuI,aAAa,GAAK,CAAC,CAAC,CAAE,CACxBN,SAAS,CAACM,aAAa,CAAC,CAAG,CACzB,GAAGN,SAAS,CAACM,aAAa,CAAC,CAC3B,GAAGtF,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACoI,SAAS,CAAC,CAAC,CAC7E,MAAO,CAAAA,SAAS,CAACM,aAAa,CAAC,CACjC,CAEA,KAAM,IAAI,CAAAvG,KAAK,CAAC,kBAAkB,CAAC,CACrC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAyG,cAAcA,CAACzI,EAAE,CAAE,CACjB,GAAI,CACF,KAAM,CAAAiI,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAU,iBAAiB,CAAGT,SAAS,CAAC3F,MAAM,CAACkG,GAAG,EAAIA,GAAG,CAACxI,EAAE,GAAKA,EAAE,CAAC,CAChEP,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAAC6I,iBAAiB,CAAC,CAAC,CACrF,MAAO,KAAI,CACb,CAAE,MAAO5G,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,IAAI,CAAAE,KAAK,CAAC,mBAAmB,CAAC,CACtC,CACF,CAEA;AACAqG,kBAAkBA,CAAA,CAAG,CACnB,KAAM,CAAAJ,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,MAAO,CAAAC,SAAS,CAAC9D,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG4D,SAAS,CAAC3D,GAAG,CAACkE,GAAG,EAAIA,GAAG,CAACxI,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACjF,CAEA;AACA2I,kBAAkBA,CAAA,CAAG,CACnB,GAAI,CACF,KAAM,CAAAV,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,MAAO,CAAAC,SAAS,CAAC3F,MAAM,CAAC6F,QAAQ,EAAIA,QAAQ,CAAC1G,MAAM,GAAK,QAAQ,CAAC,CACnE,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,EAAE,CACX,CACF,CAEA;AACA8G,wBAAwBA,CAACC,UAAU,CAAE,CACnC,GAAI,CACF,KAAM,CAAAZ,SAAS,CAAG,IAAI,CAACD,YAAY,CAAC,CAAC,CACrC,MAAO,CAAAC,SAAS,CAAC3F,MAAM,CAAC6F,QAAQ,EAAIA,QAAQ,CAACU,UAAU,GAAKA,UAAU,CAAC,CACzE,CAAE,MAAO/G,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,EAAE,CACX,CACF,CAEA;AACAgH,sBAAsBA,CAAA,CAAG,CACvB,GAAI,CACF,KAAM,CAAAC,eAAe,CAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC,CACjD,MAAO,CAAAI,eAAe,CAACnB,MAAM,CAAC,CAACvB,KAAK,CAAE8B,QAAQ,GAAK,CACjD,MAAO,CAAA9B,KAAK,EAAI8B,QAAQ,CAACa,MAAM,EAAI,CAAC,CAAC,CACvC,CAAC,CAAE,CAAC,CAAC,CACP,CAAE,MAAOlH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,EAAC,CACV,CACF,CAEA;AAEA;AACA/B,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CACL;AACAY,WAAW,CAAE,wBAAwB,CACrCC,SAAS,CAAE,iBAAiB,CAC5BC,OAAO,CAAE,kCAAkC,CAC3CC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,kBAAkB,CACzBkI,OAAO,CAAE,iBAAiB,CAE1B;AACAhI,aAAa,CAAE,KAAK,CACpBC,iBAAiB,CAAE,CAAC,CACpBX,OAAO,CAAE,EAAE,CACXS,QAAQ,CAAE,KAAK,CACfkI,mBAAmB,CAAE,qCAAqC,CAC1DC,gBAAgB,CAAE,IAAI,CAEtB;AACAC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,aAAa,CACvBC,UAAU,CAAE,YAAY,CACxBC,mBAAmB,CAAE,IAAI,CACzBC,cAAc,CAAE,KAAK,CACrBC,gBAAgB,CAAE,KAAK,CAEvB;AACAC,eAAe,CAAE,OAAO,CACxBC,eAAe,CAAE,QAAQ,CACzBC,eAAe,CAAE,EAAE,CACnBC,iBAAiB,CAAE,CAAC,CAEpBrJ,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACH,CAEA;AACAoJ,cAAcA,CAACC,WAAW,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAC,eAAe,CAAG,IAAI,CAAC9F,WAAW,CAAC,CAAC,CAC1C,KAAM,CAAA+F,eAAe,CAAG,CACtB,GAAGD,eAAe,CAClB,GAAGD,WAAW,CACd3G,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACoK,eAAe,CAAC,CAAC,CAClF,MAAO,CAAAA,eAAe,CACxB,CAAE,MAAOnI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,IAAI,CAAAE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACF,CAEA;AACAkI,aAAaA,CAAA,CAAG,CACd,GAAI,CACF,KAAM,CAAAC,eAAe,CAAG,IAAI,CAACpK,kBAAkB,CAAC,CAAC,CACjDN,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACsK,eAAe,CAAC,CAAC,CAClF,MAAO,CAAAA,eAAe,CACxB,CAAE,MAAOrI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,IAAI,CAAAE,KAAK,CAAC,8BAA8B,CAAC,CACjD,CACF,CAEA;AACAoI,aAAaA,CAAA,CAAG,CACd,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,CACXpG,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5B7C,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5BqB,QAAQ,CAAE,IAAI,CAACD,WAAW,CAAC,CAAC,CAC5BkB,SAAS,CAAE,IAAI,CAACD,YAAY,CAAC,CAAC,CAC9Be,SAAS,CAAE,IAAI,CAACD,YAAY,CAAC,CAAC,CAC9B6F,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5BC,cAAc,CAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC,CACxCrF,QAAQ,CAAE,IAAI,CAACD,WAAW,CAAC,CAAC,CAC5BuF,kBAAkB,CAAE,IAAI,CAACjE,qBAAqB,CAAC,CAAC,CAChDwB,SAAS,CAAE,IAAI,CAACD,YAAY,CAAC,CAAC,CAC9B2C,UAAU,CAAE,GAAI,CAAAlK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACpCkK,OAAO,CAAE,OACX,CAAC,CAED,MAAO,CAAAP,IAAI,CACb,CAAE,MAAOvI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAAE,KAAK,CAAC,uBAAuB,CAAC,CAC1C,CACF,CAEA;AACA6I,aAAaA,CAACR,IAAI,CAAE,CAClB,GAAI,CACF,GAAIA,IAAI,CAACpG,QAAQ,CAAE,CACjBxE,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpG,QAAQ,CAAC,CAAC,CAClF,CACA,GAAIoG,IAAI,CAAChJ,QAAQ,CAAE,CACjB5B,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAChJ,QAAQ,CAAC,CAAC,CAClF,CACA,GAAIgJ,IAAI,CAAC1H,QAAQ,CAAE,CACjBlD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC1H,QAAQ,CAAC,CAAC,CAClF,CACA,GAAI0H,IAAI,CAACzG,SAAS,CAAE,CAClBnE,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACzG,SAAS,CAAC,CAAC,CACpF,CACA,GAAIyG,IAAI,CAAC3F,SAAS,CAAE,CAClBjF,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC3F,SAAS,CAAC,CAAC,CACpF,CACA,GAAI2F,IAAI,CAACC,QAAQ,CAAE,CACjB7K,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACC,QAAQ,CAAC,CAAC,CAClF,CACA,GAAID,IAAI,CAACG,cAAc,CAAE,CACvB/K,YAAY,CAACE,OAAO,CAAC,kCAAkC,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACG,cAAc,CAAC,CAAC,CAC/F,CACA,GAAIH,IAAI,CAACjF,QAAQ,CAAE,CACjB3F,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACjF,QAAQ,CAAC,CAAC,CAClF,CACA,GAAIiF,IAAI,CAACK,kBAAkB,CAAE,CAC3BjL,YAAY,CAACE,OAAO,CAAC,sCAAsC,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACK,kBAAkB,CAAC,CAAC,CACvG,CACA,GAAIL,IAAI,CAACpC,SAAS,CAAE,CAClBxI,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpC,SAAS,CAAC,CAAC,CACpF,CACA,GAAIoC,IAAI,CAACS,OAAO,CAAE,CAChBrL,YAAY,CAACE,OAAO,CAAC,0BAA0B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACS,OAAO,CAAC,CAAC,CAChF,CACA,GAAIT,IAAI,CAACU,UAAU,CAAE,CACnBtL,YAAY,CAACE,OAAO,CAAC,6BAA6B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACU,UAAU,CAAC,CAAC,CACtF,CACA,GAAIV,IAAI,CAACW,gBAAgB,CAAE,CACzBvL,YAAY,CAACE,OAAO,CAAC,oCAAoC,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACW,gBAAgB,CAAC,CAAC,CACnG,CACA,GAAIX,IAAI,CAACY,QAAQ,CAAE,CACjBxL,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACY,QAAQ,CAAC,CAAC,CAClF,CAEA,MAAO,KAAI,CACb,CAAE,MAAOnJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,IAAI,CAAAE,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CACF,CAEA;AAEA;AACAkJ,UAAUA,CAAA,CAAG,CACX,GAAI,CACF,KAAM,CAAAJ,OAAO,CAAGrL,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAChE,MAAO,CAAAoL,OAAO,CAAGlL,IAAI,CAACqC,KAAK,CAAC6I,OAAO,CAAC,CAAG,EAAE,CAC3C,CAAE,MAAOhJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,EAAE,CACX,CACF,CAEA;AACAqJ,SAASA,CAACC,UAAU,CAAE,CACpB,GAAI,CACF,KAAM,CAAAN,OAAO,CAAG,IAAI,CAACI,UAAU,CAAC,CAAC,CACjC,KAAM,CAAAG,SAAS,CAAG,CAChB,GAAGD,UAAU,CACbpL,EAAE,CAAE,IAAI,CAACsL,gBAAgB,CAAC,CAAC,CAC3B9K,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDoK,OAAO,CAACpJ,IAAI,CAAC2J,SAAS,CAAC,CACvB5L,YAAY,CAACE,OAAO,CAAC,0BAA0B,CAAEC,IAAI,CAACC,SAAS,CAACiL,OAAO,CAAC,CAAC,CAEzE;AACA,GAAIM,UAAU,CAAC3J,MAAM,GAAK,UAAU,EAAI2J,UAAU,CAAC3J,MAAM,GAAK,WAAW,CAAE,CACzE,IAAI,CAAC8J,wBAAwB,CAACF,SAAS,CAAC,CAC1C,CAEA,MAAO,CAAAA,SAAS,CAClB,CAAE,MAAOvJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,IAAI,CAAAE,KAAK,CAAC,sBAAsB,CAAC,CACzC,CACF,CAEA;AACAwJ,YAAYA,CAACxL,EAAE,CAAEiD,OAAO,CAAE,CACxB,GAAI,CACF,KAAM,CAAA6H,OAAO,CAAG,IAAI,CAACI,UAAU,CAAC,CAAC,CACjC,KAAM,CAAAO,WAAW,CAAGX,OAAO,CAAC3H,SAAS,CAACuI,GAAG,EAAIA,GAAG,CAAC1L,EAAE,GAAKA,EAAE,CAAC,CAE3D,GAAIyL,WAAW,GAAK,CAAC,CAAC,CAAE,CACtB,KAAM,CAAAE,SAAS,CAAGb,OAAO,CAACW,WAAW,CAAC,CACtCX,OAAO,CAACW,WAAW,CAAC,CAAG,CACrB,GAAGE,SAAS,CACZ,GAAG1I,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,0BAA0B,CAAEC,IAAI,CAACC,SAAS,CAACiL,OAAO,CAAC,CAAC,CAEzE;AACA,GAAIa,SAAS,CAAClK,MAAM,GAAKwB,OAAO,CAACxB,MAAM,GAClCwB,OAAO,CAACxB,MAAM,GAAK,UAAU,EAAIwB,OAAO,CAACxB,MAAM,GAAK,WAAW,CAAC,CAAE,CACrE,IAAI,CAAC8J,wBAAwB,CAACT,OAAO,CAACW,WAAW,CAAC,CAAC,CACrD,CAEA,MAAO,CAAAX,OAAO,CAACW,WAAW,CAAC,CAC7B,CAEA,KAAM,IAAI,CAAAzJ,KAAK,CAAC,mBAAmB,CAAC,CACtC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,IAAI,CAAAE,KAAK,CAAC,sBAAsB,CAAC,CACzC,CACF,CAEA;AACA4J,YAAYA,CAAC5L,EAAE,CAAE,CACf,GAAI,CACF,KAAM,CAAA8K,OAAO,CAAG,IAAI,CAACI,UAAU,CAAC,CAAC,CACjC,KAAM,CAAAW,eAAe,CAAGf,OAAO,CAACxI,MAAM,CAACoJ,GAAG,EAAIA,GAAG,CAAC1L,EAAE,GAAKA,EAAE,CAAC,CAC5DP,YAAY,CAACE,OAAO,CAAC,0BAA0B,CAAEC,IAAI,CAACC,SAAS,CAACgM,eAAe,CAAC,CAAC,CACjF,MAAO,KAAI,CACb,CAAE,MAAO/J,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,oBAAoB,CAAC,CACvC,CACF,CAEA;AACA8J,aAAaA,CAAC9L,EAAE,CAAE,CAChB,GAAI,CACF,MAAO,KAAI,CAACwL,YAAY,CAACxL,EAAE,CAAE,CAAEyB,MAAM,CAAE,WAAY,CAAC,CAAC,CACvD,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAAE,KAAK,CAAC,uBAAuB,CAAC,CAC1C,CACF,CAEA;AACAsJ,gBAAgBA,CAAA,CAAG,CACjB,KAAM,CAAAR,OAAO,CAAG,IAAI,CAACI,UAAU,CAAC,CAAC,CACjC,MAAO,CAAAJ,OAAO,CAAC3G,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGyG,OAAO,CAACxG,GAAG,CAACoH,GAAG,EAAIA,GAAG,CAAC1L,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC7E,CAEA;AACAuL,wBAAwBA,CAACH,UAAU,CAAE,CACnC,GAAI,CACF,GAAIA,UAAU,CAACxJ,KAAK,EAAIwJ,UAAU,CAACxJ,KAAK,CAACuC,MAAM,CAAG,CAAC,CAAE,CACnDiH,UAAU,CAACxJ,KAAK,CAAC2B,OAAO,CAACC,IAAI,EAAI,CAC/B,GAAI4H,UAAU,CAACnE,IAAI,GAAK,cAAc,CAAE,CACtC;AACA,IAAI,CAACN,oBAAoB,CAAC,CACxBK,SAAS,CAAExD,IAAI,CAACwD,SAAS,CACzBC,IAAI,CAAE,IAAI,CACVvD,QAAQ,CAAEF,IAAI,CAACE,QAAQ,CACvBqI,MAAM,CAAE,kBAAkBX,UAAU,CAACY,YAAY,EAAE,CACnDC,SAAS,CAAEb,UAAU,CAACY,YAAY,CAClC3J,IAAI,CAAE+I,UAAU,CAACc,UACnB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAId,UAAU,CAACnE,IAAI,GAAK,iBAAiB,CAAE,CAChD;AACA,IAAI,CAACN,oBAAoB,CAAC,CACxBK,SAAS,CAAExD,IAAI,CAACwD,SAAS,CACzBC,IAAI,CAAE,KAAK,CACXvD,QAAQ,CAAEF,IAAI,CAACE,QAAQ,CACvBqI,MAAM,CAAE,mBAAmBX,UAAU,CAACY,YAAY,EAAE,CACpDC,SAAS,CAAEb,UAAU,CAACY,YAAY,CAClC3J,IAAI,CAAE+I,UAAU,CAACc,UACnB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOpK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAEA;AAEA;AACAqK,aAAaA,CAAA,CAAG,CACd,GAAI,CACF,KAAM,CAAApB,UAAU,CAAGtL,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC,CACtE,MAAO,CAAAqL,UAAU,CAAGnL,IAAI,CAACqC,KAAK,CAAC8I,UAAU,CAAC,CAAG,EAAE,CACjD,CAAE,MAAOjJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,EAAE,CACX,CACF,CAEA;AACAsK,YAAYA,CAACC,aAAa,CAAE,CAC1B,GAAI,CACF,KAAM,CAAAtB,UAAU,CAAG,IAAI,CAACoB,aAAa,CAAC,CAAC,CACvC,KAAM,CAAAG,YAAY,CAAG,CACnB,GAAGD,aAAa,CAChBrM,EAAE,CAAE,IAAI,CAACuM,mBAAmB,CAAC,CAAC,CAC9B/L,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDqK,UAAU,CAACrJ,IAAI,CAAC4K,YAAY,CAAC,CAC7B7M,YAAY,CAACE,OAAO,CAAC,6BAA6B,CAAEC,IAAI,CAACC,SAAS,CAACkL,UAAU,CAAC,CAAC,CAE/E,MAAO,CAAAuB,YAAY,CACrB,CAAE,MAAOxK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,IAAI,CAAAE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACF,CAEA;AACAwK,eAAeA,CAACxM,EAAE,CAAEiD,OAAO,CAAE,CAC3B,GAAI,CACF,KAAM,CAAA8H,UAAU,CAAG,IAAI,CAACoB,aAAa,CAAC,CAAC,CACvC,KAAM,CAAAM,cAAc,CAAG1B,UAAU,CAAC5H,SAAS,CAACuJ,IAAI,EAAIA,IAAI,CAAC1M,EAAE,GAAKA,EAAE,CAAC,CAEnE,GAAIyM,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB1B,UAAU,CAAC0B,cAAc,CAAC,CAAG,CAC3B,GAAG1B,UAAU,CAAC0B,cAAc,CAAC,CAC7B,GAAGxJ,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,6BAA6B,CAAEC,IAAI,CAACC,SAAS,CAACkL,UAAU,CAAC,CAAC,CAC/E,MAAO,CAAAA,UAAU,CAAC0B,cAAc,CAAC,CACnC,CAEA,KAAM,IAAI,CAAAzK,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,IAAI,CAAAE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACF,CAEA;AACA2K,eAAeA,CAAC3M,EAAE,CAAE,CAClB,GAAI,CACF,KAAM,CAAA+K,UAAU,CAAG,IAAI,CAACoB,aAAa,CAAC,CAAC,CACvC,KAAM,CAAAS,kBAAkB,CAAG7B,UAAU,CAACzI,MAAM,CAACoK,IAAI,EAAIA,IAAI,CAAC1M,EAAE,GAAKA,EAAE,CAAC,CACpEP,YAAY,CAACE,OAAO,CAAC,6BAA6B,CAAEC,IAAI,CAACC,SAAS,CAAC+M,kBAAkB,CAAC,CAAC,CACvF,MAAO,KAAI,CACb,CAAE,MAAO9K,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,IAAI,CAAAE,KAAK,CAAC,sBAAsB,CAAC,CACzC,CACF,CAEA;AACAuK,mBAAmBA,CAAA,CAAG,CACpB,KAAM,CAAAxB,UAAU,CAAG,IAAI,CAACoB,aAAa,CAAC,CAAC,CACvC,MAAO,CAAApB,UAAU,CAAC5G,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG0G,UAAU,CAACzG,GAAG,CAACoI,IAAI,EAAIA,IAAI,CAAC1M,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACrF,CAEA;AAEA;AACA6M,mBAAmBA,CAAA,CAAG,CACpB,GAAI,CACF,KAAM,CAAA7B,gBAAgB,CAAGvL,YAAY,CAACC,OAAO,CAAC,oCAAoC,CAAC,CACnF,MAAO,CAAAsL,gBAAgB,CAAGpL,IAAI,CAACqC,KAAK,CAAC+I,gBAAgB,CAAC,CAAG,EAAE,CAC7D,CAAE,MAAOlJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,EAAE,CACX,CACF,CAEA;AACAgL,kBAAkBA,CAACC,WAAW,CAAE,CAC9B,GAAI,CACF,KAAM,CAAA/B,gBAAgB,CAAG,IAAI,CAAC6B,mBAAmB,CAAC,CAAC,CACnD,KAAM,CAAAtL,UAAU,CAAG,CACjB,GAAGwL,WAAW,CACd/M,EAAE,CAAE,IAAI,CAACgN,yBAAyB,CAAC,CAAC,CACpCC,aAAa,CAAE,IAAI,CAACC,6BAA6B,CAAC,CAAC,CACnD1M,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDsK,gBAAgB,CAACtJ,IAAI,CAACH,UAAU,CAAC,CACjC9B,YAAY,CAACE,OAAO,CAAC,oCAAoC,CAAEC,IAAI,CAACC,SAAS,CAACmL,gBAAgB,CAAC,CAAC,CAE5F;AACA,GAAIzJ,UAAU,CAACK,KAAK,EAAIL,UAAU,CAACK,KAAK,CAACuC,MAAM,CAAG,CAAC,CAAE,CACnD5C,UAAU,CAACK,KAAK,CAAC2B,OAAO,CAACC,IAAI,EAAI,CAC/B,IAAI,CAACmD,oBAAoB,CAAC,CACxBK,SAAS,CAAExD,IAAI,CAACwD,SAAS,CACzBC,IAAI,CAAE,IAAI,CACVvD,QAAQ,CAAEF,IAAI,CAACE,QAAQ,CACvBqI,MAAM,CAAE,oBAAoBxK,UAAU,CAAC0L,aAAa,EAAE,CACtDhB,SAAS,CAAE1K,UAAU,CAAC0L,aAAa,CACnC5K,IAAI,CAAEd,UAAU,CAACgB,WACnB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAhB,UAAU,CACnB,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,IAAI,CAAAE,KAAK,CAAC,+BAA+B,CAAC,CAClD,CACF,CAEA;AACAgL,yBAAyBA,CAAA,CAAG,CAC1B,KAAM,CAAAhC,gBAAgB,CAAG,IAAI,CAAC6B,mBAAmB,CAAC,CAAC,CACnD,MAAO,CAAA7B,gBAAgB,CAAC7G,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG2G,gBAAgB,CAAC1G,GAAG,CAAC4B,GAAG,EAAIA,GAAG,CAAClG,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC/F,CAEA;AACAkN,6BAA6BA,CAAA,CAAG,CAC9B,KAAM,CAAAC,MAAM,CAAG,KAAK,CACpB,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACP,mBAAmB,CAAC,CAAC,CAAC1I,MAAM,CAAG,CAAC,CACxD,MAAO,GAAGgJ,MAAM,IAAIC,UAAU,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9D,CAEA;AAEA;AACAC,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAtC,QAAQ,CAAGxL,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAAuL,QAAQ,CAAGrL,IAAI,CAACqC,KAAK,CAACgJ,QAAQ,CAAC,CAAG,EAAE,CAC7C,CAAE,MAAOnJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,MAAO,EAAE,CACX,CACF,CAEA;AACA0L,UAAUA,CAACC,WAAW,CAAE,CACtB,GAAI,CACF,KAAM,CAAAxC,QAAQ,CAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAG,UAAU,CAAG,CACjB,GAAGD,WAAW,CACdzN,EAAE,CAAE,IAAI,CAAC2N,iBAAiB,CAAC,CAAC,CAC5BC,aAAa,CAAE,IAAI,CAACC,qBAAqB,CAACJ,WAAW,CAACxG,IAAI,CAAC,CAC3DzG,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDuK,QAAQ,CAACvJ,IAAI,CAACgM,UAAU,CAAC,CACzBjO,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACoL,QAAQ,CAAC,CAAC,CAE3E;AACA,IAAI,CAAC6C,yBAAyB,CAACJ,UAAU,CAAC,CAE1C,MAAO,CAAAA,UAAU,CACnB,CAAE,MAAO5L,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,oBAAoB,CAAC,CACvC,CACF,CAEA;AACA+L,aAAaA,CAAC/N,EAAE,CAAEiD,OAAO,CAAE,CACzB,GAAI,CACF,KAAM,CAAAgI,QAAQ,CAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAS,YAAY,CAAG/C,QAAQ,CAAC9H,SAAS,CAAC8K,OAAO,EAAIA,OAAO,CAACjO,EAAE,GAAKA,EAAE,CAAC,CAErE,GAAIgO,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB/C,QAAQ,CAAC+C,YAAY,CAAC,CAAG,CACvB,GAAG/C,QAAQ,CAAC+C,YAAY,CAAC,CACzB,GAAG/K,OAAO,CACVG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACoL,QAAQ,CAAC,CAAC,CAC3E,MAAO,CAAAA,QAAQ,CAAC+C,YAAY,CAAC,CAC/B,CAEA,KAAM,IAAI,CAAAhM,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,oBAAoB,CAAC,CACvC,CACF,CAEA;AACAkM,aAAaA,CAAClO,EAAE,CAAE,CAChB,GAAI,CACF,KAAM,CAAAiL,QAAQ,CAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAY,gBAAgB,CAAGlD,QAAQ,CAAC3I,MAAM,CAAC2L,OAAO,EAAIA,OAAO,CAACjO,EAAE,GAAKA,EAAE,CAAC,CACtEP,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACsO,gBAAgB,CAAC,CAAC,CACnF,MAAO,KAAI,CACb,CAAE,MAAOrM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzC,KAAM,IAAI,CAAAE,KAAK,CAAC,kBAAkB,CAAC,CACrC,CACF,CAEA;AACA2L,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAA1C,QAAQ,CAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAtC,QAAQ,CAAC9G,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG4G,QAAQ,CAAC3G,GAAG,CAAC2J,OAAO,EAAIA,OAAO,CAACjO,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACvF,CAEA;AACA6N,qBAAqBA,CAAC5G,IAAI,CAAE,CAC1B,KAAM,CAAAgE,QAAQ,CAAG,IAAI,CAACsC,WAAW,CAAC,CAAC,CAACjL,MAAM,CAAC8L,CAAC,EAAIA,CAAC,CAACnH,IAAI,GAAKA,IAAI,CAAC,CAChE,KAAM,CAAAkG,MAAM,CAAGlG,IAAI,GAAK,SAAS,CAAG,KAAK,CAAG,KAAK,CACjD,KAAM,CAAAmG,UAAU,CAAGnC,QAAQ,CAAC9G,MAAM,CAAG,CAAC,CACtC,MAAO,GAAGgJ,MAAM,IAAIC,UAAU,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9D,CAEA;AACAQ,yBAAyBA,CAACG,OAAO,CAAE,CACjC,GAAI,CACF,KAAM,CAAAI,YAAY,CAAG,CACnBhM,IAAI,CAAE4L,OAAO,CAACK,WAAW,CACzBrC,SAAS,CAAEgC,OAAO,CAACL,aAAa,CAChCtN,WAAW,CAAE,GAAG2N,OAAO,CAAChH,IAAI,GAAK,SAAS,CAAG,SAAS,CAAG,SAAS,MAAMgH,OAAO,CAAC3N,WAAW,EAAE,CAC7F2G,IAAI,CAAEgH,OAAO,CAAChH,IAAI,GAAK,SAAS,CAAG,SAAS,CAAG,SAAS,CACxDxF,MAAM,CAAE,QACV,CAAC,CAED,GAAIwM,OAAO,CAAChH,IAAI,GAAK,SAAS,CAAE,CAC9B;AACAoH,YAAY,CAACE,YAAY,CAAG,CAAC,CAC3BC,SAAS,CAAEP,OAAO,CAACO,SAAS,CAAE;AAC9B7I,MAAM,CAAEsI,OAAO,CAACtI,MAAM,CACtBrF,WAAW,CAAE2N,OAAO,CAAC3N,WACvB,CAAC,CAAC,CACF+N,YAAY,CAACI,aAAa,CAAG,CAAC,CAC5BD,SAAS,CAAEP,OAAO,CAACS,aAAa,EAAIT,OAAO,CAAC1H,UAAU,CAAE;AACxDZ,MAAM,CAAEsI,OAAO,CAACtI,MAAM,CACtBrF,WAAW,CAAE2N,OAAO,CAAC3N,WACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA+N,YAAY,CAACE,YAAY,CAAG,CAAC,CAC3BC,SAAS,CAAEP,OAAO,CAACU,WAAW,EAAIV,OAAO,CAACW,UAAU,CAAE;AACtDjJ,MAAM,CAAEsI,OAAO,CAACtI,MAAM,CACtBrF,WAAW,CAAE2N,OAAO,CAAC3N,WACvB,CAAC,CAAC,CACF+N,YAAY,CAACI,aAAa,CAAG,CAAC,CAC5BD,SAAS,CAAEP,OAAO,CAACO,SAAS,CAAE;AAC9B7I,MAAM,CAAEsI,OAAO,CAACtI,MAAM,CACtBrF,WAAW,CAAE2N,OAAO,CAAC3N,WACvB,CAAC,CAAC,CACJ,CAEA+N,YAAY,CAACQ,WAAW,CAAGZ,OAAO,CAACtI,MAAM,CACzC,IAAI,CAACmJ,eAAe,CAACT,YAAY,CAAC,CACpC,CAAE,MAAOvM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAEA;AAEA;AACAiN,gBAAgBA,CAAC/O,EAAE,CAAE,CACnB,GAAI,CACF,KAAM,CAAAgP,OAAO,CAAG,IAAI,CAACvE,iBAAiB,CAAC,CAAC,CACxC,KAAM,CAAAwE,UAAU,CAAGD,OAAO,CAAC7L,SAAS,CAAC+L,KAAK,EAAIA,KAAK,CAAClP,EAAE,GAAKA,EAAE,CAAC,CAE9D,GAAIiP,UAAU,GAAK,CAAC,CAAC,CAAE,CACrBD,OAAO,CAACC,UAAU,CAAC,CAAG,CACpB,GAAGD,OAAO,CAACC,UAAU,CAAC,CACtBxN,MAAM,CAAE,QAAQ,CAChB0N,QAAQ,CAAE,GAAI,CAAA1O,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAClC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDjB,YAAY,CAACE,OAAO,CAAC,kCAAkC,CAAEC,IAAI,CAACC,SAAS,CAACmP,OAAO,CAAC,CAAC,CAEjF;AACA,IAAI,CAACI,qBAAqB,CAACJ,OAAO,CAACC,UAAU,CAAC,CAAC,CAE/C,MAAO,CAAAD,OAAO,CAACC,UAAU,CAAC,CAC5B,CAEA,KAAM,IAAI,CAAAjN,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,oBAAoB,CAAC,CACvC,CACF,CAEA;AACAoN,qBAAqBA,CAACf,YAAY,CAAE,CAClC,GAAI,CACF,KAAM,CAAA/D,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAEnC;AACA,GAAI8D,YAAY,CAACE,YAAY,CAAE,CAC7BF,YAAY,CAACE,YAAY,CAAChL,OAAO,CAAC8L,UAAU,EAAI,CAC9C,KAAM,CAAAC,YAAY,CAAGhF,QAAQ,CAACnH,SAAS,CAACoM,GAAG,EAAIA,GAAG,CAACvP,EAAE,GAAKwP,QAAQ,CAACH,UAAU,CAACb,SAAS,CAAC,CAAC,CACzF,GAAIc,YAAY,GAAK,CAAC,CAAC,CAAE,CACvBhF,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,CAAG,CAACnF,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,EAAI,CAAC,EAAIC,UAAU,CAACL,UAAU,CAAC1J,MAAM,CAAC,CACtG2E,QAAQ,CAACgF,YAAY,CAAC,CAAClM,SAAS,CAAG,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC7D,CACF,CAAC,CAAC,CACJ,CAEA;AACA,GAAI2N,YAAY,CAACI,aAAa,CAAE,CAC9BJ,YAAY,CAACI,aAAa,CAAClL,OAAO,CAACoM,WAAW,EAAI,CAChD,KAAM,CAAAL,YAAY,CAAGhF,QAAQ,CAACnH,SAAS,CAACoM,GAAG,EAAIA,GAAG,CAACvP,EAAE,GAAKwP,QAAQ,CAACG,WAAW,CAACnB,SAAS,CAAC,CAAC,CAC1F,GAAIc,YAAY,GAAK,CAAC,CAAC,CAAE,CACvBhF,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,CAAG,CAACnF,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,EAAI,CAAC,EAAIC,UAAU,CAACC,WAAW,CAAChK,MAAM,CAAC,CACvG2E,QAAQ,CAACgF,YAAY,CAAC,CAAClM,SAAS,CAAG,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC7D,CACF,CAAC,CAAC,CACJ,CAEAjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACyK,QAAQ,CAAC,CAAC,CAC7E,CAAE,MAAOxI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAEA;AAEA;AACAyI,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAD,QAAQ,CAAG7K,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAA4K,QAAQ,CAAG1K,IAAI,CAACqC,KAAK,CAACqI,QAAQ,CAAC,CAAG,IAAI,CAACsF,kBAAkB,CAAC,CAAC,CACpE,CAAE,MAAO9N,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,KAAI,CAAC8N,kBAAkB,CAAC,CAAC,CAClC,CACF,CAEA;AACAA,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CACL,CAAE5P,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,SAAS,CAAEgH,IAAI,CAAE,OAAO,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,oBAAqB,CAAC,CACtG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,OAAO,CAAEgH,IAAI,CAAE,OAAO,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,wBAAyB,CAAC,CACxG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,SAAS,CAAEgH,IAAI,CAAE,OAAO,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,eAAgB,CAAC,CACjG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,SAAS,CAAEgH,IAAI,CAAE,OAAO,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,eAAgB,CAAC,CACjG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,UAAU,CAAEgH,IAAI,CAAE,WAAW,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,gBAAiB,CAAC,CACvG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,sBAAsB,CAAEgH,IAAI,CAAE,WAAW,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,+BAAgC,CAAC,CAClI,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,WAAW,CAAEgH,IAAI,CAAE,QAAQ,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,gBAAiB,CAAC,CACrG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,UAAU,CAAEgH,IAAI,CAAE,SAAS,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,kBAAmB,CAAC,CACvG,CAAEN,EAAE,CAAE,CAAC,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,uBAAuB,CAAEgH,IAAI,CAAE,SAAS,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,uBAAwB,CAAC,CACzH,CAAEN,EAAE,CAAE,EAAE,CAAE6P,IAAI,CAAE,MAAM,CAAE5P,IAAI,CAAE,gBAAgB,CAAEgH,IAAI,CAAE,SAAS,CAAEwI,OAAO,CAAE,CAAC,CAAEnP,WAAW,CAAE,oBAAqB,CAAC,CACjH,CACH,CAEA;AACAwP,UAAUA,CAACC,OAAO,CAAE,CAClB,GAAI,CACF,KAAM,CAAAzF,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAyF,UAAU,CAAG,CACjB,GAAGD,OAAO,CACV/P,EAAE,CAAE,IAAI,CAACiQ,iBAAiB,CAAC,CAAC,CAC5BzP,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAED4J,QAAQ,CAAC5I,IAAI,CAACsO,UAAU,CAAC,CACzBvQ,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACyK,QAAQ,CAAC,CAAC,CAC3E,MAAO,CAAA0F,UAAU,CACnB,CAAE,MAAOlO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAiO,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAA3F,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAD,QAAQ,CAACnG,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGiG,QAAQ,CAAChG,GAAG,CAAC4L,CAAC,EAAIA,CAAC,CAAClQ,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAC3E,CAEA;AACAyK,iBAAiBA,CAAA,CAAG,CAClB,GAAI,CACF,KAAM,CAAAuE,OAAO,CAAGvP,YAAY,CAACC,OAAO,CAAC,kCAAkC,CAAC,CACxE,MAAO,CAAAsP,OAAO,CAAGpP,IAAI,CAACqC,KAAK,CAAC+M,OAAO,CAAC,CAAG,EAAE,CAC3C,CAAE,MAAOlN,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,MAAO,EAAE,CACX,CACF,CAEA;AACAgN,eAAeA,CAACI,KAAK,CAAE,CACrB,GAAI,CACF,KAAM,CAAAF,OAAO,CAAG,IAAI,CAACvE,iBAAiB,CAAC,CAAC,CACxC,KAAM,CAAA0F,QAAQ,CAAG,CACf,GAAGjB,KAAK,CACRlP,EAAE,CAAE,IAAI,CAACoQ,sBAAsB,CAAC,CAAC,CACjC5P,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC0C,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDsO,OAAO,CAACtN,IAAI,CAACyO,QAAQ,CAAC,CACtB1Q,YAAY,CAACE,OAAO,CAAC,kCAAkC,CAAEC,IAAI,CAACC,SAAS,CAACmP,OAAO,CAAC,CAAC,CAEjF;AACA,IAAI,CAACI,qBAAqB,CAACe,QAAQ,CAAC,CAEpC,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOrO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,oBAAoB,CAAC,CACvC,CACF,CAEA;AACAoN,qBAAqBA,CAACF,KAAK,CAAE,CAC3B,GAAI,CACF,KAAM,CAAA5E,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAEnC2E,KAAK,CAACF,OAAO,CAACzL,OAAO,CAAC8M,IAAI,EAAI,CAC5B,KAAM,CAAAf,YAAY,CAAGhF,QAAQ,CAACnH,SAAS,CAAC+M,CAAC,EAAIA,CAAC,CAAClQ,EAAE,GAAKwP,QAAQ,CAACa,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAC/E,GAAIc,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB;AACA;AACA,GAAIhF,QAAQ,CAACgF,YAAY,CAAC,CAACrI,IAAI,GAAK,OAAO,EAAIqD,QAAQ,CAACgF,YAAY,CAAC,CAACrI,IAAI,GAAK,SAAS,CAAE,CACxFqD,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,EAAKY,IAAI,CAACC,KAAK,CAAGD,IAAI,CAACE,MAAO,CAC9D,CAAC,IAAM,CACLjG,QAAQ,CAACgF,YAAY,CAAC,CAACG,OAAO,EAAKY,IAAI,CAACE,MAAM,CAAGF,IAAI,CAACC,KAAM,CAC9D,CACAhG,QAAQ,CAACgF,YAAY,CAAC,CAAClM,SAAS,CAAG,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC7D,CACF,CAAC,CAAC,CAEFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACyK,QAAQ,CAAC,CAAC,CAC7E,CAAE,MAAOxI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAEA;AACAsO,sBAAsBA,CAAA,CAAG,CACvB,KAAM,CAAApB,OAAO,CAAG,IAAI,CAACvE,iBAAiB,CAAC,CAAC,CACxC,MAAO,CAAAuE,OAAO,CAAC7K,MAAM,CAAG,CAAC,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG2K,OAAO,CAAC1K,GAAG,CAACkM,CAAC,EAAIA,CAAC,CAACxQ,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACzE,CAEA6B,sBAAsBA,CAAA,CAAG,CACvB,GAAI,CACF,KAAM,CAAAoC,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnCD,QAAQ,CAAC/C,iBAAiB,EAAI,CAAC,CAC/BzB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACoE,QAAQ,CAAC,CAAC,CAC7E,CAAE,MAAOnC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAEA;AAEAoC,WAAWA,CAAA,CAAG,CACZ,GAAI,CACF,KAAM,CAAAD,QAAQ,CAAGxE,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAAuE,QAAQ,CAAGrE,IAAI,CAACqC,KAAK,CAACgC,QAAQ,CAAC,CAAG,IAAI,CAAClE,kBAAkB,CAAC,CAAC,CACpE,CAAE,MAAO+B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,KAAI,CAAC/B,kBAAkB,CAAC,CAAC,CAClC,CACF,CAEA+J,cAAcA,CAAC7G,OAAO,CAAE,CACtB,GAAI,CACF,KAAM,CAAAgB,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAA6F,WAAW,CAAG,CAAE,GAAG9F,QAAQ,CAAE,GAAGhB,OAAO,CAAEG,SAAS,CAAE,GAAI,CAAA3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE,CAAC,CACpFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACkK,WAAW,CAAC,CAAC,CAC9E,MAAO,CAAAA,WAAW,CACpB,CAAE,MAAOjI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,IAAI,CAAAE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACF,CAEA;AAEA;AACAyO,aAAaA,CAAA,CAAG,CACd,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAjQ,IAAI,CAAC,CAAC,CAAC+B,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAmO,aAAa,CAAG,IAAI,CAACvO,iBAAiB,CAACsO,KAAK,CAAC,CAEnD,KAAM,CAAAE,UAAU,CAAGD,aAAa,CAAC/I,MAAM,CAAC,CAACiJ,GAAG,CAAEzP,OAAO,GAAKyP,GAAG,CAAGzP,OAAO,CAACiF,KAAK,CAAE,CAAC,CAAC,CACjF,KAAM,CAAAyK,QAAQ,CAAGH,aAAa,CAAC/I,MAAM,CAAC,CAACiJ,GAAG,CAAEzP,OAAO,GAAKyP,GAAG,CAAGzP,OAAO,CAAC2P,GAAG,CAAE,CAAC,CAAC,CAC7E,KAAM,CAAAC,aAAa,CAAGL,aAAa,CAACxM,MAAM,CAE1C,MAAO,CACLyM,UAAU,CACVE,QAAQ,CACRE,aAAa,CACbC,cAAc,CAAED,aAAa,CAAG,CAAC,CAAGJ,UAAU,CAAGI,aAAa,CAAG,CACnE,CAAC,CACH,CAEA;AACAE,aAAaA,CAAA,CAAG,CACd,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA1Q,IAAI,CAAC,CAAC,CAAC2Q,QAAQ,CAAC,CAAC,CAC1C,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAA5Q,IAAI,CAAC,CAAC,CAAC6Q,WAAW,CAAC,CAAC,CAE5C,KAAM,CAAAC,aAAa,CAAG,IAAI,CAACjQ,WAAW,CAAC,CAAC,CAACgB,MAAM,CAAClB,OAAO,EAAI,CACzD,KAAM,CAAAmB,WAAW,CAAG,GAAI,CAAA9B,IAAI,CAACW,OAAO,CAACZ,SAAS,CAAC,CAC/C,MAAO,CAAA+B,WAAW,CAAC6O,QAAQ,CAAC,CAAC,GAAKD,YAAY,EAAI5O,WAAW,CAAC+O,WAAW,CAAC,CAAC,GAAKD,WAAW,CAC7F,CAAC,CAAC,CAEF,KAAM,CAAAT,UAAU,CAAGW,aAAa,CAAC3J,MAAM,CAAC,CAACiJ,GAAG,CAAEzP,OAAO,GAAKyP,GAAG,CAAGzP,OAAO,CAACiF,KAAK,CAAE,CAAC,CAAC,CACjF,KAAM,CAAAyK,QAAQ,CAAGS,aAAa,CAAC3J,MAAM,CAAC,CAACiJ,GAAG,CAAEzP,OAAO,GAAKyP,GAAG,CAAGzP,OAAO,CAAC2P,GAAG,CAAE,CAAC,CAAC,CAC7E,KAAM,CAAAC,aAAa,CAAGO,aAAa,CAACpN,MAAM,CAE1C,MAAO,CACLyM,UAAU,CACVE,QAAQ,CACRE,aAAa,CACbC,cAAc,CAAED,aAAa,CAAG,CAAC,CAAGJ,UAAU,CAAGI,aAAa,CAAG,CACnE,CAAC,CACH,CAEA;AACAnJ,mBAAmBA,CAAA,CAAgB,IAAf,CAAA2J,SAAS,CAAAC,SAAA,CAAAtN,MAAA,IAAAsN,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CAC/B,KAAM,CAAA9O,QAAQ,CAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CACnC,MAAO,CAAAC,QAAQ,CAACL,MAAM,CAACO,OAAO,EAAIA,OAAO,CAAC1C,KAAK,EAAIqR,SAAS,CAAC,CAC/D,CAEA;AACAG,qBAAqBA,CAAA,CAAY,IAAX,CAAAC,KAAK,CAAAH,SAAA,CAAAtN,MAAA,IAAAsN,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CAC7B,KAAM,CAAApQ,QAAQ,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACnC,KAAM,CAAAuQ,YAAY,CAAG,CAAC,CAAC,CAEvBxQ,QAAQ,CAACkC,OAAO,CAACnC,OAAO,EAAI,CAC1BA,OAAO,CAACQ,KAAK,CAAC2B,OAAO,CAACC,IAAI,EAAI,CAC5B,GAAIqO,YAAY,CAACrO,IAAI,CAACxD,EAAE,CAAC,CAAE,CACzB6R,YAAY,CAACrO,IAAI,CAACxD,EAAE,CAAC,CAAC0D,QAAQ,EAAIF,IAAI,CAACE,QAAQ,CAC/CmO,YAAY,CAACrO,IAAI,CAACxD,EAAE,CAAC,CAAC8R,OAAO,EAAItO,IAAI,CAACtD,KAAK,CAAGsD,IAAI,CAACE,QAAQ,CAC7D,CAAC,IAAM,CACLmO,YAAY,CAACrO,IAAI,CAACxD,EAAE,CAAC,CAAG,CACtBA,EAAE,CAAEwD,IAAI,CAACxD,EAAE,CACXC,IAAI,CAAEuD,IAAI,CAACvD,IAAI,CACfyD,QAAQ,CAAEF,IAAI,CAACE,QAAQ,CACvBoO,OAAO,CAAEtO,IAAI,CAACtD,KAAK,CAAGsD,IAAI,CAACE,QAC7B,CAAC,CACH,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAqO,MAAM,CAACC,MAAM,CAACH,YAAY,CAAC,CAC/BI,IAAI,CAAC,CAAC/B,CAAC,CAAEgC,CAAC,GAAKA,CAAC,CAACxO,QAAQ,CAAGwM,CAAC,CAACxM,QAAQ,CAAC,CACvCyO,KAAK,CAAC,CAAC,CAAEP,KAAK,CAAC,CACpB,CAEA;AACAQ,UAAUA,CAAA,CAAG,CACX,MAAO,CACL/Q,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5BqB,QAAQ,CAAE,IAAI,CAACD,WAAW,CAAC,CAAC,CAC5BkB,SAAS,CAAE,IAAI,CAACD,YAAY,CAAC,CAAC,CAC9Be,SAAS,CAAE,IAAI,CAACD,YAAY,CAAC,CAAC,CAC9B6F,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5BC,cAAc,CAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC,CACxCxG,QAAQ,CAAE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC5ByG,UAAU,CAAE,GAAI,CAAAlK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CACH,CAEA;AACA2R,UAAUA,CAAChI,IAAI,CAAE,CACf,GAAI,CACF,GAAIA,IAAI,CAAChJ,QAAQ,CAAE,CACjB5B,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAChJ,QAAQ,CAAC,CAAC,CAClF,CACA,GAAIgJ,IAAI,CAAC1H,QAAQ,CAAE,CACjBlD,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC1H,QAAQ,CAAC,CAAC,CAClF,CACA,GAAI0H,IAAI,CAACzG,SAAS,CAAE,CAClBnE,YAAY,CAACE,OAAO,CAAC,4BAA4B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACzG,SAAS,CAAC,CAAC,CACpF,CACA,GAAIyG,IAAI,CAACpG,QAAQ,CAAE,CACjBxE,YAAY,CAACE,OAAO,CAAC,2BAA2B,CAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpG,QAAQ,CAAC,CAAC,CAClF,CACA,MAAO,KAAI,CACb,CAAE,MAAOnC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,IAAI,CAAAE,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CACF,CAEA;AACAsQ,YAAYA,CAAA,CAAG,CACb,GAAI,CACF7S,YAAY,CAAC8S,UAAU,CAAC,2BAA2B,CAAC,CACpD9S,YAAY,CAAC8S,UAAU,CAAC,2BAA2B,CAAC,CACpD9S,YAAY,CAAC8S,UAAU,CAAC,4BAA4B,CAAC,CACrD9S,YAAY,CAAC8S,UAAU,CAAC,2BAA2B,CAAC,CACpD,IAAI,CAAC/S,kBAAkB,CAAC,CAAC,CACzB,MAAO,KAAI,CACb,CAAE,MAAOsC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAAE,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACAwQ,WAAWA,CAACC,QAAQ,CAAE,CACpB,GAAI,CACF,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACC,aAAa,CAAC,CAAC,CACvC,KAAM,CAAAC,WAAW,CAAG,CAClB5S,EAAE,CAAES,IAAI,CAACoS,GAAG,CAAC,CAAC,CACd,GAAGJ,QAAQ,CACXK,SAAS,CAAEL,QAAQ,CAACK,SAAS,EAAI,GAAI,CAAArS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAC1D,CAAC,CACDgS,UAAU,CAAChR,IAAI,CAACkR,WAAW,CAAC,CAC5BnT,YAAY,CAACE,OAAO,CAAC,YAAY,CAAEC,IAAI,CAACC,SAAS,CAAC6S,UAAU,CAAC,CAAC,CAC9D,MAAO,CAAAE,WAAW,CACpB,CAAE,MAAO9Q,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,MAAO,KAAI,CACb,CACF,CAEA;AACA6Q,aAAaA,CAAA,CAAG,CACd,GAAI,CACF,MAAO,CAAA/S,IAAI,CAACqC,KAAK,CAACxC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAI,IAAI,CAAC,CAC/D,CAAE,MAAOoC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,EAAE,CACX,CACF,CACF,CAEA;AACA,KAAM,CAAAiR,QAAQ,CAAG,GAAI,CAAAzT,aAAa,CAAC,CAAC,CAEpC,cAAe,CAAAyT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}