import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Inventory.css';

const Inventory = () => {
  const [products, setProducts] = useState([]);
  const [movements, setMovements] = useState([]);
  const [showMovementForm, setShowMovementForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newMovement, setNewMovement] = useState({
    productId: '',
    type: 'in',
    quantity: '',
    reason: '',
    reference: '',
    notes: '',
    date: new Date().toISOString().split('T')[0]
  });

  const movementTypes = [
    { value: 'in', label: '📈 إدخال', color: '#10b981' },
    { value: 'out', label: '📉 إخراج', color: '#ef4444' },
    { value: 'adjustment', label: '⚖️ تعديل', color: '#f59e0b' },
    { value: 'transfer', label: '🔄 نقل', color: '#6366f1' },
    { value: 'damaged', label: '💔 تالف', color: '#8b5cf6' },
    { value: 'expired', label: '⏰ منتهي الصلاحية', color: '#ec4899' }
  ];

  const movementReasons = [
    'شراء جديد',
    'مبيعات',
    'إرجاع من عميل',
    'إرجاع للمورد',
    'جرد دوري',
    'تلف',
    'انتهاء صلاحية',
    'عينات مجانية',
    'نقل بين فروع',
    'تعديل خطأ إدخال'
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const productsData = database.getProducts();
      const movementsData = database.getInventoryMovements();
      
      setProducts(productsData);
      setMovements(movementsData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewMovement(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const movementData = {
        ...newMovement,
        quantity: parseInt(newMovement.quantity),
        productId: parseInt(newMovement.productId)
      };

      await database.addInventoryMovement(movementData);
      setMessage('تم تسجيل حركة المخزون بنجاح!');
      
      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في تسجيل حركة المخزون:', error);
      setMessage('خطأ في تسجيل حركة المخزون');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewMovement({
      productId: '',
      type: 'in',
      quantity: '',
      reason: '',
      reference: '',
      notes: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowMovementForm(false);
  };

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId);
    return product ? product.name : 'غير محدد';
  };

  const getMovementTypeInfo = (type) => {
    const typeObj = movementTypes.find(t => t.value === type);
    return typeObj || { label: type, color: '#64748b' };
  };

  const calculateStockValue = () => {
    return products.reduce((total, product) => {
      return total + (product.stock * product.price);
    }, 0);
  };

  const getLowStockProducts = () => {
    return products.filter(product => product.stock <= (product.minStock || 5));
  };

  const getOutOfStockProducts = () => {
    return products.filter(product => product.stock === 0);
  };

  const getCategories = () => {
    const categories = [...new Set(products.map(p => p.category))];
    return categories.filter(cat => cat);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;
    
    let matchesType = true;
    if (filterType === 'low_stock') {
      matchesType = product.stock <= (product.minStock || 5);
    } else if (filterType === 'out_of_stock') {
      matchesType = product.stock === 0;
    } else if (filterType === 'in_stock') {
      matchesType = product.stock > 0;
    }
    
    return matchesSearch && matchesCategory && matchesType;
  });

  const filteredMovements = movements.filter(movement => {
    const productName = getProductName(movement.productId);
    return productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           movement.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
           movement.reference.toLowerCase().includes(searchTerm.toLowerCase());
  });

  return (
    <div className="inventory-container">
      <div className="inventory-header">
        <h1>📦 إدارة المخزون المتقدمة</h1>
        <p>تتبع وإدارة حركات المخزون والمنتجات</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Dashboard */}
      <div className="inventory-stats">
        <div className="stat-card total-value">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>قيمة المخزون الإجمالية</h3>
            <p>{calculateStockValue().toLocaleString()} ريال</p>
          </div>
        </div>
        <div className="stat-card total-products">
          <div className="stat-icon">📦</div>
          <div className="stat-info">
            <h3>إجمالي المنتجات</h3>
            <p>{products.length} منتج</p>
          </div>
        </div>
        <div className="stat-card low-stock">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <h3>مخزون منخفض</h3>
            <p>{getLowStockProducts().length} منتج</p>
          </div>
        </div>
        <div className="stat-card out-of-stock">
          <div className="stat-icon">🚫</div>
          <div className="stat-info">
            <h3>نفد المخزون</h3>
            <p>{getOutOfStockProducts().length} منتج</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="inventory-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع المنتجات</option>
            <option value="in_stock">متوفر في المخزون</option>
            <option value="low_stock">مخزون منخفض</option>
            <option value="out_of_stock">نفد المخزون</option>
          </select>
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الفئات</option>
            {getCategories().map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setShowMovementForm(true)}
          className="add-movement-btn"
        >
          ➕ تسجيل حركة مخزون
        </button>
      </div>

      {/* Inventory Tabs */}
      <div className="inventory-tabs">
        <div className="tab-content">
          {/* Products Inventory Table */}
          <div className="inventory-section">
            <h3>📋 جرد المنتجات</h3>
            <div className="products-table-container">
              <table className="products-table">
                <thead>
                  <tr>
                    <th>المنتج</th>
                    <th>الباركود</th>
                    <th>الفئة</th>
                    <th>الكمية الحالية</th>
                    <th>الحد الأدنى</th>
                    <th>سعر الوحدة</th>
                    <th>القيمة الإجمالية</th>
                    <th>الحالة</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.length === 0 ? (
                    <tr>
                      <td colSpan="8" className="no-data">
                        لا توجد منتجات مطابقة للبحث
                      </td>
                    </tr>
                  ) : (
                    filteredProducts.map(product => {
                      const isLowStock = product.stock <= (product.minStock || 5);
                      const isOutOfStock = product.stock === 0;
                      const totalValue = product.stock * product.price;
                      
                      return (
                        <tr key={product.id} className={isOutOfStock ? 'out-of-stock' : isLowStock ? 'low-stock' : ''}>
                          <td className="product-name">{product.name}</td>
                          <td>{product.barcode}</td>
                          <td>{product.category}</td>
                          <td className="quantity">{product.stock}</td>
                          <td>{product.minStock || 5}</td>
                          <td className="price">{product.price.toLocaleString()} ريال</td>
                          <td className="total-value">{totalValue.toLocaleString()} ريال</td>
                          <td>
                            <span className={`status-badge ${isOutOfStock ? 'out-of-stock' : isLowStock ? 'low-stock' : 'in-stock'}`}>
                              {isOutOfStock ? '🚫 نفد المخزون' : isLowStock ? '⚠️ مخزون منخفض' : '✅ متوفر'}
                            </span>
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Recent Movements */}
          <div className="movements-section">
            <h3>📊 حركات المخزون الأخيرة</h3>
            <div className="movements-table-container">
              <table className="movements-table">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>المنتج</th>
                    <th>نوع الحركة</th>
                    <th>الكمية</th>
                    <th>السبب</th>
                    <th>المرجع</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMovements.slice(0, 10).map(movement => {
                    const typeInfo = getMovementTypeInfo(movement.type);
                    return (
                      <tr key={movement.id}>
                        <td>{new Date(movement.date).toLocaleDateString('ar-SA')}</td>
                        <td>{getProductName(movement.productId)}</td>
                        <td>
                          <span 
                            className="movement-type"
                            style={{ color: typeInfo.color }}
                          >
                            {typeInfo.label}
                          </span>
                        </td>
                        <td className={`quantity ${movement.type === 'in' ? 'positive' : 'negative'}`}>
                          {movement.type === 'in' ? '+' : '-'}{movement.quantity}
                        </td>
                        <td>{movement.reason}</td>
                        <td>{movement.reference || '-'}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Add Movement Form Modal */}
      {showMovementForm && (
        <div className="modal-overlay">
          <div className="movement-form-modal">
            <div className="modal-header">
              <h3>➕ تسجيل حركة مخزون جديدة</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="movement-form">
              <div className="form-grid">
                <div className="form-group">
                  <label>المنتج</label>
                  <select
                    name="productId"
                    value={newMovement.productId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر المنتج</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.name} - المخزون الحالي: {product.stock}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>نوع الحركة</label>
                  <select
                    name="type"
                    value={newMovement.type}
                    onChange={handleInputChange}
                    required
                  >
                    {movementTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الكمية</label>
                  <input
                    type="number"
                    name="quantity"
                    value={newMovement.quantity}
                    onChange={handleInputChange}
                    min="1"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>السبب</label>
                  <select
                    name="reason"
                    value={newMovement.reason}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر السبب</option>
                    {movementReasons.map(reason => (
                      <option key={reason} value={reason}>
                        {reason}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>التاريخ</label>
                  <input
                    type="date"
                    name="date"
                    value={newMovement.date}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label>رقم المرجع</label>
                  <input
                    type="text"
                    name="reference"
                    value={newMovement.reference}
                    onChange={handleInputChange}
                    placeholder="رقم الفاتورة، أمر الشراء، إلخ"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    name="notes"
                    value={newMovement.notes}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : '💾 تسجيل الحركة'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inventory;
