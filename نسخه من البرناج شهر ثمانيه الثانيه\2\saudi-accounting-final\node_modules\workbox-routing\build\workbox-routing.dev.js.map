{"version": 3, "file": "workbox-routing.dev.js", "sources": ["../_version.js", "../utils/constants.js", "../utils/normalizeHandler.js", "../Route.js", "../NavigationRoute.js", "../RegExpRoute.js", "../Router.js", "../utils/getOrCreateDefaultRouter.js", "../registerRoute.js", "../setCatchHandler.js", "../setDefaultHandler.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * NavigationRoute makes it easy to create a\n * {@link workbox-routing.Route} that matches for browser\n * [navigation requests]{@link https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests}.\n *\n * It will only match incoming Requests whose\n * {@link https://fetch.spec.whatwg.org/#concept-request-mode|mode}\n * is set to `navigate`.\n *\n * You can optionally only apply this route to a subset of navigation requests\n * by using one or both of the `denylist` and `allowlist` parameters.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass NavigationRoute extends Route {\n    /**\n     * If both `denylist` and `allowlist` are provided, the `denylist` will\n     * take precedence and the request will not match this route.\n     *\n     * The regular expressions in `allowlist` and `denylist`\n     * are matched against the concatenated\n     * [`pathname`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/pathname}\n     * and [`search`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/search}\n     * portions of the requested URL.\n     *\n     * *Note*: These RegExps may be evaluated against every destination URL during\n     * a navigation. Avoid using\n     * [complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\n     * or else your users may see delays when navigating your site.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {Object} options\n     * @param {Array<RegExp>} [options.denylist] If any of these patterns match,\n     * the route will not handle the request (even if a allowlist RegExp matches).\n     * @param {Array<RegExp>} [options.allowlist=[/./]] If any of these patterns\n     * match the URL's pathname and search parameter, the route will handle the\n     * request (assuming the denylist doesn't match).\n     */\n    constructor(handler, { allowlist = [/./], denylist = [] } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArrayOfClass(allowlist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.allowlist',\n            });\n            assert.isArrayOfClass(denylist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.denylist',\n            });\n        }\n        super((options) => this._match(options), handler);\n        this._allowlist = allowlist;\n        this._denylist = denylist;\n    }\n    /**\n     * Routes match handler.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request\n     * @return {boolean}\n     *\n     * @private\n     */\n    _match({ url, request }) {\n        if (request && request.mode !== 'navigate') {\n            return false;\n        }\n        const pathnameAndSearch = url.pathname + url.search;\n        for (const regExp of this._denylist) {\n            if (regExp.test(pathnameAndSearch)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                        `being used, since the URL matches this denylist pattern: ` +\n                        `${regExp.toString()}`);\n                }\n                return false;\n            }\n        }\n        if (this._allowlist.some((regExp) => regExp.test(pathnameAndSearch))) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`The navigation route ${pathnameAndSearch} ` + `is being used.`);\n            }\n            return true;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                `being used, since the URL being navigated to doesn't ` +\n                `match the allowlist.`);\n        }\n        return false;\n    }\n}\nexport { NavigationRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * {@link workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * {@link workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if (url.origin !== location.origin && result.index !== 0) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a `FetchEvent` using one or more\n * {@link workbox-routing.Route}, responding with a `Response` if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event, }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([`Found a route to handle this request:`, route]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`,\n                        params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise &&\n            (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event, }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if (matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call {@link workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {workbox-routing.Route} The generated `Route`.\n *\n * @memberof workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http')\n                ? captureUrl.pathname\n                : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if (new RegExp(`${wildcards}`).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (url.pathname === captureUrl.pathname &&\n                    url.origin !== captureUrl.origin) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * If a Route throws an error while handling a request, this `handler`\n * will be called and given a chance to provide a response.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setCatchHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setCatchHandler(handler);\n}\nexport { setCatchHandler };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Define a default `handler` that's called when no routes explicitly\n * match the incoming request.\n *\n * Without a default handler, unmatched requests will go against the\n * network as if there were no service worker present.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setDefaultHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setDefaultHandler(handler);\n}\nexport { setDefaultHandler };\n"], "names": ["self", "_", "e", "defaultMethod", "validMethods", "normalize<PERSON><PERSON><PERSON>", "handler", "assert", "has<PERSON><PERSON><PERSON>", "moduleName", "className", "funcName", "paramName", "isType", "handle", "Route", "constructor", "match", "method", "isOneOf", "setCatchHandler", "<PERSON><PERSON><PERSON><PERSON>", "NavigationRoute", "allowlist", "denylist", "isArrayOfClass", "RegExp", "options", "_match", "_allowlist", "_denylist", "url", "request", "mode", "pathnameAndSearch", "pathname", "search", "regExp", "test", "logger", "log", "toString", "some", "debug", "RegExpRoute", "isInstance", "result", "exec", "href", "origin", "location", "index", "slice", "Router", "_routes", "Map", "_defaultHandlerMap", "routes", "addFetchListener", "addEventListener", "event", "responsePromise", "handleRequest", "respondWith", "addCacheListener", "data", "type", "payload", "urlsToCache", "requestPromises", "Promise", "all", "map", "entry", "Request", "waitUntil", "ports", "then", "postMessage", "URL", "protocol", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "params", "route", "findMatchingRoute", "debugMessages", "push", "has", "get", "getFriendlyURL", "groupCollapsed", "for<PERSON>ach", "msg", "Array", "isArray", "groupEnd", "err", "reject", "_catch<PERSON><PERSON>ler", "catch", "error", "catchErr", "Error", "matchResult", "warn", "length", "undefined", "Object", "keys", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "registerRoute", "unregisterRoute", "WorkboxError", "routeIndex", "indexOf", "splice", "defaultRouter", "getOrCreateDefaultRouter", "capture", "captureUrl", "valueToCheck", "wildcards", "matchCallback"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAD,CAAJ,IAAiCC,CAAC,EAAlC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,MAAMC,aAAa,GAAG,KAAtB;IACP;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,MAAMC,YAAY,GAAG,CACxB,QADwB,EAExB,KAFwB,EAGxB,MAHwB,EAIxB,OAJwB,EAKxB,MALwB,EAMxB,KANwB,CAArB;;ICxBP;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,MAAMC,gBAAgB,GAAIC,OAAD,IAAa;IACzC,MAAIA,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAAlC,EAA4C;IACxC,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,SAAP,CAAiBF,OAAjB,EAA0B,QAA1B,EAAoC;IAChCG,QAAAA,UAAU,EAAE,iBADoB;IAEhCC,QAAAA,SAAS,EAAE,OAFqB;IAGhCC,QAAAA,QAAQ,EAAE,aAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,WAAON,OAAP;IACH,GAVD,MAWK;IACD,IAA2C;IACvCC,MAAAA,gBAAM,CAACM,MAAP,CAAcP,OAAd,EAAuB,UAAvB,EAAmC;IAC/BG,QAAAA,UAAU,EAAE,iBADmB;IAE/BC,QAAAA,SAAS,EAAE,OAFoB;IAG/BC,QAAAA,QAAQ,EAAE,aAHqB;IAI/BC,QAAAA,SAAS,EAAE;IAJoB,OAAnC;IAMH;;IACD,WAAO;IAAEE,MAAAA,MAAM,EAAER;IAAV,KAAP;IACH;IACJ,CAvBM;;IChBP;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMS,KAAN,CAAY;IACR;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAACC,KAAD,EAAQX,OAAR,EAAiBY,MAAM,GAAGf,aAA1B,EAAyC;IAChD,IAA2C;IACvCI,MAAAA,gBAAM,CAACM,MAAP,CAAcI,KAAd,EAAqB,UAArB,EAAiC;IAC7BR,QAAAA,UAAU,EAAE,iBADiB;IAE7BC,QAAAA,SAAS,EAAE,OAFkB;IAG7BC,QAAAA,QAAQ,EAAE,aAHmB;IAI7BC,QAAAA,SAAS,EAAE;IAJkB,OAAjC;;IAMA,UAAIM,MAAJ,EAAY;IACRX,QAAAA,gBAAM,CAACY,OAAP,CAAeD,MAAf,EAAuBd,YAAvB,EAAqC;IAAEQ,UAAAA,SAAS,EAAE;IAAb,SAArC;IACH;IACJ,KAX+C;IAahD;;;IACA,SAAKN,OAAL,GAAeD,gBAAgB,CAACC,OAAD,CAA/B;IACA,SAAKW,KAAL,GAAaA,KAAb;IACA,SAAKC,MAAL,GAAcA,MAAd;IACH;IACD;IACJ;IACA;IACA;IACA;;;IACIE,EAAAA,eAAe,CAACd,OAAD,EAAU;IACrB,SAAKe,YAAL,GAAoBhB,gBAAgB,CAACC,OAAD,CAApC;IACH;;IArCO;;ICpBZ;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMgB,eAAN,SAA8BP,KAA9B,CAAoC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAACV,OAAD,EAAU;IAAEiB,IAAAA,SAAS,GAAG,CAAC,GAAD,CAAd;IAAqBC,IAAAA,QAAQ,GAAG;IAAhC,MAAuC,EAAjD,EAAqD;IAC5D,IAA2C;IACvCjB,MAAAA,gBAAM,CAACkB,cAAP,CAAsBF,SAAtB,EAAiCG,MAAjC,EAAyC;IACrCjB,QAAAA,UAAU,EAAE,iBADyB;IAErCC,QAAAA,SAAS,EAAE,iBAF0B;IAGrCC,QAAAA,QAAQ,EAAE,aAH2B;IAIrCC,QAAAA,SAAS,EAAE;IAJ0B,OAAzC;IAMAL,MAAAA,gBAAM,CAACkB,cAAP,CAAsBD,QAAtB,EAAgCE,MAAhC,EAAwC;IACpCjB,QAAAA,UAAU,EAAE,iBADwB;IAEpCC,QAAAA,SAAS,EAAE,iBAFyB;IAGpCC,QAAAA,QAAQ,EAAE,aAH0B;IAIpCC,QAAAA,SAAS,EAAE;IAJyB,OAAxC;IAMH;;IACD,UAAOe,OAAD,IAAa,KAAKC,MAAL,CAAYD,OAAZ,CAAnB,EAAyCrB,OAAzC;IACA,SAAKuB,UAAL,GAAkBN,SAAlB;IACA,SAAKO,SAAL,GAAiBN,QAAjB;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACII,EAAAA,MAAM,CAAC;IAAEG,IAAAA,GAAF;IAAOC,IAAAA;IAAP,GAAD,EAAmB;IACrB,QAAIA,OAAO,IAAIA,OAAO,CAACC,IAAR,KAAiB,UAAhC,EAA4C;IACxC,aAAO,KAAP;IACH;;IACD,UAAMC,iBAAiB,GAAGH,GAAG,CAACI,QAAJ,GAAeJ,GAAG,CAACK,MAA7C;;IACA,SAAK,MAAMC,MAAX,IAAqB,KAAKP,SAA1B,EAAqC;IACjC,UAAIO,MAAM,CAACC,IAAP,CAAYJ,iBAAZ,CAAJ,EAAoC;IAChC,QAA2C;IACvCK,UAAAA,gBAAM,CAACC,GAAP,CAAY,wBAAuBN,iBAAkB,UAA1C,GACN,2DADM,GAEN,GAAEG,MAAM,CAACI,QAAP,EAAkB,EAFzB;IAGH;;IACD,eAAO,KAAP;IACH;IACJ;;IACD,QAAI,KAAKZ,UAAL,CAAgBa,IAAhB,CAAsBL,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYJ,iBAAZ,CAAjC,CAAJ,EAAsE;IAClE,MAA2C;IACvCK,QAAAA,gBAAM,CAACI,KAAP,CAAc,wBAAuBT,iBAAkB,GAA1C,GAAgD,gBAA7D;IACH;;IACD,aAAO,IAAP;IACH;;IACD,IAA2C;IACvCK,MAAAA,gBAAM,CAACC,GAAP,CAAY,wBAAuBN,iBAAkB,UAA1C,GACN,uDADM,GAEN,sBAFL;IAGH;;IACD,WAAO,KAAP;IACH;;IAjF+B;;IC1BpC;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMU,WAAN,SAA0B7B,KAA1B,CAAgC;IAC5B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAACqB,MAAD,EAAS/B,OAAT,EAAkBY,MAAlB,EAA0B;IACjC,IAA2C;IACvCX,MAAAA,gBAAM,CAACsC,UAAP,CAAkBR,MAAlB,EAA0BX,MAA1B,EAAkC;IAC9BjB,QAAAA,UAAU,EAAE,iBADkB;IAE9BC,QAAAA,SAAS,EAAE,aAFmB;IAG9BC,QAAAA,QAAQ,EAAE,aAHoB;IAI9BC,QAAAA,SAAS,EAAE;IAJmB,OAAlC;IAMH;;IACD,UAAMK,KAAK,GAAG,CAAC;IAAEc,MAAAA;IAAF,KAAD,KAAa;IACvB,YAAMe,MAAM,GAAGT,MAAM,CAACU,IAAP,CAAYhB,GAAG,CAACiB,IAAhB,CAAf,CADuB;;IAGvB,UAAI,CAACF,MAAL,EAAa;IACT;IACH,OALsB;IAOvB;IACA;IACA;;;IACA,UAAIf,GAAG,CAACkB,MAAJ,KAAeC,QAAQ,CAACD,MAAxB,IAAkCH,MAAM,CAACK,KAAP,KAAiB,CAAvD,EAA0D;IACtD,QAA2C;IACvCZ,UAAAA,gBAAM,CAACI,KAAP,CAAc,2BAA0BN,MAAM,CAACI,QAAP,EAAkB,2BAA7C,GACR,iCAAgCV,GAAG,CAACU,QAAJ,EAAe,6BADvC,GAER,4DAFL;IAGH;;IACD;IACH,OAjBsB;IAmBvB;IACA;IACA;;;IACA,aAAOK,MAAM,CAACM,KAAP,CAAa,CAAb,CAAP;IACH,KAvBD;;IAwBA,UAAMnC,KAAN,EAAaX,OAAb,EAAsBY,MAAtB;IACH;;IAhD2B;;ICtBhC;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMmC,MAAN,CAAa;IACT;IACJ;IACA;IACIrC,EAAAA,WAAW,GAAG;IACV,SAAKsC,OAAL,GAAe,IAAIC,GAAJ,EAAf;IACA,SAAKC,kBAAL,GAA0B,IAAID,GAAJ,EAA1B;IACH;IACD;IACJ;IACA;IACA;IACA;;;IACI,MAAIE,MAAJ,GAAa;IACT,WAAO,KAAKH,OAAZ;IACH;IACD;IACJ;IACA;IACA;;;IACII,EAAAA,gBAAgB,GAAG;IACf;IACA1D,IAAAA,IAAI,CAAC2D,gBAAL,CAAsB,OAAtB,EAAiCC,KAAD,IAAW;IACvC,YAAM;IAAE5B,QAAAA;IAAF,UAAc4B,KAApB;IACA,YAAMC,eAAe,GAAG,KAAKC,aAAL,CAAmB;IAAE9B,QAAAA,OAAF;IAAW4B,QAAAA;IAAX,OAAnB,CAAxB;;IACA,UAAIC,eAAJ,EAAqB;IACjBD,QAAAA,KAAK,CAACG,WAAN,CAAkBF,eAAlB;IACH;IACJ,KAND;IAOH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIG,EAAAA,gBAAgB,GAAG;IACf;IACAhE,IAAAA,IAAI,CAAC2D,gBAAL,CAAsB,SAAtB,EAAmCC,KAAD,IAAW;IACzC;IACA;IACA,UAAIA,KAAK,CAACK,IAAN,IAAcL,KAAK,CAACK,IAAN,CAAWC,IAAX,KAAoB,YAAtC,EAAoD;IAChD;IACA,cAAM;IAAEC,UAAAA;IAAF,YAAcP,KAAK,CAACK,IAA1B;;IACA,QAA2C;IACvC1B,UAAAA,gBAAM,CAACI,KAAP,CAAc,8BAAd,EAA6CwB,OAAO,CAACC,WAArD;IACH;;IACD,cAAMC,eAAe,GAAGC,OAAO,CAACC,GAAR,CAAYJ,OAAO,CAACC,WAAR,CAAoBI,GAApB,CAAyBC,KAAD,IAAW;IACnE,cAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC3BA,YAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;IACH;;IACD,gBAAMzC,OAAO,GAAG,IAAI0C,OAAJ,CAAY,GAAGD,KAAf,CAAhB;IACA,iBAAO,KAAKX,aAAL,CAAmB;IAAE9B,YAAAA,OAAF;IAAW4B,YAAAA;IAAX,WAAnB,CAAP,CALmE;IAOnE;IACA;IACH,SATmC,CAAZ,CAAxB,CANgD;;IAgBhDA,QAAAA,KAAK,CAACe,SAAN,CAAgBN,eAAhB,EAhBgD;;IAkBhD,YAAIT,KAAK,CAACgB,KAAN,IAAehB,KAAK,CAACgB,KAAN,CAAY,CAAZ,CAAnB,EAAmC;IAC/B,eAAKP,eAAe,CAACQ,IAAhB,CAAqB,MAAMjB,KAAK,CAACgB,KAAN,CAAY,CAAZ,EAAeE,WAAf,CAA2B,IAA3B,CAA3B,CAAL;IACH;IACJ;IACJ,KAzBD;IA0BH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIhB,EAAAA,aAAa,CAAC;IAAE9B,IAAAA,OAAF;IAAW4B,IAAAA;IAAX,GAAD,EAAsB;IAC/B,IAA2C;IACvCrD,MAAAA,gBAAM,CAACsC,UAAP,CAAkBb,OAAlB,EAA2B0C,OAA3B,EAAoC;IAChCjE,QAAAA,UAAU,EAAE,iBADoB;IAEhCC,QAAAA,SAAS,EAAE,QAFqB;IAGhCC,QAAAA,QAAQ,EAAE,eAHsB;IAIhCC,QAAAA,SAAS,EAAE;IAJqB,OAApC;IAMH;;IACD,UAAMmB,GAAG,GAAG,IAAIgD,GAAJ,CAAQ/C,OAAO,CAACD,GAAhB,EAAqBmB,QAAQ,CAACF,IAA9B,CAAZ;;IACA,QAAI,CAACjB,GAAG,CAACiD,QAAJ,CAAaC,UAAb,CAAwB,MAAxB,CAAL,EAAsC;IAClC,MAA2C;IACvC1C,QAAAA,gBAAM,CAACI,KAAP,CAAc,2DAAd;IACH;;IACD;IACH;;IACD,UAAMuC,UAAU,GAAGnD,GAAG,CAACkB,MAAJ,KAAeC,QAAQ,CAACD,MAA3C;IACA,UAAM;IAAEkC,MAAAA,MAAF;IAAUC,MAAAA;IAAV,QAAoB,KAAKC,iBAAL,CAAuB;IAC7CzB,MAAAA,KAD6C;IAE7C5B,MAAAA,OAF6C;IAG7CkD,MAAAA,UAH6C;IAI7CnD,MAAAA;IAJ6C,KAAvB,CAA1B;IAMA,QAAIzB,OAAO,GAAG8E,KAAK,IAAIA,KAAK,CAAC9E,OAA7B;IACA,UAAMgF,aAAa,GAAG,EAAtB;;IACA,IAA2C;IACvC,UAAIhF,OAAJ,EAAa;IACTgF,QAAAA,aAAa,CAACC,IAAd,CAAmB,CAAE,uCAAF,EAA0CH,KAA1C,CAAnB;;IACA,YAAID,MAAJ,EAAY;IACRG,UAAAA,aAAa,CAACC,IAAd,CAAmB,CACd,sDADc,EAEfJ,MAFe,CAAnB;IAIH;IACJ;IACJ,KAnC8B;IAqC/B;;;IACA,UAAMjE,MAAM,GAAGc,OAAO,CAACd,MAAvB;;IACA,QAAI,CAACZ,OAAD,IAAY,KAAKkD,kBAAL,CAAwBgC,GAAxB,CAA4BtE,MAA5B,CAAhB,EAAqD;IACjD,MAA2C;IACvCoE,QAAAA,aAAa,CAACC,IAAd,CAAoB,2CAAD,GACd,mCAAkCrE,MAAO,GAD9C;IAEH;;IACDZ,MAAAA,OAAO,GAAG,KAAKkD,kBAAL,CAAwBiC,GAAxB,CAA4BvE,MAA5B,CAAV;IACH;;IACD,QAAI,CAACZ,OAAL,EAAc;IACV,MAA2C;IACvC;IACA;IACAiC,QAAAA,gBAAM,CAACI,KAAP,CAAc,uBAAsB+C,gCAAc,CAAC3D,GAAD,CAAM,EAAxD;IACH;;IACD;IACH;;IACD,IAA2C;IACvC;IACA;IACAQ,MAAAA,gBAAM,CAACoD,cAAP,CAAuB,4BAA2BD,gCAAc,CAAC3D,GAAD,CAAM,EAAtE;IACAuD,MAAAA,aAAa,CAACM,OAAd,CAAuBC,GAAD,IAAS;IAC3B,YAAIC,KAAK,CAACC,OAAN,CAAcF,GAAd,CAAJ,EAAwB;IACpBtD,UAAAA,gBAAM,CAACC,GAAP,CAAW,GAAGqD,GAAd;IACH,SAFD,MAGK;IACDtD,UAAAA,gBAAM,CAACC,GAAP,CAAWqD,GAAX;IACH;IACJ,OAPD;IAQAtD,MAAAA,gBAAM,CAACyD,QAAP;IACH,KAnE8B;IAqE/B;;;IACA,QAAInC,eAAJ;;IACA,QAAI;IACAA,MAAAA,eAAe,GAAGvD,OAAO,CAACQ,MAAR,CAAe;IAAEiB,QAAAA,GAAF;IAAOC,QAAAA,OAAP;IAAgB4B,QAAAA,KAAhB;IAAuBuB,QAAAA;IAAvB,OAAf,CAAlB;IACH,KAFD,CAGA,OAAOc,GAAP,EAAY;IACRpC,MAAAA,eAAe,GAAGS,OAAO,CAAC4B,MAAR,CAAeD,GAAf,CAAlB;IACH,KA5E8B;;;IA8E/B,UAAM5E,YAAY,GAAG+D,KAAK,IAAIA,KAAK,CAAC/D,YAApC;;IACA,QAAIwC,eAAe,YAAYS,OAA3B,KACC,KAAK6B,aAAL,IAAsB9E,YADvB,CAAJ,EAC0C;IACtCwC,MAAAA,eAAe,GAAGA,eAAe,CAACuC,KAAhB,CAAsB,MAAOH,GAAP,IAAe;IACnD;IACA,YAAI5E,YAAJ,EAAkB;IACd,UAA2C;IACvC;IACA;IACAkB,YAAAA,gBAAM,CAACoD,cAAP,CAAuB,mCAAD,GACjB,IAAGD,gCAAc,CAAC3D,GAAD,CAAM,0CAD5B;IAEAQ,YAAAA,gBAAM,CAAC8D,KAAP,CAAc,kBAAd,EAAiCjB,KAAjC;IACA7C,YAAAA,gBAAM,CAAC8D,KAAP,CAAaJ,GAAb;IACA1D,YAAAA,gBAAM,CAACyD,QAAP;IACH;;IACD,cAAI;IACA,mBAAO,MAAM3E,YAAY,CAACP,MAAb,CAAoB;IAAEiB,cAAAA,GAAF;IAAOC,cAAAA,OAAP;IAAgB4B,cAAAA,KAAhB;IAAuBuB,cAAAA;IAAvB,aAApB,CAAb;IACH,WAFD,CAGA,OAAOmB,QAAP,EAAiB;IACb,gBAAIA,QAAQ,YAAYC,KAAxB,EAA+B;IAC3BN,cAAAA,GAAG,GAAGK,QAAN;IACH;IACJ;IACJ;;IACD,YAAI,KAAKH,aAAT,EAAwB;IACpB,UAA2C;IACvC;IACA;IACA5D,YAAAA,gBAAM,CAACoD,cAAP,CAAuB,mCAAD,GACjB,IAAGD,gCAAc,CAAC3D,GAAD,CAAM,yCAD5B;IAEAQ,YAAAA,gBAAM,CAAC8D,KAAP,CAAc,kBAAd,EAAiCjB,KAAjC;IACA7C,YAAAA,gBAAM,CAAC8D,KAAP,CAAaJ,GAAb;IACA1D,YAAAA,gBAAM,CAACyD,QAAP;IACH;;IACD,iBAAO,KAAKG,aAAL,CAAmBrF,MAAnB,CAA0B;IAAEiB,YAAAA,GAAF;IAAOC,YAAAA,OAAP;IAAgB4B,YAAAA;IAAhB,WAA1B,CAAP;IACH;;IACD,cAAMqC,GAAN;IACH,OAlCiB,CAAlB;IAmCH;;IACD,WAAOpC,eAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIwB,EAAAA,iBAAiB,CAAC;IAAEtD,IAAAA,GAAF;IAAOmD,IAAAA,UAAP;IAAmBlD,IAAAA,OAAnB;IAA4B4B,IAAAA;IAA5B,GAAD,EAAuC;IACpD,UAAMH,MAAM,GAAG,KAAKH,OAAL,CAAamC,GAAb,CAAiBzD,OAAO,CAACd,MAAzB,KAAoC,EAAnD;;IACA,SAAK,MAAMkE,KAAX,IAAoB3B,MAApB,EAA4B;IACxB,UAAI0B,MAAJ,CADwB;IAGxB;;IACA,YAAMqB,WAAW,GAAGpB,KAAK,CAACnE,KAAN,CAAY;IAAEc,QAAAA,GAAF;IAAOmD,QAAAA,UAAP;IAAmBlD,QAAAA,OAAnB;IAA4B4B,QAAAA;IAA5B,OAAZ,CAApB;;IACA,UAAI4C,WAAJ,EAAiB;IACb,QAA2C;IACvC;IACA;IACA,cAAIA,WAAW,YAAYlC,OAA3B,EAAoC;IAChC/B,YAAAA,gBAAM,CAACkE,IAAP,CAAa,iBAAgBf,gCAAc,CAAC3D,GAAD,CAAM,aAArC,GACP,sDADO,GAEP,8DAFL,EAEoEqD,KAFpE;IAGH;IACJ,SATY;IAWb;;;IACAD,QAAAA,MAAM,GAAGqB,WAAT;;IACA,YAAIV,KAAK,CAACC,OAAN,CAAcZ,MAAd,KAAyBA,MAAM,CAACuB,MAAP,KAAkB,CAA/C,EAAkD;IAC9C;IACAvB,UAAAA,MAAM,GAAGwB,SAAT;IACH,SAHD,MAIK,IAAIH,WAAW,CAACxF,WAAZ,KAA4B4F,MAA5B;IACLA,QAAAA,MAAM,CAACC,IAAP,CAAYL,WAAZ,EAAyBE,MAAzB,KAAoC,CADnC,EACsC;IACvC;IACAvB,UAAAA,MAAM,GAAGwB,SAAT;IACH,SAJI,MAKA,IAAI,OAAOH,WAAP,KAAuB,SAA3B,EAAsC;IACvC;IACA;IACA;IACArB,UAAAA,MAAM,GAAGwB,SAAT;IACH,SA3BY;;;IA6Bb,eAAO;IAAEvB,UAAAA,KAAF;IAASD,UAAAA;IAAT,SAAP;IACH;IACJ,KAtCmD;;;IAwCpD,WAAO,EAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI2B,EAAAA,iBAAiB,CAACxG,OAAD,EAAUY,MAAM,GAAGf,aAAnB,EAAkC;IAC/C,SAAKqD,kBAAL,CAAwBuD,GAAxB,CAA4B7F,MAA5B,EAAoCb,gBAAgB,CAACC,OAAD,CAApD;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACIc,EAAAA,eAAe,CAACd,OAAD,EAAU;IACrB,SAAK6F,aAAL,GAAqB9F,gBAAgB,CAACC,OAAD,CAArC;IACH;IACD;IACJ;IACA;IACA;IACA;;;IACI0G,EAAAA,aAAa,CAAC5B,KAAD,EAAQ;IACjB,IAA2C;IACvC7E,MAAAA,gBAAM,CAACM,MAAP,CAAcuE,KAAd,EAAqB,QAArB,EAA+B;IAC3B3E,QAAAA,UAAU,EAAE,iBADe;IAE3BC,QAAAA,SAAS,EAAE,QAFgB;IAG3BC,QAAAA,QAAQ,EAAE,eAHiB;IAI3BC,QAAAA,SAAS,EAAE;IAJgB,OAA/B;IAMAL,MAAAA,gBAAM,CAACC,SAAP,CAAiB4E,KAAjB,EAAwB,OAAxB,EAAiC;IAC7B3E,QAAAA,UAAU,EAAE,iBADiB;IAE7BC,QAAAA,SAAS,EAAE,QAFkB;IAG7BC,QAAAA,QAAQ,EAAE,eAHmB;IAI7BC,QAAAA,SAAS,EAAE;IAJkB,OAAjC;IAMAL,MAAAA,gBAAM,CAACM,MAAP,CAAcuE,KAAK,CAAC9E,OAApB,EAA6B,QAA7B,EAAuC;IACnCG,QAAAA,UAAU,EAAE,iBADuB;IAEnCC,QAAAA,SAAS,EAAE,QAFwB;IAGnCC,QAAAA,QAAQ,EAAE,eAHyB;IAInCC,QAAAA,SAAS,EAAE;IAJwB,OAAvC;IAMAL,MAAAA,gBAAM,CAACC,SAAP,CAAiB4E,KAAK,CAAC9E,OAAvB,EAAgC,QAAhC,EAA0C;IACtCG,QAAAA,UAAU,EAAE,iBAD0B;IAEtCC,QAAAA,SAAS,EAAE,QAF2B;IAGtCC,QAAAA,QAAQ,EAAE,eAH4B;IAItCC,QAAAA,SAAS,EAAE;IAJ2B,OAA1C;IAMAL,MAAAA,gBAAM,CAACM,MAAP,CAAcuE,KAAK,CAAClE,MAApB,EAA4B,QAA5B,EAAsC;IAClCT,QAAAA,UAAU,EAAE,iBADsB;IAElCC,QAAAA,SAAS,EAAE,QAFuB;IAGlCC,QAAAA,QAAQ,EAAE,eAHwB;IAIlCC,QAAAA,SAAS,EAAE;IAJuB,OAAtC;IAMH;;IACD,QAAI,CAAC,KAAK0C,OAAL,CAAakC,GAAb,CAAiBJ,KAAK,CAAClE,MAAvB,CAAL,EAAqC;IACjC,WAAKoC,OAAL,CAAayD,GAAb,CAAiB3B,KAAK,CAAClE,MAAvB,EAA+B,EAA/B;IACH,KAnCgB;IAqCjB;;;IACA,SAAKoC,OAAL,CAAamC,GAAb,CAAiBL,KAAK,CAAClE,MAAvB,EAA+BqE,IAA/B,CAAoCH,KAApC;IACH;IACD;IACJ;IACA;IACA;IACA;;;IACI6B,EAAAA,eAAe,CAAC7B,KAAD,EAAQ;IACnB,QAAI,CAAC,KAAK9B,OAAL,CAAakC,GAAb,CAAiBJ,KAAK,CAAClE,MAAvB,CAAL,EAAqC;IACjC,YAAM,IAAIgG,4BAAJ,CAAiB,4CAAjB,EAA+D;IACjEhG,QAAAA,MAAM,EAAEkE,KAAK,CAAClE;IADmD,OAA/D,CAAN;IAGH;;IACD,UAAMiG,UAAU,GAAG,KAAK7D,OAAL,CAAamC,GAAb,CAAiBL,KAAK,CAAClE,MAAvB,EAA+BkG,OAA/B,CAAuChC,KAAvC,CAAnB;;IACA,QAAI+B,UAAU,GAAG,CAAC,CAAlB,EAAqB;IACjB,WAAK7D,OAAL,CAAamC,GAAb,CAAiBL,KAAK,CAAClE,MAAvB,EAA+BmG,MAA/B,CAAsCF,UAAtC,EAAkD,CAAlD;IACH,KAFD,MAGK;IACD,YAAM,IAAID,4BAAJ,CAAiB,uCAAjB,CAAN;IACH;IACJ;;IAvWQ;;IC/Bb;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAII,aAAJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,MAAMC,wBAAwB,GAAG,MAAM;IAC1C,MAAI,CAACD,aAAL,EAAoB;IAChBA,IAAAA,aAAa,GAAG,IAAIjE,MAAJ,EAAhB,CADgB;;IAGhBiE,IAAAA,aAAa,CAAC5D,gBAAd;IACA4D,IAAAA,aAAa,CAACtD,gBAAd;IACH;;IACD,SAAOsD,aAAP;IACH,CARM;;ICjBP;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASN,aAAT,CAAuBQ,OAAvB,EAAgClH,OAAhC,EAAyCY,MAAzC,EAAiD;IAC7C,MAAIkE,KAAJ;;IACA,MAAI,OAAOoC,OAAP,KAAmB,QAAvB,EAAiC;IAC7B,UAAMC,UAAU,GAAG,IAAI1C,GAAJ,CAAQyC,OAAR,EAAiBtE,QAAQ,CAACF,IAA1B,CAAnB;;IACA,IAA2C;IACvC,UAAI,EAAEwE,OAAO,CAACvC,UAAR,CAAmB,GAAnB,KAA2BuC,OAAO,CAACvC,UAAR,CAAmB,MAAnB,CAA7B,CAAJ,EAA8D;IAC1D,cAAM,IAAIiC,4BAAJ,CAAiB,gBAAjB,EAAmC;IACrCzG,UAAAA,UAAU,EAAE,iBADyB;IAErCE,UAAAA,QAAQ,EAAE,eAF2B;IAGrCC,UAAAA,SAAS,EAAE;IAH0B,SAAnC,CAAN;IAKH,OAPsC;IASvC;;;IACA,YAAM8G,YAAY,GAAGF,OAAO,CAACvC,UAAR,CAAmB,MAAnB,IACfwC,UAAU,CAACtF,QADI,GAEfqF,OAFN,CAVuC;;IAcvC,YAAMG,SAAS,GAAG,QAAlB;;IACA,UAAI,IAAIjG,MAAJ,CAAY,GAAEiG,SAAU,EAAxB,EAA2B5E,IAA3B,CAAgC2E,YAAhC,CAAJ,EAAmD;IAC/CnF,QAAAA,gBAAM,CAACI,KAAP,CAAc,8DAAD,GACR,cAAagF,SAAU,2CADf,GAER,8DAFL;IAGH;IACJ;;IACD,UAAMC,aAAa,GAAG,CAAC;IAAE7F,MAAAA;IAAF,KAAD,KAAa;IAC/B,MAA2C;IACvC,YAAIA,GAAG,CAACI,QAAJ,KAAiBsF,UAAU,CAACtF,QAA5B,IACAJ,GAAG,CAACkB,MAAJ,KAAewE,UAAU,CAACxE,MAD9B,EACsC;IAClCV,UAAAA,gBAAM,CAACI,KAAP,CAAc,GAAE6E,OAAQ,+CAAX,GACR,GAAEzF,GAAG,CAACU,QAAJ,EAAe,sDADT,GAER,+BAFL;IAGH;IACJ;;IACD,aAAOV,GAAG,CAACiB,IAAJ,KAAayE,UAAU,CAACzE,IAA/B;IACH,KAVD,CAvB6B;;;IAmC7BoC,IAAAA,KAAK,GAAG,IAAIrE,KAAJ,CAAU6G,aAAV,EAAyBtH,OAAzB,EAAkCY,MAAlC,CAAR;IACH,GApCD,MAqCK,IAAIsG,OAAO,YAAY9F,MAAvB,EAA+B;IAChC;IACA0D,IAAAA,KAAK,GAAG,IAAIxC,WAAJ,CAAgB4E,OAAhB,EAAyBlH,OAAzB,EAAkCY,MAAlC,CAAR;IACH,GAHI,MAIA,IAAI,OAAOsG,OAAP,KAAmB,UAAvB,EAAmC;IACpC;IACApC,IAAAA,KAAK,GAAG,IAAIrE,KAAJ,CAAUyG,OAAV,EAAmBlH,OAAnB,EAA4BY,MAA5B,CAAR;IACH,GAHI,MAIA,IAAIsG,OAAO,YAAYzG,KAAvB,EAA8B;IAC/BqE,IAAAA,KAAK,GAAGoC,OAAR;IACH,GAFI,MAGA;IACD,UAAM,IAAIN,4BAAJ,CAAiB,wBAAjB,EAA2C;IAC7CzG,MAAAA,UAAU,EAAE,iBADiC;IAE7CE,MAAAA,QAAQ,EAAE,eAFmC;IAG7CC,MAAAA,SAAS,EAAE;IAHkC,KAA3C,CAAN;IAKH;;IACD,QAAM0G,aAAa,GAAGC,wBAAwB,EAA9C;IACAD,EAAAA,aAAa,CAACN,aAAd,CAA4B5B,KAA5B;IACA,SAAOA,KAAP;IACH;;IC3FD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAAShE,eAAT,CAAyBd,OAAzB,EAAkC;IAC9B,QAAMgH,aAAa,GAAGC,wBAAwB,EAA9C;IACAD,EAAAA,aAAa,CAAClG,eAAd,CAA8Bd,OAA9B;IACH;;ICrBD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASwG,iBAAT,CAA2BxG,OAA3B,EAAoC;IAChC,QAAMgH,aAAa,GAAGC,wBAAwB,EAA9C;IACAD,EAAAA,aAAa,CAACR,iBAAd,CAAgCxG,OAAhC;IACH;;;;;;;;;;;;;;;;"}