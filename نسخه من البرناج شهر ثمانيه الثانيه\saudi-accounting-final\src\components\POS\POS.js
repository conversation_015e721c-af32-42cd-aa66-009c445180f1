import React, { useState, useEffect } from 'react';
import './POS.css';
import database from '../../utils/database';

const POS = ({ user, onBack }) => {
  const [cart, setCart] = useState([]);
  const [products, setProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [customer, setCustomer] = useState({
    name: '',
    phone: '',
    taxNumber: ''
  });
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('cash'); // 'cash', 'card', 'transfer'
  const [isProcessing, setIsProcessing] = useState(false);

  // تحميل المنتجات من قاعدة البيانات
  useEffect(() => {
    try {
      const dbProducts = database.getProducts();
      setProducts(dbProducts);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
      // في حالة الخطأ، استخدم منتجات افتراضية
      const fallbackProducts = [
        { id: 1, name: 'لابتوب HP', price: 2500, stock: 10, barcode: '*********', category: 'إلكترونيات' },
        { id: 2, name: 'ماوس لاسلكي', price: 85, stock: 25, barcode: '*********', category: 'إلكترونيات' }
      ];
      setProducts(fallbackProducts);
    }
  }, []);

  // إضافة منتج للسلة
  const addToCart = (product) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      if (existingItem.quantity < product.stock) {
        setCart(cart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        ));
      } else {
        alert('⚠️ لا توجد كمية كافية في المخزون');
      }
    } else {
      setCart([...cart, { ...product, quantity: 1 }]);
    }
  };

  // تحديث كمية المنتج
  const updateQuantity = (id, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(id);
      return;
    }
    
    const product = products.find(p => p.id === id);
    if (newQuantity > product.stock) {
      alert('⚠️ لا توجد كمية كافية في المخزون');
      return;
    }

    setCart(cart.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    ));
  };

  // حذف منتج من السلة
  const removeFromCart = (id) => {
    setCart(cart.filter(item => item.id !== id));
  };

  // حساب الإجماليات
  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const vatRate = 0.15; // 15% ضريبة القيمة المضافة
  const vatAmount = subtotal * vatRate;
  const total = subtotal + vatAmount;

  // تصفية المنتجات حسب البحث
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode.includes(searchTerm)
  );

  // معالجة الدفع
  const processPayment = async () => {
    if (cart.length === 0) {
      alert('⚠️ السلة فارغة');
      return;
    }

    setIsProcessing(true);

    try {
      // محاكاة معالجة الدفع
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إنشاء بيانات الفاتورة
      const invoiceData = {
        customer: customer.name || 'عميل نقدي',
        customerPhone: customer.phone || '',
        customerTaxNumber: customer.taxNumber || '',
        items: cart,
        subtotal: subtotal,
        vat: vatAmount,
        total: total,
        cashier: user.name,
        paymentMethod: paymentMethod === 'cash' ? 'نقدي' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'تحويل بنكي'
      };

      // حفظ الفاتورة في قاعدة البيانات
      const savedInvoice = database.saveInvoice(invoiceData);

      // إضافة التاريخ المنسق للطباعة
      const invoiceForPrint = {
        ...savedInvoice,
        dateArabic: new Date(savedInvoice.createdAt).toLocaleString('ar-SA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      };

      // طباعة الفاتورة
      printInvoice(invoiceForPrint);

      // تحديث المنتجات لعكس المخزون الجديد
      const updatedProducts = database.getProducts();
      setProducts(updatedProducts);

      // تفريغ السلة
      setCart([]);
      setCustomer({ name: '', phone: '', taxNumber: '' });
      setShowCustomerForm(false);
      setIsProcessing(false);

      alert(`✅ تم إتمام البيع بنجاح!\nرقم الفاتورة: ${savedInvoice.id}`);
    } catch (error) {
      console.error('خطأ في معالجة البيع:', error);
      setIsProcessing(false);
      alert('❌ حدث خطأ في معالجة البيع. يرجى المحاولة مرة أخرى.');
    }
  };

  // إنشاء QR Code للفاتورة (حسب المعايير السعودية)
  const generateQRCode = (invoice) => {
    // الرقم الضريبي السعودي (15 رقم)
    const vatNumber = "***************";

    // تنسيق التاريخ حسب المعايير السعودية (ISO 8601)
    const invoiceDate = new Date();
    const formattedDate = invoiceDate.toISOString();

    // البيانات المطلوبة للـ QR Code السعودي (بالإنجليزية لتجنب مشاكل التشفير)
    const qrData = {
      // 1. اسم البائع
      sellerName: "Saudi Accounting System Co. Ltd",
      // 2. الرقم الضريبي للبائع (15 رقم)
      vatRegistrationNumber: vatNumber,
      // 3. الطابع الزمني للفاتورة (ISO 8601)
      invoiceTimestamp: formattedDate,
      // 4. إجمالي الفاتورة شامل الضريبة
      invoiceTotal: parseFloat(invoice.total.toFixed(2)),
      // 5. إجمالي ضريبة القيمة المضافة
      vatTotal: parseFloat(invoice.vat.toFixed(2))
    };

    // تحويل البيانات إلى تنسيق TLV (Tag-Length-Value) حسب المعايير السعودية
    const createTLV = (tag, value) => {
      const valueStr = value.toString();
      const length = valueStr.length;
      return String.fromCharCode(tag) + String.fromCharCode(length) + valueStr;
    };

    // إنشاء QR Code حسب المعايير السعودية (بالإنجليزية)
    const qrString =
      createTLV(1, qrData.sellerName) +
      createTLV(2, qrData.vatRegistrationNumber) +
      createTLV(3, qrData.invoiceTimestamp) +
      createTLV(4, qrData.invoiceTotal) +
      createTLV(5, qrData.vatTotal);

    // تحويل إلى Base64 بطريقة آمنة
    let base64QR;
    try {
      // تحويل النص إلى UTF-8 bytes ثم إلى Base64
      const utf8Bytes = new TextEncoder().encode(qrString);
      const binaryString = Array.from(utf8Bytes, byte => String.fromCharCode(byte)).join('');
      base64QR = btoa(binaryString);
    } catch (error) {
      // في حالة فشل التشفير، استخدم نص بسيط
      const simpleData = `Company:${qrData.sellerName}|VAT:${qrData.vatRegistrationNumber}|Date:${qrData.invoiceTimestamp}|Total:${qrData.invoiceTotal}|VATAmount:${qrData.vatTotal}`;
      base64QR = btoa(simpleData);
    }

    // إنشاء QR Code باستخدام خدمة مجانية
    return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(base64QR)}`;
  };

  // طباعة الفاتورة المحسنة
  const printInvoice = (invoice) => {
    const qrCodeUrl = generateQRCode(invoice);
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html dir="rtl">
        <head>
          <title>فاتورة ضريبية رقم ${invoice.id}</title>
          <style>
            body {
              font-family: 'Arial', sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #1890ff;
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 {
              color: #1890ff;
              margin: 0;
              font-size: 1.8em;
            }
            .header h2 {
              color: #666;
              margin: 5px 0;
              font-size: 1.2em;
            }
            .company-info {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 20px;
              border-right: 4px solid #1890ff;
            }
            .invoice-details {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin: 20px 0;
            }
            .invoice-details div {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
            }
            .invoice-details p {
              margin: 8px 0;
              font-size: 1em;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            .items-table th {
              background: linear-gradient(135deg, #1890ff, #40a9ff);
              color: white;
              padding: 12px 8px;
              text-align: right;
              font-weight: 600;
            }
            .items-table td {
              border: 1px solid #e8e8e8;
              padding: 10px 8px;
              text-align: right;
            }
            .items-table tbody tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .totals {
              background: #f8f9fa;
              padding: 20px;
              border-radius: 8px;
              margin-top: 20px;
              border-right: 4px solid #52c41a;
            }
            .total-line {
              display: flex;
              justify-content: space-between;
              margin: 8px 0;
              font-size: 1.1em;
            }
            .vat-line {
              color: #fa8c16;
              font-weight: 600;
            }
            .final-total {
              font-weight: bold;
              font-size: 1.3em;
              border-top: 2px solid #1890ff;
              padding-top: 10px;
              margin-top: 10px;
              color: #1890ff;
            }
            .footer {
              margin-top: 30px;
              display: grid;
              grid-template-columns: 1fr auto;
              gap: 20px;
              align-items: center;
            }
            .footer-text {
              text-align: right;
              color: #666;
            }
            .qr-section {
              text-align: center;
              border: 2px solid #e8e8e8;
              padding: 15px;
              border-radius: 8px;
              background: white;
            }
            .qr-section h4 {
              margin: 0 0 10px 0;
              color: #1890ff;
              font-size: 0.9em;
            }
            .tax-info {
              background: #e6f7ff;
              border: 1px solid #91d5ff;
              padding: 15px;
              border-radius: 8px;
              margin: 20px 0;
            }
            .tax-info h3 {
              color: #1890ff;
              margin: 0 0 10px 0;
              font-size: 1.1em;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>🧮 نظام المحاسبة السعودي</h1>
            <h2>فاتورة ضريبية مبسطة</h2>
          </div>

          <div class="company-info">
            <h3 style="margin: 0 0 10px 0; color: #1890ff;">بيانات الشركة</h3>
            <p><strong>اسم الشركة:</strong> شركة نظام المحاسبة السعودي المحدودة</p>
            <p><strong>الرقم الضريبي:</strong> ***************</p>
            <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>
            <p><strong>الهاتف:</strong> +966 11 123 4567</p>
            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
          </div>

          <div class="invoice-details">
            <div>
              <h4 style="margin: 0 0 10px 0; color: #1890ff;">تفاصيل الفاتورة</h4>
              <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>
              <p><strong>تاريخ الإصدار:</strong> ${invoice.dateArabic}</p>
              <p><strong>نوع الفاتورة:</strong> فاتورة ضريبية مبسطة</p>
              <p><strong>حالة الفاتورة:</strong> مدفوعة</p>
            </div>
            <div>
              <h4 style="margin: 0 0 10px 0; color: #1890ff;">بيانات العملية</h4>
              <p><strong>العميل:</strong> ${invoice.customer}</p>
              <p><strong>أمين الصندوق:</strong> ${invoice.cashier}</p>
              <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod}</p>
              <p><strong>رقم المرجع:</strong> ${invoice.id}-PAY</p>
            </div>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th style="width: 40%">وصف السلعة/الخدمة</th>
                <th style="width: 15%">السعر الوحدة</th>
                <th style="width: 10%">الكمية</th>
                <th style="width: 15%">الإجمالي قبل الضريبة</th>
                <th style="width: 10%">معدل الضريبة</th>
                <th style="width: 10%">مبلغ الضريبة</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map(item => {
                const itemSubtotal = item.price * item.quantity;
                const itemVat = itemSubtotal * 0.15;
                return `
                  <tr>
                    <td>${item.name}</td>
                    <td>${item.price.toFixed(2)} ريال</td>
                    <td>${item.quantity}</td>
                    <td>${itemSubtotal.toFixed(2)} ريال</td>
                    <td>15%</td>
                    <td>${itemVat.toFixed(2)} ريال</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <div class="tax-info">
            <h3>📊 ملخص الضرائب</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
              <div>
                <p><strong>إجمالي السلع والخدمات الخاضعة للضريبة:</strong></p>
                <p style="color: #1890ff; font-size: 1.1em;">${invoice.subtotal.toFixed(2)} ريال</p>
              </div>
              <div>
                <p><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></p>
                <p style="color: #fa8c16; font-size: 1.1em;">${invoice.vat.toFixed(2)} ريال</p>
              </div>
            </div>
          </div>

          <div class="totals">
            <div class="total-line">
              <span>المجموع الفرعي (قبل الضريبة):</span>
              <span>${invoice.subtotal.toFixed(2)} ريال</span>
            </div>
            <div class="total-line vat-line">
              <span>ضريبة القيمة المضافة (15%):</span>
              <span>${invoice.vat.toFixed(2)} ريال</span>
            </div>
            <div class="total-line final-total">
              <span>الإجمالي النهائي (شامل الضريبة):</span>
              <span>${invoice.total.toFixed(2)} ريال</span>
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">
              <h4 style="color: #1890ff; margin: 0 0 10px 0;">شكراً لتعاملكم معنا</h4>
              <p style="margin: 5px 0;">هذه فاتورة ضريبية صادرة إلكترونياً</p>
              <p style="margin: 5px 0;">تم إنشاؤها بواسطة نظام المحاسبة السعودي المعتمد</p>
              <p style="margin: 5px 0; font-size: 0.9em;">للاستفسارات: <EMAIL></p>
            </div>
            <div class="qr-section">
              <h4>🔍 رمز الاستجابة السريعة</h4>
              <img src="${qrCodeUrl}" alt="QR Code" style="max-width: 120px; height: auto;">
              <p style="font-size: 0.8em; margin: 5px 0 0 0; color: #666;">امسح للتحقق من الفاتورة</p>
            </div>
          </div>

          <div style="margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 0.85em; color: #666;">
            <p style="margin: 0;">هذا المستند تم إنشاؤه إلكترونياً ولا يتطلب توقيع أو ختم</p>
            <p style="margin: 5px 0 0 0;">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="pos-container">
      {/* شريط التنقل */}
      <header className="pos-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ↩️ رجوع للوحة التحكم
          </button>
          <h1>🛒 نقطة البيع</h1>
          <div className="user-info">
            <span>{user.name} - {user.role}</span>
          </div>
        </div>
      </header>

      <div className="pos-main">
        {/* قسم المنتجات */}
        <div className="products-section">
          <div className="search-bar">
            <input
              type="text"
              placeholder="🔍 البحث عن منتج أو باركود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="products-grid">
            {filteredProducts.map(product => (
              <div key={product.id} className="product-card">
                <div className="product-info">
                  <h3>{product.name}</h3>
                  <p className="product-category">{product.category}</p>
                  <p className="product-price">{product.price} ريال</p>
                  <p className="product-stock">المخزون: {product.stock}</p>
                </div>
                <button
                  onClick={() => addToCart(product)}
                  className="add-to-cart-btn"
                  disabled={product.stock === 0}
                >
                  {product.stock === 0 ? 'نفد المخزون' : 'إضافة للسلة'}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* قسم السلة والدفع */}
        <div className="cart-section">
          <div className="cart-header">
            <h2>🛒 السلة</h2>
            {cart.length > 0 && (
              <button
                onClick={() => setCart([])}
                className="clear-cart-btn"
              >
                🗑️ تفريغ السلة
              </button>
            )}
          </div>

          <div className="cart-items">
            {cart.length === 0 ? (
              <p className="empty-cart">السلة فارغة</p>
            ) : (
              cart.map(item => (
                <div key={item.id} className="cart-item">
                  <div className="item-info">
                    <h4>{item.name}</h4>
                    <p>{item.price} ريال</p>
                  </div>
                  <div className="quantity-controls">
                    <button
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      className="qty-btn"
                    >
                      -
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      className="qty-btn"
                    >
                      +
                    </button>
                  </div>
                  <div className="item-total">
                    {(item.price * item.quantity).toFixed(2)} ريال
                  </div>
                  <button
                    onClick={() => removeFromCart(item.id)}
                    className="remove-btn"
                  >
                    ❌
                  </button>
                </div>
              ))
            )}
          </div>

          {cart.length > 0 && (
            <>
              <div className="cart-totals">
                <div className="total-line">
                  <span>المجموع الفرعي:</span>
                  <span>{subtotal.toFixed(2)} ريال</span>
                </div>
                <div className="total-line">
                  <span>ضريبة القيمة المضافة (15%):</span>
                  <span>{vatAmount.toFixed(2)} ريال</span>
                </div>
                <div className="total-line final-total">
                  <span>الإجمالي:</span>
                  <span>{total.toFixed(2)} ريال</span>
                </div>
              </div>

              <div className="payment-section">
                <h4 className="section-subtitle">💳 طريقة الدفع</h4>
                <div className="payment-methods">
                  <label className={`payment-option ${paymentMethod === 'cash' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cash"
                      checked={paymentMethod === 'cash'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <span className="payment-icon">💵</span>
                    <span>نقدي</span>
                  </label>
                  <label className={`payment-option ${paymentMethod === 'card' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <span className="payment-icon">💳</span>
                    <span>بطاقة ائتمان</span>
                  </label>
                  <label className={`payment-option ${paymentMethod === 'transfer' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="transfer"
                      checked={paymentMethod === 'transfer'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <span className="payment-icon">🏦</span>
                    <span>تحويل بنكي</span>
                  </label>
                </div>
              </div>

              <div className="customer-section">
                <button
                  onClick={() => setShowCustomerForm(!showCustomerForm)}
                  className="customer-btn"
                >
                  👤 {showCustomerForm ? 'إخفاء' : 'إضافة'} بيانات العميل
                </button>

                {showCustomerForm && (
                  <div className="customer-form">
                    <input
                      type="text"
                      placeholder="اسم العميل"
                      value={customer.name}
                      onChange={(e) => setCustomer({...customer, name: e.target.value})}
                      className="customer-input"
                    />
                    <input
                      type="text"
                      placeholder="رقم الهاتف"
                      value={customer.phone}
                      onChange={(e) => setCustomer({...customer, phone: e.target.value})}
                      className="customer-input"
                    />
                    <input
                      type="text"
                      placeholder="الرقم الضريبي (اختياري)"
                      value={customer.taxNumber}
                      onChange={(e) => setCustomer({...customer, taxNumber: e.target.value})}
                      className="customer-input"
                    />
                  </div>
                )}
              </div>

              <button
                onClick={processPayment}
                disabled={isProcessing}
                className={`checkout-btn ${isProcessing ? 'processing' : ''}`}
              >
                {isProcessing ? (
                  <>
                    <span className="spinner"></span>
                    جاري المعالجة...
                  </>
                ) : (
                  '💳 إتمام البيع وطباعة الفاتورة'
                )}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default POS;
