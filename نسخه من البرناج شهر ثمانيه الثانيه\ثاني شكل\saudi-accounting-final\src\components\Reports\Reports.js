import React, { useState, useEffect } from 'react';
import './Reports.css';
import database from '../../utils/database';

const Reports = ({ user, onBack }) => {
  const [activeTab, setActiveTab] = useState('daily');
  const [todayStats, setTodayStats] = useState({});
  const [monthStats, setMonthStats] = useState({});
  const [recentInvoices, setRecentInvoices] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    loadReportsData();
  }, [selectedDate]);

  const loadReportsData = () => {
    try {
      // إحصائيات اليوم
      const todayData = database.getTodayStats();
      setTodayStats(todayData);

      // إحصائيات الشهر
      const monthData = database.getMonthStats();
      setMonthStats(monthData);

      // آخر الفواتير
      const allInvoices = database.getInvoices();
      const recent = allInvoices.slice(-10).reverse();
      setRecentInvoices(recent);

      // المنتجات منخفضة المخزون
      const lowStock = database.getLowStockProducts(5);
      setLowStockProducts(lowStock);

      // أفضل المنتجات مبيعاً
      const topSelling = database.getTopSellingProducts(5);
      setTopProducts(topSelling);
    } catch (error) {
      console.error('خطأ في تحميل التقارير:', error);
    }
  };

  // تصدير التقارير
  const exportReports = () => {
    try {
      const data = database.exportData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `saudi-accounting-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      alert('✅ تم تصدير البيانات بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('❌ فشل في تصدير البيانات');
    }
  };

  // طباعة التقرير
  const printReport = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html dir="rtl">
        <head>
          <title>تقرير المبيعات - ${new Date().toLocaleDateString('ar-SA')}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
            .stat-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f5f5f5; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>🧮 نظام المحاسبة السعودي</h1>
            <h2>تقرير المبيعات اليومي</h2>
            <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <h3>إحصائيات اليوم</h3>
              <p>إجمالي المبيعات: ${todayStats.totalSales?.toFixed(2) || 0} ريال</p>
              <p>إجمالي الضرائب: ${todayStats.totalVAT?.toFixed(2) || 0} ريال</p>
              <p>عدد الفواتير: ${todayStats.totalInvoices || 0}</p>
            </div>
            <div class="stat-card">
              <h3>إحصائيات الشهر</h3>
              <p>إجمالي المبيعات: ${monthStats.totalSales?.toFixed(2) || 0} ريال</p>
              <p>إجمالي الضرائب: ${monthStats.totalVAT?.toFixed(2) || 0} ريال</p>
              <p>عدد الفواتير: ${monthStats.totalInvoices || 0}</p>
            </div>
          </div>

          <h3>آخر الفواتير</h3>
          <table>
            <thead>
              <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>الإجمالي</th>
                <th>التاريخ</th>
              </tr>
            </thead>
            <tbody>
              ${recentInvoices.map(invoice => `
                <tr>
                  <td>${invoice.id}</td>
                  <td>${invoice.customer}</td>
                  <td>${invoice.total.toFixed(2)} ريال</td>
                  <td>${new Date(invoice.createdAt).toLocaleDateString('ar-SA')}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="reports-container">
      <div className="reports-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ← العودة للوحة التحكم
          </button>
          <h1>📊 التقارير والإحصائيات</h1>
          <div className="header-actions">
            <button onClick={printReport} className="action-btn print-btn">
              🖨️ طباعة التقرير
            </button>
            <button onClick={exportReports} className="action-btn export-btn">
              📤 تصدير البيانات
            </button>
          </div>
        </div>
      </div>

      <div className="reports-tabs">
        <button 
          className={`tab-btn ${activeTab === 'daily' ? 'active' : ''}`}
          onClick={() => setActiveTab('daily')}
        >
          📅 التقرير اليومي
        </button>
        <button 
          className={`tab-btn ${activeTab === 'monthly' ? 'active' : ''}`}
          onClick={() => setActiveTab('monthly')}
        >
          📊 التقرير الشهري
        </button>
        <button 
          className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}
          onClick={() => setActiveTab('products')}
        >
          📦 تقرير المنتجات
        </button>
        <button 
          className={`tab-btn ${activeTab === 'invoices' ? 'active' : ''}`}
          onClick={() => setActiveTab('invoices')}
        >
          🧾 سجل الفواتير
        </button>
      </div>

      <div className="reports-content">
        {activeTab === 'daily' && (
          <div className="daily-report">
            <div className="stats-grid">
              <div className="stat-card sales">
                <div className="stat-icon">💰</div>
                <div className="stat-info">
                  <h3>إجمالي المبيعات اليوم</h3>
                  <p className="stat-value">{todayStats.totalSales?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
              
              <div className="stat-card vat">
                <div className="stat-icon">🧾</div>
                <div className="stat-info">
                  <h3>إجمالي الضرائب</h3>
                  <p className="stat-value">{todayStats.totalVAT?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
              
              <div className="stat-card invoices">
                <div className="stat-icon">📋</div>
                <div className="stat-info">
                  <h3>عدد الفواتير</h3>
                  <p className="stat-value">{todayStats.totalInvoices || 0}</p>
                </div>
              </div>
              
              <div className="stat-card average">
                <div className="stat-icon">📈</div>
                <div className="stat-info">
                  <h3>متوسط الفاتورة</h3>
                  <p className="stat-value">{todayStats.averageInvoice?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'monthly' && (
          <div className="monthly-report">
            <div className="stats-grid">
              <div className="stat-card sales">
                <div className="stat-icon">💰</div>
                <div className="stat-info">
                  <h3>إجمالي المبيعات الشهر</h3>
                  <p className="stat-value">{monthStats.totalSales?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
              
              <div className="stat-card vat">
                <div className="stat-icon">🧾</div>
                <div className="stat-info">
                  <h3>إجمالي الضرائب</h3>
                  <p className="stat-value">{monthStats.totalVAT?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
              
              <div className="stat-card invoices">
                <div className="stat-icon">📋</div>
                <div className="stat-info">
                  <h3>عدد الفواتير</h3>
                  <p className="stat-value">{monthStats.totalInvoices || 0}</p>
                </div>
              </div>
              
              <div className="stat-card average">
                <div className="stat-icon">📈</div>
                <div className="stat-info">
                  <h3>متوسط الفاتورة</h3>
                  <p className="stat-value">{monthStats.averageInvoice?.toFixed(2) || 0} ريال</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'products' && (
          <div className="products-report">
            <div className="report-section">
              <h3>🔥 أفضل المنتجات مبيعاً</h3>
              <div className="products-list">
                {topProducts.length > 0 ? topProducts.map((product, index) => (
                  <div key={product.id} className="product-item">
                    <span className="rank">#{index + 1}</span>
                    <span className="name">{product.name}</span>
                    <span className="quantity">الكمية: {product.quantity}</span>
                    <span className="revenue">{product.revenue.toFixed(2)} ريال</span>
                  </div>
                )) : (
                  <p className="no-data">لا توجد بيانات مبيعات</p>
                )}
              </div>
            </div>

            <div className="report-section">
              <h3>⚠️ منتجات منخفضة المخزون</h3>
              <div className="products-list">
                {lowStockProducts.length > 0 ? lowStockProducts.map(product => (
                  <div key={product.id} className="product-item low-stock">
                    <span className="name">{product.name}</span>
                    <span className="stock">المخزون: {product.stock}</span>
                    <span className="price">{product.price} ريال</span>
                  </div>
                )) : (
                  <p className="no-data">جميع المنتجات متوفرة بكميات كافية</p>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'invoices' && (
          <div className="invoices-report">
            <div className="invoices-header">
              <h3>📋 سجل الفواتير</h3>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="date-filter"
              />
            </div>
            
            <div className="invoices-list">
              {recentInvoices.length > 0 ? recentInvoices.map(invoice => (
                <div key={invoice.id} className="invoice-item">
                  <div className="invoice-header">
                    <span className="invoice-id">#{invoice.id}</span>
                    <span className="invoice-date">
                      {new Date(invoice.createdAt).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  <div className="invoice-details">
                    <span className="customer">العميل: {invoice.customer}</span>
                    <span className="total">الإجمالي: {invoice.total.toFixed(2)} ريال</span>
                    <span className="payment">الدفع: {invoice.paymentMethod}</span>
                  </div>
                </div>
              )) : (
                <p className="no-data">لا توجد فواتير</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Reports;
