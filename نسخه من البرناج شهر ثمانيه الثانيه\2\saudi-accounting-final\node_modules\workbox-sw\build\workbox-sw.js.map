{"version": 3, "file": "workbox-sw.js", "sources": ["../_version.mjs", "../controllers/WorkboxSW.mjs", "../index.mjs"], "sourcesContent": ["try{self['workbox:sw:6.6.0']&&_()}catch(e){}// eslint-disable-line", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n\nimport '../_version.mjs';\n\nconst CDN_PATH = `WORKBOX_CDN_ROOT_URL`;\n\nconst MODULE_KEY_TO_NAME_MAPPING = {\n  /**\n   * @name backgroundSync\n   * @memberof workbox\n   * @see module:workbox-background-sync\n   */\n  backgroundSync: 'background-sync',\n  /**\n   * @name broadcastUpdate\n   * @memberof workbox\n   * @see module:workbox-broadcast-update\n   */\n  broadcastUpdate: 'broadcast-update',\n  /**\n   * @name cacheableResponse\n   * @memberof workbox\n   * @see module:workbox-cacheable-response\n   */\n  cacheableResponse: 'cacheable-response',\n  /**\n   * @name core\n   * @memberof workbox\n   * @see module:workbox-core\n   */\n  core: 'core',\n  /**\n   * @name expiration\n   * @memberof workbox\n   * @see module:workbox-expiration\n   */\n  expiration: 'expiration',\n  /**\n   * @name googleAnalytics\n   * @memberof workbox\n   * @see module:workbox-google-analytics\n   */\n  googleAnalytics: 'offline-ga',\n  /**\n   * @name navigationPreload\n   * @memberof workbox\n   * @see module:workbox-navigation-preload\n   */\n  navigationPreload: 'navigation-preload',\n  /**\n   * @name precaching\n   * @memberof workbox\n   * @see module:workbox-precaching\n   */\n  precaching: 'precaching',\n  /**\n   * @name rangeRequests\n   * @memberof workbox\n   * @see module:workbox-range-requests\n   */\n  rangeRequests: 'range-requests',\n  /**\n   * @name routing\n   * @memberof workbox\n   * @see module:workbox-routing\n   */\n  routing: 'routing',\n  /**\n   * @name strategies\n   * @memberof workbox\n   * @see module:workbox-strategies\n   */\n  strategies: 'strategies',\n  /**\n   * @name streams\n   * @memberof workbox\n   * @see module:workbox-streams\n   */\n  streams: 'streams',\n  /**\n   * @name recipes\n   * @memberof workbox\n   * @see module:workbox-recipes\n   */\n  recipes: 'recipes',\n};\n\n/**\n * This class can be used to make it easy to use the various parts of\n * Workbox.\n *\n * @private\n */\nexport class WorkboxSW {\n  /**\n   * Creates a proxy that automatically loads workbox namespaces on demand.\n   *\n   * @private\n   */\n  constructor() {\n    this.v = {};\n    this._options = {\n      debug: self.location.hostname === 'localhost',\n      modulePathPrefix: null,\n      modulePathCb: null,\n    };\n\n    this._env = this._options.debug ? 'dev' : 'prod';\n    this._modulesLoaded = false;\n\n    return new Proxy(this, {\n      get(target, key) {\n        if (target[key]) {\n          return target[key];\n        }\n\n        const moduleName = MODULE_KEY_TO_NAME_MAPPING[key];\n        if (moduleName) {\n          target.loadModule(`workbox-${moduleName}`);\n        }\n\n        return target[key];\n      },\n    });\n  }\n\n  /**\n   * Updates the configuration options. You can specify whether to treat as a\n   * debug build and whether to use a CDN or a specific path when importing\n   * other workbox-modules\n   *\n   * @param {Object} [options]\n   * @param {boolean} [options.debug] If true, `dev` builds are using, otherwise\n   * `prod` builds are used. By default, `prod` is used unless on localhost.\n   * @param {Function} [options.modulePathPrefix] To avoid using the CDN with\n   * `workbox-sw` set the path prefix of where modules should be loaded from.\n   * For example `modulePathPrefix: '/third_party/workbox/v3.0.0/'`.\n   * @param {workbox~ModulePathCallback} [options.modulePathCb] If defined,\n   * this callback will be responsible for determining the path of each\n   * workbox module.\n   *\n   * @alias workbox.setConfig\n   */\n  setConfig(options = {}) {\n    if (!this._modulesLoaded) {\n      Object.assign(this._options, options);\n      this._env = this._options.debug ? 'dev' : 'prod';\n    } else {\n      throw new Error('Config must be set before accessing workbox.* modules');\n    }\n  }\n\n  /**\n   * Load a Workbox module by passing in the appropriate module name.\n   *\n   * This is not generally needed unless you know there are modules that are\n   * dynamically used and you want to safe guard use of the module while the\n   * user may be offline.\n   *\n   * @param {string} moduleName\n   *\n   * @alias workbox.loadModule\n   */\n  loadModule(moduleName) {\n    const modulePath = this._getImportPath(moduleName);\n    try {\n      importScripts(modulePath);\n      this._modulesLoaded = true;\n    } catch (err) {\n      // TODO Add context of this error if using the CDN vs the local file.\n\n      // We can't rely on workbox-core being loaded so using console\n      // eslint-disable-next-line\n      console.error(\n          `Unable to import module '${moduleName}' from '${modulePath}'.`);\n      throw err;\n    }\n  }\n\n  /**\n   * This method will get the path / CDN URL to be used for importScript calls.\n   *\n   * @param {string} moduleName\n   * @return {string} URL to the desired module.\n   *\n   * @private\n   */\n  _getImportPath(moduleName) {\n    if (this._options.modulePathCb) {\n      return this._options.modulePathCb(moduleName, this._options.debug);\n    }\n\n    // TODO: This needs to be dynamic some how.\n    let pathParts = [CDN_PATH];\n\n    const fileName = `${moduleName}.${this._env}.js`;\n\n    const pathPrefix = this._options.modulePathPrefix;\n    if (pathPrefix) {\n      // Split to avoid issues with developers ending / not ending with slash\n      pathParts = pathPrefix.split('/');\n\n      // We don't need a slash at the end as we will be adding\n      // a filename regardless\n      if (pathParts[pathParts.length - 1] === '') {\n        pathParts.splice(pathParts.length - 1, 1);\n      }\n    }\n\n    pathParts.push(fileName);\n\n    return pathParts.join('/');\n  }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n\nimport {WorkboxSW} from './controllers/WorkboxSW.mjs';\nimport './_version.mjs';\n\n/**\n * @namespace workbox\n */\n\n// Don't export anything, just expose a global.\nself.workbox = new WorkboxSW();\n"], "names": ["self", "_", "e", "MODULE_KEY_TO_NAME_MAPPING", "backgroundSync", "broadcastUpdate", "cacheableResponse", "core", "expiration", "googleAnalytics", "navigationPreload", "precaching", "rangeRequests", "routing", "strategies", "streams", "recipes", "workbox", "constructor", "v", "_options", "debug", "location", "hostname", "modulePathPrefix", "modulePathCb", "_env", "this", "_modulesLoaded", "Proxy", "get", "target", "key", "moduleName", "loadModule", "setConfig", "options", "Error", "Object", "assign", "modulePath", "_getImportPath", "importScripts", "err", "console", "error", "pathParts", "fileName", "pathPrefix", "split", "length", "splice", "push", "join"], "mappings": "yBAAA,IAAIA,KAAK,qBAAqBC,GAAI,CAAA,MAAMC,ICUxC,MAEMC,EAA6B,CAMjCC,eAAgB,kBAMhBC,gBAAiB,mBAMjBC,kBAAmB,qBAMnBC,KAAM,OAMNC,WAAY,aAMZC,gBAAiB,aAMjBC,kBAAmB,qBAMnBC,WAAY,aAMZC,cAAe,iBAMfC,QAAS,UAMTC,WAAY,aAMZC,QAAS,UAMTC,QAAS,WC1EXhB,KAAKiB,QAAU,IDmFR,MAMLC,0BACOC,EAAI,QACJC,GAAW,CACdC,MAAkC,cAA3BrB,KAAKsB,SAASC,SACrBC,iBAAkB,KAClBC,aAAc,WAGXC,GAAOC,KAAKP,GAASC,MAAQ,MAAQ,YACrCO,IAAiB,EAEf,IAAIC,MAAMF,KAAM,CACrBG,IAAIC,EAAQC,MACND,EAAOC,UACFD,EAAOC,SAGVC,EAAa9B,EAA2B6B,UAC1CC,GACFF,EAAOG,WAAY,WAAUD,KAGxBF,EAAOC,MAsBpBG,UAAUC,EAAU,OACbT,KAAKC,SAIF,IAAIS,MAAM,yDAHhBC,OAAOC,OAAOZ,KAAKP,GAAUgB,QACxBV,GAAOC,KAAKP,GAASC,MAAQ,MAAQ,OAiB9Ca,WAAWD,SACHO,EAAab,KAAKc,GAAeR,OAErCS,cAAcF,QACTZ,IAAiB,EACtB,MAAOe,SAKPC,QAAQC,MACH,4BAA2BZ,YAAqBO,OAC/CG,GAYVF,GAAeR,MACTN,KAAKP,GAASK,oBACTE,KAAKP,GAASK,aAAaQ,EAAYN,KAAKP,GAASC,WAI1DyB,EAAY,CA7LF,mEA+LRC,EAAY,GAAEd,KAAcN,KAAKD,QAEjCsB,EAAarB,KAAKP,GAASI,wBAC7BwB,IAEFF,EAAYE,EAAWC,MAAM,KAIW,KAApCH,EAAUA,EAAUI,OAAS,IAC/BJ,EAAUK,OAAOL,EAAUI,OAAS,EAAG,IAI3CJ,EAAUM,KAAKL,GAERD,EAAUO,KAAK"}