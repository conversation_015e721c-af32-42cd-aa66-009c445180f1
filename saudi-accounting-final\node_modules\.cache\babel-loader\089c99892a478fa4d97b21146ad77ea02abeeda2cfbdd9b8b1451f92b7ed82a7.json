{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\POS\\\\ProfessionalPOS.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport database from '../../utils/database';\nimport './ProfessionalPOS.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfessionalPOS = ({\n  user,\n  onBack\n}) => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [cashReceived, setCashReceived] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const searchInputRef = useRef(null);\n  useEffect(() => {\n    loadProducts();\n    generateInvoiceNumber();\n\n    // Keyboard shortcuts\n    const handleKeyPress = e => {\n      if (e.key === 'F2') {\n        e.preventDefault();\n        handleCheckout();\n      } else if (e.key === 'F3') {\n        e.preventDefault();\n        clearCart();\n      } else if (e.key === 'F4') {\n        var _searchInputRef$curre;\n        e.preventDefault();\n        (_searchInputRef$curre = searchInputRef.current) === null || _searchInputRef$curre === void 0 ? void 0 : _searchInputRef$curre.focus();\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, []);\n  const loadProducts = () => {\n    try {\n      const allProducts = database.getProducts();\n      setProducts(allProducts);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      setMessage('❌ خطأ في تحميل المنتجات');\n    }\n  };\n  const generateInvoiceNumber = () => {\n    const settings = database.getSettings();\n    setCurrentInvoiceNumber(settings.nextInvoiceNumber.toString().padStart(6, '0'));\n  };\n  const addToCart = product => {\n    if (product.stock <= 0) {\n      setMessage('⚠️ المنتج غير متوفر في المخزون');\n      setTimeout(() => setMessage(''), 3000);\n      return;\n    }\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity >= product.stock) {\n        setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');\n        setTimeout(() => setMessage(''), 3000);\n        return;\n      }\n      setCart(cart.map(item => item.id === product.id ? {\n        ...item,\n        quantity: item.quantity + 1\n      } : item));\n    } else {\n      setCart([...cart, {\n        ...product,\n        quantity: 1\n      }]);\n    }\n    setMessage(`✅ تم إضافة ${product.name} إلى السلة`);\n    setTimeout(() => setMessage(''), 2000);\n  };\n  const updateQuantity = (productId, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(productId);\n      return;\n    }\n    const product = products.find(p => p.id === productId);\n    if (newQuantity > product.stock) {\n      setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');\n      return;\n    }\n    setCart(cart.map(item => item.id === productId ? {\n      ...item,\n      quantity: newQuantity\n    } : item));\n  };\n  const removeFromCart = productId => {\n    setCart(cart.filter(item => item.id !== productId));\n    setMessage('🗑️ تم حذف المنتج من السلة');\n    setTimeout(() => setMessage(''), 2000);\n  };\n  const clearCart = () => {\n    setCart([]);\n    setMessage('🗑️ تم مسح السلة');\n    setTimeout(() => setMessage(''), 2000);\n  };\n  const calculateTotal = () => {\n    const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);\n    const vat = subtotal * 0.15;\n    return {\n      subtotal,\n      vat,\n      total: subtotal + vat\n    };\n  };\n  const handleCheckout = () => {\n    if (cart.length === 0) {\n      setMessage('⚠️ السلة فارغة');\n      return;\n    }\n    setShowPaymentModal(true);\n  };\n  const processPayment = async () => {\n    try {\n      setLoading(true);\n      const {\n        total\n      } = calculateTotal();\n      const cash = parseFloat(cashReceived);\n      if (cash < total) {\n        setMessage('❌ المبلغ المدفوع أقل من إجمالي الفاتورة');\n        return;\n      }\n      const invoiceData = {\n        invoiceNumber: currentInvoiceNumber,\n        items: cart,\n        ...calculateTotal(),\n        cashReceived: cash,\n        change: cash - total,\n        cashier: user.name,\n        paymentMethod: 'cash'\n      };\n      const invoice = database.addInvoice(invoiceData);\n\n      // Update inventory\n      cart.forEach(item => {\n        database.setProductStock(item.id, item.stock - item.quantity);\n      });\n      setMessage('✅ تم إتمام البيع بنجاح');\n      setCart([]);\n      setCashReceived('');\n      setShowPaymentModal(false);\n      generateInvoiceNumber();\n      loadProducts();\n    } catch (error) {\n      console.error('خطأ في معالجة الدفع:', error);\n      setMessage('❌ خطأ في معالجة الدفع');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredProducts = products.filter(product => {\n    var _product$barcode;\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_product$barcode = product.barcode) === null || _product$barcode === void 0 ? void 0 : _product$barcode.includes(searchTerm));\n    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const categories = ['all', ...new Set(products.map(p => p.category).filter(Boolean))];\n  const {\n    subtotal,\n    vat,\n    total\n  } = calculateTotal();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"professional-pos\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pos-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-btn\",\n          onClick: onBack,\n          children: \"\\u2190 \\u0627\\u0644\\u0639\\u0648\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pos-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"invoice-number\",\n            children: [\"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629: \", currentInvoiceNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-container\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: searchInputRef,\n            type: \"text\",\n            placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0623\\u0648 \\u0645\\u0633\\u062D \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            onKeyDown: e => {\n              if (e.key === 'Enter' && searchTerm) {\n                // البحث بالباركود أو اسم المنتج\n                const product = products.find(p => p.barcode === searchTerm || p.name.toLowerCase().includes(searchTerm.toLowerCase()));\n                if (product) {\n                  addToCart(product);\n                  setSearchTerm('');\n                } else {\n                  setMessage('⚠️ لم يتم العثور على منتج بهذا الباركود أو الاسم');\n                }\n              }\n            },\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"time-display\",\n          children: new Date().toLocaleTimeString('ar-SA', {\n            hour: '2-digit',\n            minute: '2-digit'\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cashier-info\",\n          children: [\"\\u0627\\u0644\\u0643\\u0627\\u0634\\u064A\\u0631: \", user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `pos-message ${message.includes('❌') || message.includes('⚠️') ? 'error' : 'success'}`,\n      children: [message, /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setMessage(''),\n        className: \"close-message\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pos-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"categories-bar\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category ? 'active' : ''}`,\n            onClick: () => setSelectedCategory(category),\n            children: category === 'all' ? 'جميع المنتجات' : category\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `product-card ${product.stock <= 0 ? 'out-of-stock' : ''}`,\n            onClick: () => addToCart(product),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-image\",\n              children: product.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-image\",\n                children: \"\\uD83D\\uDCE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"product-name\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-price\",\n                children: [product.price.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-stock\",\n                children: [\"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646: \", product.stock]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), product.barcode && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-barcode\",\n                children: product.barcode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), product.stock <= 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"out-of-stock-overlay\",\n              children: \"\\u0646\\u0641\\u062F \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u0633\\u0644\\u0629 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"cart-count\",\n            children: [\"(\", cart.length, \" \\u0645\\u0646\\u062A\\u062C)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items\",\n          children: cart.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-cart\",\n            children: \"\\uD83D\\uDED2 \\u0627\\u0644\\u0633\\u0644\\u0629 \\u0641\\u0627\\u0631\\u063A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this) : cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-price\",\n                children: [item.price.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qty-btn\",\n                onClick: () => updateQuantity(item.id, item.quantity - 1),\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"quantity\",\n                children: item.quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qty-btn\",\n                onClick: () => updateQuantity(item.id, item.quantity + 1),\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-btn\",\n                onClick: () => removeFromCart(item.id),\n                children: \"\\uD83D\\uDDD1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-total\",\n              children: [(item.price * item.quantity).toFixed(2), \" \\u0631.\\u0633\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [subtotal.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [vat.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-row total\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [total.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"clear-btn\",\n              onClick: clearCart,\n              children: \"\\uD83D\\uDDD1\\uFE0F \\u0645\\u0633\\u062D \\u0627\\u0644\\u0633\\u0644\\u0629 (F3)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"checkout-btn\",\n              onClick: handleCheckout,\n              children: \"\\uD83D\\uDCB3 \\u0625\\u062A\\u0645\\u0627\\u0645 \\u0627\\u0644\\u0628\\u064A\\u0639 (F2)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), showPaymentModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u0625\\u062A\\u0645\\u0627\\u0645 \\u0639\\u0645\\u0644\\u064A\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-modal\",\n            onClick: () => setShowPaymentModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-summary\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"amount\",\n                children: [total.toFixed(2), \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-input\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A \\u0627\\u0644\\u0645\\u0633\\u062A\\u0644\\u0645:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: cashReceived,\n              onChange: e => setCashReceived(e.target.value),\n              placeholder: \"0.00\",\n              step: \"0.01\",\n              min: \"0\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), cashReceived && parseFloat(cashReceived) >= total && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"change-amount\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"change\",\n              children: [(parseFloat(cashReceived) - total).toFixed(2), \" \\u0631.\\u0633\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-btn\",\n            onClick: () => setShowPaymentModal(false),\n            children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-btn\",\n            onClick: processPayment,\n            disabled: !cashReceived || parseFloat(cashReceived) < total || loading,\n            children: loading ? 'جاري المعالجة...' : 'تأكيد الدفع'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalPOS, \"LC0e7BwWTSCo1MwbJG8usAviA3Y=\");\n_c = ProfessionalPOS;\nexport default ProfessionalPOS;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalPOS\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "database", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfessionalPOS", "user", "onBack", "_s", "products", "setProducts", "cart", "setCart", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "currentInvoiceNumber", "setCurrentInvoiceNumber", "showPaymentModal", "setShowPaymentModal", "cashReceived", "setCashReceived", "message", "setMessage", "loading", "setLoading", "searchInputRef", "loadProducts", "generateInvoiceNumber", "handleKeyPress", "e", "key", "preventDefault", "handleCheckout", "clearCart", "_searchInputRef$curre", "current", "focus", "window", "addEventListener", "removeEventListener", "allProducts", "getProducts", "error", "console", "settings", "getSettings", "nextInvoiceNumber", "toString", "padStart", "addToCart", "product", "stock", "setTimeout", "existingItem", "find", "item", "id", "quantity", "map", "name", "updateQuantity", "productId", "newQuantity", "removeFromCart", "p", "filter", "calculateTotal", "subtotal", "reduce", "sum", "price", "vat", "total", "length", "processPayment", "cash", "parseFloat", "invoiceData", "invoiceNumber", "items", "change", "cashier", "paymentMethod", "invoice", "addInvoice", "for<PERSON>ach", "setProductStock", "filteredProducts", "_product$barcode", "matchesSearch", "toLowerCase", "includes", "barcode", "matchesCategory", "category", "categories", "Set", "Boolean", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "type", "placeholder", "value", "onChange", "target", "onKeyDown", "Date", "toLocaleTimeString", "hour", "minute", "image", "src", "alt", "toFixed", "step", "min", "autoFocus", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/POS/ProfessionalPOS.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport database from '../../utils/database';\nimport './ProfessionalPOS.css';\n\nconst ProfessionalPOS = ({ user, onBack }) => {\n  const [products, setProducts] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [cashReceived, setCashReceived] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const searchInputRef = useRef(null);\n\n  useEffect(() => {\n    loadProducts();\n    generateInvoiceNumber();\n    \n    // Keyboard shortcuts\n    const handleKeyPress = (e) => {\n      if (e.key === 'F2') {\n        e.preventDefault();\n        handleCheckout();\n      } else if (e.key === 'F3') {\n        e.preventDefault();\n        clearCart();\n      } else if (e.key === 'F4') {\n        e.preventDefault();\n        searchInputRef.current?.focus();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, []);\n\n  const loadProducts = () => {\n    try {\n      const allProducts = database.getProducts();\n      setProducts(allProducts);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      setMessage('❌ خطأ في تحميل المنتجات');\n    }\n  };\n\n  const generateInvoiceNumber = () => {\n    const settings = database.getSettings();\n    setCurrentInvoiceNumber(settings.nextInvoiceNumber.toString().padStart(6, '0'));\n  };\n\n  const addToCart = (product) => {\n    if (product.stock <= 0) {\n      setMessage('⚠️ المنتج غير متوفر في المخزون');\n      setTimeout(() => setMessage(''), 3000);\n      return;\n    }\n\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity >= product.stock) {\n        setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');\n        setTimeout(() => setMessage(''), 3000);\n        return;\n      }\n      setCart(cart.map(item =>\n        item.id === product.id\n          ? { ...item, quantity: item.quantity + 1 }\n          : item\n      ));\n    } else {\n      setCart([...cart, { ...product, quantity: 1 }]);\n    }\n    setMessage(`✅ تم إضافة ${product.name} إلى السلة`);\n    setTimeout(() => setMessage(''), 2000);\n  };\n\n  const updateQuantity = (productId, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(productId);\n      return;\n    }\n\n    const product = products.find(p => p.id === productId);\n    if (newQuantity > product.stock) {\n      setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');\n      return;\n    }\n\n    setCart(cart.map(item =>\n      item.id === productId\n        ? { ...item, quantity: newQuantity }\n        : item\n    ));\n  };\n\n  const removeFromCart = (productId) => {\n    setCart(cart.filter(item => item.id !== productId));\n    setMessage('🗑️ تم حذف المنتج من السلة');\n    setTimeout(() => setMessage(''), 2000);\n  };\n\n  const clearCart = () => {\n    setCart([]);\n    setMessage('🗑️ تم مسح السلة');\n    setTimeout(() => setMessage(''), 2000);\n  };\n\n  const calculateTotal = () => {\n    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n    const vat = subtotal * 0.15;\n    return { subtotal, vat, total: subtotal + vat };\n  };\n\n  const handleCheckout = () => {\n    if (cart.length === 0) {\n      setMessage('⚠️ السلة فارغة');\n      return;\n    }\n    setShowPaymentModal(true);\n  };\n\n  const processPayment = async () => {\n    try {\n      setLoading(true);\n      const { total } = calculateTotal();\n      const cash = parseFloat(cashReceived);\n\n      if (cash < total) {\n        setMessage('❌ المبلغ المدفوع أقل من إجمالي الفاتورة');\n        return;\n      }\n\n      const invoiceData = {\n        invoiceNumber: currentInvoiceNumber,\n        items: cart,\n        ...calculateTotal(),\n        cashReceived: cash,\n        change: cash - total,\n        cashier: user.name,\n        paymentMethod: 'cash'\n      };\n\n      const invoice = database.addInvoice(invoiceData);\n      \n      // Update inventory\n      cart.forEach(item => {\n        database.setProductStock(item.id, item.stock - item.quantity);\n      });\n\n      setMessage('✅ تم إتمام البيع بنجاح');\n      setCart([]);\n      setCashReceived('');\n      setShowPaymentModal(false);\n      generateInvoiceNumber();\n      loadProducts();\n\n    } catch (error) {\n      console.error('خطأ في معالجة الدفع:', error);\n      setMessage('❌ خطأ في معالجة الدفع');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.barcode?.includes(searchTerm);\n    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const categories = ['all', ...new Set(products.map(p => p.category).filter(Boolean))];\n  const { subtotal, vat, total } = calculateTotal();\n\n  return (\n    <div className=\"professional-pos\">\n      {/* Header */}\n      <div className=\"pos-header\">\n        <div className=\"header-left\">\n          <button className=\"back-btn\" onClick={onBack}>\n            ← العودة\n          </button>\n          <div className=\"pos-title\">\n            <h1>نقطة البيع</h1>\n            <span className=\"invoice-number\">فاتورة: {currentInvoiceNumber}</span>\n          </div>\n        </div>\n        \n        <div className=\"header-center\">\n          <div className=\"search-container\">\n            <input\n              ref={searchInputRef}\n              type=\"text\"\n              placeholder=\"🔍 البحث في المنتجات أو مسح الباركود...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' && searchTerm) {\n                  // البحث بالباركود أو اسم المنتج\n                  const product = products.find(p =>\n                    p.barcode === searchTerm ||\n                    p.name.toLowerCase().includes(searchTerm.toLowerCase())\n                  );\n\n                  if (product) {\n                    addToCart(product);\n                    setSearchTerm('');\n                  } else {\n                    setMessage('⚠️ لم يتم العثور على منتج بهذا الباركود أو الاسم');\n                  }\n                }\n              }}\n              className=\"search-input\"\n            />\n          </div>\n        </div>\n        \n        <div className=\"header-right\">\n          <div className=\"time-display\">\n            {new Date().toLocaleTimeString('ar-SA', { \n              hour: '2-digit', \n              minute: '2-digit' \n            })}\n          </div>\n          <div className=\"cashier-info\">\n            الكاشير: {user.name}\n          </div>\n        </div>\n      </div>\n\n      {/* Message */}\n      {message && (\n        <div className={`pos-message ${message.includes('❌') || message.includes('⚠️') ? 'error' : 'success'}`}>\n          {message}\n          <button onClick={() => setMessage('')} className=\"close-message\">×</button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"pos-main\">\n        {/* Products Section */}\n        <div className=\"products-section\">\n          {/* Categories */}\n          <div className=\"categories-bar\">\n            {categories.map(category => (\n              <button\n                key={category}\n                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}\n                onClick={() => setSelectedCategory(category)}\n              >\n                {category === 'all' ? 'جميع المنتجات' : category}\n              </button>\n            ))}\n          </div>\n\n          {/* Products Grid */}\n          <div className=\"products-grid\">\n            {filteredProducts.map(product => (\n              <div\n                key={product.id}\n                className={`product-card ${product.stock <= 0 ? 'out-of-stock' : ''}`}\n                onClick={() => addToCart(product)}\n              >\n                <div className=\"product-image\">\n                  {product.image ? (\n                    <img src={product.image} alt={product.name} />\n                  ) : (\n                    <div className=\"no-image\">📦</div>\n                  )}\n                </div>\n                <div className=\"product-info\">\n                  <h3 className=\"product-name\">{product.name}</h3>\n                  <div className=\"product-price\">{product.price.toFixed(2)} ر.س</div>\n                  <div className=\"product-stock\">\n                    المخزون: {product.stock}\n                  </div>\n                  {product.barcode && (\n                    <div className=\"product-barcode\">{product.barcode}</div>\n                  )}\n                </div>\n                {product.stock <= 0 && (\n                  <div className=\"out-of-stock-overlay\">\n                    نفد المخزون\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Cart Section */}\n        <div className=\"cart-section\">\n          <div className=\"cart-header\">\n            <h2>سلة المشتريات</h2>\n            <span className=\"cart-count\">({cart.length} منتج)</span>\n          </div>\n\n          <div className=\"cart-items\">\n            {cart.length === 0 ? (\n              <div className=\"empty-cart\">\n                🛒 السلة فارغة\n              </div>\n            ) : (\n              cart.map(item => (\n                <div key={item.id} className=\"cart-item\">\n                  <div className=\"item-info\">\n                    <h4>{item.name}</h4>\n                    <div className=\"item-price\">{item.price.toFixed(2)} ر.س</div>\n                  </div>\n                  <div className=\"item-controls\">\n                    <button\n                      className=\"qty-btn\"\n                      onClick={() => updateQuantity(item.id, item.quantity - 1)}\n                    >\n                      -\n                    </button>\n                    <span className=\"quantity\">{item.quantity}</span>\n                    <button\n                      className=\"qty-btn\"\n                      onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                    >\n                      +\n                    </button>\n                    <button\n                      className=\"remove-btn\"\n                      onClick={() => removeFromCart(item.id)}\n                    >\n                      🗑️\n                    </button>\n                  </div>\n                  <div className=\"item-total\">\n                    {(item.price * item.quantity).toFixed(2)} ر.س\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {cart.length > 0 && (\n            <>\n              <div className=\"cart-summary\">\n                <div className=\"summary-row\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{subtotal.toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"summary-row\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{vat.toFixed(2)} ر.س</span>\n                </div>\n                <div className=\"summary-row total\">\n                  <span>الإجمالي:</span>\n                  <span>{total.toFixed(2)} ر.س</span>\n                </div>\n              </div>\n\n              <div className=\"cart-actions\">\n                <button className=\"clear-btn\" onClick={clearCart}>\n                  🗑️ مسح السلة (F3)\n                </button>\n                <button className=\"checkout-btn\" onClick={handleCheckout}>\n                  💳 إتمام البيع (F2)\n                </button>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <div className=\"payment-modal-overlay\">\n          <div className=\"payment-modal\">\n            <div className=\"modal-header\">\n              <h2>إتمام عملية الدفع</h2>\n              <button\n                className=\"close-modal\"\n                onClick={() => setShowPaymentModal(false)}\n              >\n                ×\n              </button>\n            </div>\n            \n            <div className=\"modal-content\">\n              <div className=\"payment-summary\">\n                <div className=\"summary-item\">\n                  <span>الإجمالي المطلوب:</span>\n                  <span className=\"amount\">{total.toFixed(2)} ر.س</span>\n                </div>\n              </div>\n\n              <div className=\"payment-input\">\n                <label>المبلغ المستلم:</label>\n                <input\n                  type=\"number\"\n                  value={cashReceived}\n                  onChange={(e) => setCashReceived(e.target.value)}\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  autoFocus\n                />\n              </div>\n\n              {cashReceived && parseFloat(cashReceived) >= total && (\n                <div className=\"change-amount\">\n                  <span>المبلغ المرتجع:</span>\n                  <span className=\"change\">\n                    {(parseFloat(cashReceived) - total).toFixed(2)} ر.س\n                  </span>\n                </div>\n              )}\n            </div>\n\n            <div className=\"modal-actions\">\n              <button\n                className=\"cancel-btn\"\n                onClick={() => setShowPaymentModal(false)}\n              >\n                إلغاء\n              </button>\n              <button\n                className=\"confirm-btn\"\n                onClick={processPayment}\n                disabled={!cashReceived || parseFloat(cashReceived) < total || loading}\n              >\n                {loading ? 'جاري المعالجة...' : 'تأكيد الدفع'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProfessionalPOS;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM8B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd8B,YAAY,CAAC,CAAC;IACdC,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMC,cAAc,GAAIC,CAAC,IAAK;MAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,IAAI,EAAE;QAClBD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClBC,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIH,CAAC,CAACC,GAAG,KAAK,IAAI,EAAE;QACzBD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClBE,SAAS,CAAC,CAAC;MACb,CAAC,MAAM,IAAIJ,CAAC,CAACC,GAAG,KAAK,IAAI,EAAE;QAAA,IAAAI,qBAAA;QACzBL,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,CAAAG,qBAAA,GAAAT,cAAc,CAACU,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,KAAK,CAAC,CAAC;MACjC;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEV,cAAc,CAAC;IAClD,OAAO,MAAMS,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEX,cAAc,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI;MACF,MAAMc,WAAW,GAAG1C,QAAQ,CAAC2C,WAAW,CAAC,CAAC;MAC1CjC,WAAW,CAACgC,WAAW,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpB,UAAU,CAAC,yBAAyB,CAAC;IACvC;EACF,CAAC;EAED,MAAMK,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMiB,QAAQ,GAAG9C,QAAQ,CAAC+C,WAAW,CAAC,CAAC;IACvC7B,uBAAuB,CAAC4B,QAAQ,CAACE,iBAAiB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,SAAS,GAAIC,OAAO,IAAK;IAC7B,IAAIA,OAAO,CAACC,KAAK,IAAI,CAAC,EAAE;MACtB7B,UAAU,CAAC,gCAAgC,CAAC;MAC5C8B,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACtC;IACF;IAEA,MAAM+B,YAAY,GAAG5C,IAAI,CAAC6C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKN,OAAO,CAACM,EAAE,CAAC;IAC9D,IAAIH,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACI,QAAQ,IAAIP,OAAO,CAACC,KAAK,EAAE;QAC1C7B,UAAU,CAAC,0CAA0C,CAAC;QACtD8B,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACtC;MACF;MACAZ,OAAO,CAACD,IAAI,CAACiD,GAAG,CAACH,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKN,OAAO,CAACM,EAAE,GAClB;QAAE,GAAGD,IAAI;QAAEE,QAAQ,EAAEF,IAAI,CAACE,QAAQ,GAAG;MAAE,CAAC,GACxCF,IACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACL7C,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGyC,OAAO;QAAEO,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IACjD;IACAnC,UAAU,CAAC,cAAc4B,OAAO,CAACS,IAAI,YAAY,CAAC;IAClDP,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAMsC,cAAc,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;IACjD,IAAIA,WAAW,IAAI,CAAC,EAAE;MACpBC,cAAc,CAACF,SAAS,CAAC;MACzB;IACF;IAEA,MAAMX,OAAO,GAAG3C,QAAQ,CAAC+C,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,SAAS,CAAC;IACtD,IAAIC,WAAW,GAAGZ,OAAO,CAACC,KAAK,EAAE;MAC/B7B,UAAU,CAAC,0CAA0C,CAAC;MACtD;IACF;IAEAZ,OAAO,CAACD,IAAI,CAACiD,GAAG,CAACH,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKK,SAAS,GACjB;MAAE,GAAGN,IAAI;MAAEE,QAAQ,EAAEK;IAAY,CAAC,GAClCP,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMQ,cAAc,GAAIF,SAAS,IAAK;IACpCnD,OAAO,CAACD,IAAI,CAACwD,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKK,SAAS,CAAC,CAAC;IACnDvC,UAAU,CAAC,4BAA4B,CAAC;IACxC8B,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAMW,SAAS,GAAGA,CAAA,KAAM;IACtBvB,OAAO,CAAC,EAAE,CAAC;IACXY,UAAU,CAAC,kBAAkB,CAAC;IAC9B8B,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,QAAQ,GAAG1D,IAAI,CAAC2D,MAAM,CAAC,CAACC,GAAG,EAAEd,IAAI,KAAKc,GAAG,GAAId,IAAI,CAACe,KAAK,GAAGf,IAAI,CAACE,QAAS,EAAE,CAAC,CAAC;IAClF,MAAMc,GAAG,GAAGJ,QAAQ,GAAG,IAAI;IAC3B,OAAO;MAAEA,QAAQ;MAAEI,GAAG;MAAEC,KAAK,EAAEL,QAAQ,GAAGI;IAAI,CAAC;EACjD,CAAC;EAED,MAAMvC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvB,IAAI,CAACgE,MAAM,KAAK,CAAC,EAAE;MACrBnD,UAAU,CAAC,gBAAgB,CAAC;MAC5B;IACF;IACAJ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMwD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFlD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM;QAAEgD;MAAM,CAAC,GAAGN,cAAc,CAAC,CAAC;MAClC,MAAMS,IAAI,GAAGC,UAAU,CAACzD,YAAY,CAAC;MAErC,IAAIwD,IAAI,GAAGH,KAAK,EAAE;QAChBlD,UAAU,CAAC,yCAAyC,CAAC;QACrD;MACF;MAEA,MAAMuD,WAAW,GAAG;QAClBC,aAAa,EAAE/D,oBAAoB;QACnCgE,KAAK,EAAEtE,IAAI;QACX,GAAGyD,cAAc,CAAC,CAAC;QACnB/C,YAAY,EAAEwD,IAAI;QAClBK,MAAM,EAAEL,IAAI,GAAGH,KAAK;QACpBS,OAAO,EAAE7E,IAAI,CAACuD,IAAI;QAClBuB,aAAa,EAAE;MACjB,CAAC;MAED,MAAMC,OAAO,GAAGrF,QAAQ,CAACsF,UAAU,CAACP,WAAW,CAAC;;MAEhD;MACApE,IAAI,CAAC4E,OAAO,CAAC9B,IAAI,IAAI;QACnBzD,QAAQ,CAACwF,eAAe,CAAC/B,IAAI,CAACC,EAAE,EAAED,IAAI,CAACJ,KAAK,GAAGI,IAAI,CAACE,QAAQ,CAAC;MAC/D,CAAC,CAAC;MAEFnC,UAAU,CAAC,wBAAwB,CAAC;MACpCZ,OAAO,CAAC,EAAE,CAAC;MACXU,eAAe,CAAC,EAAE,CAAC;MACnBF,mBAAmB,CAAC,KAAK,CAAC;MAC1BS,qBAAqB,CAAC,CAAC;MACvBD,YAAY,CAAC,CAAC;IAEhB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CpB,UAAU,CAAC,uBAAuB,CAAC;IACrC,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,gBAAgB,GAAGhF,QAAQ,CAAC0D,MAAM,CAACf,OAAO,IAAI;IAAA,IAAAsC,gBAAA;IAClD,MAAMC,aAAa,GAAGvC,OAAO,CAACS,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC,MAAAF,gBAAA,GAC9DtC,OAAO,CAAC0C,OAAO,cAAAJ,gBAAA,uBAAfA,gBAAA,CAAiBG,QAAQ,CAAChF,UAAU,CAAC;IAC1D,MAAMkF,eAAe,GAAGhF,gBAAgB,KAAK,KAAK,IAAIqC,OAAO,CAAC4C,QAAQ,KAAKjF,gBAAgB;IAC3F,OAAO4E,aAAa,IAAII,eAAe;EACzC,CAAC,CAAC;EAEF,MAAME,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,IAAIC,GAAG,CAACzF,QAAQ,CAACmD,GAAG,CAACM,CAAC,IAAIA,CAAC,CAAC8B,QAAQ,CAAC,CAAC7B,MAAM,CAACgC,OAAO,CAAC,CAAC,CAAC;EACrF,MAAM;IAAE9B,QAAQ;IAAEI,GAAG;IAAEC;EAAM,CAAC,GAAGN,cAAc,CAAC,CAAC;EAEjD,oBACElE,OAAA;IAAKkG,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BnG,OAAA;MAAKkG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBnG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnG,OAAA;UAAQkG,SAAS,EAAC,UAAU;UAACE,OAAO,EAAE/F,MAAO;UAAA8F,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxG,OAAA;UAAKkG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnG,OAAA;YAAAmG,QAAA,EAAI;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBxG,OAAA;YAAMkG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,wCAAQ,EAACpF,oBAAoB;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxG,OAAA;QAAKkG,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BnG,OAAA;UAAKkG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BnG,OAAA;YACEyG,GAAG,EAAEhF,cAAe;YACpBiF,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,+LAAyC;YACrDC,KAAK,EAAEjG,UAAW;YAClBkG,QAAQ,EAAGhF,CAAC,IAAKjB,aAAa,CAACiB,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;YAC/CG,SAAS,EAAGlF,CAAC,IAAK;cAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAInB,UAAU,EAAE;gBACnC;gBACA,MAAMuC,OAAO,GAAG3C,QAAQ,CAAC+C,IAAI,CAACU,CAAC,IAC7BA,CAAC,CAAC4B,OAAO,KAAKjF,UAAU,IACxBqD,CAAC,CAACL,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CACxD,CAAC;gBAED,IAAIxC,OAAO,EAAE;kBACXD,SAAS,CAACC,OAAO,CAAC;kBAClBtC,aAAa,CAAC,EAAE,CAAC;gBACnB,CAAC,MAAM;kBACLU,UAAU,CAAC,kDAAkD,CAAC;gBAChE;cACF;YACF,CAAE;YACF4E,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxG,OAAA;QAAKkG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnG,OAAA;UAAKkG,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,IAAIa,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;YACtCC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC;QAAC;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxG,OAAA;UAAKkG,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,8CACnB,EAAC/F,IAAI,CAACuD,IAAI;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnF,OAAO,iBACNrB,OAAA;MAAKkG,SAAS,EAAE,eAAe7E,OAAO,CAACsE,QAAQ,CAAC,GAAG,CAAC,IAAItE,OAAO,CAACsE,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAQ,QAAA,GACpG9E,OAAO,eACRrB,OAAA;QAAQoG,OAAO,EAAEA,CAAA,KAAM9E,UAAU,CAAC,EAAE,CAAE;QAAC4E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CACN,eAGDxG,OAAA;MAAKkG,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEvBnG,OAAA;QAAKkG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE/BnG,OAAA;UAAKkG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BJ,UAAU,CAACrC,GAAG,CAACoC,QAAQ,iBACtB9F,OAAA;YAEEkG,SAAS,EAAE,gBAAgBrF,gBAAgB,KAAKiF,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC3EM,OAAO,EAAEA,CAAA,KAAMtF,mBAAmB,CAACgF,QAAQ,CAAE;YAAAK,QAAA,EAE5CL,QAAQ,KAAK,KAAK,GAAG,eAAe,GAAGA;UAAQ,GAJ3CA,QAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKP,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxG,OAAA;UAAKkG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BZ,gBAAgB,CAAC7B,GAAG,CAACR,OAAO,iBAC3BlD,OAAA;YAEEkG,SAAS,EAAE,gBAAgBhD,OAAO,CAACC,KAAK,IAAI,CAAC,GAAG,cAAc,GAAG,EAAE,EAAG;YACtEiD,OAAO,EAAEA,CAAA,KAAMnD,SAAS,CAACC,OAAO,CAAE;YAAAiD,QAAA,gBAElCnG,OAAA;cAAKkG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BjD,OAAO,CAACkE,KAAK,gBACZpH,OAAA;gBAAKqH,GAAG,EAAEnE,OAAO,CAACkE,KAAM;gBAACE,GAAG,EAAEpE,OAAO,CAACS;cAAK;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9CxG,OAAA;gBAAKkG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAClC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxG,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnG,OAAA;gBAAIkG,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEjD,OAAO,CAACS;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDxG,OAAA;gBAAKkG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAEjD,OAAO,CAACoB,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnExG,OAAA;gBAAKkG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,8CACpB,EAACjD,OAAO,CAACC,KAAK;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACLtD,OAAO,CAAC0C,OAAO,iBACd5F,OAAA;gBAAKkG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEjD,OAAO,CAAC0C;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACxD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACLtD,OAAO,CAACC,KAAK,IAAI,CAAC,iBACjBnD,OAAA;cAAKkG,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,GAzBItD,OAAO,CAACM,EAAE;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxG,OAAA;QAAKkG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnG,OAAA;UAAKkG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnG,OAAA;YAAAmG,QAAA,EAAI;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBxG,OAAA;YAAMkG,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,GAAC,EAAC1F,IAAI,CAACgE,MAAM,EAAC,4BAAM;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAENxG,OAAA;UAAKkG,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB1F,IAAI,CAACgE,MAAM,KAAK,CAAC,gBAChBzE,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEN/F,IAAI,CAACiD,GAAG,CAACH,IAAI,iBACXvD,OAAA;YAAmBkG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtCnG,OAAA;cAAKkG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnG,OAAA;gBAAAmG,QAAA,EAAK5C,IAAI,CAACI;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBxG,OAAA;gBAAKkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE5C,IAAI,CAACe,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNxG,OAAA;cAAKkG,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BnG,OAAA;gBACEkG,SAAS,EAAC,SAAS;gBACnBE,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAACL,IAAI,CAACC,EAAE,EAAED,IAAI,CAACE,QAAQ,GAAG,CAAC,CAAE;gBAAA0C,QAAA,EAC3D;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA;gBAAMkG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAE5C,IAAI,CAACE;cAAQ;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDxG,OAAA;gBACEkG,SAAS,EAAC,SAAS;gBACnBE,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAACL,IAAI,CAACC,EAAE,EAAED,IAAI,CAACE,QAAQ,GAAG,CAAC,CAAE;gBAAA0C,QAAA,EAC3D;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA;gBACEkG,SAAS,EAAC,YAAY;gBACtBE,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACR,IAAI,CAACC,EAAE,CAAE;gBAAA2C,QAAA,EACxC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNxG,OAAA;cAAKkG,SAAS,EAAC,YAAY;cAAAC,QAAA,GACxB,CAAC5C,IAAI,CAACe,KAAK,GAAGf,IAAI,CAACE,QAAQ,EAAE8D,OAAO,CAAC,CAAC,CAAC,EAAC,gBAC3C;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GA5BEjD,IAAI,CAACC,EAAE;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BZ,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL/F,IAAI,CAACgE,MAAM,GAAG,CAAC,iBACdzE,OAAA,CAAAE,SAAA;UAAAiG,QAAA,gBACEnG,OAAA;YAAKkG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnG,OAAA;cAAKkG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnG,OAAA;gBAAAmG,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BxG,OAAA;gBAAAmG,QAAA,GAAOhC,QAAQ,CAACoD,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNxG,OAAA;cAAKkG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnG,OAAA;gBAAAmG,QAAA,EAAM;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCxG,OAAA;gBAAAmG,QAAA,GAAO5B,GAAG,CAACgD,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNxG,OAAA;cAAKkG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnG,OAAA;gBAAAmG,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtBxG,OAAA;gBAAAmG,QAAA,GAAO3B,KAAK,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA;YAAKkG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnG,OAAA;cAAQkG,SAAS,EAAC,WAAW;cAACE,OAAO,EAAEnE,SAAU;cAAAkE,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxG,OAAA;cAAQkG,SAAS,EAAC,cAAc;cAACE,OAAO,EAAEpE,cAAe;cAAAmE,QAAA,EAAC;YAE1D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvF,gBAAgB,iBACfjB,OAAA;MAAKkG,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCnG,OAAA;QAAKkG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnG,OAAA;UAAKkG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnG,OAAA;YAAAmG,QAAA,EAAI;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BxG,OAAA;YACEkG,SAAS,EAAC,aAAa;YACvBE,OAAO,EAAEA,CAAA,KAAMlF,mBAAmB,CAAC,KAAK,CAAE;YAAAiF,QAAA,EAC3C;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENxG,OAAA;UAAKkG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnG,OAAA;YAAKkG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BnG,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnG,OAAA;gBAAAmG,QAAA,EAAM;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9BxG,OAAA;gBAAMkG,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAAE3B,KAAK,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA;YAAKkG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnG,OAAA;cAAAmG,QAAA,EAAO;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9BxG,OAAA;cACE0G,IAAI,EAAC,QAAQ;cACbE,KAAK,EAAEzF,YAAa;cACpB0F,QAAQ,EAAGhF,CAAC,IAAKT,eAAe,CAACS,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cACjDD,WAAW,EAAC,MAAM;cAClBa,IAAI,EAAC,MAAM;cACXC,GAAG,EAAC,GAAG;cACPC,SAAS;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELrF,YAAY,IAAIyD,UAAU,CAACzD,YAAY,CAAC,IAAIqD,KAAK,iBAChDxE,OAAA;YAAKkG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnG,OAAA;cAAAmG,QAAA,EAAM;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxG,OAAA;cAAMkG,SAAS,EAAC,QAAQ;cAAAC,QAAA,GACrB,CAACvB,UAAU,CAACzD,YAAY,CAAC,GAAGqD,KAAK,EAAE+C,OAAO,CAAC,CAAC,CAAC,EAAC,gBACjD;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxG,OAAA;UAAKkG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnG,OAAA;YACEkG,SAAS,EAAC,YAAY;YACtBE,OAAO,EAAEA,CAAA,KAAMlF,mBAAmB,CAAC,KAAK,CAAE;YAAAiF,QAAA,EAC3C;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxG,OAAA;YACEkG,SAAS,EAAC,aAAa;YACvBE,OAAO,EAAE1B,cAAe;YACxBiD,QAAQ,EAAE,CAACxG,YAAY,IAAIyD,UAAU,CAACzD,YAAY,CAAC,GAAGqD,KAAK,IAAIjD,OAAQ;YAAA4E,QAAA,EAEtE5E,OAAO,GAAG,kBAAkB,GAAG;UAAa;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClG,EAAA,CAhbIH,eAAe;AAAAyH,EAAA,GAAfzH,eAAe;AAkbrB,eAAeA,eAAe;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}