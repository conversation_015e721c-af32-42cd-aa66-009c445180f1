import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import { testDatabase } from '../../utils/testDatabase';
import './EnhancedLogin.css';

const EnhancedLogin = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  useEffect(() => {
    // Test database on component mount
    console.log('🔧 اختبار قاعدة البيانات عند تحميل المكون...');
    testDatabase();

    // Check for remembered credentials
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    if (rememberedUsername) {
      setFormData(prev => ({ ...prev, username: rememberedUsername }));
      setRememberMe(true);
    }

    // Auto-focus on username field
    const usernameField = document.getElementById('username');
    if (usernameField) {
      usernameField.focus();
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.username.trim() || !formData.password.trim()) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate login delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('محاولة تسجيل الدخول:', formData.username);

      const user = database.authenticateUser(formData.username, formData.password);

      console.log('نتيجة المصادقة:', user);

      if (user) {
        // Handle remember me
        if (rememberMe) {
          localStorage.setItem('rememberedUsername', formData.username);
        } else {
          localStorage.removeItem('rememberedUsername');
        }

        // Log the login activity
        try {
          database.logActivity({
            type: 'login',
            userId: user.id,
            username: user.username,
            timestamp: new Date().toISOString(),
            details: 'تسجيل دخول ناجح'
          });
        } catch (logError) {
          console.warn('خطأ في تسجيل النشاط:', logError);
        }

        onLogin(user);
      } else {
        setError('اسم المستخدم أو كلمة المرور غير صحيحة');
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setFormData({
      username: 'admin',
      password: 'admin123'
    });
    setError('');

    // تسجيل دخول تلقائي
    setTimeout(() => {
      const form = document.querySelector('.login-form');
      if (form) {
        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
      }
    }, 100);
  };

  const handleResetDatabase = () => {
    try {
      console.log('إعادة تهيئة قاعدة البيانات...');

      // مسح البيانات الحالية
      localStorage.removeItem('saudi_accounting_users');

      // إعادة تهيئة قاعدة البيانات
      database.initializeDatabase();

      // اختبار قاعدة البيانات
      testDatabase();

      setError('');
      setFormData({
        username: 'admin',
        password: 'admin123'
      });

      alert('تم إعادة تهيئة قاعدة البيانات بنجاح!\nيمكنك الآن تسجيل الدخول باستخدام:\nاسم المستخدم: admin\nكلمة المرور: admin123');

    } catch (error) {
      console.error('خطأ في إعادة تهيئة قاعدة البيانات:', error);
      setError('فشل في إعادة تهيئة قاعدة البيانات');
    }
  };

  const getCurrentTime = () => {
    return new Date().toLocaleString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="enhanced-login">
      <div className="login-background">
        <div className="background-pattern"></div>
        <div className="floating-shapes">
          <div className="shape shape-1"></div>
          <div className="shape shape-2"></div>
          <div className="shape shape-3"></div>
          <div className="shape shape-4"></div>
        </div>
      </div>

      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <div className="company-logo">
              <div className="logo-icon">🏪</div>
              <div className="logo-text">
                <h1>Aronim EXP</h1>
                <p>نظام المحاسبة المتطور</p>
              </div>
            </div>
            <div className="current-time">
              {getCurrentTime()}
            </div>
          </div>

          <div className="login-body">
            <div className="welcome-text">
              <h2>مرحباً بك</h2>
              <p>سجل دخولك للوصول إلى نظام المحاسبة</p>
            </div>

            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="login-form">
              <div className="form-group">
                <label htmlFor="username">اسم المستخدم</label>
                <div className="input-container">
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم المستخدم"
                    className="form-input"
                    disabled={loading}
                    autoComplete="username"
                  />
                  <span className="input-icon">👤</span>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="password">كلمة المرور</label>
                <div className="input-container">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="أدخل كلمة المرور"
                    className="form-input"
                    disabled={loading}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? '🙈' : '👁️'}
                  </button>
                </div>
              </div>

              <div className="form-options">
                <label className="checkbox-container">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    disabled={loading}
                  />
                  <span className="checkmark"></span>
                  تذكرني
                </label>
              </div>

              <button
                type="submit"
                className="login-button"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="loading-spinner"></span>
                    جاري تسجيل الدخول...
                  </>
                ) : (
                  <>
                    <span className="button-icon">🔐</span>
                    تسجيل الدخول
                  </>
                )}
              </button>
            </form>

            <div className="demo-section">
              <div className="divider">
                <span>أو</span>
              </div>
              <button
                type="button"
                onClick={handleDemoLogin}
                className="demo-button"
                disabled={loading}
              >
                <span className="button-icon">🎯</span>
                تجربة النظام (Demo)
              </button>

              <button
                type="button"
                onClick={handleResetDatabase}
                className="reset-button"
                disabled={loading}
                title="إعادة تهيئة قاعدة البيانات في حالة وجود مشكلة"
              >
                <span className="button-icon">🔄</span>
                إعادة تهيئة النظام
              </button>
            </div>
          </div>

          <div className="login-footer">
            <div className="system-info">
              <div className="info-item">
                <span className="info-icon">🇸🇦</span>
                <span>متوافق مع المتطلبات السعودية</span>
              </div>
              <div className="info-item">
                <span className="info-icon">🔒</span>
                <span>نظام آمن ومحمي</span>
              </div>
              <div className="info-item">
                <span className="info-icon">📱</span>
                <span>متجاوب مع جميع الأجهزة</span>
              </div>
            </div>
            
            <div className="copyright">
              <p>© 2024 Aronim EXP. جميع الحقوق محفوظة.</p>
              <p>نسخة 2.0.0</p>
            </div>
          </div>
        </div>

        <div className="features-sidebar">
          <h3>مميزات النظام</h3>
          <div className="features-list">
            <div className="feature-item">
              <div className="feature-icon">🛒</div>
              <div className="feature-content">
                <h4>نقطة بيع متطورة</h4>
                <p>نظام بيع سريع وفعال مع دعم الباركود</p>
              </div>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">📊</div>
              <div className="feature-content">
                <h4>تقارير شاملة</h4>
                <p>تقارير مالية مفصلة وإحصائيات دقيقة</p>
              </div>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">📦</div>
              <div className="feature-content">
                <h4>إدارة المخزون</h4>
                <p>تتبع دقيق للمخزون والتنبيهات الذكية</p>
              </div>
            </div>
            
            <div className="feature-item">
              <div className="feature-icon">💰</div>
              <div className="feature-content">
                <h4>محاسبة متكاملة</h4>
                <p>نظام محاسبي شامل مع ضريبة القيمة المضافة</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedLogin;
