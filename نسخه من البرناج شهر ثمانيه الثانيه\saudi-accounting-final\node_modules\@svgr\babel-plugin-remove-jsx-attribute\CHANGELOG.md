# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [5.4.0](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-remove-jsx-attribute/compare/v5.3.1...v5.4.0) (2020-04-27)

**Note:** Version bump only for package @svgr/babel-plugin-remove-jsx-attribute





## [5.0.1](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-remove-jsx-attribute/compare/v5.0.0...v5.0.1) (2019-12-29)


### Bug Fixes

* fix engines in package.json ([a45d6fc](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-remove-jsx-attribute/commit/a45d6fc8b43402bec60ed4e9273f90fdc65a23a7))





# [4.2.0](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-remove-jsx-attribute/compare/v4.1.0...v4.2.0) (2019-04-11)

**Note:** Version bump only for package @svgr/babel-plugin-remove-jsx-attribute





## [4.0.3](https://github.com/gregberge/svgr/compare/v4.0.2...v4.0.3) (2018-11-13)


### Bug Fixes

* **babel-plugin:** fix usage of spread attribute([#231](https://github.com/gregberge/svgr/issues/231)) ([4186953](https://github.com/gregberge/svgr/commit/4186953))





# [4.0.0](https://github.com/gregberge/svgr/compare/v3.1.0...v4.0.0) (2018-11-04)


### Features

* **v4:** new architecture ([ac8b8ca](https://github.com/gregberge/svgr/commit/ac8b8ca))


### BREAKING CHANGES

* **v4:** - `template` option must now returns a Babel AST
- `@svgr/core` does not include svgo & prettier by default
