/* صفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  direction: rtl;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
  background-size: cover;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 10;
}

.login-card .ant-card-body {
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo {
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 4rem;
  color: #1890ff;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(24, 144, 255, 0.3));
}

.login-title {
  margin-bottom: 8px !important;
  color: #262626;
  font-weight: 700;
  background: linear-gradient(135deg, #262626, #595959);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #8c8c8c;
  font-size: 1rem;
  font-weight: 500;
}

.login-form {
  margin-top: 20px;
}

.login-form .ant-form-item-label > label {
  font-weight: 600;
  color: #262626;
}

.login-form .ant-input,
.login-form .ant-input-password {
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  height: 50px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.login-form .ant-input:focus,
.login-form .ant-input-password:focus,
.login-form .ant-input-focused,
.login-form .ant-input-password-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
}

.login-form .ant-input-prefix {
  color: #8c8c8c;
  font-size: 16px;
  margin-left: 12px;
}

.login-form .ant-input-password-icon {
  color: #8c8c8c;
}

.login-button {
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.demo-button {
  height: 45px;
  border-radius: 12px;
  font-weight: 500;
  border: 2px dashed #d9d9d9;
  color: #595959;
  transition: all 0.3s ease;
  text-align: right;
}

.demo-button:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
  transform: translateY(-1px);
}

.login-error {
  margin-bottom: 20px;
  border-radius: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.footer-text {
  font-size: 12px;
  color: #bfbfbf;
}

/* تحسين Checkbox */
.login-form .ant-checkbox-wrapper {
  color: #595959;
  font-weight: 500;
}

.login-form .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* تحسين الروابط */
.login-form .ant-btn-link {
  color: #1890ff;
  font-weight: 500;
  padding: 0;
  height: auto;
}

.login-form .ant-btn-link:hover {
  color: #40a9ff;
}

/* تحسين الفواصل */
.login-form .ant-divider {
  margin: 24px 0;
  color: #8c8c8c;
  font-weight: 500;
}

.login-form .ant-divider-inner-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 16px;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .login-card .ant-card-body {
    padding: 30px 24px;
  }
  
  .logo-icon {
    font-size: 3rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .login-form .ant-input,
  .login-form .ant-input-password,
  .login-button,
  .demo-button {
    height: 45px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card .ant-card-body {
    padding: 24px 20px;
  }
  
  .logo-icon {
    font-size: 2.5rem;
  }
  
  .login-title {
    font-size: 1.3rem;
  }
  
  .login-form .ant-input,
  .login-form .ant-input-password,
  .login-button,
  .demo-button {
    height: 40px;
  }
}

/* تحسين الطباعة */
@media print {
  .login-container {
    display: none;
  }
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: fadeInUp 0.6s ease-out;
}

/* تحسين إمكانية الوصول */
.login-form .ant-input:focus-visible,
.login-form .ant-input-password:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.login-button:focus-visible,
.demo-button:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* تحسين الأداء */
.login-background,
.login-overlay {
  will-change: transform;
}

.login-card {
  will-change: transform;
}

.login-button,
.demo-button {
  will-change: transform, box-shadow;
}
