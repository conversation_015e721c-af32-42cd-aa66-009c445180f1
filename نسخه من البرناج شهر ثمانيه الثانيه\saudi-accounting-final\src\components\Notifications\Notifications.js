import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Notifications.css';

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filterType, setFilterType] = useState('all');
  const [showSettings, setShowSettings] = useState(false);

  const notificationTypes = [
    { value: 'low_stock', label: '📦 مخزون منخفض', color: '#f59e0b' },
    { value: 'payment_due', label: '💰 دفعة مستحقة', color: '#ef4444' },
    { value: 'invoice_overdue', label: '📋 فاتورة متأخرة', color: '#dc2626' },
    { value: 'new_order', label: '🛒 طلب جديد', color: '#10b981' },
    { value: 'system', label: '⚙️ نظام', color: '#6366f1' },
    { value: 'backup', label: '💾 نسخ احتياطي', color: '#8b5cf6' }
  ];

  useEffect(() => {
    loadNotifications();
    generateSystemNotifications();
  }, []);

  const loadNotifications = () => {
    try {
      const savedNotifications = JSON.parse(localStorage.getItem('notifications') || '[]');
      const sortedNotifications = savedNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setNotifications(sortedNotifications);
      setUnreadCount(sortedNotifications.filter(n => !n.read).length);
    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
    }
  };

  const generateSystemNotifications = () => {
    try {
      const existingNotifications = JSON.parse(localStorage.getItem('notifications') || '[]');
      const today = new Date().toDateString();
      
      // تحقق من وجود إشعارات اليوم
      const todayNotifications = existingNotifications.filter(n => 
        new Date(n.createdAt).toDateString() === today
      );

      const newNotifications = [];

      // إشعارات المخزون المنخفض
      const lowStockProducts = database.getLowStockProducts();
      if (lowStockProducts.length > 0 && !todayNotifications.some(n => n.type === 'low_stock')) {
        newNotifications.push({
          id: Date.now() + Math.random(),
          type: 'low_stock',
          title: 'تحذير: مخزون منخفض',
          message: `يوجد ${lowStockProducts.length} منتج بمخزون منخفض يحتاج إعادة تموين`,
          createdAt: new Date().toISOString(),
          read: false,
          priority: 'high',
          data: { products: lowStockProducts.slice(0, 3) }
        });
      }

      // إشعارات المنتجات نافدة المخزون
      const outOfStockProducts = database.getOutOfStockProducts();
      if (outOfStockProducts.length > 0 && !todayNotifications.some(n => n.type === 'out_of_stock')) {
        newNotifications.push({
          id: Date.now() + Math.random() + 1,
          type: 'low_stock',
          title: 'تحذير: نفد المخزون',
          message: `يوجد ${outOfStockProducts.length} منتج نفد مخزونه تماماً`,
          createdAt: new Date().toISOString(),
          read: false,
          priority: 'urgent',
          data: { products: outOfStockProducts.slice(0, 3) }
        });
      }

      // إشعار النسخ الاحتياطي
      const lastBackup = localStorage.getItem('last_backup_date');
      const daysSinceBackup = lastBackup ? 
        Math.floor((Date.now() - new Date(lastBackup)) / (1000 * 60 * 60 * 24)) : 30;
      
      if (daysSinceBackup >= 7 && !todayNotifications.some(n => n.type === 'backup')) {
        newNotifications.push({
          id: Date.now() + Math.random() + 2,
          type: 'backup',
          title: 'تذكير: النسخ الاحتياطي',
          message: `لم يتم إنشاء نسخة احتياطية منذ ${daysSinceBackup} يوم`,
          createdAt: new Date().toISOString(),
          read: false,
          priority: 'medium',
          action: 'backup'
        });
      }

      // إشعار ترحيب للمستخدمين الجدد
      if (existingNotifications.length === 0) {
        newNotifications.push({
          id: Date.now() + Math.random() + 3,
          type: 'system',
          title: 'مرحباً بك في نظام المحاسبة السعودي',
          message: 'نظام محاسبة متكامل مع دعم ضريبة القيمة المضافة والفواتير الإلكترونية',
          createdAt: new Date().toISOString(),
          read: false,
          priority: 'low'
        });
      }

      if (newNotifications.length > 0) {
        const allNotifications = [...existingNotifications, ...newNotifications];
        localStorage.setItem('notifications', JSON.stringify(allNotifications));
        loadNotifications();
      }

    } catch (error) {
      console.error('خطأ في توليد الإشعارات:', error);
    }
  };

  const markAsRead = (id) => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    );
    setNotifications(updatedNotifications);
    localStorage.setItem('notifications', JSON.stringify(updatedNotifications));
    setUnreadCount(updatedNotifications.filter(n => !n.read).length);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map(notification => ({
      ...notification,
      read: true
    }));
    setNotifications(updatedNotifications);
    localStorage.setItem('notifications', JSON.stringify(updatedNotifications));
    setUnreadCount(0);
  };

  const deleteNotification = (id) => {
    const updatedNotifications = notifications.filter(n => n.id !== id);
    setNotifications(updatedNotifications);
    localStorage.setItem('notifications', JSON.stringify(updatedNotifications));
    setUnreadCount(updatedNotifications.filter(n => !n.read).length);
  };

  const clearAllNotifications = () => {
    if (window.confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
      setNotifications([]);
      localStorage.setItem('notifications', JSON.stringify([]));
      setUnreadCount(0);
    }
  };

  const getTypeInfo = (type) => {
    const typeObj = notificationTypes.find(t => t.value === type);
    return typeObj || { label: type, color: '#64748b' };
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return '#dc2626';
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6366f1';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filterType === 'all') return true;
    if (filterType === 'unread') return !notification.read;
    return notification.type === filterType;
  });

  const handleAction = (notification) => {
    if (notification.action === 'backup') {
      // يمكن إضافة منطق للانتقال لصفحة النسخ الاحتياطي
      window.location.hash = '#backup';
    }
    markAsRead(notification.id);
  };

  return (
    <div className="notifications-container">
      <div className="notifications-header">
        <div className="header-content">
          <h1>🔔 الإشعارات</h1>
          <div className="notification-badge">
            {unreadCount > 0 && <span className="unread-count">{unreadCount}</span>}
          </div>
        </div>
        <p>تتبع التحديثات والتنبيهات المهمة</p>
      </div>

      {/* Controls */}
      <div className="notifications-controls">
        <div className="filter-controls">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الإشعارات</option>
            <option value="unread">غير مقروءة</option>
            {notificationTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div className="action-controls">
          {unreadCount > 0 && (
            <button onClick={markAllAsRead} className="mark-all-btn">
              ✅ تحديد الكل كمقروء
            </button>
          )}
          <button onClick={clearAllNotifications} className="clear-all-btn">
            🗑️ حذف الكل
          </button>
          <button 
            onClick={() => setShowSettings(!showSettings)} 
            className="settings-btn"
          >
            ⚙️ الإعدادات
          </button>
        </div>
      </div>

      {/* Notifications Settings */}
      {showSettings && (
        <div className="notification-settings">
          <h3>⚙️ إعدادات الإشعارات</h3>
          <div className="settings-grid">
            <label className="setting-item">
              <input type="checkbox" defaultChecked />
              <span>إشعارات المخزون المنخفض</span>
            </label>
            <label className="setting-item">
              <input type="checkbox" defaultChecked />
              <span>إشعارات الدفعات المستحقة</span>
            </label>
            <label className="setting-item">
              <input type="checkbox" defaultChecked />
              <span>إشعارات النسخ الاحتياطي</span>
            </label>
            <label className="setting-item">
              <input type="checkbox" defaultChecked />
              <span>إشعارات النظام</span>
            </label>
          </div>
        </div>
      )}

      {/* Notifications List */}
      <div className="notifications-list">
        {filteredNotifications.length === 0 ? (
          <div className="no-notifications">
            <div className="no-notifications-icon">🔕</div>
            <h3>لا توجد إشعارات</h3>
            <p>ستظهر الإشعارات هنا عند توفرها</p>
          </div>
        ) : (
          filteredNotifications.map(notification => {
            const typeInfo = getTypeInfo(notification.type);
            const priorityColor = getPriorityColor(notification.priority);
            
            return (
              <div 
                key={notification.id} 
                className={`notification-item ${!notification.read ? 'unread' : ''}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="notification-content">
                  <div className="notification-header">
                    <div className="notification-type" style={{ color: typeInfo.color }}>
                      {typeInfo.label}
                    </div>
                    <div className="notification-time">
                      {new Date(notification.createdAt).toLocaleString('ar-SA')}
                    </div>
                  </div>
                  
                  <h4 className="notification-title">{notification.title}</h4>
                  <p className="notification-message">{notification.message}</p>
                  
                  {notification.data && notification.data.products && (
                    <div className="notification-data">
                      <strong>المنتجات المتأثرة:</strong>
                      <ul>
                        {notification.data.products.map(product => (
                          <li key={product.id}>
                            {product.name} - المخزون: {product.stock}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  <div className="notification-footer">
                    <div 
                      className="priority-indicator"
                      style={{ backgroundColor: priorityColor }}
                      title={`أولوية: ${notification.priority}`}
                    ></div>
                    
                    <div className="notification-actions">
                      {notification.action && (
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAction(notification);
                          }}
                          className="action-btn"
                        >
                          اتخاذ إجراء
                        </button>
                      )}
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteNotification(notification.id);
                        }}
                        className="delete-btn"
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
                
                {!notification.read && <div className="unread-indicator"></div>}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default Notifications;
