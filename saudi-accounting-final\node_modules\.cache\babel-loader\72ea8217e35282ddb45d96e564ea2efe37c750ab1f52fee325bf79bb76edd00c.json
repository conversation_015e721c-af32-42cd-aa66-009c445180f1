{"ast": null, "code": "import React,{useState,useEffect}from'react';import database from'../../utils/database';import'./EnhancedDashboard.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EnhancedDashboard=_ref=>{let{user,onLogout,onNavigate}=_ref;const[stats,setStats]=useState({totalSales:0,totalInvoices:0,totalProducts:0,totalCustomers:0,lowStockProducts:0,todaySales:0,monthSales:0,pendingInvoices:0});const[recentInvoices,setRecentInvoices]=useState([]);const[lowStockProducts,setLowStockProducts]=useState([]);const[salesChart,setSalesChart]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{loadDashboardData();const interval=setInterval(loadDashboardData,30000);// Refresh every 30 seconds\nreturn()=>clearInterval(interval);},[]);const loadDashboardData=async()=>{try{setLoading(true);// Load all data\nconst invoices=database.getInvoices();const products=database.getProducts();const customers=database.getCustomers();// Calculate statistics\nconst totalSales=invoices.reduce((sum,inv)=>sum+inv.total,0);const lowStock=products.filter(p=>p.stock<=5);// Today's sales\nconst today=new Date().toDateString();const todayInvoices=invoices.filter(inv=>new Date(inv.invoiceDate).toDateString()===today);const todaySales=todayInvoices.reduce((sum,inv)=>sum+inv.total,0);// This month's sales\nconst currentMonth=new Date().getMonth();const currentYear=new Date().getFullYear();const monthInvoices=invoices.filter(inv=>{const invDate=new Date(inv.invoiceDate);return invDate.getMonth()===currentMonth&&invDate.getFullYear()===currentYear;});const monthSales=monthInvoices.reduce((sum,inv)=>sum+inv.total,0);// Pending invoices\nconst pendingInvoices=invoices.filter(inv=>inv.status==='pending').length;// Recent invoices (last 5)\nconst recent=invoices.sort((a,b)=>new Date(b.invoiceDate)-new Date(a.invoiceDate)).slice(0,5);// Sales chart data (last 7 days)\nconst chartData=[];for(let i=6;i>=0;i--){const date=new Date();date.setDate(date.getDate()-i);const dayInvoices=invoices.filter(inv=>new Date(inv.invoiceDate).toDateString()===date.toDateString());const dayTotal=dayInvoices.reduce((sum,inv)=>sum+inv.total,0);chartData.push({date:date.toLocaleDateString('ar-SA',{weekday:'short'}),sales:dayTotal,invoices:dayInvoices.length});}setStats({totalSales,totalInvoices:invoices.length,totalProducts:products.length,totalCustomers:customers.length,lowStockProducts:lowStock.length,todaySales,monthSales,pendingInvoices});setRecentInvoices(recent);setLowStockProducts(lowStock.slice(0,5));setSalesChart(chartData);}catch(error){console.error('خطأ في تحميل بيانات لوحة التحكم:',error);}finally{setLoading(false);}};const formatCurrency=amount=>{return new Intl.NumberFormat('ar-SA',{style:'currency',currency:'SAR',minimumFractionDigits:2}).format(amount);};const getGreeting=()=>{const hour=new Date().getHours();if(hour<12)return'صباح الخير';if(hour<17)return'مساء الخير';return'مساء الخير';};const quickActions=[{id:'pos',title:'نقطة البيع',description:'بدء عملية بيع جديدة',icon:'🛒',color:'primary',action:()=>onNavigate('pos')},{id:'products',title:'إدارة المنتجات',description:'إضافة وتعديل المنتجات',icon:'📦',color:'success',action:()=>onNavigate('products')},{id:'invoices',title:'الفواتير',description:'عرض وإدارة الفواتير',icon:'🧾',color:'info',action:()=>onNavigate('invoices')},{id:'reports',title:'التقارير',description:'تقارير المبيعات والأرباح',icon:'📊',color:'warning',action:()=>onNavigate('reports')}];if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"enhanced-dashboard\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-content\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[getGreeting(),\"\\u060C \",(user===null||user===void 0?void 0:user.username)||'المستخدم']}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0646\\u0638\\u0627\\u0645 Aronim EXP \\u0644\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:\"current-time\",children:new Date().toLocaleDateString('ar-SA',{weekday:'long',year:'numeric',month:'long',day:'numeric'})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"welcome-actions\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>onNavigate('pos'),className:\"btn btn-primary btn-lg\",children:\"\\uD83D\\uDED2 \\u0628\\u062F\\u0621 \\u0627\\u0644\\u0628\\u064A\\u0639\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDCB0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatCurrency(stats.totalSales)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card success\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDCC8\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatCurrency(stats.todaySales)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83E\\uDDFE\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.totalInvoices}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card warning\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDCE6\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.totalProducts}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card secondary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDC65\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.totalCustomers}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card danger\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:stats.lowStockProducts}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0645\\u0646\\u062E\\u0641\\u0636\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"quick-actions-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:\"quick-actions-grid\",children:quickActions.map(action=>/*#__PURE__*/_jsxs(\"div\",{className:`quick-action-card ${action.color}`,onClick:action.action,children:[/*#__PURE__*/_jsx(\"div\",{className:\"action-icon\",children:action.icon}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:action.title}),/*#__PURE__*/_jsx(\"p\",{children:action.description})]})]},action.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0622\\u062E\\u0631 7 \\u0623\\u064A\\u0627\\u0645\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"div\",{className:\"sales-chart\",children:salesChart.map((day,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"chart-bar\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bar\",style:{height:`${Math.max(10,day.sales/Math.max(...salesChart.map(d=>d.sales))*100)}%`},title:`${day.date}: ${formatCurrency(day.sales)}`}),/*#__PURE__*/_jsx(\"div\",{className:\"bar-label\",children:day.date}),/*#__PURE__*/_jsx(\"div\",{className:\"bar-value\",children:formatCurrency(day.sales)})]},index))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0622\\u062E\\u0631 \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onNavigate('invoices'),className:\"btn btn-sm btn-outline\",children:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:recentInvoices.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"empty-state\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u062D\\u062F\\u064A\\u062B\\u0629\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"invoices-list\",children:recentInvoices.map(invoice=>/*#__PURE__*/_jsxs(\"div\",{className:\"invoice-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"invoice-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"invoice-number\",children:invoice.invoiceNumber}),/*#__PURE__*/_jsx(\"div\",{className:\"invoice-date\",children:new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')})]}),/*#__PURE__*/_jsx(\"div\",{className:\"invoice-amount\",children:formatCurrency(invoice.total)})]},invoice.id))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0628\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0645\\u0646\\u062E\\u0641\\u0636\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onNavigate('products'),className:\"btn btn-sm btn-outline\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:lowStockProducts.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"empty-state\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062A\\u0648\\u0641\\u0631\\u0629 \\u0628\\u0643\\u0645\\u064A\\u0627\\u062A \\u0643\\u0627\\u0641\\u064A\\u0629\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"products-list\",children:lowStockProducts.map(product=>/*#__PURE__*/_jsxs(\"div\",{className:\"product-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"product-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"product-name\",children:product.name}),/*#__PURE__*/_jsx(\"div\",{className:\"product-category\",children:product.category})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"product-stock\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stock-value danger\",children:product.stock}),/*#__PURE__*/_jsx(\"span\",{className:\"stock-label\",children:\"\\u0645\\u062A\\u0628\\u0642\\u064A\"})]})]},product.id))})})]})]})]});};export default EnhancedDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsx", "_jsx", "jsxs", "_jsxs", "EnhancedDashboard", "_ref", "user", "onLogout", "onNavigate", "stats", "setStats", "totalSales", "totalInvoices", "totalProducts", "totalCustomers", "lowStockProducts", "todaySales", "monthSales", "pendingInvoices", "recentInvoices", "setRecentInvoices", "setLowStockProducts", "salesChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "loadDashboardData", "interval", "setInterval", "clearInterval", "invoices", "getInvoices", "products", "getProducts", "customers", "getCustomers", "reduce", "sum", "inv", "total", "lowStock", "filter", "p", "stock", "today", "Date", "toDateString", "todayInvoices", "invoiceDate", "currentMonth", "getMonth", "currentYear", "getFullYear", "monthInvoices", "invDate", "status", "length", "recent", "sort", "a", "b", "slice", "chartData", "i", "date", "setDate", "getDate", "dayInvoices", "dayTotal", "push", "toLocaleDateString", "weekday", "sales", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "getGreeting", "hour", "getHours", "quickActions", "id", "title", "description", "icon", "color", "action", "className", "children", "username", "year", "month", "day", "onClick", "map", "index", "height", "Math", "max", "d", "invoice", "invoiceNumber", "product", "name", "category"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Dashboard/EnhancedDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './EnhancedDashboard.css';\n\nconst EnhancedDashboard = ({ user, onLogout, onNavigate }) => {\n  const [stats, setStats] = useState({\n    totalSales: 0,\n    totalInvoices: 0,\n    totalProducts: 0,\n    totalCustomers: 0,\n    lowStockProducts: 0,\n    todaySales: 0,\n    monthSales: 0,\n    pendingInvoices: 0\n  });\n  \n  const [recentInvoices, setRecentInvoices] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [salesChart, setSalesChart] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadDashboardData();\n    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load all data\n      const invoices = database.getInvoices();\n      const products = database.getProducts();\n      const customers = database.getCustomers();\n      \n      // Calculate statistics\n      const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);\n      const lowStock = products.filter(p => p.stock <= 5);\n      \n      // Today's sales\n      const today = new Date().toDateString();\n      const todayInvoices = invoices.filter(inv => \n        new Date(inv.invoiceDate).toDateString() === today\n      );\n      const todaySales = todayInvoices.reduce((sum, inv) => sum + inv.total, 0);\n      \n      // This month's sales\n      const currentMonth = new Date().getMonth();\n      const currentYear = new Date().getFullYear();\n      const monthInvoices = invoices.filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate.getMonth() === currentMonth && invDate.getFullYear() === currentYear;\n      });\n      const monthSales = monthInvoices.reduce((sum, inv) => sum + inv.total, 0);\n      \n      // Pending invoices\n      const pendingInvoices = invoices.filter(inv => inv.status === 'pending').length;\n      \n      // Recent invoices (last 5)\n      const recent = invoices\n        .sort((a, b) => new Date(b.invoiceDate) - new Date(a.invoiceDate))\n        .slice(0, 5);\n      \n      // Sales chart data (last 7 days)\n      const chartData = [];\n      for (let i = 6; i >= 0; i--) {\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dayInvoices = invoices.filter(inv => \n          new Date(inv.invoiceDate).toDateString() === date.toDateString()\n        );\n        const dayTotal = dayInvoices.reduce((sum, inv) => sum + inv.total, 0);\n        chartData.push({\n          date: date.toLocaleDateString('ar-SA', { weekday: 'short' }),\n          sales: dayTotal,\n          invoices: dayInvoices.length\n        });\n      }\n      \n      setStats({\n        totalSales,\n        totalInvoices: invoices.length,\n        totalProducts: products.length,\n        totalCustomers: customers.length,\n        lowStockProducts: lowStock.length,\n        todaySales,\n        monthSales,\n        pendingInvoices\n      });\n      \n      setRecentInvoices(recent);\n      setLowStockProducts(lowStock.slice(0, 5));\n      setSalesChart(chartData);\n      \n    } catch (error) {\n      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR',\n      minimumFractionDigits: 2\n    }).format(amount);\n  };\n\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'صباح الخير';\n    if (hour < 17) return 'مساء الخير';\n    return 'مساء الخير';\n  };\n\n  const quickActions = [\n    {\n      id: 'pos',\n      title: 'نقطة البيع',\n      description: 'بدء عملية بيع جديدة',\n      icon: '🛒',\n      color: 'primary',\n      action: () => onNavigate('pos')\n    },\n    {\n      id: 'products',\n      title: 'إدارة المنتجات',\n      description: 'إضافة وتعديل المنتجات',\n      icon: '📦',\n      color: 'success',\n      action: () => onNavigate('products')\n    },\n    {\n      id: 'invoices',\n      title: 'الفواتير',\n      description: 'عرض وإدارة الفواتير',\n      icon: '🧾',\n      color: 'info',\n      action: () => onNavigate('invoices')\n    },\n    {\n      id: 'reports',\n      title: 'التقارير',\n      description: 'تقارير المبيعات والأرباح',\n      icon: '📊',\n      color: 'warning',\n      action: () => onNavigate('reports')\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>جاري تحميل لوحة التحكم...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"enhanced-dashboard\">\n      {/* Welcome Section */}\n      <div className=\"welcome-section\">\n        <div className=\"welcome-content\">\n          <h1>{getGreeting()}، {user?.username || 'المستخدم'}</h1>\n          <p>مرحباً بك في نظام Aronim EXP للمحاسبة</p>\n          <div className=\"current-time\">\n            {new Date().toLocaleDateString('ar-SA', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </div>\n        </div>\n        <div className=\"welcome-actions\">\n          <button onClick={() => onNavigate('pos')} className=\"btn btn-primary btn-lg\">\n            🛒 بدء البيع\n          </button>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card primary\">\n          <div className=\"stat-icon\">💰</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{formatCurrency(stats.totalSales)}</div>\n            <div className=\"stat-label\">إجمالي المبيعات</div>\n          </div>\n        </div>\n        \n        <div className=\"stat-card success\">\n          <div className=\"stat-icon\">📈</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{formatCurrency(stats.todaySales)}</div>\n            <div className=\"stat-label\">مبيعات اليوم</div>\n          </div>\n        </div>\n        \n        <div className=\"stat-card info\">\n          <div className=\"stat-icon\">🧾</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{stats.totalInvoices}</div>\n            <div className=\"stat-label\">إجمالي الفواتير</div>\n          </div>\n        </div>\n        \n        <div className=\"stat-card warning\">\n          <div className=\"stat-icon\">📦</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{stats.totalProducts}</div>\n            <div className=\"stat-label\">المنتجات</div>\n          </div>\n        </div>\n        \n        <div className=\"stat-card secondary\">\n          <div className=\"stat-icon\">👥</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{stats.totalCustomers}</div>\n            <div className=\"stat-label\">العملاء</div>\n          </div>\n        </div>\n        \n        <div className=\"stat-card danger\">\n          <div className=\"stat-icon\">⚠️</div>\n          <div className=\"stat-content\">\n            <div className=\"stat-value\">{stats.lowStockProducts}</div>\n            <div className=\"stat-label\">مخزون منخفض</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"quick-actions-section\">\n        <h2>الإجراءات السريعة</h2>\n        <div className=\"quick-actions-grid\">\n          {quickActions.map(action => (\n            <div\n              key={action.id}\n              className={`quick-action-card ${action.color}`}\n              onClick={action.action}\n            >\n              <div className=\"action-icon\">{action.icon}</div>\n              <div className=\"action-content\">\n                <h3>{action.title}</h3>\n                <p>{action.description}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Content Grid */}\n      <div className=\"dashboard-content\">\n        {/* Sales Chart */}\n        <div className=\"dashboard-card\">\n          <div className=\"card-header\">\n            <h3>مبيعات آخر 7 أيام</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"sales-chart\">\n              {salesChart.map((day, index) => (\n                <div key={index} className=\"chart-bar\">\n                  <div \n                    className=\"bar\" \n                    style={{ \n                      height: `${Math.max(10, (day.sales / Math.max(...salesChart.map(d => d.sales))) * 100)}%` \n                    }}\n                    title={`${day.date}: ${formatCurrency(day.sales)}`}\n                  ></div>\n                  <div className=\"bar-label\">{day.date}</div>\n                  <div className=\"bar-value\">{formatCurrency(day.sales)}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Invoices */}\n        <div className=\"dashboard-card\">\n          <div className=\"card-header\">\n            <h3>آخر الفواتير</h3>\n            <button onClick={() => onNavigate('invoices')} className=\"btn btn-sm btn-outline\">\n              عرض الكل\n            </button>\n          </div>\n          <div className=\"card-body\">\n            {recentInvoices.length === 0 ? (\n              <div className=\"empty-state\">\n                <p>لا توجد فواتير حديثة</p>\n              </div>\n            ) : (\n              <div className=\"invoices-list\">\n                {recentInvoices.map(invoice => (\n                  <div key={invoice.id} className=\"invoice-item\">\n                    <div className=\"invoice-info\">\n                      <div className=\"invoice-number\">{invoice.invoiceNumber}</div>\n                      <div className=\"invoice-date\">\n                        {new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')}\n                      </div>\n                    </div>\n                    <div className=\"invoice-amount\">\n                      {formatCurrency(invoice.total)}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Low Stock Products */}\n        <div className=\"dashboard-card\">\n          <div className=\"card-header\">\n            <h3>منتجات بمخزون منخفض</h3>\n            <button onClick={() => onNavigate('products')} className=\"btn btn-sm btn-outline\">\n              إدارة المنتجات\n            </button>\n          </div>\n          <div className=\"card-body\">\n            {lowStockProducts.length === 0 ? (\n              <div className=\"empty-state\">\n                <p>جميع المنتجات متوفرة بكميات كافية</p>\n              </div>\n            ) : (\n              <div className=\"products-list\">\n                {lowStockProducts.map(product => (\n                  <div key={product.id} className=\"product-item\">\n                    <div className=\"product-info\">\n                      <div className=\"product-name\">{product.name}</div>\n                      <div className=\"product-category\">{product.category}</div>\n                    </div>\n                    <div className=\"product-stock\">\n                      <span className=\"stock-value danger\">{product.stock}</span>\n                      <span className=\"stock-label\">متبقي</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjC,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAAoC,IAAnC,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,UAAW,CAAC,CAAAH,IAAA,CACvD,KAAM,CAACI,KAAK,CAAEC,QAAQ,CAAC,CAAGb,QAAQ,CAAC,CACjCc,UAAU,CAAE,CAAC,CACbC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,gBAAgB,CAAE,CAAC,CACnBC,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,eAAe,CAAE,CACnB,CAAC,CAAC,CAEF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACkB,gBAAgB,CAAEM,mBAAmB,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACyB,UAAU,CAAEC,aAAa,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd4B,iBAAiB,CAAC,CAAC,CACnB,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAACF,iBAAiB,CAAE,KAAK,CAAC,CAAE;AACxD,MAAO,IAAMG,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFD,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAK,QAAQ,CAAG/B,QAAQ,CAACgC,WAAW,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,CAAGjC,QAAQ,CAACkC,WAAW,CAAC,CAAC,CACvC,KAAM,CAAAC,SAAS,CAAGnC,QAAQ,CAACoC,YAAY,CAAC,CAAC,CAEzC;AACA,KAAM,CAAAxB,UAAU,CAAGmB,QAAQ,CAACM,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAAKD,GAAG,CAAGC,GAAG,CAACC,KAAK,CAAE,CAAC,CAAC,CACpE,KAAM,CAAAC,QAAQ,CAAGR,QAAQ,CAACS,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,KAAK,EAAI,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,aAAa,CAAGjB,QAAQ,CAACW,MAAM,CAACH,GAAG,EACvC,GAAI,CAAAO,IAAI,CAACP,GAAG,CAACU,WAAW,CAAC,CAACF,YAAY,CAAC,CAAC,GAAKF,KAC/C,CAAC,CACD,KAAM,CAAA5B,UAAU,CAAG+B,aAAa,CAACX,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAAKD,GAAG,CAAGC,GAAG,CAACC,KAAK,CAAE,CAAC,CAAC,CAEzE;AACA,KAAM,CAAAU,YAAY,CAAG,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAC1C,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAN,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAC5C,KAAM,CAAAC,aAAa,CAAGvB,QAAQ,CAACW,MAAM,CAACH,GAAG,EAAI,CAC3C,KAAM,CAAAgB,OAAO,CAAG,GAAI,CAAAT,IAAI,CAACP,GAAG,CAACU,WAAW,CAAC,CACzC,MAAO,CAAAM,OAAO,CAACJ,QAAQ,CAAC,CAAC,GAAKD,YAAY,EAAIK,OAAO,CAACF,WAAW,CAAC,CAAC,GAAKD,WAAW,CACrF,CAAC,CAAC,CACF,KAAM,CAAAlC,UAAU,CAAGoC,aAAa,CAACjB,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAAKD,GAAG,CAAGC,GAAG,CAACC,KAAK,CAAE,CAAC,CAAC,CAEzE;AACA,KAAM,CAAArB,eAAe,CAAGY,QAAQ,CAACW,MAAM,CAACH,GAAG,EAAIA,GAAG,CAACiB,MAAM,GAAK,SAAS,CAAC,CAACC,MAAM,CAE/E;AACA,KAAM,CAAAC,MAAM,CAAG3B,QAAQ,CACpB4B,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAf,IAAI,CAACe,CAAC,CAACZ,WAAW,CAAC,CAAG,GAAI,CAAAH,IAAI,CAACc,CAAC,CAACX,WAAW,CAAC,CAAC,CACjEa,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEd;AACA,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC3B,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAnB,IAAI,CAAC,CAAC,CACvBmB,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAGH,CAAC,CAAC,CAChC,KAAM,CAAAI,WAAW,CAAGrC,QAAQ,CAACW,MAAM,CAACH,GAAG,EACrC,GAAI,CAAAO,IAAI,CAACP,GAAG,CAACU,WAAW,CAAC,CAACF,YAAY,CAAC,CAAC,GAAKkB,IAAI,CAAClB,YAAY,CAAC,CACjE,CAAC,CACD,KAAM,CAAAsB,QAAQ,CAAGD,WAAW,CAAC/B,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAAKD,GAAG,CAAGC,GAAG,CAACC,KAAK,CAAE,CAAC,CAAC,CACrEuB,SAAS,CAACO,IAAI,CAAC,CACbL,IAAI,CAAEA,IAAI,CAACM,kBAAkB,CAAC,OAAO,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAC,CAAC,CAC5DC,KAAK,CAAEJ,QAAQ,CACftC,QAAQ,CAAEqC,WAAW,CAACX,MACxB,CAAC,CAAC,CACJ,CAEA9C,QAAQ,CAAC,CACPC,UAAU,CACVC,aAAa,CAAEkB,QAAQ,CAAC0B,MAAM,CAC9B3C,aAAa,CAAEmB,QAAQ,CAACwB,MAAM,CAC9B1C,cAAc,CAAEoB,SAAS,CAACsB,MAAM,CAChCzC,gBAAgB,CAAEyB,QAAQ,CAACgB,MAAM,CACjCxC,UAAU,CACVC,UAAU,CACVC,eACF,CAAC,CAAC,CAEFE,iBAAiB,CAACqC,MAAM,CAAC,CACzBpC,mBAAmB,CAACmB,QAAQ,CAACqB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CACzCtC,aAAa,CAACuC,SAAS,CAAC,CAE1B,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CAAC,OAAS,CACRhD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkD,cAAc,CAAIC,MAAM,EAAK,CACjC,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACN,MAAM,CAAC,CACnB,CAAC,CAED,KAAM,CAAAO,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAvC,IAAI,CAAC,CAAC,CAACwC,QAAQ,CAAC,CAAC,CAClC,GAAID,IAAI,CAAG,EAAE,CAAE,MAAO,YAAY,CAClC,GAAIA,IAAI,CAAG,EAAE,CAAE,MAAO,YAAY,CAClC,MAAO,YAAY,CACrB,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,CACnB,CACEC,EAAE,CAAE,KAAK,CACTC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,qBAAqB,CAClCC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAEA,CAAA,GAAMpF,UAAU,CAAC,KAAK,CAChC,CAAC,CACD,CACE+E,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,uBAAuB,CACpCC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAEA,CAAA,GAAMpF,UAAU,CAAC,UAAU,CACrC,CAAC,CACD,CACE+E,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,qBAAqB,CAClCC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAEA,CAAA,GAAMpF,UAAU,CAAC,UAAU,CACrC,CAAC,CACD,CACE+E,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,0BAA0B,CACvCC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAEA,CAAA,GAAMpF,UAAU,CAAC,SAAS,CACpC,CAAC,CACF,CAED,GAAIgB,OAAO,CAAE,CACX,mBACErB,KAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7F,IAAA,QAAK4F,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC5F,IAAA,MAAA6F,QAAA,CAAG,0HAAyB,CAAG,CAAC,EAC7B,CAAC,CAEV,CAEA,mBACE3F,KAAA,QAAK0F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAEjC3F,KAAA,QAAK0F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3F,KAAA,QAAK0F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3F,KAAA,OAAA2F,QAAA,EAAKX,WAAW,CAAC,CAAC,CAAC,SAAE,CAAC,CAAA7E,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEyF,QAAQ,GAAI,UAAU,EAAK,CAAC,cACxD9F,IAAA,MAAA6F,QAAA,CAAG,qJAAqC,CAAG,CAAC,cAC5C7F,IAAA,QAAK4F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B,GAAI,CAAAjD,IAAI,CAAC,CAAC,CAACyB,kBAAkB,CAAC,OAAO,CAAE,CACtCC,OAAO,CAAE,MAAM,CACfyB,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cACNjG,IAAA,QAAK4F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B7F,IAAA,WAAQkG,OAAO,CAAEA,CAAA,GAAM3F,UAAU,CAAC,KAAK,CAAE,CAACqF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,gEAE7E,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAGN3F,KAAA,QAAK0F,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3F,KAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEnB,cAAc,CAAClE,KAAK,CAACE,UAAU,CAAC,CAAM,CAAC,cACpEV,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uFAAe,CAAK,CAAC,EAC9C,CAAC,EACH,CAAC,cAEN3F,KAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEnB,cAAc,CAAClE,KAAK,CAACO,UAAU,CAAC,CAAM,CAAC,cACpEf,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qEAAY,CAAK,CAAC,EAC3C,CAAC,EACH,CAAC,cAEN3F,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAErF,KAAK,CAACG,aAAa,CAAM,CAAC,cACvDX,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uFAAe,CAAK,CAAC,EAC9C,CAAC,EACH,CAAC,cAEN3F,KAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAErF,KAAK,CAACI,aAAa,CAAM,CAAC,cACvDZ,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kDAAQ,CAAK,CAAC,EACvC,CAAC,EACH,CAAC,cAEN3F,KAAA,QAAK0F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAErF,KAAK,CAACK,cAAc,CAAM,CAAC,cACxDb,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,4CAAO,CAAK,CAAC,EACtC,CAAC,EACH,CAAC,cAEN3F,KAAA,QAAK0F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAErF,KAAK,CAACM,gBAAgB,CAAM,CAAC,cAC1Dd,IAAA,QAAK4F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,+DAAW,CAAK,CAAC,EAC1C,CAAC,EACH,CAAC,EACH,CAAC,cAGN3F,KAAA,QAAK0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC7F,IAAA,OAAA6F,QAAA,CAAI,mGAAiB,CAAI,CAAC,cAC1B7F,IAAA,QAAK4F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCR,YAAY,CAACc,GAAG,CAACR,MAAM,eACtBzF,KAAA,QAEE0F,SAAS,CAAE,qBAAqBD,MAAM,CAACD,KAAK,EAAG,CAC/CQ,OAAO,CAAEP,MAAM,CAACA,MAAO,CAAAE,QAAA,eAEvB7F,IAAA,QAAK4F,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEF,MAAM,CAACF,IAAI,CAAM,CAAC,cAChDvF,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7F,IAAA,OAAA6F,QAAA,CAAKF,MAAM,CAACJ,KAAK,CAAK,CAAC,cACvBvF,IAAA,MAAA6F,QAAA,CAAIF,MAAM,CAACH,WAAW,CAAI,CAAC,EACxB,CAAC,GARDG,MAAM,CAACL,EAST,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGNpF,KAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAEhC3F,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7F,IAAA,QAAK4F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B7F,IAAA,OAAA6F,QAAA,CAAI,oFAAiB,CAAI,CAAC,CACvB,CAAC,cACN7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB7F,IAAA,QAAK4F,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBxE,UAAU,CAAC8E,GAAG,CAAC,CAACF,GAAG,CAAEG,KAAK,gBACzBlG,KAAA,QAAiB0F,SAAS,CAAC,WAAW,CAAAC,QAAA,eACpC7F,IAAA,QACE4F,SAAS,CAAC,KAAK,CACfd,KAAK,CAAE,CACLuB,MAAM,CAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,CAAGN,GAAG,CAAC1B,KAAK,CAAG+B,IAAI,CAACC,GAAG,CAAC,GAAGlF,UAAU,CAAC8E,GAAG,CAACK,CAAC,EAAIA,CAAC,CAACjC,KAAK,CAAC,CAAC,CAAI,GAAG,CAAC,GACxF,CAAE,CACFgB,KAAK,CAAE,GAAGU,GAAG,CAAClC,IAAI,KAAKW,cAAc,CAACuB,GAAG,CAAC1B,KAAK,CAAC,EAAG,CAC/C,CAAC,cACPvE,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEI,GAAG,CAAClC,IAAI,CAAM,CAAC,cAC3C/D,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEnB,cAAc,CAACuB,GAAG,CAAC1B,KAAK,CAAC,CAAM,CAAC,GATpD6B,KAUL,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,cAGNlG,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3F,KAAA,QAAK0F,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7F,IAAA,OAAA6F,QAAA,CAAI,qEAAY,CAAI,CAAC,cACrB7F,IAAA,WAAQkG,OAAO,CAAEA,CAAA,GAAM3F,UAAU,CAAC,UAAU,CAAE,CAACqF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,6CAElF,CAAQ,CAAC,EACN,CAAC,cACN7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB3E,cAAc,CAACqC,MAAM,GAAK,CAAC,cAC1BvD,IAAA,QAAK4F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B7F,IAAA,MAAA6F,QAAA,CAAG,2GAAoB,CAAG,CAAC,CACxB,CAAC,cAEN7F,IAAA,QAAK4F,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3B3E,cAAc,CAACiF,GAAG,CAACM,OAAO,eACzBvG,KAAA,QAAsB0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5C3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEY,OAAO,CAACC,aAAa,CAAM,CAAC,cAC7D1G,IAAA,QAAK4F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B,GAAI,CAAAjD,IAAI,CAAC6D,OAAO,CAAC1D,WAAW,CAAC,CAACsB,kBAAkB,CAAC,OAAO,CAAC,CACvD,CAAC,EACH,CAAC,cACNrE,IAAA,QAAK4F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BnB,cAAc,CAAC+B,OAAO,CAACnE,KAAK,CAAC,CAC3B,CAAC,GATEmE,OAAO,CAACnB,EAUb,CACN,CAAC,CACC,CACN,CACE,CAAC,EACH,CAAC,cAGNpF,KAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3F,KAAA,QAAK0F,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7F,IAAA,OAAA6F,QAAA,CAAI,0GAAmB,CAAI,CAAC,cAC5B7F,IAAA,WAAQkG,OAAO,CAAEA,CAAA,GAAM3F,UAAU,CAAC,UAAU,CAAE,CAACqF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,iFAElF,CAAQ,CAAC,EACN,CAAC,cACN7F,IAAA,QAAK4F,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB/E,gBAAgB,CAACyC,MAAM,GAAK,CAAC,cAC5BvD,IAAA,QAAK4F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B7F,IAAA,MAAA6F,QAAA,CAAG,oLAAiC,CAAG,CAAC,CACrC,CAAC,cAEN7F,IAAA,QAAK4F,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3B/E,gBAAgB,CAACqF,GAAG,CAACQ,OAAO,eAC3BzG,KAAA,QAAsB0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5C3F,KAAA,QAAK0F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7F,IAAA,QAAK4F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEc,OAAO,CAACC,IAAI,CAAM,CAAC,cAClD5G,IAAA,QAAK4F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEc,OAAO,CAACE,QAAQ,CAAM,CAAC,EACvD,CAAC,cACN3G,KAAA,QAAK0F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B7F,IAAA,SAAM4F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEc,OAAO,CAACjE,KAAK,CAAO,CAAC,cAC3D1C,IAAA,SAAM4F,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,gCAAK,CAAM,CAAC,EACvC,CAAC,GAREc,OAAO,CAACrB,EASb,CACN,CAAC,CACC,CACN,CACE,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}