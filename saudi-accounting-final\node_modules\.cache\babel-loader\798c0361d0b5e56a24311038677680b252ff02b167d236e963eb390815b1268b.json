{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Quotations\\\\Quotations.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Quotations.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quotations = () => {\n  _s();\n  const [quotations, setQuotations] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingQuotation, setEditingQuotation] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [newQuotation, setNewQuotation] = useState({\n    quotationNumber: '',\n    customerId: '',\n    quotationDate: new Date().toISOString().split('T')[0],\n    validUntil: '',\n    status: 'draft',\n    items: [{\n      productId: '',\n      quantity: 1,\n      price: 0,\n      discount: 0,\n      total: 0\n    }],\n    subtotal: 0,\n    discountAmount: 0,\n    vatAmount: 0,\n    total: 0,\n    terms: '',\n    notes: '',\n    paymentTerms: 'نقداً عند التسليم'\n  });\n  const quotationStatuses = [{\n    value: 'draft',\n    label: '📝 مسودة',\n    color: '#6b7280'\n  }, {\n    value: 'sent',\n    label: '📤 مرسل',\n    color: '#3b82f6'\n  }, {\n    value: 'accepted',\n    label: '✅ مقبول',\n    color: '#10b981'\n  }, {\n    value: 'rejected',\n    label: '❌ مرفوض',\n    color: '#ef4444'\n  }, {\n    value: 'expired',\n    label: '⏰ منتهي الصلاحية',\n    color: '#f59e0b'\n  }, {\n    value: 'converted',\n    label: '🔄 تم التحويل لفاتورة',\n    color: '#8b5cf6'\n  }];\n  const paymentTermsOptions = ['نقداً عند التسليم', 'الدفع خلال 15 يوم', 'الدفع خلال 30 يوم', 'الدفع خلال 60 يوم', 'دفع مقدم 50%', 'دفع على دفعات', 'تحويل بنكي', 'شروط خاصة'];\n  useEffect(() => {\n    loadData();\n    generateQuotationNumber();\n  }, []);\n  const loadData = () => {\n    try {\n      const quotationsData = database.getQuotations();\n      const customersData = database.getCustomers();\n      const productsData = database.getProducts();\n      setQuotations(quotationsData);\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n  const generateQuotationNumber = () => {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    const quotationNumber = `QUO-${year}${month}${day}-${random}`;\n    setNewQuotation(prev => ({\n      ...prev,\n      quotationNumber\n    }));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewQuotation(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleItemChange = (index, field, value) => {\n    const updatedItems = [...newQuotation.items];\n    updatedItems[index] = {\n      ...updatedItems[index],\n      [field]: value\n    };\n\n    // Auto-fill product price\n    if (field === 'productId' && value) {\n      const selectedProduct = products.find(p => p.id === parseInt(value));\n      if (selectedProduct) {\n        updatedItems[index].price = selectedProduct.price;\n      }\n    }\n\n    // Calculate item total\n    if (field === 'quantity' || field === 'price' || field === 'discount') {\n      const quantity = parseFloat(updatedItems[index].quantity) || 0;\n      const price = parseFloat(updatedItems[index].price) || 0;\n      const discount = parseFloat(updatedItems[index].discount) || 0;\n      const itemTotal = quantity * price - discount;\n      updatedItems[index].total = Math.max(0, itemTotal);\n    }\n    setNewQuotation(prev => ({\n      ...prev,\n      items: updatedItems\n    }));\n    calculateTotals(updatedItems);\n  };\n  const calculateTotals = (items = newQuotation.items) => {\n    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);\n    const discountAmount = parseFloat(newQuotation.discountAmount) || 0;\n    const afterDiscount = subtotal - discountAmount;\n    const vatRate = 0.15; // 15% VAT\n    const vatAmount = afterDiscount * vatRate;\n    const total = afterDiscount + vatAmount;\n    setNewQuotation(prev => ({\n      ...prev,\n      subtotal,\n      vatAmount,\n      total: Math.max(0, total)\n    }));\n  };\n  const addItem = () => {\n    setNewQuotation(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        productId: '',\n        quantity: 1,\n        price: 0,\n        discount: 0,\n        total: 0\n      }]\n    }));\n  };\n  const removeItem = index => {\n    if (newQuotation.items.length > 1) {\n      const updatedItems = newQuotation.items.filter((_, i) => i !== index);\n      setNewQuotation(prev => ({\n        ...prev,\n        items: updatedItems\n      }));\n      calculateTotals(updatedItems);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      // Set valid until date if not set (30 days from quotation date)\n      let validUntil = newQuotation.validUntil;\n      if (!validUntil) {\n        const quotationDate = new Date(newQuotation.quotationDate);\n        quotationDate.setDate(quotationDate.getDate() + 30);\n        validUntil = quotationDate.toISOString().split('T')[0];\n      }\n      const quotationData = {\n        ...newQuotation,\n        validUntil,\n        items: newQuotation.items.filter(item => item.productId && item.quantity > 0),\n        customerId: parseInt(newQuotation.customerId)\n      };\n      if (editingQuotation) {\n        await database.updateQuotation(editingQuotation.id, quotationData);\n        setMessage('تم تحديث عرض السعر بنجاح!');\n        setEditingQuotation(null);\n      } else {\n        await database.addQuotation(quotationData);\n        setMessage('تم إضافة عرض السعر بنجاح!');\n      }\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ عرض السعر:', error);\n      setMessage('خطأ في حفظ عرض السعر');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetForm = () => {\n    setNewQuotation({\n      quotationNumber: '',\n      customerId: '',\n      quotationDate: new Date().toISOString().split('T')[0],\n      validUntil: '',\n      status: 'draft',\n      items: [{\n        productId: '',\n        quantity: 1,\n        price: 0,\n        discount: 0,\n        total: 0\n      }],\n      subtotal: 0,\n      discountAmount: 0,\n      vatAmount: 0,\n      total: 0,\n      terms: '',\n      notes: '',\n      paymentTerms: 'نقداً عند التسليم'\n    });\n    setShowAddForm(false);\n    setEditingQuotation(null);\n    generateQuotationNumber();\n  };\n  const handleEdit = quotation => {\n    setNewQuotation({\n      ...quotation,\n      quotationDate: quotation.quotationDate.split('T')[0],\n      validUntil: quotation.validUntil.split('T')[0]\n    });\n    setEditingQuotation(quotation);\n    setShowAddForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف عرض السعر؟')) {\n      try {\n        await database.deleteQuotation(id);\n        setMessage('تم حذف عرض السعر بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف عرض السعر:', error);\n        setMessage('خطأ في حذف عرض السعر');\n      }\n    }\n  };\n  const convertToInvoice = async quotationId => {\n    if (window.confirm('هل تريد تحويل عرض السعر إلى فاتورة؟')) {\n      try {\n        const quotation = quotations.find(q => q.id === quotationId);\n        if (quotation) {\n          // إنشاء فاتورة جديدة من عرض السعر\n          const invoiceData = {\n            customerId: quotation.customerId,\n            items: quotation.items,\n            subtotal: quotation.subtotal,\n            vatAmount: quotation.vatAmount,\n            total: quotation.total,\n            notes: `تم التحويل من عرض السعر رقم: ${quotation.quotationNumber}`,\n            paymentTerms: quotation.paymentTerms\n          };\n          await database.addInvoice(invoiceData);\n          await database.updateQuotation(quotationId, {\n            status: 'converted'\n          });\n          setMessage('تم تحويل عرض السعر إلى فاتورة بنجاح!');\n          loadData();\n          setTimeout(() => setMessage(''), 3000);\n        }\n      } catch (error) {\n        console.error('خطأ في تحويل عرض السعر:', error);\n        setMessage('خطأ في تحويل عرض السعر');\n      }\n    }\n  };\n  const printQuotation = quotation => {\n    const customer = customers.find(c => c.id === quotation.customerId);\n    const settings = database.getSettings();\n    const printWindow = window.open('', '_blank');\n    const printContent = `\n      <html>\n        <head>\n          <title>عرض سعر - ${quotation.quotationNumber}</title>\n          <style>\n            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }\n            .company-info { text-align: center; margin-bottom: 20px; }\n            .quotation-info { display: flex; justify-content: space-between; margin-bottom: 20px; }\n            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n            .items-table th { background-color: #f5f5f5; }\n            .totals { text-align: left; margin-top: 20px; }\n            .total-row { margin: 5px 0; }\n            .final-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #333; padding-top: 10px; }\n            .terms { margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }\n            @media print { body { margin: 0; } }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>عرض سعر</h1>\n            <h2>${settings.companyName}</h2>\n          </div>\n          \n          <div class=\"company-info\">\n            <p><strong>العنوان:</strong> ${settings.address}</p>\n            <p><strong>الهاتف:</strong> ${settings.phone} | <strong>البريد الإلكتروني:</strong> ${settings.email}</p>\n            <p><strong>الرقم الضريبي:</strong> ${settings.vatNumber}</p>\n          </div>\n\n          <div class=\"quotation-info\">\n            <div>\n              <p><strong>رقم عرض السعر:</strong> ${quotation.quotationNumber}</p>\n              <p><strong>التاريخ:</strong> ${new Date(quotation.quotationDate).toLocaleDateString('ar-SA')}</p>\n              <p><strong>صالح حتى:</strong> ${new Date(quotation.validUntil).toLocaleDateString('ar-SA')}</p>\n            </div>\n            <div>\n              <p><strong>العميل:</strong> ${(customer === null || customer === void 0 ? void 0 : customer.name) || 'غير محدد'}</p>\n              <p><strong>الهاتف:</strong> ${(customer === null || customer === void 0 ? void 0 : customer.phone) || ''}</p>\n              <p><strong>العنوان:</strong> ${(customer === null || customer === void 0 ? void 0 : customer.address) || ''}</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th>م</th>\n                <th>الصنف</th>\n                <th>الكمية</th>\n                <th>السعر</th>\n                <th>الخصم</th>\n                <th>الإجمالي</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${quotation.items.map((item, index) => {\n      const product = products.find(p => p.id === item.productId);\n      return `\n                  <tr>\n                    <td>${index + 1}</td>\n                    <td>${(product === null || product === void 0 ? void 0 : product.name) || 'غير محدد'}</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.price.toLocaleString()} ريال</td>\n                    <td>${item.discount.toLocaleString()} ريال</td>\n                    <td>${item.total.toLocaleString()} ريال</td>\n                  </tr>\n                `;\n    }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"totals\">\n            <div class=\"total-row\">المجموع الفرعي: ${quotation.subtotal.toLocaleString()} ريال</div>\n            <div class=\"total-row\">الخصم: ${quotation.discountAmount.toLocaleString()} ريال</div>\n            <div class=\"total-row\">ضريبة القيمة المضافة (15%): ${quotation.vatAmount.toLocaleString()} ريال</div>\n            <div class=\"total-row final-total\">الإجمالي: ${quotation.total.toLocaleString()} ريال</div>\n          </div>\n\n          ${quotation.terms ? `\n            <div class=\"terms\">\n              <h4>الشروط والأحكام:</h4>\n              <p>${quotation.terms}</p>\n            </div>\n          ` : ''}\n\n          ${quotation.paymentTerms ? `\n            <div class=\"terms\">\n              <h4>شروط الدفع:</h4>\n              <p>${quotation.paymentTerms}</p>\n            </div>\n          ` : ''}\n\n          ${quotation.notes ? `\n            <div class=\"terms\">\n              <h4>ملاحظات:</h4>\n              <p>${quotation.notes}</p>\n            </div>\n          ` : ''}\n\n          <div style=\"margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;\">\n            <p>شكراً لتعاملكم معنا</p>\n            <p>هذا عرض سعر وليس فاتورة ضريبية</p>\n          </div>\n        </body>\n      </html>\n    `;\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n  const getCustomerName = customerId => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n  const getProductName = productId => {\n    const product = products.find(p => p.id === parseInt(productId));\n    return product ? product.name : 'غير محدد';\n  };\n  const getStatusInfo = status => {\n    const statusObj = quotationStatuses.find(s => s.value === status);\n    return statusObj || {\n      label: status,\n      color: '#64748b'\n    };\n  };\n  const filteredQuotations = quotations.filter(quotation => {\n    const matchesSearch = quotation.quotationNumber.toLowerCase().includes(searchTerm.toLowerCase()) || getCustomerName(quotation.customerId).toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || quotation.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n  const totalQuotations = filteredQuotations.reduce((sum, q) => sum + q.total, 0);\n  const acceptedQuotations = filteredQuotations.filter(q => q.status === 'accepted');\n  const pendingQuotations = filteredQuotations.filter(q => q.status === 'sent');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quotations-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quotations-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCBC \\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631 \\u0644\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('خطأ') ? 'error' : 'success'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card total\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCBC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0631\\u0648\\u0636\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [quotations.length, \" \\u0639\\u0631\\u0636\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card accepted\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0639\\u0631\\u0648\\u0636 \\u0645\\u0642\\u0628\\u0648\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [acceptedQuotations.length, \" \\u0639\\u0631\\u0636\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card pending\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0639\\u0631\\u0648\\u0636 \\u0645\\u0639\\u0644\\u0642\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [pendingQuotations.length, \" \\u0639\\u0631\\u0636\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card amount\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [totalQuotations.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quotations-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-filters\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterStatus,\n          onChange: e => setFilterStatus(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0627\\u0644\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), quotationStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: status.value,\n            children: status.label\n          }, status.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddForm(true),\n        className: \"add-quotation-btn\",\n        children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0631\\u0636 \\u0633\\u0639\\u0631 \\u062C\\u062F\\u064A\\u062F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quotation-form-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: editingQuotation ? '✏️ تعديل عرض السعر' : '➕ إضافة عرض سعر جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetForm,\n            className: \"close-btn\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"quotation-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-header\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0631\\u0642\\u0645 \\u0639\\u0631\\u0636 \\u0627\\u0644\\u0633\\u0639\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"quotationNumber\",\n                  value: newQuotation.quotationNumber,\n                  onChange: handleInputChange,\n                  required: true,\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"customerId\",\n                  value: newQuotation.customerId,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 23\n                  }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: customer.id,\n                    children: customer.name\n                  }, customer.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0639\\u0631\\u0636\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"quotationDate\",\n                  value: newQuotation.quotationDate,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0635\\u0627\\u0644\\u062D \\u062D\\u062A\\u0649\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"validUntil\",\n                  value: newQuotation.validUntil,\n                  onChange: handleInputChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"status\",\n                  value: newQuotation.status,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: quotationStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: status.value,\n                    children: status.label\n                  }, status.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0634\\u0631\\u0648\\u0637 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"paymentTerms\",\n                  value: newQuotation.paymentTerms,\n                  onChange: handleInputChange,\n                  children: paymentTermsOptions.map(term => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: term,\n                    children: term\n                  }, term, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDCE6 \\u0623\\u0635\\u0646\\u0627\\u0641 \\u0627\\u0644\\u0639\\u0631\\u0636\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addItem,\n                className: \"add-item-btn\",\n                children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0635\\u0646\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"items-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"items-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0635\\u0646\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0633\\u0639\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u062E\\u0635\\u0645\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), newQuotation.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: item.productId,\n                  onChange: e => handleItemChange(index, 'productId', e.target.value),\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 25\n                  }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: product.id,\n                    children: [product.name, \" - \", product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n                  }, product.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\",\n                  value: item.quantity,\n                  onChange: e => handleItemChange(index, 'quantity', e.target.value),\n                  min: \"1\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0633\\u0639\\u0631\",\n                  value: item.price,\n                  onChange: e => handleItemChange(index, 'price', e.target.value),\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u062E\\u0635\\u0645\",\n                  value: item.discount,\n                  onChange: e => handleItemChange(index, 'discount', e.target.value),\n                  step: \"0.01\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: item.total,\n                  readOnly: true,\n                  className: \"total-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 23\n                }, this), newQuotation.items.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeItem(index),\n                  className: \"remove-item-btn\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discount-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u062E\\u0635\\u0645 \\u0625\\u0636\\u0627\\u0641\\u064A \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"discountAmount\",\n                  value: newQuotation.discountAmount,\n                  onChange: e => {\n                    handleInputChange(e);\n                    calculateTotals();\n                  },\n                  step: \"0.01\",\n                  min: \"0\",\n                  placeholder: \"0.00\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"totals-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newQuotation.subtotal.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0627\\u0644\\u062E\\u0635\\u0645:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newQuotation.discountAmount.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newQuotation.vatAmount.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row final-total\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newQuotation.total.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u0627\\u0644\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0627\\u0644\\u0623\\u062D\\u0643\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"terms\",\n              value: newQuotation.terms,\n              onChange: handleInputChange,\n              rows: \"3\",\n              placeholder: \"\\u0627\\u0644\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0627\\u0644\\u0623\\u062D\\u0643\\u0627\\u0645 \\u0627\\u0644\\u062E\\u0627\\u0635\\u0629 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0636...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"notes\",\n              value: newQuotation.notes,\n              onChange: handleInputChange,\n              rows: \"3\",\n              placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: resetForm,\n              className: \"cancel-btn\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"save-btn\",\n              children: loading ? '⏳ جاري الحفظ...' : editingQuotation ? '💾 تحديث' : '💾 حفظ'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quotations-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"quotations-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0639\\u0631\\u0636\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0635\\u0627\\u0644\\u062D \\u062D\\u062A\\u0649\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredQuotations.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"7\",\n              className: \"no-data\",\n              children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0639\\u0631\\u0648\\u0636 \\u0623\\u0633\\u0639\\u0627\\u0631 \\u0645\\u0637\\u0627\\u0628\\u0642\\u0629 \\u0644\\u0644\\u0628\\u062D\\u062B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this) : filteredQuotations.map(quotation => {\n            const statusInfo = getStatusInfo(quotation.status);\n            const isExpired = new Date(quotation.validUntil) < new Date();\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: isExpired ? 'expired' : '',\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"quotation-number\",\n                children: quotation.quotationNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getCustomerName(quotation.customerId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(quotation.quotationDate).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: isExpired ? 'expired-date' : '',\n                children: [new Date(quotation.validUntil).toLocaleDateString('ar-SA'), isExpired && ' ⚠️']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"amount\",\n                children: [quotation.total.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: statusInfo.color\n                  },\n                  children: statusInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(quotation),\n                  className: \"edit-btn\",\n                  title: \"\\u062A\\u0639\\u062F\\u064A\\u0644\",\n                  disabled: quotation.status === 'converted',\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => printQuotation(quotation),\n                  className: \"print-btn\",\n                  title: \"\\u0637\\u0628\\u0627\\u0639\\u0629\",\n                  children: \"\\uD83D\\uDDA8\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 23\n                }, this), quotation.status === 'accepted' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => convertToInvoice(quotation.id),\n                  className: \"convert-btn\",\n                  title: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\",\n                  children: \"\\uD83D\\uDD04\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(quotation.id),\n                  className: \"delete-btn\",\n                  title: \"\\u062D\\u0630\\u0641\",\n                  disabled: quotation.status === 'converted',\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, quotation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 411,\n    columnNumber: 5\n  }, this);\n};\n_s(Quotations, \"Q/hZfHBr/Tv+zxYhz7pbeI56D34=\");\n_c = Quotations;\nexport default Quotations;\nvar _c;\n$RefreshReg$(_c, \"Quotations\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Quotations", "_s", "quotations", "setQuotations", "customers", "setCustomers", "products", "setProducts", "showAddForm", "setShowAddForm", "editingQuotation", "setEditingQuotation", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "loading", "setLoading", "message", "setMessage", "newQuotation", "setNewQuotation", "quotationNumber", "customerId", "quotationDate", "Date", "toISOString", "split", "validUntil", "status", "items", "productId", "quantity", "price", "discount", "total", "subtotal", "discountAmount", "vatAmount", "terms", "notes", "paymentTerms", "quotationStatuses", "value", "label", "color", "paymentTermsOptions", "loadData", "generateQuotationNumber", "quotationsData", "getQuotations", "customersData", "getCustomers", "productsData", "getProducts", "error", "console", "today", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "random", "Math", "floor", "toString", "prev", "handleInputChange", "e", "name", "target", "handleItemChange", "index", "field", "updatedItems", "selectedProduct", "find", "p", "id", "parseInt", "parseFloat", "itemTotal", "max", "calculateTotals", "reduce", "sum", "item", "afterDiscount", "vatRate", "addItem", "removeItem", "length", "filter", "_", "i", "handleSubmit", "preventDefault", "setDate", "quotationData", "updateQuotation", "addQuotation", "resetForm", "setTimeout", "handleEdit", "quotation", "handleDelete", "window", "confirm", "deleteQuotation", "convertToInvoice", "quotationId", "q", "invoiceData", "addInvoice", "printQuotation", "customer", "c", "settings", "getSettings", "printWindow", "open", "printContent", "companyName", "address", "phone", "email", "vatNumber", "toLocaleDateString", "map", "product", "toLocaleString", "join", "document", "write", "close", "print", "getCustomerName", "getProductName", "getStatusInfo", "statusObj", "s", "filteredQuotations", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "totalQuotations", "acceptedQuotations", "pendingQuotations", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "required", "readOnly", "term", "min", "step", "rows", "disabled", "colSpan", "statusInfo", "isExpired", "style", "backgroundColor", "title", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Quotations/Quotations.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Quotations.css';\n\nconst Quotations = () => {\n  const [quotations, setQuotations] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingQuotation, setEditingQuotation] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const [newQuotation, setNewQuotation] = useState({\n    quotationNumber: '',\n    customerId: '',\n    quotationDate: new Date().toISOString().split('T')[0],\n    validUntil: '',\n    status: 'draft',\n    items: [{ productId: '', quantity: 1, price: 0, discount: 0, total: 0 }],\n    subtotal: 0,\n    discountAmount: 0,\n    vatAmount: 0,\n    total: 0,\n    terms: '',\n    notes: '',\n    paymentTerms: 'نقداً عند التسليم'\n  });\n\n  const quotationStatuses = [\n    { value: 'draft', label: '📝 مسودة', color: '#6b7280' },\n    { value: 'sent', label: '📤 مرسل', color: '#3b82f6' },\n    { value: 'accepted', label: '✅ مقبول', color: '#10b981' },\n    { value: 'rejected', label: '❌ مرفوض', color: '#ef4444' },\n    { value: 'expired', label: '⏰ منتهي الصلاحية', color: '#f59e0b' },\n    { value: 'converted', label: '🔄 تم التحويل لفاتورة', color: '#8b5cf6' }\n  ];\n\n  const paymentTermsOptions = [\n    'نقداً عند التسليم',\n    'الدفع خلال 15 يوم',\n    'الدفع خلال 30 يوم',\n    'الدفع خلال 60 يوم',\n    'دفع مقدم 50%',\n    'دفع على دفعات',\n    'تحويل بنكي',\n    'شروط خاصة'\n  ];\n\n  useEffect(() => {\n    loadData();\n    generateQuotationNumber();\n  }, []);\n\n  const loadData = () => {\n    try {\n      const quotationsData = database.getQuotations();\n      const customersData = database.getCustomers();\n      const productsData = database.getProducts();\n      \n      setQuotations(quotationsData);\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n\n  const generateQuotationNumber = () => {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    \n    const quotationNumber = `QUO-${year}${month}${day}-${random}`;\n    setNewQuotation(prev => ({ ...prev, quotationNumber }));\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewQuotation(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleItemChange = (index, field, value) => {\n    const updatedItems = [...newQuotation.items];\n    updatedItems[index] = { ...updatedItems[index], [field]: value };\n\n    // Auto-fill product price\n    if (field === 'productId' && value) {\n      const selectedProduct = products.find(p => p.id === parseInt(value));\n      if (selectedProduct) {\n        updatedItems[index].price = selectedProduct.price;\n      }\n    }\n\n    // Calculate item total\n    if (field === 'quantity' || field === 'price' || field === 'discount') {\n      const quantity = parseFloat(updatedItems[index].quantity) || 0;\n      const price = parseFloat(updatedItems[index].price) || 0;\n      const discount = parseFloat(updatedItems[index].discount) || 0;\n      const itemTotal = (quantity * price) - discount;\n      updatedItems[index].total = Math.max(0, itemTotal);\n    }\n\n    setNewQuotation(prev => ({ ...prev, items: updatedItems }));\n    calculateTotals(updatedItems);\n  };\n\n  const calculateTotals = (items = newQuotation.items) => {\n    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);\n    const discountAmount = parseFloat(newQuotation.discountAmount) || 0;\n    const afterDiscount = subtotal - discountAmount;\n    const vatRate = 0.15; // 15% VAT\n    const vatAmount = afterDiscount * vatRate;\n    const total = afterDiscount + vatAmount;\n\n    setNewQuotation(prev => ({\n      ...prev,\n      subtotal,\n      vatAmount,\n      total: Math.max(0, total)\n    }));\n  };\n\n  const addItem = () => {\n    setNewQuotation(prev => ({\n      ...prev,\n      items: [...prev.items, { productId: '', quantity: 1, price: 0, discount: 0, total: 0 }]\n    }));\n  };\n\n  const removeItem = (index) => {\n    if (newQuotation.items.length > 1) {\n      const updatedItems = newQuotation.items.filter((_, i) => i !== index);\n      setNewQuotation(prev => ({ ...prev, items: updatedItems }));\n      calculateTotals(updatedItems);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      // Set valid until date if not set (30 days from quotation date)\n      let validUntil = newQuotation.validUntil;\n      if (!validUntil) {\n        const quotationDate = new Date(newQuotation.quotationDate);\n        quotationDate.setDate(quotationDate.getDate() + 30);\n        validUntil = quotationDate.toISOString().split('T')[0];\n      }\n\n      const quotationData = {\n        ...newQuotation,\n        validUntil,\n        items: newQuotation.items.filter(item => item.productId && item.quantity > 0),\n        customerId: parseInt(newQuotation.customerId)\n      };\n\n      if (editingQuotation) {\n        await database.updateQuotation(editingQuotation.id, quotationData);\n        setMessage('تم تحديث عرض السعر بنجاح!');\n        setEditingQuotation(null);\n      } else {\n        await database.addQuotation(quotationData);\n        setMessage('تم إضافة عرض السعر بنجاح!');\n      }\n\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ عرض السعر:', error);\n      setMessage('خطأ في حفظ عرض السعر');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetForm = () => {\n    setNewQuotation({\n      quotationNumber: '',\n      customerId: '',\n      quotationDate: new Date().toISOString().split('T')[0],\n      validUntil: '',\n      status: 'draft',\n      items: [{ productId: '', quantity: 1, price: 0, discount: 0, total: 0 }],\n      subtotal: 0,\n      discountAmount: 0,\n      vatAmount: 0,\n      total: 0,\n      terms: '',\n      notes: '',\n      paymentTerms: 'نقداً عند التسليم'\n    });\n    setShowAddForm(false);\n    setEditingQuotation(null);\n    generateQuotationNumber();\n  };\n\n  const handleEdit = (quotation) => {\n    setNewQuotation({\n      ...quotation,\n      quotationDate: quotation.quotationDate.split('T')[0],\n      validUntil: quotation.validUntil.split('T')[0]\n    });\n    setEditingQuotation(quotation);\n    setShowAddForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف عرض السعر؟')) {\n      try {\n        await database.deleteQuotation(id);\n        setMessage('تم حذف عرض السعر بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف عرض السعر:', error);\n        setMessage('خطأ في حذف عرض السعر');\n      }\n    }\n  };\n\n  const convertToInvoice = async (quotationId) => {\n    if (window.confirm('هل تريد تحويل عرض السعر إلى فاتورة؟')) {\n      try {\n        const quotation = quotations.find(q => q.id === quotationId);\n        if (quotation) {\n          // إنشاء فاتورة جديدة من عرض السعر\n          const invoiceData = {\n            customerId: quotation.customerId,\n            items: quotation.items,\n            subtotal: quotation.subtotal,\n            vatAmount: quotation.vatAmount,\n            total: quotation.total,\n            notes: `تم التحويل من عرض السعر رقم: ${quotation.quotationNumber}`,\n            paymentTerms: quotation.paymentTerms\n          };\n\n          await database.addInvoice(invoiceData);\n          await database.updateQuotation(quotationId, { status: 'converted' });\n          \n          setMessage('تم تحويل عرض السعر إلى فاتورة بنجاح!');\n          loadData();\n          setTimeout(() => setMessage(''), 3000);\n        }\n      } catch (error) {\n        console.error('خطأ في تحويل عرض السعر:', error);\n        setMessage('خطأ في تحويل عرض السعر');\n      }\n    }\n  };\n\n  const printQuotation = (quotation) => {\n    const customer = customers.find(c => c.id === quotation.customerId);\n    const settings = database.getSettings();\n    \n    const printWindow = window.open('', '_blank');\n    const printContent = `\n      <html>\n        <head>\n          <title>عرض سعر - ${quotation.quotationNumber}</title>\n          <style>\n            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }\n            .company-info { text-align: center; margin-bottom: 20px; }\n            .quotation-info { display: flex; justify-content: space-between; margin-bottom: 20px; }\n            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n            .items-table th { background-color: #f5f5f5; }\n            .totals { text-align: left; margin-top: 20px; }\n            .total-row { margin: 5px 0; }\n            .final-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #333; padding-top: 10px; }\n            .terms { margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }\n            @media print { body { margin: 0; } }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>عرض سعر</h1>\n            <h2>${settings.companyName}</h2>\n          </div>\n          \n          <div class=\"company-info\">\n            <p><strong>العنوان:</strong> ${settings.address}</p>\n            <p><strong>الهاتف:</strong> ${settings.phone} | <strong>البريد الإلكتروني:</strong> ${settings.email}</p>\n            <p><strong>الرقم الضريبي:</strong> ${settings.vatNumber}</p>\n          </div>\n\n          <div class=\"quotation-info\">\n            <div>\n              <p><strong>رقم عرض السعر:</strong> ${quotation.quotationNumber}</p>\n              <p><strong>التاريخ:</strong> ${new Date(quotation.quotationDate).toLocaleDateString('ar-SA')}</p>\n              <p><strong>صالح حتى:</strong> ${new Date(quotation.validUntil).toLocaleDateString('ar-SA')}</p>\n            </div>\n            <div>\n              <p><strong>العميل:</strong> ${customer?.name || 'غير محدد'}</p>\n              <p><strong>الهاتف:</strong> ${customer?.phone || ''}</p>\n              <p><strong>العنوان:</strong> ${customer?.address || ''}</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th>م</th>\n                <th>الصنف</th>\n                <th>الكمية</th>\n                <th>السعر</th>\n                <th>الخصم</th>\n                <th>الإجمالي</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${quotation.items.map((item, index) => {\n                const product = products.find(p => p.id === item.productId);\n                return `\n                  <tr>\n                    <td>${index + 1}</td>\n                    <td>${product?.name || 'غير محدد'}</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.price.toLocaleString()} ريال</td>\n                    <td>${item.discount.toLocaleString()} ريال</td>\n                    <td>${item.total.toLocaleString()} ريال</td>\n                  </tr>\n                `;\n              }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"totals\">\n            <div class=\"total-row\">المجموع الفرعي: ${quotation.subtotal.toLocaleString()} ريال</div>\n            <div class=\"total-row\">الخصم: ${quotation.discountAmount.toLocaleString()} ريال</div>\n            <div class=\"total-row\">ضريبة القيمة المضافة (15%): ${quotation.vatAmount.toLocaleString()} ريال</div>\n            <div class=\"total-row final-total\">الإجمالي: ${quotation.total.toLocaleString()} ريال</div>\n          </div>\n\n          ${quotation.terms ? `\n            <div class=\"terms\">\n              <h4>الشروط والأحكام:</h4>\n              <p>${quotation.terms}</p>\n            </div>\n          ` : ''}\n\n          ${quotation.paymentTerms ? `\n            <div class=\"terms\">\n              <h4>شروط الدفع:</h4>\n              <p>${quotation.paymentTerms}</p>\n            </div>\n          ` : ''}\n\n          ${quotation.notes ? `\n            <div class=\"terms\">\n              <h4>ملاحظات:</h4>\n              <p>${quotation.notes}</p>\n            </div>\n          ` : ''}\n\n          <div style=\"margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;\">\n            <p>شكراً لتعاملكم معنا</p>\n            <p>هذا عرض سعر وليس فاتورة ضريبية</p>\n          </div>\n        </body>\n      </html>\n    `;\n\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n\n  const getCustomerName = (customerId) => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n\n  const getProductName = (productId) => {\n    const product = products.find(p => p.id === parseInt(productId));\n    return product ? product.name : 'غير محدد';\n  };\n\n  const getStatusInfo = (status) => {\n    const statusObj = quotationStatuses.find(s => s.value === status);\n    return statusObj || { label: status, color: '#64748b' };\n  };\n\n  const filteredQuotations = quotations.filter(quotation => {\n    const matchesSearch = \n      quotation.quotationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      getCustomerName(quotation.customerId).toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesStatus = filterStatus === 'all' || quotation.status === filterStatus;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const totalQuotations = filteredQuotations.reduce((sum, q) => sum + q.total, 0);\n  const acceptedQuotations = filteredQuotations.filter(q => q.status === 'accepted');\n  const pendingQuotations = filteredQuotations.filter(q => q.status === 'sent');\n\n  return (\n    <div className=\"quotations-container\">\n      <div className=\"quotations-header\">\n        <h1>💼 عروض الأسعار</h1>\n        <p>إدارة وتتبع عروض الأسعار للعملاء</p>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>\n          {message}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card total\">\n          <div className=\"stat-icon\">💼</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي العروض</h3>\n            <p>{quotations.length} عرض</p>\n          </div>\n        </div>\n        <div className=\"stat-card accepted\">\n          <div className=\"stat-icon\">✅</div>\n          <div className=\"stat-info\">\n            <h3>عروض مقبولة</h3>\n            <p>{acceptedQuotations.length} عرض</p>\n          </div>\n        </div>\n        <div className=\"stat-card pending\">\n          <div className=\"stat-icon\">⏳</div>\n          <div className=\"stat-info\">\n            <h3>عروض معلقة</h3>\n            <p>{pendingQuotations.length} عرض</p>\n          </div>\n        </div>\n        <div className=\"stat-card amount\">\n          <div className=\"stat-icon\">💰</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي القيمة</h3>\n            <p>{totalQuotations.toLocaleString()} ريال</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"quotations-controls\">\n        <div className=\"search-filters\">\n          <input\n            type=\"text\"\n            placeholder=\"🔍 البحث في عروض الأسعار...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">جميع الحالات</option>\n            {quotationStatuses.map(status => (\n              <option key={status.value} value={status.value}>\n                {status.label}\n              </option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"add-quotation-btn\"\n        >\n          ➕ إضافة عرض سعر جديد\n        </button>\n      </div>\n\n      {/* Add/Edit Form */}\n      {showAddForm && (\n        <div className=\"modal-overlay\">\n          <div className=\"quotation-form-modal\">\n            <div className=\"modal-header\">\n              <h3>{editingQuotation ? '✏️ تعديل عرض السعر' : '➕ إضافة عرض سعر جديد'}</h3>\n              <button onClick={resetForm} className=\"close-btn\">✕</button>\n            </div>\n            \n            <form onSubmit={handleSubmit} className=\"quotation-form\">\n              <div className=\"form-header\">\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>رقم عرض السعر</label>\n                    <input\n                      type=\"text\"\n                      name=\"quotationNumber\"\n                      value={newQuotation.quotationNumber}\n                      onChange={handleInputChange}\n                      required\n                      readOnly\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>العميل</label>\n                    <select\n                      name=\"customerId\"\n                      value={newQuotation.customerId}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">اختر العميل</option>\n                      {customers.map(customer => (\n                        <option key={customer.id} value={customer.id}>\n                          {customer.name}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>تاريخ العرض</label>\n                    <input\n                      type=\"date\"\n                      name=\"quotationDate\"\n                      value={newQuotation.quotationDate}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>صالح حتى</label>\n                    <input\n                      type=\"date\"\n                      name=\"validUntil\"\n                      value={newQuotation.validUntil}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>الحالة</label>\n                    <select\n                      name=\"status\"\n                      value={newQuotation.status}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {quotationStatuses.map(status => (\n                        <option key={status.value} value={status.value}>\n                          {status.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>شروط الدفع</label>\n                    <select\n                      name=\"paymentTerms\"\n                      value={newQuotation.paymentTerms}\n                      onChange={handleInputChange}\n                    >\n                      {paymentTermsOptions.map(term => (\n                        <option key={term} value={term}>\n                          {term}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* Items Section */}\n              <div className=\"items-section\">\n                <div className=\"section-header\">\n                  <h4>📦 أصناف العرض</h4>\n                  <button type=\"button\" onClick={addItem} className=\"add-item-btn\">\n                    ➕ إضافة صنف\n                  </button>\n                </div>\n\n                <div className=\"items-table\">\n                  <div className=\"items-header\">\n                    <div>الصنف</div>\n                    <div>الكمية</div>\n                    <div>السعر</div>\n                    <div>الخصم</div>\n                    <div>الإجمالي</div>\n                    <div>إجراءات</div>\n                  </div>\n\n                  {newQuotation.items.map((item, index) => (\n                    <div key={index} className=\"item-row\">\n                      <select\n                        value={item.productId}\n                        onChange={(e) => handleItemChange(index, 'productId', e.target.value)}\n                        required\n                      >\n                        <option value=\"\">اختر الصنف</option>\n                        {products.map(product => (\n                          <option key={product.id} value={product.id}>\n                            {product.name} - {product.price} ريال\n                          </option>\n                        ))}\n                      </select>\n                      <input\n                        type=\"number\"\n                        placeholder=\"الكمية\"\n                        value={item.quantity}\n                        onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}\n                        min=\"1\"\n                        required\n                      />\n                      <input\n                        type=\"number\"\n                        placeholder=\"السعر\"\n                        value={item.price}\n                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        required\n                      />\n                      <input\n                        type=\"number\"\n                        placeholder=\"الخصم\"\n                        value={item.discount}\n                        onChange={(e) => handleItemChange(index, 'discount', e.target.value)}\n                        step=\"0.01\"\n                        min=\"0\"\n                      />\n                      <input\n                        type=\"number\"\n                        value={item.total}\n                        readOnly\n                        className=\"total-field\"\n                      />\n                      {newQuotation.items.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeItem(index)}\n                          className=\"remove-item-btn\"\n                        >\n                          🗑️\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"discount-section\">\n                  <div className=\"form-group\">\n                    <label>خصم إضافي على الإجمالي</label>\n                    <input\n                      type=\"number\"\n                      name=\"discountAmount\"\n                      value={newQuotation.discountAmount}\n                      onChange={(e) => {\n                        handleInputChange(e);\n                        calculateTotals();\n                      }}\n                      step=\"0.01\"\n                      min=\"0\"\n                      placeholder=\"0.00\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"totals-section\">\n                  <div className=\"total-row\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{newQuotation.subtotal.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"total-row\">\n                    <span>الخصم:</span>\n                    <span>{newQuotation.discountAmount.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"total-row\">\n                    <span>ضريبة القيمة المضافة (15%):</span>\n                    <span>{newQuotation.vatAmount.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"total-row final-total\">\n                    <span>الإجمالي:</span>\n                    <span>{newQuotation.total.toLocaleString()} ريال</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>الشروط والأحكام</label>\n                <textarea\n                  name=\"terms\"\n                  value={newQuotation.terms}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  placeholder=\"الشروط والأحكام الخاصة بالعرض...\"\n                />\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>ملاحظات</label>\n                <textarea\n                  name=\"notes\"\n                  value={newQuotation.notes}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  placeholder=\"ملاحظات إضافية...\"\n                />\n              </div>\n\n              <div className=\"form-actions\">\n                <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                  إلغاء\n                </button>\n                <button type=\"submit\" disabled={loading} className=\"save-btn\">\n                  {loading ? '⏳ جاري الحفظ...' : (editingQuotation ? '💾 تحديث' : '💾 حفظ')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Quotations Table */}\n      <div className=\"quotations-table-container\">\n        <table className=\"quotations-table\">\n          <thead>\n            <tr>\n              <th>رقم العرض</th>\n              <th>العميل</th>\n              <th>التاريخ</th>\n              <th>صالح حتى</th>\n              <th>المبلغ</th>\n              <th>الحالة</th>\n              <th>الإجراءات</th>\n            </tr>\n          </thead>\n          <tbody>\n            {filteredQuotations.length === 0 ? (\n              <tr>\n                <td colSpan=\"7\" className=\"no-data\">\n                  لا توجد عروض أسعار مطابقة للبحث\n                </td>\n              </tr>\n            ) : (\n              filteredQuotations.map(quotation => {\n                const statusInfo = getStatusInfo(quotation.status);\n                const isExpired = new Date(quotation.validUntil) < new Date();\n                \n                return (\n                  <tr key={quotation.id} className={isExpired ? 'expired' : ''}>\n                    <td className=\"quotation-number\">{quotation.quotationNumber}</td>\n                    <td>{getCustomerName(quotation.customerId)}</td>\n                    <td>{new Date(quotation.quotationDate).toLocaleDateString('ar-SA')}</td>\n                    <td className={isExpired ? 'expired-date' : ''}>\n                      {new Date(quotation.validUntil).toLocaleDateString('ar-SA')}\n                      {isExpired && ' ⚠️'}\n                    </td>\n                    <td className=\"amount\">{quotation.total.toLocaleString()} ريال</td>\n                    <td>\n                      <span \n                        className=\"status-badge\"\n                        style={{ backgroundColor: statusInfo.color }}\n                      >\n                        {statusInfo.label}\n                      </span>\n                    </td>\n                    <td className=\"actions\">\n                      <button\n                        onClick={() => handleEdit(quotation)}\n                        className=\"edit-btn\"\n                        title=\"تعديل\"\n                        disabled={quotation.status === 'converted'}\n                      >\n                        ✏️\n                      </button>\n                      <button\n                        onClick={() => printQuotation(quotation)}\n                        className=\"print-btn\"\n                        title=\"طباعة\"\n                      >\n                        🖨️\n                      </button>\n                      {quotation.status === 'accepted' && (\n                        <button\n                          onClick={() => convertToInvoice(quotation.id)}\n                          className=\"convert-btn\"\n                          title=\"تحويل لفاتورة\"\n                        >\n                          🔄\n                        </button>\n                      )}\n                      <button\n                        onClick={() => handleDelete(quotation.id)}\n                        className=\"delete-btn\"\n                        title=\"حذف\"\n                        disabled={quotation.status === 'converted'}\n                      >\n                        🗑️\n                      </button>\n                    </td>\n                  </tr>\n                );\n              })\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default Quotations;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC;IAC/C2B,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrDC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IACxEC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZH,KAAK,EAAE,CAAC;IACRI,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAG,CACxB;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjE;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,KAAK,EAAE;EAAU,CAAC,CACzE;EAED,MAAMC,mBAAmB,GAAG,CAC1B,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,YAAY,EACZ,WAAW,CACZ;EAEDlD,SAAS,CAAC,MAAM;IACdmD,QAAQ,CAAC,CAAC;IACVC,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI;MACF,MAAME,cAAc,GAAGpD,QAAQ,CAACqD,aAAa,CAAC,CAAC;MAC/C,MAAMC,aAAa,GAAGtD,QAAQ,CAACuD,YAAY,CAAC,CAAC;MAC7C,MAAMC,YAAY,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;MAE3CnD,aAAa,CAAC8C,cAAc,CAAC;MAC7B5C,YAAY,CAAC8C,aAAa,CAAC;MAC3B5C,WAAW,CAAC8C,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpC,UAAU,CAAC,uBAAuB,CAAC;IACrC;EACF,CAAC;EAED,MAAM6B,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMS,KAAK,GAAG,IAAIhC,IAAI,CAAC,CAAC;IACxB,MAAMiC,IAAI,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IAChC,MAAMC,KAAK,GAAGC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAACG,QAAQ,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE3E,MAAMzC,eAAe,GAAG,OAAOoC,IAAI,GAAGE,KAAK,GAAGI,GAAG,IAAIE,MAAM,EAAE;IAC7D7C,eAAe,CAACiD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhD;IAAgB,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,MAAMiD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAE9B;IAAM,CAAC,GAAG6B,CAAC,CAACE,MAAM;IAChCrD,eAAe,CAACiD,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACG,IAAI,GAAG9B;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAElC,KAAK,KAAK;IAChD,MAAMmC,YAAY,GAAG,CAAC,GAAG1D,YAAY,CAACU,KAAK,CAAC;IAC5CgD,YAAY,CAACF,KAAK,CAAC,GAAG;MAAE,GAAGE,YAAY,CAACF,KAAK,CAAC;MAAE,CAACC,KAAK,GAAGlC;IAAM,CAAC;;IAEhE;IACA,IAAIkC,KAAK,KAAK,WAAW,IAAIlC,KAAK,EAAE;MAClC,MAAMoC,eAAe,GAAGzE,QAAQ,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACxC,KAAK,CAAC,CAAC;MACpE,IAAIoC,eAAe,EAAE;QACnBD,YAAY,CAACF,KAAK,CAAC,CAAC3C,KAAK,GAAG8C,eAAe,CAAC9C,KAAK;MACnD;IACF;;IAEA;IACA,IAAI4C,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,UAAU,EAAE;MACrE,MAAM7C,QAAQ,GAAGoD,UAAU,CAACN,YAAY,CAACF,KAAK,CAAC,CAAC5C,QAAQ,CAAC,IAAI,CAAC;MAC9D,MAAMC,KAAK,GAAGmD,UAAU,CAACN,YAAY,CAACF,KAAK,CAAC,CAAC3C,KAAK,CAAC,IAAI,CAAC;MACxD,MAAMC,QAAQ,GAAGkD,UAAU,CAACN,YAAY,CAACF,KAAK,CAAC,CAAC1C,QAAQ,CAAC,IAAI,CAAC;MAC9D,MAAMmD,SAAS,GAAIrD,QAAQ,GAAGC,KAAK,GAAIC,QAAQ;MAC/C4C,YAAY,CAACF,KAAK,CAAC,CAACzC,KAAK,GAAGgC,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAED,SAAS,CAAC;IACpD;IAEAhE,eAAe,CAACiD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,KAAK,EAAEgD;IAAa,CAAC,CAAC,CAAC;IAC3DS,eAAe,CAACT,YAAY,CAAC;EAC/B,CAAC;EAED,MAAMS,eAAe,GAAGA,CAACzD,KAAK,GAAGV,YAAY,CAACU,KAAK,KAAK;IACtD,MAAMM,QAAQ,GAAGN,KAAK,CAAC0D,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIL,UAAU,CAACM,IAAI,CAACvD,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACpF,MAAME,cAAc,GAAG+C,UAAU,CAAChE,YAAY,CAACiB,cAAc,CAAC,IAAI,CAAC;IACnE,MAAMsD,aAAa,GAAGvD,QAAQ,GAAGC,cAAc;IAC/C,MAAMuD,OAAO,GAAG,IAAI,CAAC,CAAC;IACtB,MAAMtD,SAAS,GAAGqD,aAAa,GAAGC,OAAO;IACzC,MAAMzD,KAAK,GAAGwD,aAAa,GAAGrD,SAAS;IAEvCjB,eAAe,CAACiD,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPlC,QAAQ;MACRE,SAAS;MACTH,KAAK,EAAEgC,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEnD,KAAK;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0D,OAAO,GAAGA,CAAA,KAAM;IACpBxE,eAAe,CAACiD,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPxC,KAAK,EAAE,CAAC,GAAGwC,IAAI,CAACxC,KAAK,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;IACxF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2D,UAAU,GAAIlB,KAAK,IAAK;IAC5B,IAAIxD,YAAY,CAACU,KAAK,CAACiE,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMjB,YAAY,GAAG1D,YAAY,CAACU,KAAK,CAACkE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKtB,KAAK,CAAC;MACrEvD,eAAe,CAACiD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,KAAK,EAAEgD;MAAa,CAAC,CAAC,CAAC;MAC3DS,eAAe,CAACT,YAAY,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAO3B,CAAC,IAAK;IAChCA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IAClBnF,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA,IAAIS,UAAU,GAAGR,YAAY,CAACQ,UAAU;MACxC,IAAI,CAACA,UAAU,EAAE;QACf,MAAMJ,aAAa,GAAG,IAAIC,IAAI,CAACL,YAAY,CAACI,aAAa,CAAC;QAC1DA,aAAa,CAAC6E,OAAO,CAAC7E,aAAa,CAACyC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;QACnDrC,UAAU,GAAGJ,aAAa,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD;MAEA,MAAM2E,aAAa,GAAG;QACpB,GAAGlF,YAAY;QACfQ,UAAU;QACVE,KAAK,EAAEV,YAAY,CAACU,KAAK,CAACkE,MAAM,CAACN,IAAI,IAAIA,IAAI,CAAC3D,SAAS,IAAI2D,IAAI,CAAC1D,QAAQ,GAAG,CAAC,CAAC;QAC7ET,UAAU,EAAE4D,QAAQ,CAAC/D,YAAY,CAACG,UAAU;MAC9C,CAAC;MAED,IAAIb,gBAAgB,EAAE;QACpB,MAAMb,QAAQ,CAAC0G,eAAe,CAAC7F,gBAAgB,CAACwE,EAAE,EAAEoB,aAAa,CAAC;QAClEnF,UAAU,CAAC,2BAA2B,CAAC;QACvCR,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMd,QAAQ,CAAC2G,YAAY,CAACF,aAAa,CAAC;QAC1CnF,UAAU,CAAC,2BAA2B,CAAC;MACzC;MAEAsF,SAAS,CAAC,CAAC;MACX1D,QAAQ,CAAC,CAAC;MACV2D,UAAU,CAAC,MAAMvF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CpC,UAAU,CAAC,sBAAsB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,SAAS,GAAGA,CAAA,KAAM;IACtBpF,eAAe,CAAC;MACdC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrDC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;MACxEC,QAAQ,EAAE,CAAC;MACXC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC;MACZH,KAAK,EAAE,CAAC;MACRI,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE;IAChB,CAAC,CAAC;IACFhC,cAAc,CAAC,KAAK,CAAC;IACrBE,mBAAmB,CAAC,IAAI,CAAC;IACzBqC,uBAAuB,CAAC,CAAC;EAC3B,CAAC;EAED,MAAM2D,UAAU,GAAIC,SAAS,IAAK;IAChCvF,eAAe,CAAC;MACd,GAAGuF,SAAS;MACZpF,aAAa,EAAEoF,SAAS,CAACpF,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDC,UAAU,EAAEgF,SAAS,CAAChF,UAAU,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC;IACFhB,mBAAmB,CAACiG,SAAS,CAAC;IAC9BnG,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoG,YAAY,GAAG,MAAO3B,EAAE,IAAK;IACjC,IAAI4B,MAAM,CAACC,OAAO,CAAC,gCAAgC,CAAC,EAAE;MACpD,IAAI;QACF,MAAMlH,QAAQ,CAACmH,eAAe,CAAC9B,EAAE,CAAC;QAClC/D,UAAU,CAAC,yBAAyB,CAAC;QACrC4B,QAAQ,CAAC,CAAC;QACV2D,UAAU,CAAC,MAAMvF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOoC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CpC,UAAU,CAAC,sBAAsB,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAM8F,gBAAgB,GAAG,MAAOC,WAAW,IAAK;IAC9C,IAAIJ,MAAM,CAACC,OAAO,CAAC,qCAAqC,CAAC,EAAE;MACzD,IAAI;QACF,MAAMH,SAAS,GAAG1G,UAAU,CAAC8E,IAAI,CAACmC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKgC,WAAW,CAAC;QAC5D,IAAIN,SAAS,EAAE;UACb;UACA,MAAMQ,WAAW,GAAG;YAClB7F,UAAU,EAAEqF,SAAS,CAACrF,UAAU;YAChCO,KAAK,EAAE8E,SAAS,CAAC9E,KAAK;YACtBM,QAAQ,EAAEwE,SAAS,CAACxE,QAAQ;YAC5BE,SAAS,EAAEsE,SAAS,CAACtE,SAAS;YAC9BH,KAAK,EAAEyE,SAAS,CAACzE,KAAK;YACtBK,KAAK,EAAE,gCAAgCoE,SAAS,CAACtF,eAAe,EAAE;YAClEmB,YAAY,EAAEmE,SAAS,CAACnE;UAC1B,CAAC;UAED,MAAM5C,QAAQ,CAACwH,UAAU,CAACD,WAAW,CAAC;UACtC,MAAMvH,QAAQ,CAAC0G,eAAe,CAACW,WAAW,EAAE;YAAErF,MAAM,EAAE;UAAY,CAAC,CAAC;UAEpEV,UAAU,CAAC,sCAAsC,CAAC;UAClD4B,QAAQ,CAAC,CAAC;UACV2D,UAAU,CAAC,MAAMvF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACxC;MACF,CAAC,CAAC,OAAOoC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CpC,UAAU,CAAC,wBAAwB,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMmG,cAAc,GAAIV,SAAS,IAAK;IACpC,MAAMW,QAAQ,GAAGnH,SAAS,CAAC4E,IAAI,CAACwC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAK0B,SAAS,CAACrF,UAAU,CAAC;IACnE,MAAMkG,QAAQ,GAAG5H,QAAQ,CAAC6H,WAAW,CAAC,CAAC;IAEvC,MAAMC,WAAW,GAAGb,MAAM,CAACc,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7C,MAAMC,YAAY,GAAG;AACzB;AACA;AACA,6BAA6BjB,SAAS,CAACtF,eAAe;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBmG,QAAQ,CAACK,WAAW;AACtC;AACA;AACA;AACA,2CAA2CL,QAAQ,CAACM,OAAO;AAC3D,0CAA0CN,QAAQ,CAACO,KAAK,0CAA0CP,QAAQ,CAACQ,KAAK;AAChH,iDAAiDR,QAAQ,CAACS,SAAS;AACnE;AACA;AACA;AACA;AACA,mDAAmDtB,SAAS,CAACtF,eAAe;AAC5E,6CAA6C,IAAIG,IAAI,CAACmF,SAAS,CAACpF,aAAa,CAAC,CAAC2G,kBAAkB,CAAC,OAAO,CAAC;AAC1G,8CAA8C,IAAI1G,IAAI,CAACmF,SAAS,CAAChF,UAAU,CAAC,CAACuG,kBAAkB,CAAC,OAAO,CAAC;AACxG;AACA;AACA,4CAA4C,CAAAZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9C,IAAI,KAAI,UAAU;AACxE,4CAA4C,CAAA8C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,KAAK,KAAI,EAAE;AACjE,6CAA6C,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,OAAO,KAAI,EAAE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBnB,SAAS,CAAC9E,KAAK,CAACsG,GAAG,CAAC,CAAC1C,IAAI,EAAEd,KAAK,KAAK;MACrC,MAAMyD,OAAO,GAAG/H,QAAQ,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKQ,IAAI,CAAC3D,SAAS,CAAC;MAC3D,OAAO;AACvB;AACA,0BAA0B6C,KAAK,GAAG,CAAC;AACnC,0BAA0B,CAAAyD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE5D,IAAI,KAAI,UAAU;AACrD,0BAA0BiB,IAAI,CAAC1D,QAAQ;AACvC,0BAA0B0D,IAAI,CAACzD,KAAK,CAACqG,cAAc,CAAC,CAAC;AACrD,0BAA0B5C,IAAI,CAACxD,QAAQ,CAACoG,cAAc,CAAC,CAAC;AACxD,0BAA0B5C,IAAI,CAACvD,KAAK,CAACmG,cAAc,CAAC,CAAC;AACrD;AACA,iBAAiB;IACH,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA,qDAAqD3B,SAAS,CAACxE,QAAQ,CAACkG,cAAc,CAAC,CAAC;AACxF,4CAA4C1B,SAAS,CAACvE,cAAc,CAACiG,cAAc,CAAC,CAAC;AACrF,iEAAiE1B,SAAS,CAACtE,SAAS,CAACgG,cAAc,CAAC,CAAC;AACrG,2DAA2D1B,SAAS,CAACzE,KAAK,CAACmG,cAAc,CAAC,CAAC;AAC3F;AACA;AACA,YAAY1B,SAAS,CAACrE,KAAK,GAAG;AAC9B;AACA;AACA,mBAAmBqE,SAAS,CAACrE,KAAK;AAClC;AACA,WAAW,GAAG,EAAE;AAChB;AACA,YAAYqE,SAAS,CAACnE,YAAY,GAAG;AACrC;AACA;AACA,mBAAmBmE,SAAS,CAACnE,YAAY;AACzC;AACA,WAAW,GAAG,EAAE;AAChB;AACA,YAAYmE,SAAS,CAACpE,KAAK,GAAG;AAC9B;AACA;AACA,mBAAmBoE,SAAS,CAACpE,KAAK;AAClC;AACA,WAAW,GAAG,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAEDmF,WAAW,CAACa,QAAQ,CAACC,KAAK,CAACZ,YAAY,CAAC;IACxCF,WAAW,CAACa,QAAQ,CAACE,KAAK,CAAC,CAAC;IAC5BhC,UAAU,CAAC,MAAMiB,WAAW,CAACgB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C,CAAC;EAED,MAAMC,eAAe,GAAIrH,UAAU,IAAK;IACtC,MAAMgG,QAAQ,GAAGnH,SAAS,CAAC4E,IAAI,CAACwC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAK3D,UAAU,CAAC;IACzD,OAAOgG,QAAQ,GAAGA,QAAQ,CAAC9C,IAAI,GAAG,UAAU;EAC9C,CAAC;EAED,MAAMoE,cAAc,GAAI9G,SAAS,IAAK;IACpC,MAAMsG,OAAO,GAAG/H,QAAQ,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACpD,SAAS,CAAC,CAAC;IAChE,OAAOsG,OAAO,GAAGA,OAAO,CAAC5D,IAAI,GAAG,UAAU;EAC5C,CAAC;EAED,MAAMqE,aAAa,GAAIjH,MAAM,IAAK;IAChC,MAAMkH,SAAS,GAAGrG,iBAAiB,CAACsC,IAAI,CAACgE,CAAC,IAAIA,CAAC,CAACrG,KAAK,KAAKd,MAAM,CAAC;IACjE,OAAOkH,SAAS,IAAI;MAAEnG,KAAK,EAAEf,MAAM;MAAEgB,KAAK,EAAE;IAAU,CAAC;EACzD,CAAC;EAED,MAAMoG,kBAAkB,GAAG/I,UAAU,CAAC8F,MAAM,CAACY,SAAS,IAAI;IACxD,MAAMsC,aAAa,GACjBtC,SAAS,CAACtF,eAAe,CAAC6H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxI,UAAU,CAACuI,WAAW,CAAC,CAAC,CAAC,IAC1EP,eAAe,CAAChC,SAAS,CAACrF,UAAU,CAAC,CAAC4H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxI,UAAU,CAACuI,WAAW,CAAC,CAAC,CAAC;IAExF,MAAME,aAAa,GAAGvI,YAAY,KAAK,KAAK,IAAI8F,SAAS,CAAC/E,MAAM,KAAKf,YAAY;IAEjF,OAAOoI,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAGL,kBAAkB,CAACzD,MAAM,CAAC,CAACC,GAAG,EAAE0B,CAAC,KAAK1B,GAAG,GAAG0B,CAAC,CAAChF,KAAK,EAAE,CAAC,CAAC;EAC/E,MAAMoH,kBAAkB,GAAGN,kBAAkB,CAACjD,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAACtF,MAAM,KAAK,UAAU,CAAC;EAClF,MAAM2H,iBAAiB,GAAGP,kBAAkB,CAACjD,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAACtF,MAAM,KAAK,MAAM,CAAC;EAE7E,oBACE9B,OAAA;IAAK0J,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnC3J,OAAA;MAAK0J,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3J,OAAA;QAAA2J,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB/J,OAAA;QAAA2J,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,EAEL5I,OAAO,iBACNnB,OAAA;MAAK0J,SAAS,EAAE,WAAWvI,OAAO,CAACkI,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAM,QAAA,EACxExI;IAAO;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD/J,OAAA;MAAK0J,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3J,OAAA;QAAK0J,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3J,OAAA;YAAA2J,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB/J,OAAA;YAAA2J,QAAA,GAAIxJ,UAAU,CAAC6F,MAAM,EAAC,qBAAI;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN/J,OAAA;QAAK0J,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC3J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClC/J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3J,OAAA;YAAA2J,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB/J,OAAA;YAAA2J,QAAA,GAAIH,kBAAkB,CAACxD,MAAM,EAAC,qBAAI;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN/J,OAAA;QAAK0J,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClC/J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3J,OAAA;YAAA2J,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB/J,OAAA;YAAA2J,QAAA,GAAIF,iBAAiB,CAACzD,MAAM,EAAC,qBAAI;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN/J,OAAA;QAAK0J,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B3J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC/J,OAAA;UAAK0J,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3J,OAAA;YAAA2J,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB/J,OAAA;YAAA2J,QAAA,GAAIJ,eAAe,CAAChB,cAAc,CAAC,CAAC,EAAC,2BAAK;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/J,OAAA;MAAK0J,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC3J,OAAA;QAAK0J,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3J,OAAA;UACEgK,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,iIAA6B;UACzCrH,KAAK,EAAE/B,UAAW;UAClBqJ,QAAQ,EAAGzF,CAAC,IAAK3D,aAAa,CAAC2D,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;UAC/C8G,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACF/J,OAAA;UACE4C,KAAK,EAAE7B,YAAa;UACpBmJ,QAAQ,EAAGzF,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;UACjD8G,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzB3J,OAAA;YAAQ4C,KAAK,EAAC,KAAK;YAAA+G,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCpH,iBAAiB,CAAC0F,GAAG,CAACvG,MAAM,iBAC3B9B,OAAA;YAA2B4C,KAAK,EAAEd,MAAM,CAACc,KAAM;YAAA+G,QAAA,EAC5C7H,MAAM,CAACe;UAAK,GADFf,MAAM,CAACc,KAAK;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/J,OAAA;QACEmK,OAAO,EAAEA,CAAA,KAAMzJ,cAAc,CAAC,IAAI,CAAE;QACpCgJ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC9B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLtJ,WAAW,iBACVT,OAAA;MAAK0J,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B3J,OAAA;QAAK0J,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC3J,OAAA;UAAK0J,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3J,OAAA;YAAA2J,QAAA,EAAKhJ,gBAAgB,GAAG,oBAAoB,GAAG;UAAsB;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3E/J,OAAA;YAAQmK,OAAO,EAAEzD,SAAU;YAACgD,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEN/J,OAAA;UAAMoK,QAAQ,EAAEhE,YAAa;UAACsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACtD3J,OAAA;YAAK0J,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B3J,OAAA;cAAK0J,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5B/J,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXtF,IAAI,EAAC,iBAAiB;kBACtB9B,KAAK,EAAEvB,YAAY,CAACE,eAAgB;kBACpC2I,QAAQ,EAAE1F,iBAAkB;kBAC5B6F,QAAQ;kBACRC,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB/J,OAAA;kBACE0E,IAAI,EAAC,YAAY;kBACjB9B,KAAK,EAAEvB,YAAY,CAACG,UAAW;kBAC/B0I,QAAQ,EAAE1F,iBAAkB;kBAC5B6F,QAAQ;kBAAAV,QAAA,gBAER3J,OAAA;oBAAQ4C,KAAK,EAAC,EAAE;oBAAA+G,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpC1J,SAAS,CAACgI,GAAG,CAACb,QAAQ,iBACrBxH,OAAA;oBAA0B4C,KAAK,EAAE4E,QAAQ,CAACrC,EAAG;oBAAAwE,QAAA,EAC1CnC,QAAQ,CAAC9C;kBAAI,GADH8C,QAAQ,CAACrC,EAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B/J,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXtF,IAAI,EAAC,eAAe;kBACpB9B,KAAK,EAAEvB,YAAY,CAACI,aAAc;kBAClCyI,QAAQ,EAAE1F,iBAAkB;kBAC5B6F,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvB/J,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXtF,IAAI,EAAC,YAAY;kBACjB9B,KAAK,EAAEvB,YAAY,CAACQ,UAAW;kBAC/BqI,QAAQ,EAAE1F;gBAAkB;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB/J,OAAA;kBACE0E,IAAI,EAAC,QAAQ;kBACb9B,KAAK,EAAEvB,YAAY,CAACS,MAAO;kBAC3BoI,QAAQ,EAAE1F,iBAAkB;kBAC5B6F,QAAQ;kBAAAV,QAAA,EAEPhH,iBAAiB,CAAC0F,GAAG,CAACvG,MAAM,iBAC3B9B,OAAA;oBAA2B4C,KAAK,EAAEd,MAAM,CAACc,KAAM;oBAAA+G,QAAA,EAC5C7H,MAAM,CAACe;kBAAK,GADFf,MAAM,CAACc,KAAK;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzB/J,OAAA;kBACE0E,IAAI,EAAC,cAAc;kBACnB9B,KAAK,EAAEvB,YAAY,CAACqB,YAAa;kBACjCwH,QAAQ,EAAE1F,iBAAkB;kBAAAmF,QAAA,EAE3B5G,mBAAmB,CAACsF,GAAG,CAACkC,IAAI,iBAC3BvK,OAAA;oBAAmB4C,KAAK,EAAE2H,IAAK;oBAAAZ,QAAA,EAC5BY;kBAAI,GADMA,IAAI;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/J,OAAA;YAAK0J,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3J,OAAA;cAAK0J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3J,OAAA;gBAAA2J,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB/J,OAAA;gBAAQgK,IAAI,EAAC,QAAQ;gBAACG,OAAO,EAAErE,OAAQ;gBAAC4D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/J,OAAA;cAAK0J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3J,OAAA;gBAAK0J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3J,OAAA;kBAAA2J,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB/J,OAAA;kBAAA2J,QAAA,EAAK;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjB/J,OAAA;kBAAA2J,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB/J,OAAA;kBAAA2J,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB/J,OAAA;kBAAA2J,QAAA,EAAK;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnB/J,OAAA;kBAAA2J,QAAA,EAAK;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EAEL1I,YAAY,CAACU,KAAK,CAACsG,GAAG,CAAC,CAAC1C,IAAI,EAAEd,KAAK,kBAClC7E,OAAA;gBAAiB0J,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACnC3J,OAAA;kBACE4C,KAAK,EAAE+C,IAAI,CAAC3D,SAAU;kBACtBkI,QAAQ,EAAGzF,CAAC,IAAKG,gBAAgB,CAACC,KAAK,EAAE,WAAW,EAAEJ,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;kBACtEyH,QAAQ;kBAAAV,QAAA,gBAER3J,OAAA;oBAAQ4C,KAAK,EAAC,EAAE;oBAAA+G,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnCxJ,QAAQ,CAAC8H,GAAG,CAACC,OAAO,iBACnBtI,OAAA;oBAAyB4C,KAAK,EAAE0F,OAAO,CAACnD,EAAG;oBAAAwE,QAAA,GACxCrB,OAAO,CAAC5D,IAAI,EAAC,KAAG,EAAC4D,OAAO,CAACpG,KAAK,EAAC,2BAClC;kBAAA,GAFaoG,OAAO,CAACnD,EAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACT/J,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,sCAAQ;kBACpBrH,KAAK,EAAE+C,IAAI,CAAC1D,QAAS;kBACrBiI,QAAQ,EAAGzF,CAAC,IAAKG,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAEJ,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;kBACrE4H,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF/J,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,gCAAO;kBACnBrH,KAAK,EAAE+C,IAAI,CAACzD,KAAM;kBAClBgI,QAAQ,EAAGzF,CAAC,IAAKG,gBAAgB,CAACC,KAAK,EAAE,OAAO,EAAEJ,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;kBAClE6H,IAAI,EAAC,MAAM;kBACXD,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF/J,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,gCAAO;kBACnBrH,KAAK,EAAE+C,IAAI,CAACxD,QAAS;kBACrB+H,QAAQ,EAAGzF,CAAC,IAAKG,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAEJ,CAAC,CAACE,MAAM,CAAC/B,KAAK,CAAE;kBACrE6H,IAAI,EAAC,MAAM;kBACXD,GAAG,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACF/J,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbpH,KAAK,EAAE+C,IAAI,CAACvD,KAAM;kBAClBkI,QAAQ;kBACRZ,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,EACD1I,YAAY,CAACU,KAAK,CAACiE,MAAM,GAAG,CAAC,iBAC5BhG,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbG,OAAO,EAAEA,CAAA,KAAMpE,UAAU,CAAClB,KAAK,CAAE;kBACjC6E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GApDOlF,KAAK;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqDV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/J,OAAA;cAAK0J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B3J,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAO;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrC/J,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbtF,IAAI,EAAC,gBAAgB;kBACrB9B,KAAK,EAAEvB,YAAY,CAACiB,cAAe;kBACnC4H,QAAQ,EAAGzF,CAAC,IAAK;oBACfD,iBAAiB,CAACC,CAAC,CAAC;oBACpBe,eAAe,CAAC,CAAC;kBACnB,CAAE;kBACFiF,IAAI,EAAC,MAAM;kBACXD,GAAG,EAAC,GAAG;kBACPP,WAAW,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/J,OAAA;cAAK0J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3J,OAAA;gBAAK0J,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3J,OAAA;kBAAA2J,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B/J,OAAA;kBAAA2J,QAAA,GAAOtI,YAAY,CAACgB,QAAQ,CAACkG,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN/J,OAAA;gBAAK0J,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3J,OAAA;kBAAA2J,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnB/J,OAAA;kBAAA2J,QAAA,GAAOtI,YAAY,CAACiB,cAAc,CAACiG,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN/J,OAAA;gBAAK0J,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3J,OAAA;kBAAA2J,QAAA,EAAM;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC/J,OAAA;kBAAA2J,QAAA,GAAOtI,YAAY,CAACkB,SAAS,CAACgG,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACN/J,OAAA;gBAAK0J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC3J,OAAA;kBAAA2J,QAAA,EAAM;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtB/J,OAAA;kBAAA2J,QAAA,GAAOtI,YAAY,CAACe,KAAK,CAACmG,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/J,OAAA;YAAK0J,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC3J,OAAA;cAAA2J,QAAA,EAAO;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9B/J,OAAA;cACE0E,IAAI,EAAC,OAAO;cACZ9B,KAAK,EAAEvB,YAAY,CAACmB,KAAM;cAC1B0H,QAAQ,EAAE1F,iBAAkB;cAC5BkG,IAAI,EAAC,GAAG;cACRT,WAAW,EAAC;YAAkC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/J,OAAA;YAAK0J,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC3J,OAAA;cAAA2J,QAAA,EAAO;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtB/J,OAAA;cACE0E,IAAI,EAAC,OAAO;cACZ9B,KAAK,EAAEvB,YAAY,CAACoB,KAAM;cAC1ByH,QAAQ,EAAE1F,iBAAkB;cAC5BkG,IAAI,EAAC,GAAG;cACRT,WAAW,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/J,OAAA;YAAK0J,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3J,OAAA;cAAQgK,IAAI,EAAC,QAAQ;cAACG,OAAO,EAAEzD,SAAU;cAACgD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/J,OAAA;cAAQgK,IAAI,EAAC,QAAQ;cAACW,QAAQ,EAAE1J,OAAQ;cAACyI,SAAS,EAAC,UAAU;cAAAC,QAAA,EAC1D1I,OAAO,GAAG,iBAAiB,GAAIN,gBAAgB,GAAG,UAAU,GAAG;YAAS;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD/J,OAAA;MAAK0J,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC3J,OAAA;QAAO0J,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACjC3J,OAAA;UAAA2J,QAAA,eACE3J,OAAA;YAAA2J,QAAA,gBACE3J,OAAA;cAAA2J,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB/J,OAAA;cAAA2J,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf/J,OAAA;cAAA2J,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB/J,OAAA;cAAA2J,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjB/J,OAAA;cAAA2J,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf/J,OAAA;cAAA2J,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf/J,OAAA;cAAA2J,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR/J,OAAA;UAAA2J,QAAA,EACGT,kBAAkB,CAAClD,MAAM,KAAK,CAAC,gBAC9BhG,OAAA;YAAA2J,QAAA,eACE3J,OAAA;cAAI4K,OAAO,EAAC,GAAG;cAAClB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELb,kBAAkB,CAACb,GAAG,CAACxB,SAAS,IAAI;YAClC,MAAMgE,UAAU,GAAG9B,aAAa,CAAClC,SAAS,CAAC/E,MAAM,CAAC;YAClD,MAAMgJ,SAAS,GAAG,IAAIpJ,IAAI,CAACmF,SAAS,CAAChF,UAAU,CAAC,GAAG,IAAIH,IAAI,CAAC,CAAC;YAE7D,oBACE1B,OAAA;cAAuB0J,SAAS,EAAEoB,SAAS,GAAG,SAAS,GAAG,EAAG;cAAAnB,QAAA,gBAC3D3J,OAAA;gBAAI0J,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAE9C,SAAS,CAACtF;cAAe;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjE/J,OAAA;gBAAA2J,QAAA,EAAKd,eAAe,CAAChC,SAAS,CAACrF,UAAU;cAAC;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD/J,OAAA;gBAAA2J,QAAA,EAAK,IAAIjI,IAAI,CAACmF,SAAS,CAACpF,aAAa,CAAC,CAAC2G,kBAAkB,CAAC,OAAO;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxE/J,OAAA;gBAAI0J,SAAS,EAAEoB,SAAS,GAAG,cAAc,GAAG,EAAG;gBAAAnB,QAAA,GAC5C,IAAIjI,IAAI,CAACmF,SAAS,CAAChF,UAAU,CAAC,CAACuG,kBAAkB,CAAC,OAAO,CAAC,EAC1D0C,SAAS,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACL/J,OAAA;gBAAI0J,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAAE9C,SAAS,CAACzE,KAAK,CAACmG,cAAc,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnE/J,OAAA;gBAAA2J,QAAA,eACE3J,OAAA;kBACE0J,SAAS,EAAC,cAAc;kBACxBqB,KAAK,EAAE;oBAAEC,eAAe,EAAEH,UAAU,CAAC/H;kBAAM,CAAE;kBAAA6G,QAAA,EAE5CkB,UAAU,CAAChI;gBAAK;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL/J,OAAA;gBAAI0J,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACrB3J,OAAA;kBACEmK,OAAO,EAAEA,CAAA,KAAMvD,UAAU,CAACC,SAAS,CAAE;kBACrC6C,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,gCAAO;kBACbN,QAAQ,EAAE9D,SAAS,CAAC/E,MAAM,KAAK,WAAY;kBAAA6H,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/J,OAAA;kBACEmK,OAAO,EAAEA,CAAA,KAAM5C,cAAc,CAACV,SAAS,CAAE;kBACzC6C,SAAS,EAAC,WAAW;kBACrBuB,KAAK,EAAC,gCAAO;kBAAAtB,QAAA,EACd;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRlD,SAAS,CAAC/E,MAAM,KAAK,UAAU,iBAC9B9B,OAAA;kBACEmK,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAACL,SAAS,CAAC1B,EAAE,CAAE;kBAC9CuE,SAAS,EAAC,aAAa;kBACvBuB,KAAK,EAAC,2EAAe;kBAAAtB,QAAA,EACtB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACD/J,OAAA;kBACEmK,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAACD,SAAS,CAAC1B,EAAE,CAAE;kBAC1CuE,SAAS,EAAC,YAAY;kBACtBuB,KAAK,EAAC,oBAAK;kBACXN,QAAQ,EAAE9D,SAAS,CAAC/E,MAAM,KAAK,WAAY;kBAAA6H,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDElD,SAAS,CAAC1B,EAAE;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDjB,CAAC;UAET,CAAC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7J,EAAA,CA7yBID,UAAU;AAAAiL,EAAA,GAAVjL,UAAU;AA+yBhB,eAAeA,UAAU;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}