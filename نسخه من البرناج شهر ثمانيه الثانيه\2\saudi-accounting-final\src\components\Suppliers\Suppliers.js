import React, { useState, useEffect } from 'react';
import './Suppliers.css';
import database from '../../utils/database';

const Suppliers = ({ user, onBack }) => {
  const [suppliers, setSuppliers] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [isLoading, setIsLoading] = useState(false);

  const [newSupplier, setNewSupplier] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    taxNumber: '',
    type: 'local',
    paymentTerms: '30',
    discount: '0',
    notes: '',
    contactPerson: '',
    website: '',
    bankAccount: '',
    iban: ''
  });

  const supplierTypes = [
    { value: 'local', label: 'محلي' },
    { value: 'international', label: 'دولي' },
    { value: 'manufacturer', label: 'مصنع' },
    { value: 'distributor', label: 'موزع' },
    { value: 'wholesaler', label: 'تاجر جملة' }
  ];

  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = () => {
    try {
      const dbSuppliers = database.getSuppliers();
      setSuppliers(dbSuppliers);
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
    }
  };

  const handleAddSupplier = async () => {
    if (!newSupplier.name || !newSupplier.phone) {
      alert('يرجى ملء الحقول المطلوبة (الاسم والهاتف)');
      return;
    }

    setIsLoading(true);
    try {
      const supplierData = {
        ...newSupplier,
        paymentTerms: parseInt(newSupplier.paymentTerms) || 30,
        discount: parseFloat(newSupplier.discount) || 0
      };

      const savedSupplier = database.addSupplier(supplierData);
      setSuppliers([...suppliers, savedSupplier]);
      
      setNewSupplier({
        name: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        taxNumber: '',
        type: 'local',
        paymentTerms: '30',
        discount: '0',
        notes: '',
        contactPerson: '',
        website: '',
        bankAccount: '',
        iban: ''
      });
      
      setShowAddForm(false);
      alert('تم إضافة المورد بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة المورد:', error);
      alert('فشل في إضافة المورد');
    }
    setIsLoading(false);
  };

  const handleEditSupplier = async () => {
    if (!editingSupplier.name || !editingSupplier.phone) {
      alert('يرجى ملء الحقول المطلوبة (الاسم والهاتف)');
      return;
    }

    setIsLoading(true);
    try {
      const supplierData = {
        ...editingSupplier,
        paymentTerms: parseInt(editingSupplier.paymentTerms) || 30,
        discount: parseFloat(editingSupplier.discount) || 0
      };

      const updatedSupplier = database.updateSupplier(editingSupplier.id, supplierData);
      setSuppliers(suppliers.map(s => s.id === editingSupplier.id ? updatedSupplier : s));
      
      setEditingSupplier(null);
      alert('تم تحديث المورد بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث المورد:', error);
      alert('فشل في تحديث المورد');
    }
    setIsLoading(false);
  };

  const handleDeleteSupplier = (supplierId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      try {
        database.deleteSupplier(supplierId);
        setSuppliers(suppliers.filter(s => s.id !== supplierId));
        alert('تم حذف المورد بنجاح');
      } catch (error) {
        console.error('خطأ في حذف المورد:', error);
        alert('فشل في حذف المورد');
      }
    }
  };

  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.phone.includes(searchTerm) ||
                         supplier.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !selectedType || supplier.type === selectedType;
    return matchesSearch && matchesType;
  });

  const sortedSuppliers = [...filteredSuppliers].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'type':
        return a.type.localeCompare(b.type);
      case 'paymentTerms':
        return a.paymentTerms - b.paymentTerms;
      case 'createdAt':
        return new Date(b.createdAt) - new Date(a.createdAt);
      default:
        return 0;
    }
  });

  const getTotalSuppliers = () => suppliers.length;
  const getLocalSuppliers = () => suppliers.filter(s => s.type === 'local').length;
  const getInternationalSuppliers = () => suppliers.filter(s => s.type === 'international').length;
  const getAveragePaymentTerms = () => {
    const totalTerms = suppliers.reduce((total, s) => total + s.paymentTerms, 0);
    return suppliers.length > 0 ? Math.round(totalTerms / suppliers.length) : 0;
  };

  const getSupplierTypeLabel = (type) => {
    const typeObj = supplierTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  return (
    <div className="suppliers-container">
      <div className="suppliers-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ← العودة للوحة التحكم
          </button>
          <h1>🏭 إدارة الموردين</h1>
          <button 
            onClick={() => setShowAddForm(true)} 
            className="add-btn"
            disabled={isLoading}
          >
            ➕ إضافة مورد جديد
          </button>
        </div>
      </div>

      <div className="suppliers-stats">
        <div className="stat-card">
          <div className="stat-icon">🏭</div>
          <div className="stat-info">
            <h3>إجمالي الموردين</h3>
            <p>{getTotalSuppliers()}</p>
          </div>
        </div>
        <div className="stat-card info">
          <div className="stat-icon">🇸🇦</div>
          <div className="stat-info">
            <h3>موردين محليين</h3>
            <p>{getLocalSuppliers()}</p>
          </div>
        </div>
        <div className="stat-card warning">
          <div className="stat-icon">🌍</div>
          <div className="stat-info">
            <h3>موردين دوليين</h3>
            <p>{getInternationalSuppliers()}</p>
          </div>
        </div>
        <div className="stat-card success">
          <div className="stat-icon">📅</div>
          <div className="stat-info">
            <h3>متوسط مدة السداد</h3>
            <p>{getAveragePaymentTerms()} يوم</p>
          </div>
        </div>
      </div>

      <div className="suppliers-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث بالاسم أو الهاتف أو الإيميل..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="filter-select"
        >
          <option value="">جميع الأنواع</option>
          {supplierTypes.map(type => (
            <option key={type.value} value={type.value}>{type.label}</option>
          ))}
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="sort-select"
        >
          <option value="name">ترتيب بالاسم</option>
          <option value="type">ترتيب بالنوع</option>
          <option value="paymentTerms">ترتيب بمدة السداد</option>
          <option value="createdAt">ترتيب بتاريخ الإضافة</option>
        </select>
      </div>

      <div className="suppliers-table-container">
        <table className="suppliers-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>النوع</th>
              <th>الهاتف</th>
              <th>الإيميل</th>
              <th>المدينة</th>
              <th>مدة السداد</th>
              <th>الخصم</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {sortedSuppliers.map(supplier => (
              <tr key={supplier.id}>
                <td>
                  <div className="supplier-name">
                    <strong>{supplier.name}</strong>
                    {supplier.contactPerson && <small>جهة الاتصال: {supplier.contactPerson}</small>}
                  </div>
                </td>
                <td>
                  <span className={`supplier-type ${supplier.type}`}>
                    {getSupplierTypeLabel(supplier.type)}
                  </span>
                </td>
                <td>{supplier.phone}</td>
                <td>{supplier.email || '-'}</td>
                <td>{supplier.city || '-'}</td>
                <td>{supplier.paymentTerms} يوم</td>
                <td>{supplier.discount}%</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      onClick={() => setEditingSupplier(supplier)}
                      className="edit-btn"
                      disabled={isLoading}
                      title="تعديل"
                    >
                      ✏️
                    </button>
                    <button 
                      onClick={() => handleDeleteSupplier(supplier.id)}
                      className="delete-btn"
                      disabled={isLoading}
                      title="حذف"
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {sortedSuppliers.length === 0 && (
          <div className="no-suppliers">
            <p>لا توجد موردين تطابق البحث</p>
          </div>
        )}
      </div>

      {showAddForm && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>إضافة مورد جديد</h2>
              <button onClick={() => setShowAddForm(false)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم المورد *</label>
                  <input
                    type="text"
                    value={newSupplier.name}
                    onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                    placeholder="أدخل اسم المورد"
                  />
                </div>

                <div className="form-group">
                  <label>نوع المورد</label>
                  <select
                    value={newSupplier.type}
                    onChange={(e) => setNewSupplier({...newSupplier, type: e.target.value})}
                  >
                    {supplierTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف *</label>
                  <input
                    type="tel"
                    value={newSupplier.phone}
                    onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                    placeholder="05xxxxxxxx"
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={newSupplier.email}
                    onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="form-group">
                  <label>المدينة</label>
                  <input
                    type="text"
                    value={newSupplier.city}
                    onChange={(e) => setNewSupplier({...newSupplier, city: e.target.value})}
                    placeholder="الرياض"
                  />
                </div>

                <div className="form-group">
                  <label>الرقم الضريبي</label>
                  <input
                    type="text"
                    value={newSupplier.taxNumber}
                    onChange={(e) => setNewSupplier({...newSupplier, taxNumber: e.target.value})}
                    placeholder="300000000000003"
                  />
                </div>

                <div className="form-group">
                  <label>مدة السداد (يوم)</label>
                  <input
                    type="number"
                    value={newSupplier.paymentTerms}
                    onChange={(e) => setNewSupplier({...newSupplier, paymentTerms: e.target.value})}
                    placeholder="30"
                  />
                </div>

                <div className="form-group">
                  <label>نسبة الخصم (%)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newSupplier.discount}
                    onChange={(e) => setNewSupplier({...newSupplier, discount: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>جهة الاتصال</label>
                  <input
                    type="text"
                    value={newSupplier.contactPerson}
                    onChange={(e) => setNewSupplier({...newSupplier, contactPerson: e.target.value})}
                    placeholder="اسم الشخص المسؤول"
                  />
                </div>

                <div className="form-group">
                  <label>الموقع الإلكتروني</label>
                  <input
                    type="url"
                    value={newSupplier.website}
                    onChange={(e) => setNewSupplier({...newSupplier, website: e.target.value})}
                    placeholder="https://example.com"
                  />
                </div>

                <div className="form-group">
                  <label>رقم الحساب البنكي</label>
                  <input
                    type="text"
                    value={newSupplier.bankAccount}
                    onChange={(e) => setNewSupplier({...newSupplier, bankAccount: e.target.value})}
                    placeholder="رقم الحساب"
                  />
                </div>

                <div className="form-group">
                  <label>رقم الآيبان</label>
                  <input
                    type="text"
                    value={newSupplier.iban}
                    onChange={(e) => setNewSupplier({...newSupplier, iban: e.target.value})}
                    placeholder="************************"
                  />
                </div>

                <div className="form-group full-width">
                  <label>العنوان</label>
                  <textarea
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                    placeholder="العنوان التفصيلي"
                    rows="2"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={newSupplier.notes}
                    onChange={(e) => setNewSupplier({...newSupplier, notes: e.target.value})}
                    placeholder="ملاحظات إضافية"
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowAddForm(false)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleAddSupplier} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري الحفظ...' : 'حفظ المورد'}
              </button>
            </div>
          </div>
        </div>
      )}

      {editingSupplier && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>تعديل المورد</h2>
              <button onClick={() => setEditingSupplier(null)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم المورد *</label>
                  <input
                    type="text"
                    value={editingSupplier.name}
                    onChange={(e) => setEditingSupplier({...editingSupplier, name: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>نوع المورد</label>
                  <select
                    value={editingSupplier.type}
                    onChange={(e) => setEditingSupplier({...editingSupplier, type: e.target.value})}
                  >
                    {supplierTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف *</label>
                  <input
                    type="tel"
                    value={editingSupplier.phone}
                    onChange={(e) => setEditingSupplier({...editingSupplier, phone: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={editingSupplier.email || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, email: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>المدينة</label>
                  <input
                    type="text"
                    value={editingSupplier.city || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, city: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الرقم الضريبي</label>
                  <input
                    type="text"
                    value={editingSupplier.taxNumber || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, taxNumber: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>مدة السداد (يوم)</label>
                  <input
                    type="number"
                    value={editingSupplier.paymentTerms}
                    onChange={(e) => setEditingSupplier({...editingSupplier, paymentTerms: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>نسبة الخصم (%)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingSupplier.discount}
                    onChange={(e) => setEditingSupplier({...editingSupplier, discount: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>جهة الاتصال</label>
                  <input
                    type="text"
                    value={editingSupplier.contactPerson || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, contactPerson: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الموقع الإلكتروني</label>
                  <input
                    type="url"
                    value={editingSupplier.website || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, website: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>رقم الحساب البنكي</label>
                  <input
                    type="text"
                    value={editingSupplier.bankAccount || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, bankAccount: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>رقم الآيبان</label>
                  <input
                    type="text"
                    value={editingSupplier.iban || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, iban: e.target.value})}
                  />
                </div>

                <div className="form-group full-width">
                  <label>العنوان</label>
                  <textarea
                    value={editingSupplier.address || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, address: e.target.value})}
                    rows="2"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={editingSupplier.notes || ''}
                    onChange={(e) => setEditingSupplier({...editingSupplier, notes: e.target.value})}
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setEditingSupplier(null)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleEditSupplier} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري التحديث...' : 'تحديث المورد'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Suppliers;
