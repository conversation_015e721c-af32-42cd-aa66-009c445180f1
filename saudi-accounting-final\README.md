# 🏢 نظام المحاسبة السعودي
## Saudi Accounting System

نظام محاسبة متكامل مصمم خصيصاً للشركات السعودية مع دعم كامل للغة العربية وضريبة القيمة المضافة.

---

## 🚀 التشغيل السريع / Quick Start

### 🖱️ الطريقة السهلة / Easy Way
انقر مرتين على ملف:
```
start-app.bat
```

### 💻 الطريقة التقنية / Technical Way
```bash
npm install
npm start
```

---

## 📋 المتطلبات / Requirements

- **Node.js** 16.0 أو أحدث
- **npm** 8.0 أو أحدث
- **متصفح حديث** (Chrome, Firefox, Safari, Edge)

---

## 🎯 الميزات الرئيسية / Main Features

### 💰 نقطة البيع المتقدمة / Advanced Point of Sale
- إنشاء فواتير سريعة وذكية
- حساب ضريبة القيمة المضافة (15%)
- طباعة الفواتير الاحترافية
- إدارة طرق الدفع المتعددة
- خصومات وعروض ترويجية
- دعم الباركود

### 📦 إدارة المخزون الذكية / Smart Inventory Management
- تتبع المنتجات والكميات في الوقت الفعلي
- إنذارات المخزون المنخفض التلقائية
- إدارة حركات المخزون التفصيلية
- تقارير الجرد الدورية
- إدارة الفئات والوحدات
- تتبع المنتجات التالفة والمنتهية الصلاحية

### 💳 إدارة المدفوعات الشاملة / Comprehensive Payment Management
- تتبع جميع أنواع المدفوعات
- دعم طرق الدفع المتعددة (نقد، بطاقة، تحويل، شيك، محفظة رقمية)
- إدارة الأقساط والدفعات الجزئية
- ربط المدفوعات بالفواتير
- تقارير المدفوعات التفصيلية

### 👥 إدارة العملاء والموردين / Customer & Supplier Management
- قاعدة بيانات شاملة للعملاء والموردين
- سجل المعاملات التفصيلي
- معلومات الاتصال والعناوين
- تتبع الأرصدة والمديونيات
- تقارير العملاء والموردين

### 👨‍💼 إدارة الموظفين / Employee Management
- قاعدة بيانات الموظفين الكاملة
- إدارة الرواتب والأقسام
- تتبع تواريخ التوظيف والحالات
- معلومات الاتصال في الطوارئ
- تقارير الموارد البشرية

### 📊 النظام المحاسبي المتكامل / Integrated Accounting System
- دليل الحسابات الشامل
- القيود المحاسبية التلقائية
- الميزانية العمومية
- قائمة الدخل والأرباح والخسائر
- التدفقات النقدية
- التقارير المالية المعتمدة

### 📈 التقارير والتحليلات / Reports & Analytics
- تقارير المبيعات اليومية والشهرية
- تقارير المخزون والحركات
- التقارير المالية الشاملة
- تقارير الموظفين والرواتب
- تصدير PDF/Excel
- رسوم بيانية تفاعلية

### ⚙️ إعدادات النظام المتقدمة / Advanced System Settings
- إعدادات الشركة والضرائب
- تخصيص الفواتير والتقارير
- إدارة المستخدمين والصلاحيات
- إعدادات النسخ الاحتياطي
- تخصيص الواجهة والألوان

### 💾 النسخ الاحتياطي الذكي / Smart Backup System
- نسخ احتياطي تلقائي ويدوي
- استعادة البيانات الآمنة
- تشفير البيانات
- تاريخ النسخ الاحتياطية
- تصدير واستيراد البيانات

### 🔔 نظام الإشعارات الذكي / Smart Notification System
- إشعارات المخزون المنخفض
- تنبيهات الدفعات المستحقة
- إشعارات النسخ الاحتياطي
- تنبيهات النظام والأمان
- إعدادات الإشعارات المخصصة

### 📋 القيود اليومية المتقدمة / Advanced Journal Entries
- إنشاء وإدارة القيود المحاسبية
- قيود يدوية وتلقائية
- ترحيل القيود وتحديث الأرصدة
- أنواع قيود متعددة (مبيعات، مشتريات، تسويات)
- ميزان المراجعة التلقائي

### ↩️ إدارة المرتجعات الشاملة / Comprehensive Returns Management
- مرتجعات المبيعات والمشتريات
- مرتجعات التلف وانتهاء الصلاحية
- ربط المرتجعات بالفواتير الأصلية
- تحديث المخزون التلقائي
- تقارير المرتجعات التفصيلية

### 💼 عروض الأسعار الاحترافية / Professional Quotations
- إنشاء عروض أسعار تفاعلية
- تحويل العروض إلى فواتير
- إدارة صلاحية العروض
- شروط دفع مخصصة
- طباعة عروض احترافية

### 📊 إدارة الباركود المتقدمة / Advanced Barcode Management
- إنشاء باركود تلقائي للمنتجات
- طباعة ملصقات باركود مخصصة
- مسح الباركود والبحث
- أنواع باركود متعددة
- إعدادات طباعة متقدمة

### 🧾 سندات القبض والصرف / Receipt & Payment Vouchers
- سندات قبض وصرف احترافية
- ربط السندات بالحسابات
- طرق دفع متعددة
- قيود محاسبية تلقائية
- طباعة سندات رسمية

### 📈 التقارير المالية المتقدمة / Advanced Financial Reports
- قائمة الدخل التفصيلية
- الميزانية العمومية
- قائمة التدفقات النقدية
- ميزان المراجعة
- التقرير الضريبي
- تحليل الربحية
- إغلاق الوردية
- تقارير العملاء والموردين

## 🛠️ الميزات التقنية / Technical Features

### 🔒 الأمان والحماية / Security & Protection
- نظام مصادقة متقدم
- تشفير البيانات المحلية
- نسخ احتياطية آمنة
- سجل العمليات والتدقيق
- حماية من فقدان البيانات

### 📱 واجهة المستخدم / User Interface
- تصميم عصري ومتجاوب
- دعم اللغة العربية بالكامل
- واجهة سهلة الاستخدام
- ألوان وأيقونات واضحة
- تجربة مستخدم محسنة

### 💾 إدارة البيانات / Data Management
- قاعدة بيانات محلية آمنة
- نسخ احتياطي تلقائي
- استيراد وتصدير البيانات
- ضغط وتحسين البيانات
- استرداد البيانات المحذوفة

### 🖨️ الطباعة والتصدير / Printing & Export
- طباعة فواتير احترافية
- تصدير إلى PDF و Excel
- قوالب طباعة مخصصة
- طباعة تقارير مفصلة
- إعدادات طباعة متقدمة

### 📊 المحاسبة المتقدمة / Advanced Accounting
- نظام القيد المزدوج
- ترحيل تلقائي للقيود
- ميزان المراجعة الفوري
- تحديث الأرصدة التلقائي
- تتبع حركة الحسابات

### 🔄 التكامل والربط / Integration & Connectivity
- ربط المرتجعات بالفواتير
- تحويل العروض لفواتير
- ربط السندات بالحسابات
- تحديث المخزون التلقائي
- تزامن البيانات الفوري

### 📈 التحليلات والذكاء / Analytics & Intelligence
- تحليل الربحية التفصيلي
- تقارير الأداء المالي
- مؤشرات الأداء الرئيسية
- تحليل اتجاهات المبيعات
- توقعات مالية ذكية

---

## 📁 هيكل المشروع / Project Structure

```
saudi-accounting-final/
├── 📄 package.json              # إعدادات المشروع
├── 📄 start-app.bat             # ملف التشغيل
├── 📁 public/                   # الملفات العامة
├── 📁 src/                      # الكود المصدري
│   ├── 📄 App.js                # التطبيق الرئيسي
│   ├── 📁 components/           # مكونات الواجهة
│   └── 📁 utils/                # الأدوات المساعدة
└── 📁 node_modules/             # التبعيات (يتم إنشاؤه تلقائياً)
```

---

## 🔧 التثبيت / Installation

### 1️⃣ تحميل المشروع
انسخ مجلد `saudi-accounting-final` كاملاً

### 2️⃣ تثبيت التبعيات
```bash
cd saudi-accounting-final
npm install
```

### 3️⃣ تشغيل المشروع
```bash
npm start
```

أو انقر على `start-app.bat`

---

## 📖 كيفية الاستخدام / How to Use

### 🔐 تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 🏪 إعداد المتجر
1. انتقل إلى "الإعدادات"
2. أدخل بيانات الشركة
3. حدد العملة والضرائب
4. احفظ الإعدادات

### 📦 إضافة المنتجات
1. اذهب إلى "المنتجات"
2. اضغط "إضافة منتج جديد"
3. أدخل تفاصيل المنتج
4. حدد السعر والمخزون

### 🧾 إنشاء فاتورة
1. انتقل إلى "نقطة البيع"
2. اختر المنتجات
3. أدخل بيانات العميل
4. اطبع الفاتورة

### 👥 إدارة العملاء
1. اذهب إلى "العملاء"
2. أضف عميل جديد
3. تتبع المعاملات
4. راجع الأرصدة

### 💼 عروض الأسعار
1. انتقل إلى "عروض الأسعار"
2. أنشئ عرض سعر جديد
3. أرسل للعميل
4. حوّل لفاتورة عند الموافقة

### ↩️ إدارة المرتجعات
1. اذهب إلى "المرتجعات"
2. أنشئ مرتجع جديد
3. اربطه بالفاتورة الأصلية
4. عالج المرتجع وحدّث المخزون

### 📊 إدارة الباركود
1. انتقل إلى "إدارة الباركود"
2. اختر المنتجات
3. اطبع ملصقات الباركود
4. استخدم الماسح للبحث

### 🧾 السندات المالية
1. اذهب إلى "سندات القبض والصرف"
2. أنشئ سند قبض أو صرف
3. اربطه بالحساب المناسب
4. اطبع السند الرسمي

### 📋 القيود المحاسبية
1. انتقل إلى "القيود اليومية"
2. أنشئ قيد محاسبي
3. رحّل القيد للحسابات
4. راجع ميزان المراجعة

### 📈 التقارير المتقدمة
1. اذهب إلى "التقارير المتقدمة"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اطبع أو صدّر التقرير

---

## 🌐 الوصول للتطبيق / Access Application

بعد التشغيل، افتح المتصفح على:
```
http://localhost:3000
```

---

## 📚 الملفات المرجعية / Reference Files

- **📄 project-files-summary.md** - ملخص شامل للمشروع
- **📄 نقل-المشروع-دليل-شامل.md** - دليل نقل المشروع
- **📄 قائمة-النقل-السريعة.txt** - قائمة مرجعية سريعة
- **📄 setup-new-computer.bat** - إعداد كمبيوتر جديد

---

## 🔧 استكشاف الأخطاء / Troubleshooting

### ❗ المشاكل الشائعة / Common Issues

**"Module not found"**
```bash
npm install
```

**"Port 3000 is already in use"**
- أغلق التطبيقات الأخرى التي تستخدم البورت 3000
- أو استخدم بورت آخر: `npm start -- --port 3001`

**"Permission denied"**
- شغل Command Prompt كـ Administrator
- أو استخدم PowerShell كـ Administrator

---

## 💾 حفظ البيانات / Data Storage

البيانات محفوظة محلياً في المتصفح باستخدام `localStorage`. 
لنسخ احتياطي، استخدم ميزة "تصدير البيانات" في التطبيق.

---

## 🎨 التخصيص / Customization

يمكن تخصيص:
- ألوان الواجهة في ملفات CSS
- إعدادات الضريبة في `src/utils/database.js`
- تفاصيل الشركة في الإعدادات

---

## 📞 الدعم / Support

للمساعدة أو الاستفسارات:
- راجع الملفات المرجعية
- تحقق من ملف استكشاف الأخطاء
- تأكد من تثبيت Node.js بشكل صحيح

---

## 📄 الترخيص / License

هذا المشروع مخصص للاستخدام التعليمي والتجاري.

---

## 📊 إحصائيات المشروع / Project Statistics

**نوع المشروع:** تطبيق ويب للمحاسبة المتكاملة
**التقنيات:** React.js, CSS3, LocalStorage, JavaScript ES6+
**اللغة:** العربية مع دعم الإنجليزية
**الحجم:** 35+ ملف رئيسي + التبعيات
**المكونات:** 12 مكون رئيسي متكامل
**الوظائف:** 100+ ميزة محاسبية ومالية متقدمة
**قاعدة البيانات:** LocalStorage مع 10+ جداول
**الأمان:** تشفير البيانات ونسخ احتياطي آمن
**التوافق:** جميع المتصفحات الحديثة
**الاستجابة:** تصميم متجاوب لجميع الأجهزة

### 🏗️ المكونات الرئيسية / Main Components
- 🏠 **Dashboard** - لوحة التحكم الرئيسية
- 🛒 **POS** - نقطة البيع المتقدمة
- 📦 **Products** - إدارة المنتجات
- 👥 **Customers** - إدارة العملاء
- 🏭 **Suppliers** - إدارة الموردين
- 📊 **Accounting** - النظام المحاسبي
- 💳 **Payments** - إدارة المدفوعات
- 📦 **Inventory** - إدارة المخزون المتقدم
- 👨‍💼 **Employees** - إدارة الموظفين
- ⚙️ **Settings** - إعدادات النظام
- 💾 **Backup** - النسخ الاحتياطي
- 🔔 **Notifications** - نظام الإشعارات
- 📈 **Reports** - التقارير والتحليلات

---

**✨ نتمنى لك تجربة ممتعة مع نظام المحاسبة السعودي المتطور! ✨**
