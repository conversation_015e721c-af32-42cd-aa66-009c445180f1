import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Backup.css';

const Backup = () => {
  const [backupHistory, setBackupHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [backupProgress, setBackupProgress] = useState(0);
  const [showRestoreConfirm, setShowRestoreConfirm] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState(null);

  useEffect(() => {
    loadBackupHistory();
  }, []);

  const loadBackupHistory = () => {
    try {
      const history = JSON.parse(localStorage.getItem('backup_history') || '[]');
      setBackupHistory(history.sort((a, b) => new Date(b.date) - new Date(a.date)));
    } catch (error) {
      console.error('خطأ في تحميل تاريخ النسخ الاحتياطي:', error);
    }
  };

  const createBackup = async () => {
    setLoading(true);
    setMessage('');
    setBackupProgress(0);

    try {
      // محاكاة تقدم النسخ الاحتياطي
      const steps = [
        'جاري تجميع بيانات الفواتير...',
        'جاري تجميع بيانات المنتجات...',
        'جاري تجميع بيانات العملاء...',
        'جاري تجميع بيانات الموردين...',
        'جاري تجميع البيانات المحاسبية...',
        'جاري تجميع بيانات المدفوعات...',
        'جاري تجميع بيانات المخزون...',
        'جاري تجميع بيانات الموظفين...',
        'جاري ضغط البيانات...',
        'جاري إنشاء النسخة الاحتياطية...'
      ];

      for (let i = 0; i < steps.length; i++) {
        setMessage(steps[i]);
        setBackupProgress(((i + 1) / steps.length) * 100);
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // إنشاء النسخة الاحتياطية
      const backupData = database.exportAllData();
      const backupInfo = {
        id: Date.now(),
        date: new Date().toISOString(),
        size: JSON.stringify(backupData).length,
        version: '1.0.0',
        description: 'نسخة احتياطية تلقائية',
        dataTypes: [
          'الإعدادات',
          'الفواتير',
          'المنتجات',
          'العملاء',
          'الموردين',
          'الحسابات',
          'القيود المحاسبية',
          'المدفوعات',
          'حركات المخزون',
          'الموظفين'
        ]
      };

      // حفظ النسخة الاحتياطية
      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup-${new Date().toISOString().split('T')[0]}-${Date.now()}.json`;
      link.click();
      URL.revokeObjectURL(url);

      // حفظ معلومات النسخة الاحتياطية في التاريخ
      const history = JSON.parse(localStorage.getItem('backup_history') || '[]');
      history.push(backupInfo);
      localStorage.setItem('backup_history', JSON.stringify(history));

      setMessage('تم إنشاء النسخة الاحتياطية بنجاح!');
      loadBackupHistory();
      setTimeout(() => {
        setMessage('');
        setBackupProgress(0);
      }, 3000);

    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      setMessage('خطأ في إنشاء النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const handleFileRestore = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const backupData = JSON.parse(e.target.result);
          setSelectedBackup(backupData);
          setShowRestoreConfirm(true);
        } catch (error) {
          setMessage('خطأ في قراءة ملف النسخة الاحتياطية - تأكد من صحة الملف');
        }
      };
      reader.readAsText(file);
    }
  };

  const confirmRestore = async () => {
    if (!selectedBackup) return;

    setLoading(true);
    setMessage('جاري استعادة البيانات...');

    try {
      await database.importAllData(selectedBackup);
      setMessage('تم استعادة البيانات بنجاح! سيتم إعادة تحميل الصفحة...');
      
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('خطأ في استعادة البيانات:', error);
      setMessage('خطأ في استعادة البيانات');
    } finally {
      setLoading(false);
      setShowRestoreConfirm(false);
      setSelectedBackup(null);
    }
  };

  const cancelRestore = () => {
    setShowRestoreConfirm(false);
    setSelectedBackup(null);
  };

  const deleteBackupRecord = (id) => {
    if (window.confirm('هل أنت متأكد من حذف سجل هذه النسخة الاحتياطية؟')) {
      const history = backupHistory.filter(backup => backup.id !== id);
      localStorage.setItem('backup_history', JSON.stringify(history));
      setBackupHistory(history);
      setMessage('تم حذف سجل النسخة الاحتياطية');
      setTimeout(() => setMessage(''), 3000);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDataStats = () => {
    try {
      return {
        invoices: database.getInvoices().length,
        products: database.getProducts().length,
        customers: database.getCustomers().length,
        suppliers: database.getSuppliers().length,
        payments: database.getPayments().length,
        employees: database.getEmployees().length,
        movements: database.getInventoryMovements().length
      };
    } catch (error) {
      return {
        invoices: 0,
        products: 0,
        customers: 0,
        suppliers: 0,
        payments: 0,
        employees: 0,
        movements: 0
      };
    }
  };

  const stats = getDataStats();

  return (
    <div className="backup-container">
      <div className="backup-header">
        <h1>💾 إدارة النسخ الاحتياطي</h1>
        <p>حماية وإدارة بيانات النظام</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Progress Bar */}
      {loading && backupProgress > 0 && (
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${backupProgress}%` }}
            ></div>
          </div>
          <span className="progress-text">{Math.round(backupProgress)}%</span>
        </div>
      )}

      {/* Data Statistics */}
      <div className="data-stats">
        <h3>📊 إحصائيات البيانات الحالية</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-icon">🧾</span>
            <span className="stat-label">الفواتير</span>
            <span className="stat-value">{stats.invoices}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">📦</span>
            <span className="stat-label">المنتجات</span>
            <span className="stat-value">{stats.products}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">👥</span>
            <span className="stat-label">العملاء</span>
            <span className="stat-value">{stats.customers}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">🏭</span>
            <span className="stat-label">الموردين</span>
            <span className="stat-value">{stats.suppliers}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">💳</span>
            <span className="stat-label">المدفوعات</span>
            <span className="stat-value">{stats.payments}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">👨‍💼</span>
            <span className="stat-label">الموظفين</span>
            <span className="stat-value">{stats.employees}</span>
          </div>
        </div>
      </div>

      {/* Backup Actions */}
      <div className="backup-actions">
        <div className="action-card create-backup">
          <h3>📤 إنشاء نسخة احتياطية</h3>
          <p>إنشاء نسخة احتياطية كاملة من جميع بيانات النظام</p>
          <button 
            onClick={createBackup} 
            disabled={loading}
            className="backup-btn create"
          >
            {loading ? '⏳ جاري الإنشاء...' : '📤 إنشاء نسخة احتياطية'}
          </button>
        </div>

        <div className="action-card restore-backup">
          <h3>📥 استعادة نسخة احتياطية</h3>
          <p>استعادة البيانات من ملف نسخة احتياطية</p>
          <input
            type="file"
            accept=".json"
            onChange={handleFileRestore}
            className="file-input"
            id="restore-file"
          />
          <label htmlFor="restore-file" className="backup-btn restore">
            📥 اختيار ملف للاستعادة
          </label>
        </div>
      </div>

      {/* Backup History */}
      <div className="backup-history">
        <h3>📋 تاريخ النسخ الاحتياطي</h3>
        {backupHistory.length === 0 ? (
          <div className="no-backups">
            <p>لا توجد نسخ احتياطية محفوظة</p>
          </div>
        ) : (
          <div className="history-list">
            {backupHistory.map(backup => (
              <div key={backup.id} className="history-item">
                <div className="backup-info">
                  <div className="backup-date">
                    📅 {new Date(backup.date).toLocaleString('ar-SA')}
                  </div>
                  <div className="backup-details">
                    <span className="backup-size">📊 {formatFileSize(backup.size)}</span>
                    <span className="backup-version">🔖 {backup.version}</span>
                  </div>
                  <div className="backup-description">
                    {backup.description}
                  </div>
                  <div className="backup-types">
                    {backup.dataTypes.map(type => (
                      <span key={type} className="data-type-tag">{type}</span>
                    ))}
                  </div>
                </div>
                <div className="backup-actions-small">
                  <button
                    onClick={() => deleteBackupRecord(backup.id)}
                    className="delete-backup-btn"
                    title="حذف السجل"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Restore Confirmation Modal */}
      {showRestoreConfirm && (
        <div className="modal-overlay">
          <div className="confirm-modal">
            <div className="modal-header">
              <h3>⚠️ تأكيد استعادة البيانات</h3>
            </div>
            <div className="modal-content">
              <p>
                <strong>تحذير:</strong> ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية 
                بالبيانات الموجودة في النسخة الاحتياطية.
              </p>
              <p>هل أنت متأكد من المتابعة؟</p>
              
              {selectedBackup && selectedBackup.exportDate && (
                <div className="backup-preview">
                  <h4>معلومات النسخة الاحتياطية:</h4>
                  <p>📅 تاريخ الإنشاء: {new Date(selectedBackup.exportDate).toLocaleString('ar-SA')}</p>
                  <p>🔖 الإصدار: {selectedBackup.version}</p>
                </div>
              )}
            </div>
            <div className="modal-actions">
              <button onClick={cancelRestore} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={confirmRestore} disabled={loading} className="confirm-btn">
                {loading ? '⏳ جاري الاستعادة...' : '✅ تأكيد الاستعادة'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Backup;
