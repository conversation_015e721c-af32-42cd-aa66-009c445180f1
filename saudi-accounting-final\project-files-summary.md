# نظام المحاسبة السعودي - ملخص ملفات المشروع
## Saudi Accounting System - Project Files Summary

### 📁 بنية المشروع / Project Structure

```
saudi-accounting-final/
├── 📄 package.json                    # إعدادات المشروع والتبعيات
├── 📄 package-lock.json               # قفل إصدارات التبعيات
├── 📁 public/
│   └── 📄 index.html                  # الصفحة الرئيسية HTML
├── 📁 src/                            # مجلد الكود المصدري
│   ├── 📄 index.js                    # نقطة دخول التطبيق
│   ├── 📄 index.css                   # أنماط عامة
│   ├── 📄 App.js                      # المكون الرئيسي
│   ├── 📄 App.css                     # أنماط المكون الرئيسي
│   ├── 📁 components/                 # مكونات التطبيق
│   │   ├── 📁 Dashboard/              # لوحة التحكم
│   │   │   ├── 📄 Dashboard.js
│   │   │   └── 📄 Dashboard.css
│   │   ├── 📁 POS/                    # نقطة البيع
│   │   │   ├── 📄 POS.js
│   │   │   └── 📄 POS.css
│   │   ├── 📁 Products/               # إدارة المنتجات
│   │   │   ├── 📄 Products.js
│   │   │   └── 📄 Products.css
│   │   ├── 📁 Customers/              # إدارة العملاء
│   │   │   ├── 📄 Customers.js
│   │   │   └── 📄 Customers.css
│   │   ├── 📁 Suppliers/              # إدارة الموردين
│   │   │   ├── 📄 Suppliers.js
│   │   │   └── 📄 Suppliers.css
│   │   ├── 📁 Accounting/             # النظام المحاسبي
│   │   │   ├── 📄 Accounting.js
│   │   │   └── 📄 Accounting.css
│   │   ├── 📁 Reports/                # التقارير
│   │   │   ├── 📄 Reports.js
│   │   │   └── 📄 Reports.css
│   │   ├── 📁 Auth/                   # المصادقة
│   │   │   ├── 📄 Login.js
│   │   │   └── 📄 Login.css
│   │   └── 📁 Login/                  # تسجيل الدخول (إضافي)
│   │       ├── 📄 Login.js
│   │       └── 📄 Login.css
│   └── 📁 utils/                      # أدوات مساعدة
│       ├── 📄 database.js             # قاعدة البيانات المحلية
│       └── 📄 arabicNLP.js            # معالجة النصوص العربية
├── 📁 node_modules/                   # تبعيات المشروع
├── 📄 start-app.bat                   # ملف تشغيل Windows
├── 📄 start-app.ps1                   # ملف PowerShell
└── 📄 start.ps1                       # ملف تشغيل إضافي
```

### 🔧 الملفات الأساسية / Core Files

#### 1. package.json
- إعدادات المشروع والتبعيات
- سكريبت التشغيل والبناء
- تبعيات React و Tailwind CSS

#### 2. src/index.js
- نقطة دخول التطبيق
- تهيئة React DOM

#### 3. src/App.js
- المكون الرئيسي للتطبيق
- إدارة التنقل بين الصفحات
- حالة المصادقة

#### 4. src/utils/database.js
- نظام قاعدة البيانات المحلية
- إدارة البيانات في localStorage
- وظائف CRUD للكيانات المختلفة

### 📊 المكونات الرئيسية / Main Components

#### 1. Dashboard (لوحة التحكم)
- عرض الإحصائيات العامة
- ملخص المبيعات والأرباح
- المنتجات منخفضة المخزون

#### 2. POS (نقطة البيع)
- إنشاء الفواتير
- إضافة المنتجات للفاتورة
- حساب الضريبة والإجمالي

#### 3. Products (المنتجات)
- إدارة قائمة المنتجات
- إضافة وتعديل وحذف المنتجات
- تتبع المخزون

#### 4. Customers (العملاء)
- إدارة بيانات العملاء
- سجل المعاملات
- معلومات الاتصال

#### 5. Suppliers (الموردين)
- إدارة بيانات الموردين
- تتبع المشتريات
- معلومات التواصل

#### 6. Accounting (المحاسبة)
- إدارة الحسابات المالية
- القيود المحاسبية
- الميزانية العمومية

#### 7. Reports (التقارير)
- تقارير المبيعات
- تقارير المخزون
- التقارير المالية

#### 8. JournalEntries (القيود اليومية)
- إنشاء وإدارة القيود المحاسبية
- ترحيل القيود التلقائي
- ميزان المراجعة

#### 9. Returns (المرتجعات)
- إدارة مرتجعات المبيعات والمشتريات
- ربط المرتجعات بالفواتير الأصلية
- تحديث المخزون التلقائي

#### 10. BarcodeManager (إدارة الباركود)
- إنشاء باركود للمنتجات
- طباعة ملصقات الباركود
- مسح الباركود والبحث

#### 11. Quotations (عروض الأسعار)
- إنشاء عروض أسعار احترافية
- تحويل العروض إلى فواتير
- إدارة صلاحية العروض

#### 12. Vouchers (سندات القبض والصرف)
- إنشاء سندات القبض والصرف
- ربط السندات بالحسابات
- طباعة سندات رسمية

#### 13. AdvancedReports (التقارير المتقدمة)
- قائمة الدخل التفصيلية
- الميزانية العمومية
- قائمة التدفقات النقدية
- ميزان المراجعة
- التقرير الضريبي

### 🛠️ الأدوات المساعدة / Utilities

#### 1. database.js
- LocalDatabase class
- إدارة البيانات المحلية
- وظائف التصدير والاستيراد

#### 2. arabicNLP.js
- معالجة النصوص العربية
- تحويل الأرقام للعربية
- دعم اللغة العربية

### 🎨 ملفات الأنماط / Style Files

#### CSS Files:
- index.css - الأنماط العامة
- App.css - أنماط التطبيق الرئيسي
- Dashboard.css - أنماط لوحة التحكم
- POS.css - أنماط نقطة البيع
- Products.css - أنماط إدارة المنتجات
- Customers.css - أنماط إدارة العملاء
- Suppliers.css - أنماط إدارة الموردين
- Accounting.css - أنماط النظام المحاسبي
- Reports.css - أنماط التقارير
- Login.css - أنماط تسجيل الدخول
- JournalEntries.css - أنماط القيود اليومية
- Returns.css - أنماط إدارة المرتجعات
- BarcodeManager.css - أنماط إدارة الباركود
- Quotations.css - أنماط عروض الأسعار
- Vouchers.css - أنماط سندات القبض والصرف
- AdvancedReports.css - أنماط التقارير المتقدمة

### 🚀 ملفات التشغيل / Startup Files

#### Windows:
- start-app.bat - ملف تشغيل Windows Batch
- start-app.ps1 - ملف PowerShell
- start.ps1 - ملف تشغيل إضافي

### 📦 التبعيات الرئيسية / Main Dependencies

#### Production:
- React 18.x - مكتبة واجهة المستخدم
- React DOM - عرض React في المتصفح

#### Development:
- React Scripts - أدوات التطوير
- Tailwind CSS - إطار عمل CSS
- ESLint - فحص الكود
- Jest - اختبار الوحدة

### 🔍 ملاحظات مهمة / Important Notes

1. **قاعدة البيانات**: يستخدم localStorage للتخزين المحلي
2. **اللغة**: يدعم العربية والإنجليزية
3. **التصميم**: يستخدم Tailwind CSS للتصميم
4. **المتصفح**: يعمل على جميع المتصفحات الحديثة
5. **البيانات**: يتم حفظ البيانات محلياً في المتصفح

### 📈 الميزات المتاحة / Available Features

✅ إدارة الفواتير والمبيعات
✅ إدارة المنتجات والمخزون  
✅ إدارة العملاء والموردين
✅ النظام المحاسبي المتكامل
✅ التقارير والإحصائيات
✅ واجهة عربية متكاملة
✅ حساب ضريبة القيمة المضافة
✅ تصدير واستيراد البيانات

---

## 📋 تفاصيل محتوى الملفات / File Contents Details

### 🔧 الملفات التقنية / Technical Files

#### package.json
```json
{
  "name": "saudi-accounting-final",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.x",
    "react-dom": "^18.x",
    "react-scripts": "5.x"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test"
  }
}
```

#### public/index.html
- صفحة HTML الرئيسية
- دعم اللغة العربية (dir="rtl")
- تحسين SEO للمحتوى العربي
- ربط ملفات CSS و JavaScript

### 💻 الكود المصدري / Source Code

#### src/index.js
- تهيئة React DOM
- ربط التطبيق بعنصر root
- إعدادات الأداء

#### src/App.js
- إدارة حالة التطبيق العامة
- نظام التنقل بين الصفحات
- مكونات الهيدر والسايدبار
- إدارة المصادقة

#### src/utils/database.js (688 سطر)
**الكلاسات والوظائف الرئيسية:**
- `LocalDatabase` - الكلاس الرئيسي
- `initializeDatabase()` - تهيئة قاعدة البيانات
- `getInvoices()` - جلب الفواتير
- `addInvoice()` - إضافة فاتورة جديدة
- `getProducts()` - جلب المنتجات
- `addProduct()` - إضافة منتج جديد
- `getCustomers()` - جلب العملاء
- `addCustomer()` - إضافة عميل جديد
- `getSuppliers()` - جلب الموردين
- `addSupplier()` - إضافة مورد جديد
- `getAccounts()` - جلب الحسابات المالية
- `addJournalEntry()` - إضافة قيد محاسبي
- `getTodayStats()` - إحصائيات اليوم
- `getMonthStats()` - إحصائيات الشهر
- `exportData()` - تصدير البيانات
- `importData()` - استيراد البيانات

#### src/utils/arabicNLP.js
- تحويل الأرقام للعربية
- معالجة النصوص العربية
- دوال مساعدة للغة العربية

### 🎨 مكونات الواجهة / UI Components

#### Dashboard/Dashboard.js
**الميزات:**
- عرض إحصائيات المبيعات اليومية
- أفضل المنتجات مبيعاً
- المنتجات منخفضة المخزون
- رسوم بيانية للمبيعات
- ملخص الأرباح والخسائر

#### POS/POS.js
**الميزات:**
- واجهة نقطة البيع
- إضافة المنتجات للفاتورة
- حساب الضريبة (15%)
- طباعة الفاتورة
- إدارة طرق الدفع
- خصومات وعروض

#### Products/Products.js
**الميزات:**
- جدول المنتجات مع البحث
- إضافة منتج جديد
- تعديل بيانات المنتج
- حذف المنتج
- تتبع المخزون
- إدارة الفئات والوحدات
- رموز الباركود

#### Customers/Customers.js
**الميزات:**
- قائمة العملاء
- إضافة عميل جديد
- تعديل بيانات العميل
- سجل معاملات العميل
- معلومات الاتصال
- عنوان العميل

#### Suppliers/Suppliers.js
**الميزات:**
- قائمة الموردين
- إضافة مورد جديد
- تعديل بيانات المورد
- سجل المشتريات
- معلومات التواصل
- شروط الدفع

#### Accounting/Accounting.js
**الميزات:**
- دليل الحسابات
- القيود المحاسبية
- الميزانية العمومية
- قائمة الدخل
- التدفقات النقدية
- تقارير مالية

#### Reports/Reports.js
**الميزات:**
- تقرير المبيعات اليومي
- تقرير المبيعات الشهري
- تقرير المخزون
- تقرير الأرباح والخسائر
- تقرير العملاء
- تقرير الموردين
- تصدير التقارير PDF/Excel

### 🎨 ملفات الأنماط / CSS Files

#### index.css
- الأنماط العامة للتطبيق
- خطوط عربية
- متغيرات CSS
- أنماط الطباعة

#### App.css
- أنماط التطبيق الرئيسي
- تخطيط الصفحة
- أنماط التنقل
- أنماط الهيدر والسايدبار

#### Component CSS Files
كل مكون له ملف CSS منفصل يحتوي على:
- أنماط خاصة بالمكون
- تخطيط الجداول
- أنماط النماذج
- أنماط الأزرار
- أنماط الرسوم البيانية

### 🚀 ملفات التشغيل / Startup Scripts

#### start-app.bat
```batch
@echo off
echo بدء تشغيل نظام المحاسبة السعودي...
cd /d "%~dp0"
npm start
pause
```

#### start-app.ps1
```powershell
Write-Host "بدء تشغيل نظام المحاسبة السعودي..." -ForegroundColor Green
Set-Location $PSScriptRoot
npm start
Read-Host "اضغط Enter للخروج"
```

### 📊 هيكل البيانات / Data Structure

#### الفواتير / Invoices
```javascript
{
  id: number,
  invoiceNumber: string,
  customerId: number,
  customerName: string,
  items: Array,
  subtotal: number,
  vat: number,
  total: number,
  createdAt: string,
  status: string
}
```

#### المنتجات / Products
```javascript
{
  id: number,
  name: string,
  price: number,
  stock: number,
  category: string,
  barcode: string,
  description: string,
  vatRate: number,
  createdAt: string
}
```

#### العملاء / Customers
```javascript
{
  id: number,
  name: string,
  email: string,
  phone: string,
  address: string,
  taxNumber: string,
  createdAt: string
}
```

#### الموردين / Suppliers
```javascript
{
  id: number,
  name: string,
  email: string,
  phone: string,
  address: string,
  taxNumber: string,
  paymentTerms: string,
  createdAt: string
}
```

### 🔐 نظام المصادقة / Authentication

#### Auth/Login.js
- نموذج تسجيل الدخول
- التحقق من بيانات المستخدم
- إدارة الجلسات
- حماية الصفحات

### 📱 التوافق والأداء / Compatibility & Performance

#### المتصفحات المدعومة:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

#### الأداء:
- تحميل سريع
- تخزين محلي فعال
- واجهة مستجيبة
- دعم الأجهزة المحمولة

### 🆕 الميزات الجديدة المضافة / New Added Features

#### 📋 القيود اليومية المتقدمة
- نظام القيد المزدوج الكامل
- ترحيل تلقائي للقيود
- ميزان المراجعة الفوري
- أنواع قيود متعددة

#### ↩️ إدارة المرتجعات الشاملة
- مرتجعات المبيعات والمشتريات
- ربط المرتجعات بالفواتير الأصلية
- تحديث المخزون التلقائي
- تقارير المرتجعات التفصيلية

#### 📊 إدارة الباركود المتقدمة
- إنشاء باركود تلقائي
- طباعة ملصقات مخصصة
- مسح الباركود والبحث
- أنواع باركود متعددة

#### 💼 عروض الأسعار الاحترافية
- إنشاء عروض تفاعلية
- تحويل العروض لفواتير
- إدارة صلاحية العروض
- شروط دفع مخصصة

#### 🧾 سندات القبض والصرف
- سندات احترافية
- ربط بالحسابات المحاسبية
- طرق دفع متعددة
- قيود محاسبية تلقائية

#### 📈 التقارير المالية المتقدمة
- قائمة الدخل التفصيلية
- الميزانية العمومية
- قائمة التدفقات النقدية
- ميزان المراجعة
- التقرير الضريبي
- تحليل الربحية
- إغلاق الوردية

---

## 🎯 خلاصة المشروع / Project Summary

**نوع المشروع:** تطبيق ويب للمحاسبة المتقدمة
**التقنيات:** React.js, CSS3, LocalStorage
**اللغة:** العربية مع دعم الإنجليزية
**الحجم:** ~30 ملف رئيسي + التبعيات
**الوظائف:** 80+ ميزة محاسبية متكاملة ومتقدمة

**ملف الملخص:** `project-files-summary.md`
