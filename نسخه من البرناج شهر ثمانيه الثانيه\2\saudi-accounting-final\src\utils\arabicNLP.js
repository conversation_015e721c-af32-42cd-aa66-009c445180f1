// نظام معالجة اللغة العربية المبسط والقابل للتطبيق
// Simplified and Practical Arabic NLP System

class ArabicNLPSystem {
  constructor() {
    this.grammarRules = new Map();
    this.commonErrors = new Map();
    this.vocabularyLevels = new Map();
    this.initializeSystem();
  }

  // تهيئة النظام بالقواعد الأساسية
  initializeSystem() {
    // القواعد النحوية الأساسية
    this.grammarRules.set('sentence_types', {
      'اسمية': /^[أ-ي]+\s+[أ-ي]+/,
      'فعلية': /^(يـ|تـ|أـ|نـ|ا)[أ-ي]+/,
      'شرطية': /^(إذا|لو|إن|لولا)/,
      'استفهامية': /^(ما|من|متى|أين|كيف|لماذا|هل)/
    });

    // الأخطاء الشائعة وتصحيحها
    this.commonErrors.set('spelling', {
      'هاذا': 'هذا',
      'هذة': 'هذه', 
      'الذى': 'الذي',
      'التى': 'التي',
      'إلى أن': 'إلى أن',
      'على أن': 'على أن'
    });

    // مستويات المفردات
    this.vocabularyLevels.set('basic', [
      'كتاب', 'مدرسة', 'طالب', 'معلم', 'بيت', 'سيارة'
    ]);
    
    this.vocabularyLevels.set('intermediate', [
      'معرفة', 'تعليم', 'ثقافة', 'حضارة', 'تقدم', 'تطور'
    ]);
    
    this.vocabularyLevels.set('advanced', [
      'استراتيجية', 'منهجية', 'فلسفة', 'إبداع', 'ابتكار', 'تميز'
    ]);
  }

  // تحليل النص العربي
  analyzeText(text) {
    const analysis = {
      originalText: text,
      sentences: [],
      statistics: {},
      errors: [],
      suggestions: []
    };

    // تقسيم النص إلى جمل
    const sentences = this.splitIntoSentences(text);
    
    sentences.forEach(sentence => {
      if (sentence.trim()) {
        const sentenceAnalysis = this.analyzeSentence(sentence);
        analysis.sentences.push(sentenceAnalysis);
      }
    });

    // حساب الإحصاءات
    analysis.statistics = this.calculateStatistics(text);
    
    // الكشف عن الأخطاء
    analysis.errors = this.detectErrors(text);
    
    // توليد الاقتراحات
    analysis.suggestions = this.generateSuggestions(analysis);

    return analysis;
  }

  // تقسيم النص إلى جمل
  splitIntoSentences(text) {
    return text.split(/[.!؟]/).filter(s => s.trim());
  }

  // تحليل جملة واحدة
  analyzeSentence(sentence) {
    const words = this.tokenize(sentence);
    
    return {
      text: sentence.trim(),
      type: this.detectSentenceType(sentence),
      words: words.map(word => this.analyzeWord(word)),
      length: words.length,
      complexity: this.calculateComplexity(words)
    };
  }

  // تقسيم الجملة إلى كلمات
  tokenize(sentence) {
    return sentence.trim().split(/\s+/).filter(word => word);
  }

  // تحديد نوع الجملة
  detectSentenceType(sentence) {
    for (const [type, pattern] of this.grammarRules.get('sentence_types')) {
      if (pattern.test(sentence)) {
        return type;
      }
    }
    return 'غير محدد';
  }

  // تحليل كلمة واحدة
  analyzeWord(word) {
    return {
      text: word,
      type: this.detectWordType(word),
      level: this.detectVocabularyLevel(word),
      hasError: this.hasSpellingError(word)
    };
  }

  // تحديد نوع الكلمة
  detectWordType(word) {
    // قواعد بسيطة لتحديد نوع الكلمة
    if (/^(ال|و|ف|ب|ك|ل)/.test(word)) {
      return 'أداة';
    }
    if (/^(يـ|تـ|أـ|نـ)/.test(word)) {
      return 'فعل مضارع';
    }
    if (/ة$/.test(word)) {
      return 'اسم مؤنث';
    }
    if (/ون$|ين$/.test(word)) {
      return 'جمع مذكر';
    }
    if (/ات$/.test(word)) {
      return 'جمع مؤنث';
    }
    return 'اسم';
  }

  // تحديد مستوى المفردة
  detectVocabularyLevel(word) {
    const cleanWord = word.replace(/^(ال|و|ف|ب|ك|ل)/, '');
    
    if (this.vocabularyLevels.get('basic').includes(cleanWord)) {
      return 'أساسي';
    }
    if (this.vocabularyLevels.get('intermediate').includes(cleanWord)) {
      return 'متوسط';
    }
    if (this.vocabularyLevels.get('advanced').includes(cleanWord)) {
      return 'متقدم';
    }
    return 'عام';
  }

  // الكشف عن الأخطاء الإملائية
  hasSpellingError(word) {
    return this.commonErrors.get('spelling').hasOwnProperty(word);
  }

  // حساب تعقيد الجملة
  calculateComplexity(words) {
    let complexity = 0;
    
    // طول الجملة
    complexity += words.length * 0.1;
    
    // وجود أدوات ربط
    const connectors = words.filter(word => 
      /^(و|ف|ثم|لكن|غير|سوى)/.test(word)
    );
    complexity += connectors.length * 0.2;
    
    // وجود جمل فرعية
    const subordinates = words.filter(word => 
      /^(الذي|التي|حيث|عندما|لأن)/.test(word)
    );
    complexity += subordinates.length * 0.3;
    
    return Math.min(complexity, 1.0); // تحديد بين 0 و 1
  }

  // حساب إحصاءات النص
  calculateStatistics(text) {
    const words = this.tokenize(text);
    const uniqueWords = [...new Set(words)];
    const sentences = this.splitIntoSentences(text);
    
    return {
      wordCount: words.length,
      uniqueWordCount: uniqueWords.length,
      sentenceCount: sentences.length,
      averageWordsPerSentence: sentences.length > 0 ? 
        Math.round(words.length / sentences.length * 100) / 100 : 0,
      lexicalDiversity: words.length > 0 ? 
        Math.round(uniqueWords.length / words.length * 100) / 100 : 0,
      readabilityScore: this.calculateReadability(words, sentences)
    };
  }

  // حساب مؤشر سهولة القراءة
  calculateReadability(words, sentences) {
    if (sentences.length === 0 || words.length === 0) return 0;
    
    const avgWordsPerSentence = words.length / sentences.length;
    const complexWords = words.filter(word => word.length > 6).length;
    const complexWordRatio = complexWords / words.length;
    
    // مؤشر مبسط (كلما قل الرقم، كانت القراءة أسهل)
    const score = (avgWordsPerSentence * 0.4) + (complexWordRatio * 60);
    
    return Math.round(Math.max(0, Math.min(100, score)));
  }

  // الكشف عن الأخطاء في النص
  detectErrors(text) {
    const errors = [];
    const spellingErrors = this.commonErrors.get('spelling');
    
    for (const [error, correction] of Object.entries(spellingErrors)) {
      const regex = new RegExp(`\\b${error}\\b`, 'g');
      if (regex.test(text)) {
        errors.push({
          type: 'إملائي',
          error: error,
          correction: correction,
          explanation: `يجب كتابة "${correction}" بدلاً من "${error}"`
        });
      }
    }

    // فحص علامات الترقيم
    if (!/[.!؟]$/.test(text.trim())) {
      errors.push({
        type: 'ترقيم',
        error: 'نقص علامة ترقيم',
        correction: 'إضافة نقطة أو علامة استفهام أو تعجب',
        explanation: 'يجب إنهاء الجملة بعلامة ترقيم مناسبة'
      });
    }

    return errors;
  }

  // توليد اقتراحات التحسين
  generateSuggestions(analysis) {
    const suggestions = [];
    const stats = analysis.statistics;

    // اقتراحات بناء على الإحصاءات
    if (stats.averageWordsPerSentence > 15) {
      suggestions.push({
        type: 'بنية الجملة',
        suggestion: 'حاول تقسيم الجمل الطويلة إلى جمل أقصر لتحسين الوضوح'
      });
    }

    if (stats.lexicalDiversity < 0.4) {
      suggestions.push({
        type: 'تنوع المفردات',
        suggestion: 'استخدم مرادفات متنوعة لتجنب التكرار وإثراء النص'
      });
    }

    if (stats.readabilityScore > 70) {
      suggestions.push({
        type: 'سهولة القراءة',
        suggestion: 'النص معقد نسبياً، حاول استخدام كلمات أبسط وجمل أقصر'
      });
    }

    // اقتراحات بناء على الأخطاء
    if (analysis.errors.length > 0) {
      suggestions.push({
        type: 'تصحيح الأخطاء',
        suggestion: `تم العثور على ${analysis.errors.length} خطأ، يرجى مراجعة التصحيحات المقترحة`
      });
    }

    return suggestions;
  }

  // تصحيح النص
  correctText(text) {
    let correctedText = text;
    const corrections = [];
    const spellingErrors = this.commonErrors.get('spelling');

    // تصحيح الأخطاء الإملائية
    for (const [error, correction] of Object.entries(spellingErrors)) {
      const regex = new RegExp(`\\b${error}\\b`, 'g');
      if (regex.test(correctedText)) {
        correctedText = correctedText.replace(regex, correction);
        corrections.push({
          original: error,
          corrected: correction,
          type: 'إملائي'
        });
      }
    }

    // إضافة علامة ترقيم إذا كانت مفقودة
    if (!/[.!؟]$/.test(correctedText.trim())) {
      correctedText = correctedText.trim() + '.';
      corrections.push({
        original: 'بدون علامة ترقيم',
        corrected: 'مع نقطة',
        type: 'ترقيم'
      });
    }

    return {
      originalText: text,
      correctedText: correctedText,
      corrections: corrections,
      improvementPercentage: corrections.length > 0 ? 
        Math.round((corrections.length / text.split(' ').length) * 100) : 0
    };
  }

  // توليد تقرير شامل
  generateReport(text) {
    const analysis = this.analyzeText(text);
    const correction = this.correctText(text);

    return {
      analysis: analysis,
      correction: correction,
      summary: {
        textQuality: this.assessTextQuality(analysis),
        mainIssues: this.identifyMainIssues(analysis),
        recommendations: this.prioritizeRecommendations(analysis.suggestions)
      }
    };
  }

  // تقييم جودة النص
  assessTextQuality(analysis) {
    const stats = analysis.statistics;
    let score = 100;

    // خصم نقاط للأخطاء
    score -= analysis.errors.length * 10;

    // خصم نقاط لضعف التنوع المعجمي
    if (stats.lexicalDiversity < 0.3) score -= 15;

    // خصم نقاط للتعقيد المفرط
    if (stats.readabilityScore > 80) score -= 20;

    // خصم نقاط للجمل الطويلة جداً
    if (stats.averageWordsPerSentence > 20) score -= 10;

    return {
      score: Math.max(0, score),
      level: score >= 80 ? 'ممتاز' : 
             score >= 60 ? 'جيد' : 
             score >= 40 ? 'مقبول' : 'يحتاج تحسين'
    };
  }

  // تحديد المشاكل الرئيسية
  identifyMainIssues(analysis) {
    const issues = [];

    if (analysis.errors.length > 0) {
      issues.push(`${analysis.errors.length} أخطاء إملائية أو نحوية`);
    }

    if (analysis.statistics.lexicalDiversity < 0.3) {
      issues.push('ضعف في تنوع المفردات');
    }

    if (analysis.statistics.readabilityScore > 70) {
      issues.push('تعقيد في التركيب اللغوي');
    }

    return issues.length > 0 ? issues : ['لا توجد مشاكل رئيسية'];
  }

  // ترتيب التوصيات حسب الأولوية
  prioritizeRecommendations(suggestions) {
    const priority = {
      'تصحيح الأخطاء': 1,
      'ترقيم': 2,
      'بنية الجملة': 3,
      'تنوع المفردات': 4,
      'سهولة القراءة': 5
    };

    return suggestions.sort((a, b) => 
      (priority[a.type] || 99) - (priority[b.type] || 99)
    );
  }
}

// تصدير النظام للاستخدام
export default ArabicNLPSystem;
