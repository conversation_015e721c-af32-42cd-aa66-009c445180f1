import React, { useState, useEffect } from 'react';
import './Accounting.css';
import database from '../../utils/database';

const Accounting = ({ user, onBack }) => {
  const [activeTab, setActiveTab] = useState('accounts');
  const [accounts, setAccounts] = useState([]);
  const [journalEntries, setJournalEntries] = useState([]);
  const [showAddAccountForm, setShowAddAccountForm] = useState(false);
  const [showAddEntryForm, setShowAddEntryForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [newAccount, setNewAccount] = useState({
    code: '',
    name: '',
    type: 'asset',
    parentId: null,
    balance: '0',
    description: ''
  });

  const [newEntry, setNewEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    entries: [
      { accountId: '', debit: '', credit: '', description: '' },
      { accountId: '', debit: '', credit: '', description: '' }
    ]
  });

  const accountTypes = [
    { value: 'asset', label: 'أصول', color: '#4CAF50' },
    { value: 'liability', label: 'خصوم', color: '#f44336' },
    { value: 'equity', label: 'حقوق الملكية', color: '#2196F3' },
    { value: 'revenue', label: 'إيرادات', color: '#FF9800' },
    { value: 'expense', label: 'مصروفات', color: '#9C27B0' }
  ];

  useEffect(() => {
    loadAccounts();
    loadJournalEntries();
  }, []);

  const loadAccounts = () => {
    try {
      const dbAccounts = database.getAccounts();
      setAccounts(dbAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  const loadJournalEntries = () => {
    try {
      const dbEntries = database.getJournalEntries();
      setJournalEntries(dbEntries);
    } catch (error) {
      console.error('خطأ في تحميل القيود:', error);
    }
  };

  const handleAddAccount = async () => {
    if (!newAccount.code || !newAccount.name) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setIsLoading(true);
    try {
      const accountData = {
        ...newAccount,
        balance: parseFloat(newAccount.balance) || 0
      };

      const savedAccount = database.addAccount(accountData);
      setAccounts([...accounts, savedAccount]);
      
      setNewAccount({
        code: '',
        name: '',
        type: 'asset',
        parentId: null,
        balance: '0',
        description: ''
      });
      
      setShowAddAccountForm(false);
      alert('تم إضافة الحساب بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة الحساب:', error);
      alert('فشل في إضافة الحساب');
    }
    setIsLoading(false);
  };

  const handleAddJournalEntry = async () => {
    if (!newEntry.description || newEntry.entries.some(e => !e.accountId)) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const totalDebit = newEntry.entries.reduce((sum, e) => sum + (parseFloat(e.debit) || 0), 0);
    const totalCredit = newEntry.entries.reduce((sum, e) => sum + (parseFloat(e.credit) || 0), 0);

    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      alert('إجمالي المدين يجب أن يساوي إجمالي الدائن');
      return;
    }

    setIsLoading(true);
    try {
      const entryData = {
        ...newEntry,
        entries: newEntry.entries.map(e => ({
          ...e,
          debit: parseFloat(e.debit) || 0,
          credit: parseFloat(e.credit) || 0
        }))
      };

      const savedEntry = database.addJournalEntry(entryData);
      setJournalEntries([...journalEntries, savedEntry]);
      
      setNewEntry({
        date: new Date().toISOString().split('T')[0],
        description: '',
        reference: '',
        entries: [
          { accountId: '', debit: '', credit: '', description: '' },
          { accountId: '', debit: '', credit: '', description: '' }
        ]
      });
      
      setShowAddEntryForm(false);
      alert('تم إضافة القيد بنجاح');
      loadAccounts(); // إعادة تحميل الحسابات لتحديث الأرصدة
    } catch (error) {
      console.error('خطأ في إضافة القيد:', error);
      alert('فشل في إضافة القيد');
    }
    setIsLoading(false);
  };

  const addEntryLine = () => {
    setNewEntry({
      ...newEntry,
      entries: [...newEntry.entries, { accountId: '', debit: '', credit: '', description: '' }]
    });
  };

  const removeEntryLine = (index) => {
    if (newEntry.entries.length > 2) {
      const updatedEntries = newEntry.entries.filter((_, i) => i !== index);
      setNewEntry({ ...newEntry, entries: updatedEntries });
    }
  };

  const updateEntryLine = (index, field, value) => {
    const updatedEntries = [...newEntry.entries];
    updatedEntries[index][field] = value;
    setNewEntry({ ...newEntry, entries: updatedEntries });
  };

  const getAccountName = (accountId) => {
    const account = accounts.find(a => a.id === parseInt(accountId));
    return account ? `${account.code} - ${account.name}` : '';
  };

  const getAccountTypeLabel = (type) => {
    const typeObj = accountTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  const getAccountTypeColor = (type) => {
    const typeObj = accountTypes.find(t => t.value === type);
    return typeObj ? typeObj.color : '#666';
  };

  const calculateTotalDebit = () => {
    return newEntry.entries.reduce((sum, e) => sum + (parseFloat(e.debit) || 0), 0);
  };

  const calculateTotalCredit = () => {
    return newEntry.entries.reduce((sum, e) => sum + (parseFloat(e.credit) || 0), 0);
  };

  const getBalanceByType = (type) => {
    return accounts
      .filter(account => account.type === type)
      .reduce((sum, account) => sum + account.balance, 0);
  };

  return (
    <div className="accounting-container">
      <div className="accounting-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ← العودة للوحة التحكم
          </button>
          <h1>📊 المحاسبة المالية</h1>
          <div className="header-actions">
            <button 
              onClick={() => setShowAddAccountForm(true)} 
              className="add-btn"
              disabled={isLoading}
            >
              ➕ إضافة حساب
            </button>
            <button 
              onClick={() => setShowAddEntryForm(true)} 
              className="add-btn secondary"
              disabled={isLoading}
            >
              📝 إضافة قيد
            </button>
          </div>
        </div>
      </div>

      <div className="accounting-stats">
        <div className="stat-card asset">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي الأصول</h3>
            <p>{getBalanceByType('asset').toFixed(2)} ريال</p>
          </div>
        </div>
        <div className="stat-card liability">
          <div className="stat-icon">📋</div>
          <div className="stat-info">
            <h3>إجمالي الخصوم</h3>
            <p>{getBalanceByType('liability').toFixed(2)} ريال</p>
          </div>
        </div>
        <div className="stat-card equity">
          <div className="stat-icon">🏛️</div>
          <div className="stat-info">
            <h3>حقوق الملكية</h3>
            <p>{getBalanceByType('equity').toFixed(2)} ريال</p>
          </div>
        </div>
        <div className="stat-card revenue">
          <div className="stat-icon">📈</div>
          <div className="stat-info">
            <h3>الإيرادات</h3>
            <p>{getBalanceByType('revenue').toFixed(2)} ريال</p>
          </div>
        </div>
      </div>

      <div className="accounting-tabs">
        <button 
          className={`tab-btn ${activeTab === 'accounts' ? 'active' : ''}`}
          onClick={() => setActiveTab('accounts')}
        >
          📋 دليل الحسابات
        </button>
        <button 
          className={`tab-btn ${activeTab === 'journal' ? 'active' : ''}`}
          onClick={() => setActiveTab('journal')}
        >
          📝 دفتر اليومية
        </button>
        <button 
          className={`tab-btn ${activeTab === 'ledger' ? 'active' : ''}`}
          onClick={() => setActiveTab('ledger')}
        >
          📊 دفتر الأستاذ
        </button>
        <button 
          className={`tab-btn ${activeTab === 'trial' ? 'active' : ''}`}
          onClick={() => setActiveTab('trial')}
        >
          ⚖️ ميزان المراجعة
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'accounts' && (
          <div className="accounts-section">
            <div className="section-header">
              <h2>دليل الحسابات</h2>
              <span className="accounts-count">{accounts.length} حساب</span>
            </div>
            
            <div className="accounts-table-container">
              <table className="accounts-table">
                <thead>
                  <tr>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>النوع</th>
                    <th>الرصيد</th>
                    <th>الوصف</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map(account => (
                    <tr key={account.id}>
                      <td>{account.code}</td>
                      <td>{account.name}</td>
                      <td>
                        <span 
                          className="account-type-badge"
                          style={{ backgroundColor: getAccountTypeColor(account.type) }}
                        >
                          {getAccountTypeLabel(account.type)}
                        </span>
                      </td>
                      <td className={account.balance >= 0 ? 'positive' : 'negative'}>
                        {account.balance.toFixed(2)} ريال
                      </td>
                      <td>{account.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {accounts.length === 0 && (
                <div className="no-data">
                  <p>لا توجد حسابات مضافة بعد</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'journal' && (
          <div className="journal-section">
            <div className="section-header">
              <h2>دفتر اليومية</h2>
              <span className="entries-count">{journalEntries.length} قيد</span>
            </div>
            
            <div className="journal-entries">
              {journalEntries.map(entry => (
                <div key={entry.id} className="journal-entry">
                  <div className="entry-header">
                    <div className="entry-info">
                      <strong>قيد رقم {entry.id}</strong>
                      <span className="entry-date">{entry.date}</span>
                    </div>
                    <div className="entry-reference">
                      {entry.reference && <span>مرجع: {entry.reference}</span>}
                    </div>
                  </div>
                  <div className="entry-description">
                    {entry.description}
                  </div>
                  <div className="entry-lines">
                    <table>
                      <thead>
                        <tr>
                          <th>الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                        </tr>
                      </thead>
                      <tbody>
                        {entry.entries.map((line, index) => (
                          <tr key={index}>
                            <td>{getAccountName(line.accountId)}</td>
                            <td>{line.description}</td>
                            <td className="debit">{line.debit > 0 ? line.debit.toFixed(2) : '-'}</td>
                            <td className="credit">{line.credit > 0 ? line.credit.toFixed(2) : '-'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}

              {journalEntries.length === 0 && (
                <div className="no-data">
                  <p>لا توجد قيود محاسبية مضافة بعد</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'ledger' && (
          <div className="ledger-section">
            <div className="section-header">
              <h2>دفتر الأستاذ</h2>
            </div>

            <div className="ledger-accounts">
              {accounts.map(account => {
                const accountEntries = journalEntries.flatMap(entry =>
                  entry.entries
                    .filter(line => parseInt(line.accountId) === account.id)
                    .map(line => ({
                      ...line,
                      date: entry.date,
                      description: entry.description,
                      entryId: entry.id
                    }))
                );

                if (accountEntries.length === 0) return null;

                let runningBalance = account.balance || 0;

                return (
                  <div key={account.id} className="ledger-account">
                    <div className="account-header">
                      <h3>{account.code} - {account.name}</h3>
                      <span className="account-type">{getAccountTypeLabel(account.type)}</span>
                    </div>

                    <table className="ledger-table">
                      <thead>
                        <tr>
                          <th>التاريخ</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>الرصيد</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="opening-balance">
                          <td>-</td>
                          <td>الرصيد الافتتاحي</td>
                          <td>-</td>
                          <td>-</td>
                          <td className={runningBalance >= 0 ? 'positive' : 'negative'}>
                            {runningBalance.toFixed(2)}
                          </td>
                        </tr>
                        {accountEntries.map((entry, index) => {
                          runningBalance += (entry.debit - entry.credit);
                          return (
                            <tr key={index}>
                              <td>{entry.date}</td>
                              <td>{entry.description}</td>
                              <td className="debit">{entry.debit > 0 ? entry.debit.toFixed(2) : '-'}</td>
                              <td className="credit">{entry.credit > 0 ? entry.credit.toFixed(2) : '-'}</td>
                              <td className={runningBalance >= 0 ? 'positive' : 'negative'}>
                                {runningBalance.toFixed(2)}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                );
              })}

              {accounts.filter(account =>
                journalEntries.some(entry =>
                  entry.entries.some(line => parseInt(line.accountId) === account.id)
                )
              ).length === 0 && (
                <div className="no-data">
                  <p>لا توجد حركات في دفتر الأستاذ</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'trial' && (
          <div className="trial-section">
            <div className="section-header">
              <h2>ميزان المراجعة</h2>
              <span className="trial-date">كما في {new Date().toLocaleDateString('ar-SA')}</span>
            </div>

            <div className="trial-balance-table">
              <table>
                <thead>
                  <tr>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map(account => {
                    const accountEntries = journalEntries.flatMap(entry =>
                      entry.entries.filter(line => parseInt(line.accountId) === account.id)
                    );

                    const totalDebit = accountEntries.reduce((sum, entry) => sum + entry.debit, 0);
                    const totalCredit = accountEntries.reduce((sum, entry) => sum + entry.credit, 0);
                    const finalBalance = (account.balance || 0) + totalDebit - totalCredit;

                    return (
                      <tr key={account.id}>
                        <td>{account.code}</td>
                        <td>{account.name}</td>
                        <td className="debit">
                          {finalBalance >= 0 ? finalBalance.toFixed(2) : '-'}
                        </td>
                        <td className="credit">
                          {finalBalance < 0 ? Math.abs(finalBalance).toFixed(2) : '-'}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot>
                  <tr className="totals-row">
                    <td colSpan="2"><strong>الإجمالي</strong></td>
                    <td className="total-debit">
                      <strong>
                        {accounts.reduce((sum, account) => {
                          const accountEntries = journalEntries.flatMap(entry =>
                            entry.entries.filter(line => parseInt(line.accountId) === account.id)
                          );
                          const totalDebit = accountEntries.reduce((sum, entry) => sum + entry.debit, 0);
                          const totalCredit = accountEntries.reduce((sum, entry) => sum + entry.credit, 0);
                          const finalBalance = (account.balance || 0) + totalDebit - totalCredit;
                          return sum + (finalBalance >= 0 ? finalBalance : 0);
                        }, 0).toFixed(2)}
                      </strong>
                    </td>
                    <td className="total-credit">
                      <strong>
                        {accounts.reduce((sum, account) => {
                          const accountEntries = journalEntries.flatMap(entry =>
                            entry.entries.filter(line => parseInt(line.accountId) === account.id)
                          );
                          const totalDebit = accountEntries.reduce((sum, entry) => sum + entry.debit, 0);
                          const totalCredit = accountEntries.reduce((sum, entry) => sum + entry.credit, 0);
                          const finalBalance = (account.balance || 0) + totalDebit - totalCredit;
                          return sum + (finalBalance < 0 ? Math.abs(finalBalance) : 0);
                        }, 0).toFixed(2)}
                      </strong>
                    </td>
                  </tr>
                </tfoot>
              </table>

              {accounts.length === 0 && (
                <div className="no-data">
                  <p>لا توجد حسابات لعرض ميزان المراجعة</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {showAddAccountForm && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>إضافة حساب جديد</h2>
              <button onClick={() => setShowAddAccountForm(false)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>رقم الحساب *</label>
                  <input
                    type="text"
                    value={newAccount.code}
                    onChange={(e) => setNewAccount({...newAccount, code: e.target.value})}
                    placeholder="مثال: 1001"
                  />
                </div>

                <div className="form-group">
                  <label>اسم الحساب *</label>
                  <input
                    type="text"
                    value={newAccount.name}
                    onChange={(e) => setNewAccount({...newAccount, name: e.target.value})}
                    placeholder="مثال: النقدية"
                  />
                </div>

                <div className="form-group">
                  <label>نوع الحساب</label>
                  <select
                    value={newAccount.type}
                    onChange={(e) => setNewAccount({...newAccount, type: e.target.value})}
                  >
                    {accountTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الرصيد الافتتاحي</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newAccount.balance}
                    onChange={(e) => setNewAccount({...newAccount, balance: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newAccount.description}
                    onChange={(e) => setNewAccount({...newAccount, description: e.target.value})}
                    placeholder="وصف الحساب (اختياري)"
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowAddAccountForm(false)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleAddAccount} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري الحفظ...' : 'حفظ الحساب'}
              </button>
            </div>
          </div>
        </div>
      )}

      {showAddEntryForm && (
        <div className="modal-overlay">
          <div className="modal large">
            <div className="modal-header">
              <h2>إضافة قيد محاسبي</h2>
              <button onClick={() => setShowAddEntryForm(false)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>التاريخ *</label>
                  <input
                    type="date"
                    value={newEntry.date}
                    onChange={(e) => setNewEntry({...newEntry, date: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>المرجع</label>
                  <input
                    type="text"
                    value={newEntry.reference}
                    onChange={(e) => setNewEntry({...newEntry, reference: e.target.value})}
                    placeholder="رقم المرجع (اختياري)"
                  />
                </div>

                <div className="form-group full-width">
                  <label>البيان *</label>
                  <textarea
                    value={newEntry.description}
                    onChange={(e) => setNewEntry({...newEntry, description: e.target.value})}
                    placeholder="وصف القيد المحاسبي"
                    rows="2"
                  />
                </div>
              </div>

              <div className="entry-lines-section">
                <h3>تفاصيل القيد</h3>
                <div className="entry-lines-table">
                  <table>
                    <thead>
                      <tr>
                        <th>الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>إجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {newEntry.entries.map((entry, index) => (
                        <tr key={index}>
                          <td>
                            <select
                              value={entry.accountId}
                              onChange={(e) => updateEntryLine(index, 'accountId', e.target.value)}
                              required
                            >
                              <option value="">اختر الحساب</option>
                              {accounts.map(account => (
                                <option key={account.id} value={account.id}>
                                  {account.code} - {account.name}
                                </option>
                              ))}
                            </select>
                          </td>
                          <td>
                            <input
                              type="text"
                              value={entry.description}
                              onChange={(e) => updateEntryLine(index, 'description', e.target.value)}
                              placeholder="بيان السطر"
                            />
                          </td>
                          <td>
                            <input
                              type="number"
                              step="0.01"
                              value={entry.debit}
                              onChange={(e) => updateEntryLine(index, 'debit', e.target.value)}
                              placeholder="0.00"
                            />
                          </td>
                          <td>
                            <input
                              type="number"
                              step="0.01"
                              value={entry.credit}
                              onChange={(e) => updateEntryLine(index, 'credit', e.target.value)}
                              placeholder="0.00"
                            />
                          </td>
                          <td>
                            <button
                              type="button"
                              onClick={() => removeEntryLine(index)}
                              className="remove-line-btn"
                              disabled={newEntry.entries.length <= 2}
                            >
                              🗑️
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="totals-row">
                        <td colSpan="2"><strong>الإجمالي</strong></td>
                        <td className="total-debit"><strong>{calculateTotalDebit().toFixed(2)}</strong></td>
                        <td className="total-credit"><strong>{calculateTotalCredit().toFixed(2)}</strong></td>
                        <td></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
                
                <button
                  type="button"
                  onClick={addEntryLine}
                  className="add-line-btn"
                >
                  ➕ إضافة سطر
                </button>

                {Math.abs(calculateTotalDebit() - calculateTotalCredit()) > 0.01 && (
                  <div className="balance-warning">
                    ⚠️ إجمالي المدين لا يساوي إجمالي الدائن
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowAddEntryForm(false)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleAddJournalEntry} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري الحفظ...' : 'حفظ القيد'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Accounting;
