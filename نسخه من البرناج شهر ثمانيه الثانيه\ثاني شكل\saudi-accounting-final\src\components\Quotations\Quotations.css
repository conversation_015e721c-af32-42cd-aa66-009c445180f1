.quotations-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.quotations-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.quotations-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.quotations-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.message {
  padding: 15px 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  font-weight: 600;
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 5px solid;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-card.total { border-left-color: #8b5cf6; }
.stat-card.accepted { border-left-color: #10b981; }
.stat-card.pending { border-left-color: #f59e0b; }
.stat-card.amount { border-left-color: #6366f1; }

.stat-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-info p {
  margin: 0;
  color: #1f2937;
  font-size: 1.8rem;
  font-weight: 800;
}

.quotations-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  flex: 1;
}

.search-input,
.filter-select {
  padding: 12px 18px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  min-width: 200px;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.add-quotation-btn {
  padding: 12px 25px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-quotation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.quotation-form-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 2px solid #e5e7eb;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.quotation-form {
  padding: 30px;
}

.form-header {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 15px;
  border: 1px solid #e5e7eb;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.items-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 15px;
  border: 1px solid #e5e7eb;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 1.2rem;
  font-weight: 700;
}

.add-item-btn {
  padding: 8px 15px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-item-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

.items-table {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.items-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 10px;
  padding: 10px;
  background: #e5e7eb;
  border-radius: 8px;
  font-weight: 600;
  color: #374151;
}

.item-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 10px;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.item-row select,
.item-row input {
  padding: 8px 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.item-row select:focus,
.item-row input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.total-field {
  background: #f3f4f6 !important;
  color: #6b7280;
  font-weight: 600;
}

.remove-item-btn {
  padding: 8px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-item-btn:hover {
  background: #fecaca;
  transform: scale(1.1);
}

.discount-section {
  margin: 20px 0;
  padding: 15px;
  background: white;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.totals-section {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.total-row:last-child {
  border-bottom: none;
}

.final-total {
  font-weight: 700;
  font-size: 1.2rem;
  color: #1f2937;
  border-top: 2px solid #e5e7eb;
  padding-top: 15px;
  margin-top: 10px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #e5e7eb;
}

.cancel-btn,
.save-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.save-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.quotations-table-container {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.quotations-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1000px;
}

.quotations-table th {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  padding: 18px 15px;
  text-align: right;
  font-weight: 600;
  font-size: 0.95rem;
}

.quotations-table td {
  padding: 15px;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
}

.quotations-table tbody tr:hover {
  background: #f8fafc;
}

.quotations-table tbody tr.expired {
  background: #fef2f2;
}

.quotation-number {
  font-weight: 700;
  color: #1f2937;
}

.amount {
  font-weight: 600;
  color: #8b5cf6;
  text-align: left;
}

.expired-date {
  color: #ef4444;
  font-weight: 600;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 100px;
}

.actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.print-btn,
.convert-btn,
.delete-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.edit-btn {
  background: #f3f4f6;
  color: #374151;
}

.edit-btn:hover:not(:disabled) {
  background: #e5e7eb;
  transform: scale(1.1);
}

.print-btn {
  background: #dbeafe;
  color: #1d4ed8;
}

.print-btn:hover {
  background: #bfdbfe;
  transform: scale(1.1);
}

.convert-btn {
  background: #d1fae5;
  color: #065f46;
}

.convert-btn:hover {
  background: #a7f3d0;
  transform: scale(1.1);
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover:not(:disabled) {
  background: #fecaca;
  transform: scale(1.1);
}

.edit-btn:disabled,
.delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
  font-style: italic;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .quotations-container {
    padding: 15px;
  }
  
  .quotations-header h1 {
    font-size: 2.2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quotations-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
  }
  
  .search-input,
  .filter-select {
    min-width: auto;
  }
  
  .quotation-form-modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .items-header,
  .item-row {
    grid-template-columns: 1fr;
    gap: 5px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .quotations-table-container {
    overflow-x: auto;
  }
}
