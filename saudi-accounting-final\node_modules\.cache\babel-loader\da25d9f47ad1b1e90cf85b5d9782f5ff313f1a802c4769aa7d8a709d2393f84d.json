{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Vouchers\\\\Vouchers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Vouchers.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Vouchers = () => {\n  _s();\n  const [vouchers, setVouchers] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingVoucher, setEditingVoucher] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [newVoucher, setNewVoucher] = useState({\n    type: 'receipt',\n    voucherDate: new Date().toISOString().split('T')[0],\n    amount: '',\n    description: '',\n    accountId: '',\n    customerId: '',\n    supplierId: '',\n    fromAccountId: '',\n    toAccountId: '',\n    paymentMethod: 'cash',\n    reference: '',\n    notes: ''\n  });\n  const voucherTypes = [{\n    value: 'receipt',\n    label: '🧾 سند قبض',\n    color: '#10b981'\n  }, {\n    value: 'payment',\n    label: '💸 سند صرف',\n    color: '#ef4444'\n  }];\n  const paymentMethods = [{\n    value: 'cash',\n    label: '💵 نقداً'\n  }, {\n    value: 'bank_transfer',\n    label: '🏦 تحويل بنكي'\n  }, {\n    value: 'check',\n    label: '📝 شيك'\n  }, {\n    value: 'credit_card',\n    label: '💳 بطاقة ائتمان'\n  }, {\n    value: 'digital_wallet',\n    label: '📱 محفظة رقمية'\n  }];\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = () => {\n    try {\n      const vouchersData = database.getVouchers();\n      const accountsData = database.getAccounts();\n      const customersData = database.getCustomers();\n      const suppliersData = database.getSuppliers();\n      setVouchers(vouchersData);\n      setAccounts(accountsData);\n      setCustomers(customersData);\n      setSuppliers(suppliersData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewVoucher(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      const voucherData = {\n        ...newVoucher,\n        amount: parseFloat(newVoucher.amount),\n        customerId: newVoucher.customerId ? parseInt(newVoucher.customerId) : null,\n        supplierId: newVoucher.supplierId ? parseInt(newVoucher.supplierId) : null,\n        accountId: parseInt(newVoucher.accountId),\n        fromAccountId: newVoucher.fromAccountId ? parseInt(newVoucher.fromAccountId) : null,\n        toAccountId: newVoucher.toAccountId ? parseInt(newVoucher.toAccountId) : null\n      };\n      if (editingVoucher) {\n        await database.updateVoucher(editingVoucher.id, voucherData);\n        setMessage('تم تحديث السند بنجاح!');\n        setEditingVoucher(null);\n      } else {\n        await database.addVoucher(voucherData);\n        setMessage('تم إضافة السند بنجاح!');\n      }\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ السند:', error);\n      setMessage('خطأ في حفظ السند');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetForm = () => {\n    setNewVoucher({\n      type: 'receipt',\n      voucherDate: new Date().toISOString().split('T')[0],\n      amount: '',\n      description: '',\n      accountId: '',\n      customerId: '',\n      supplierId: '',\n      fromAccountId: '',\n      toAccountId: '',\n      paymentMethod: 'cash',\n      reference: '',\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingVoucher(null);\n  };\n  const handleEdit = voucher => {\n    setNewVoucher({\n      ...voucher,\n      voucherDate: voucher.voucherDate.split('T')[0]\n    });\n    setEditingVoucher(voucher);\n    setShowAddForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا السند؟')) {\n      try {\n        await database.deleteVoucher(id);\n        setMessage('تم حذف السند بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف السند:', error);\n        setMessage('خطأ في حذف السند');\n      }\n    }\n  };\n  const printVoucher = voucher => {\n    var _paymentMethods$find;\n    const settings = database.getSettings();\n    const account = accounts.find(acc => acc.id === voucher.accountId);\n    const customer = customers.find(c => c.id === voucher.customerId);\n    const supplier = suppliers.find(s => s.id === voucher.supplierId);\n    const printWindow = window.open('', '_blank');\n    const printContent = `\n      <html>\n        <head>\n          <title>${voucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} - ${voucher.voucherNumber}</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 20px; \n              direction: rtl; \n              background: white;\n            }\n            .voucher-container {\n              max-width: 600px;\n              margin: 0 auto;\n              border: 2px solid #333;\n              padding: 20px;\n              background: white;\n            }\n            .header { \n              text-align: center; \n              border-bottom: 2px solid #333; \n              padding-bottom: 15px; \n              margin-bottom: 20px; \n            }\n            .company-info { \n              text-align: center; \n              margin-bottom: 20px; \n              font-size: 0.9em;\n            }\n            .voucher-title {\n              font-size: 1.8em;\n              font-weight: bold;\n              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              margin: 10px 0;\n            }\n            .voucher-info { \n              display: flex; \n              justify-content: space-between; \n              margin-bottom: 20px; \n              border: 1px solid #ddd;\n              padding: 15px;\n              background: #f9f9f9;\n            }\n            .amount-section {\n              text-align: center;\n              margin: 30px 0;\n              padding: 20px;\n              border: 2px solid ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              background: ${voucher.type === 'receipt' ? '#f0fdf4' : '#fef2f2'};\n            }\n            .amount-number {\n              font-size: 2em;\n              font-weight: bold;\n              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              margin: 10px 0;\n            }\n            .amount-words {\n              font-style: italic;\n              margin-top: 10px;\n              padding: 10px;\n              background: white;\n              border: 1px solid #ddd;\n            }\n            .details-section {\n              margin: 20px 0;\n              padding: 15px;\n              border: 1px solid #ddd;\n            }\n            .signature-section {\n              margin-top: 40px;\n              display: flex;\n              justify-content: space-between;\n            }\n            .signature-box {\n              text-align: center;\n              width: 200px;\n              border-top: 1px solid #333;\n              padding-top: 10px;\n            }\n            @media print { \n              body { margin: 0; }\n              .voucher-container { border: 2px solid #000; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"voucher-container\">\n            <div class=\"header\">\n              <h2>${settings.companyName}</h2>\n              <div class=\"company-info\">\n                <p>${settings.address}</p>\n                <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>\n                <p>الرقم الضريبي: ${settings.vatNumber}</p>\n              </div>\n              <div class=\"voucher-title\">\n                ${voucher.type === 'receipt' ? '🧾 سند قبض' : '💸 سند صرف'}\n              </div>\n            </div>\n\n            <div class=\"voucher-info\">\n              <div>\n                <p><strong>رقم السند:</strong> ${voucher.voucherNumber}</p>\n                <p><strong>التاريخ:</strong> ${new Date(voucher.voucherDate).toLocaleDateString('ar-SA')}</p>\n                <p><strong>طريقة الدفع:</strong> ${((_paymentMethods$find = paymentMethods.find(m => m.value === voucher.paymentMethod)) === null || _paymentMethods$find === void 0 ? void 0 : _paymentMethods$find.label) || voucher.paymentMethod}</p>\n              </div>\n              <div>\n                <p><strong>الحساب:</strong> ${(account === null || account === void 0 ? void 0 : account.name) || 'غير محدد'}</p>\n                ${customer ? `<p><strong>العميل:</strong> ${customer.name}</p>` : ''}\n                ${supplier ? `<p><strong>المورد:</strong> ${supplier.name}</p>` : ''}\n                ${voucher.reference ? `<p><strong>المرجع:</strong> ${voucher.reference}</p>` : ''}\n              </div>\n            </div>\n\n            <div class=\"amount-section\">\n              <h3>المبلغ</h3>\n              <div class=\"amount-number\">${voucher.amount.toLocaleString()} ريال سعودي</div>\n              <div class=\"amount-words\">\n                <strong>المبلغ بالكلمات:</strong> ${convertNumberToWords(voucher.amount)} ريال سعودي فقط لا غير\n              </div>\n            </div>\n\n            <div class=\"details-section\">\n              <h4>البيان:</h4>\n              <p>${voucher.description}</p>\n              ${voucher.notes ? `\n                <h4>ملاحظات:</h4>\n                <p>${voucher.notes}</p>\n              ` : ''}\n            </div>\n\n            <div class=\"signature-section\">\n              <div class=\"signature-box\">\n                <p>المحاسب</p>\n                <br><br>\n                <p>التوقيع: ________________</p>\n              </div>\n              <div class=\"signature-box\">\n                <p>${voucher.type === 'receipt' ? 'المستلم' : 'المستفيد'}</p>\n                <br><br>\n                <p>التوقيع: ________________</p>\n              </div>\n            </div>\n\n            <div style=\"margin-top: 30px; text-align: center; font-size: 0.8em; color: #666;\">\n              <p>تم إنشاء هذا السند بواسطة نظام المحاسبة السعودي</p>\n              <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n            </div>\n          </div>\n        </body>\n      </html>\n    `;\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n  const convertNumberToWords = number => {\n    // تحويل الرقم إلى كلمات - تطبيق مبسط\n    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];\n    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];\n    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];\n    if (number === 0) return 'صفر';\n    if (number < 10) return ones[number];\n    if (number < 20) return teens[number - 10];\n    if (number < 100) {\n      const ten = Math.floor(number / 10);\n      const one = number % 10;\n      return tens[ten] + (one > 0 ? ' و' + ones[one] : '');\n    }\n\n    // للأرقام الأكبر، نعيد تمثيل مبسط\n    return number.toLocaleString();\n  };\n  const getAccountName = accountId => {\n    const account = accounts.find(acc => acc.id === accountId);\n    return account ? `${account.code} - ${account.name}` : 'غير محدد';\n  };\n  const getCustomerName = customerId => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n  const getSupplierName = supplierId => {\n    const supplier = suppliers.find(s => s.id === supplierId);\n    return supplier ? supplier.name : 'غير محدد';\n  };\n  const getTypeInfo = type => {\n    const typeObj = voucherTypes.find(t => t.value === type);\n    return typeObj || {\n      label: type,\n      color: '#64748b'\n    };\n  };\n  const getPaymentMethodLabel = method => {\n    const methodObj = paymentMethods.find(m => m.value === method);\n    return methodObj ? methodObj.label : method;\n  };\n  const filteredVouchers = vouchers.filter(voucher => {\n    const matchesSearch = voucher.voucherNumber.toLowerCase().includes(searchTerm.toLowerCase()) || voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) || getAccountName(voucher.accountId).toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || voucher.type === filterType;\n    const voucherDate = new Date(voucher.voucherDate);\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const matchesDate = voucherDate >= startDate && voucherDate <= endDate;\n    return matchesSearch && matchesType && matchesDate;\n  });\n  const totalReceipts = filteredVouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);\n  const totalPayments = filteredVouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);\n  const netCashFlow = totalReceipts - totalPayments;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vouchers-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"vouchers-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83E\\uDDFE \\u0633\\u0646\\u062F\\u0627\\u062A \\u0627\\u0644\\u0642\\u0628\\u0636 \\u0648\\u0627\\u0644\\u0635\\u0631\\u0641\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u0633\\u0646\\u062F\\u0627\\u062A \\u0627\\u0644\\u0642\\u0628\\u0636 \\u0648\\u0627\\u0644\\u0635\\u0631\\u0641 \\u0627\\u0644\\u0646\\u0642\\u062F\\u064A\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('خطأ') ? 'error' : 'success'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card total\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83E\\uDDFE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0633\\u0646\\u062F\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [vouchers.length, \" \\u0633\\u0646\\u062F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card receipts\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCC8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0642\\u0628\\u0648\\u0636\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [totalReceipts.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card payments\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCC9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [totalPayments.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card net-flow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u062A\\u062F\\u0641\\u0642 \\u0627\\u0644\\u0646\\u0642\\u062F\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: netCashFlow >= 0 ? 'positive' : 'negative',\n            children: [netCashFlow.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"vouchers-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-filters\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0633\\u0646\\u062F\\u0627\\u062A...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterType,\n          onChange: e => setFilterType(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), voucherTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type.value,\n            children: type.label\n          }, type.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.start,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            start: e.target.value\n          })),\n          className: \"date-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.end,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            end: e.target.value\n          })),\n          className: \"date-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setNewVoucher(prev => ({\n              ...prev,\n              type: 'receipt'\n            }));\n            setShowAddForm(true);\n          },\n          className: \"add-receipt-btn\",\n          children: \"\\u2795 \\u0633\\u0646\\u062F \\u0642\\u0628\\u0636\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setNewVoucher(prev => ({\n              ...prev,\n              type: 'payment'\n            }));\n            setShowAddForm(true);\n          },\n          className: \"add-payment-btn\",\n          children: \"\\u2795 \\u0633\\u0646\\u062F \\u0635\\u0631\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"voucher-form-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: editingVoucher ? `✏️ تعديل ${newVoucher.type === 'receipt' ? 'سند القبض' : 'سند الصرف'}` : `➕ إضافة ${newVoucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} جديد`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetForm,\n            className: \"close-btn\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"voucher-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0633\\u0646\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"type\",\n                value: newVoucher.type,\n                onChange: handleInputChange,\n                required: true,\n                children: voucherTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type.value,\n                  children: type.label\n                }, type.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                name: \"voucherDate\",\n                value: newVoucher.voucherDate,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"amount\",\n                value: newVoucher.amount,\n                onChange: handleInputChange,\n                step: \"0.01\",\n                min: \"0\",\n                required: true,\n                placeholder: \"0.00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"paymentMethod\",\n                value: newVoucher.paymentMethod,\n                onChange: handleInputChange,\n                required: true,\n                children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: method.value,\n                  children: method.label\n                }, method.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u062D\\u0633\\u0627\\u0628 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"accountId\",\n                value: newVoucher.accountId,\n                onChange: handleInputChange,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this), accounts.filter(acc => acc.type === 'asset' && (acc.name.includes('نقدية') || acc.name.includes('بنك') || acc.name.includes('صندوق'))).map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: account.id,\n                  children: [account.code, \" - \", account.name]\n                }, account.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), newVoucher.type === 'receipt' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"customerId\",\n                value: newVoucher.customerId,\n                onChange: handleInputChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 23\n                }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: customer.id,\n                  children: customer.name\n                }, customer.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 19\n            }, this), newVoucher.type === 'payment' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"supplierId\",\n                value: newVoucher.supplierId,\n                onChange: handleInputChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 23\n                }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: supplier.id,\n                  children: supplier.name\n                }, supplier.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"reference\",\n                value: newVoucher.reference,\n                onChange: handleInputChange,\n                placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0634\\u064A\\u0643\\u060C \\u0631\\u0642\\u0645 \\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644\\u060C \\u0625\\u0644\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"description\",\n                value: newVoucher.description,\n                onChange: handleInputChange,\n                required: true,\n                placeholder: \"\\u0648\\u0635\\u0641 \\u0645\\u062E\\u062A\\u0635\\u0631 \\u0644\\u0644\\u0639\\u0645\\u0644\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"notes\",\n                value: newVoucher.notes,\n                onChange: handleInputChange,\n                rows: \"3\",\n                placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: resetForm,\n              className: \"cancel-btn\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"save-btn\",\n              children: loading ? '⏳ جاري الحفظ...' : editingVoucher ? '💾 تحديث' : '💾 حفظ'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"vouchers-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"vouchers-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0633\\u0646\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0646\\u0648\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredVouchers.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"8\",\n              className: \"no-data\",\n              children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0633\\u0646\\u062F\\u0627\\u062A \\u0645\\u0637\\u0627\\u0628\\u0642\\u0629 \\u0644\\u0644\\u0628\\u062D\\u062B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 15\n          }, this) : filteredVouchers.map(voucher => {\n            const typeInfo = getTypeInfo(voucher.type);\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"voucher-number\",\n                children: voucher.voucherNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-badge\",\n                  style: {\n                    backgroundColor: typeInfo.color\n                  },\n                  children: typeInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(voucher.voucherDate).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: voucher.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getAccountName(voucher.accountId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: `amount ${voucher.type === 'receipt' ? 'positive' : 'negative'}`,\n                children: [voucher.type === 'receipt' ? '+' : '-', voucher.amount.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getPaymentMethodLabel(voucher.paymentMethod)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(voucher),\n                  className: \"edit-btn\",\n                  title: \"\\u062A\\u0639\\u062F\\u064A\\u0644\",\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => printVoucher(voucher),\n                  className: \"print-btn\",\n                  title: \"\\u0637\\u0628\\u0627\\u0639\\u0629\",\n                  children: \"\\uD83D\\uDDA8\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(voucher.id),\n                  className: \"delete-btn\",\n                  title: \"\\u062D\\u0630\\u0641\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 21\n              }, this)]\n            }, voucher.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n};\n_s(Vouchers, \"UjYGvvCvM+d3tIEY+hWVHfUcXnk=\");\n_c = Vouchers;\nexport default Vouchers;\nvar _c;\n$RefreshReg$(_c, \"Vouchers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Vouchers", "_s", "vouchers", "setVouchers", "accounts", "setAccounts", "customers", "setCustomers", "suppliers", "setSuppliers", "showAddForm", "setShowAddForm", "editingVoucher", "setEditingVoucher", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "toISOString", "split", "end", "loading", "setLoading", "message", "setMessage", "newVoucher", "setNewVoucher", "type", "voucherDate", "amount", "description", "accountId", "customerId", "supplierId", "fromAccountId", "toAccountId", "paymentMethod", "reference", "notes", "voucherTypes", "value", "label", "color", "paymentMethods", "loadData", "vouchersData", "getVouchers", "accountsData", "getAccounts", "customersData", "getCustomers", "suppliersData", "getSuppliers", "error", "console", "handleInputChange", "e", "name", "target", "prev", "handleSubmit", "preventDefault", "voucherData", "parseFloat", "parseInt", "updateVoucher", "id", "addVoucher", "resetForm", "setTimeout", "handleEdit", "voucher", "handleDelete", "window", "confirm", "deleteVoucher", "printVoucher", "_paymentMethods$find", "settings", "getSettings", "account", "find", "acc", "customer", "c", "supplier", "s", "printWindow", "open", "printContent", "voucherNumber", "companyName", "address", "phone", "email", "vatNumber", "toLocaleDateString", "m", "toLocaleString", "convertNumberToWords", "document", "write", "close", "print", "number", "ones", "tens", "teens", "ten", "Math", "floor", "one", "getAccountName", "code", "getCustomerName", "getSupplierName", "getTypeInfo", "typeObj", "t", "getPaymentMethodLabel", "method", "methodObj", "filteredVouchers", "filter", "matchesSearch", "toLowerCase", "includes", "matchesType", "startDate", "endDate", "matchesDate", "totalReceipts", "v", "reduce", "sum", "totalPayments", "netCashFlow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "placeholder", "onChange", "map", "onClick", "onSubmit", "required", "step", "min", "rows", "disabled", "colSpan", "typeInfo", "style", "backgroundColor", "title", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Vouchers/Vouchers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Vouchers.css';\n\nconst Vouchers = () => {\n  const [vouchers, setVouchers] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingVoucher, setEditingVoucher] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const [newVoucher, setNewVoucher] = useState({\n    type: 'receipt',\n    voucherDate: new Date().toISOString().split('T')[0],\n    amount: '',\n    description: '',\n    accountId: '',\n    customerId: '',\n    supplierId: '',\n    fromAccountId: '',\n    toAccountId: '',\n    paymentMethod: 'cash',\n    reference: '',\n    notes: ''\n  });\n\n  const voucherTypes = [\n    { value: 'receipt', label: '🧾 سند قبض', color: '#10b981' },\n    { value: 'payment', label: '💸 سند صرف', color: '#ef4444' }\n  ];\n\n  const paymentMethods = [\n    { value: 'cash', label: '💵 نقداً' },\n    { value: 'bank_transfer', label: '🏦 تحويل بنكي' },\n    { value: 'check', label: '📝 شيك' },\n    { value: 'credit_card', label: '💳 بطاقة ائتمان' },\n    { value: 'digital_wallet', label: '📱 محفظة رقمية' }\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = () => {\n    try {\n      const vouchersData = database.getVouchers();\n      const accountsData = database.getAccounts();\n      const customersData = database.getCustomers();\n      const suppliersData = database.getSuppliers();\n      \n      setVouchers(vouchersData);\n      setAccounts(accountsData);\n      setCustomers(customersData);\n      setSuppliers(suppliersData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewVoucher(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      const voucherData = {\n        ...newVoucher,\n        amount: parseFloat(newVoucher.amount),\n        customerId: newVoucher.customerId ? parseInt(newVoucher.customerId) : null,\n        supplierId: newVoucher.supplierId ? parseInt(newVoucher.supplierId) : null,\n        accountId: parseInt(newVoucher.accountId),\n        fromAccountId: newVoucher.fromAccountId ? parseInt(newVoucher.fromAccountId) : null,\n        toAccountId: newVoucher.toAccountId ? parseInt(newVoucher.toAccountId) : null\n      };\n\n      if (editingVoucher) {\n        await database.updateVoucher(editingVoucher.id, voucherData);\n        setMessage('تم تحديث السند بنجاح!');\n        setEditingVoucher(null);\n      } else {\n        await database.addVoucher(voucherData);\n        setMessage('تم إضافة السند بنجاح!');\n      }\n\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ السند:', error);\n      setMessage('خطأ في حفظ السند');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetForm = () => {\n    setNewVoucher({\n      type: 'receipt',\n      voucherDate: new Date().toISOString().split('T')[0],\n      amount: '',\n      description: '',\n      accountId: '',\n      customerId: '',\n      supplierId: '',\n      fromAccountId: '',\n      toAccountId: '',\n      paymentMethod: 'cash',\n      reference: '',\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingVoucher(null);\n  };\n\n  const handleEdit = (voucher) => {\n    setNewVoucher({\n      ...voucher,\n      voucherDate: voucher.voucherDate.split('T')[0]\n    });\n    setEditingVoucher(voucher);\n    setShowAddForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا السند؟')) {\n      try {\n        await database.deleteVoucher(id);\n        setMessage('تم حذف السند بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف السند:', error);\n        setMessage('خطأ في حذف السند');\n      }\n    }\n  };\n\n  const printVoucher = (voucher) => {\n    const settings = database.getSettings();\n    const account = accounts.find(acc => acc.id === voucher.accountId);\n    const customer = customers.find(c => c.id === voucher.customerId);\n    const supplier = suppliers.find(s => s.id === voucher.supplierId);\n    \n    const printWindow = window.open('', '_blank');\n    const printContent = `\n      <html>\n        <head>\n          <title>${voucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} - ${voucher.voucherNumber}</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 20px; \n              direction: rtl; \n              background: white;\n            }\n            .voucher-container {\n              max-width: 600px;\n              margin: 0 auto;\n              border: 2px solid #333;\n              padding: 20px;\n              background: white;\n            }\n            .header { \n              text-align: center; \n              border-bottom: 2px solid #333; \n              padding-bottom: 15px; \n              margin-bottom: 20px; \n            }\n            .company-info { \n              text-align: center; \n              margin-bottom: 20px; \n              font-size: 0.9em;\n            }\n            .voucher-title {\n              font-size: 1.8em;\n              font-weight: bold;\n              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              margin: 10px 0;\n            }\n            .voucher-info { \n              display: flex; \n              justify-content: space-between; \n              margin-bottom: 20px; \n              border: 1px solid #ddd;\n              padding: 15px;\n              background: #f9f9f9;\n            }\n            .amount-section {\n              text-align: center;\n              margin: 30px 0;\n              padding: 20px;\n              border: 2px solid ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              background: ${voucher.type === 'receipt' ? '#f0fdf4' : '#fef2f2'};\n            }\n            .amount-number {\n              font-size: 2em;\n              font-weight: bold;\n              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};\n              margin: 10px 0;\n            }\n            .amount-words {\n              font-style: italic;\n              margin-top: 10px;\n              padding: 10px;\n              background: white;\n              border: 1px solid #ddd;\n            }\n            .details-section {\n              margin: 20px 0;\n              padding: 15px;\n              border: 1px solid #ddd;\n            }\n            .signature-section {\n              margin-top: 40px;\n              display: flex;\n              justify-content: space-between;\n            }\n            .signature-box {\n              text-align: center;\n              width: 200px;\n              border-top: 1px solid #333;\n              padding-top: 10px;\n            }\n            @media print { \n              body { margin: 0; }\n              .voucher-container { border: 2px solid #000; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"voucher-container\">\n            <div class=\"header\">\n              <h2>${settings.companyName}</h2>\n              <div class=\"company-info\">\n                <p>${settings.address}</p>\n                <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>\n                <p>الرقم الضريبي: ${settings.vatNumber}</p>\n              </div>\n              <div class=\"voucher-title\">\n                ${voucher.type === 'receipt' ? '🧾 سند قبض' : '💸 سند صرف'}\n              </div>\n            </div>\n\n            <div class=\"voucher-info\">\n              <div>\n                <p><strong>رقم السند:</strong> ${voucher.voucherNumber}</p>\n                <p><strong>التاريخ:</strong> ${new Date(voucher.voucherDate).toLocaleDateString('ar-SA')}</p>\n                <p><strong>طريقة الدفع:</strong> ${paymentMethods.find(m => m.value === voucher.paymentMethod)?.label || voucher.paymentMethod}</p>\n              </div>\n              <div>\n                <p><strong>الحساب:</strong> ${account?.name || 'غير محدد'}</p>\n                ${customer ? `<p><strong>العميل:</strong> ${customer.name}</p>` : ''}\n                ${supplier ? `<p><strong>المورد:</strong> ${supplier.name}</p>` : ''}\n                ${voucher.reference ? `<p><strong>المرجع:</strong> ${voucher.reference}</p>` : ''}\n              </div>\n            </div>\n\n            <div class=\"amount-section\">\n              <h3>المبلغ</h3>\n              <div class=\"amount-number\">${voucher.amount.toLocaleString()} ريال سعودي</div>\n              <div class=\"amount-words\">\n                <strong>المبلغ بالكلمات:</strong> ${convertNumberToWords(voucher.amount)} ريال سعودي فقط لا غير\n              </div>\n            </div>\n\n            <div class=\"details-section\">\n              <h4>البيان:</h4>\n              <p>${voucher.description}</p>\n              ${voucher.notes ? `\n                <h4>ملاحظات:</h4>\n                <p>${voucher.notes}</p>\n              ` : ''}\n            </div>\n\n            <div class=\"signature-section\">\n              <div class=\"signature-box\">\n                <p>المحاسب</p>\n                <br><br>\n                <p>التوقيع: ________________</p>\n              </div>\n              <div class=\"signature-box\">\n                <p>${voucher.type === 'receipt' ? 'المستلم' : 'المستفيد'}</p>\n                <br><br>\n                <p>التوقيع: ________________</p>\n              </div>\n            </div>\n\n            <div style=\"margin-top: 30px; text-align: center; font-size: 0.8em; color: #666;\">\n              <p>تم إنشاء هذا السند بواسطة نظام المحاسبة السعودي</p>\n              <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n            </div>\n          </div>\n        </body>\n      </html>\n    `;\n\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n\n  const convertNumberToWords = (number) => {\n    // تحويل الرقم إلى كلمات - تطبيق مبسط\n    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];\n    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];\n    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];\n    \n    if (number === 0) return 'صفر';\n    if (number < 10) return ones[number];\n    if (number < 20) return teens[number - 10];\n    if (number < 100) {\n      const ten = Math.floor(number / 10);\n      const one = number % 10;\n      return tens[ten] + (one > 0 ? ' و' + ones[one] : '');\n    }\n    \n    // للأرقام الأكبر، نعيد تمثيل مبسط\n    return number.toLocaleString();\n  };\n\n  const getAccountName = (accountId) => {\n    const account = accounts.find(acc => acc.id === accountId);\n    return account ? `${account.code} - ${account.name}` : 'غير محدد';\n  };\n\n  const getCustomerName = (customerId) => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n\n  const getSupplierName = (supplierId) => {\n    const supplier = suppliers.find(s => s.id === supplierId);\n    return supplier ? supplier.name : 'غير محدد';\n  };\n\n  const getTypeInfo = (type) => {\n    const typeObj = voucherTypes.find(t => t.value === type);\n    return typeObj || { label: type, color: '#64748b' };\n  };\n\n  const getPaymentMethodLabel = (method) => {\n    const methodObj = paymentMethods.find(m => m.value === method);\n    return methodObj ? methodObj.label : method;\n  };\n\n  const filteredVouchers = vouchers.filter(voucher => {\n    const matchesSearch = \n      voucher.voucherNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      getAccountName(voucher.accountId).toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesType = filterType === 'all' || voucher.type === filterType;\n    \n    const voucherDate = new Date(voucher.voucherDate);\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const matchesDate = voucherDate >= startDate && voucherDate <= endDate;\n    \n    return matchesSearch && matchesType && matchesDate;\n  });\n\n  const totalReceipts = filteredVouchers\n    .filter(v => v.type === 'receipt')\n    .reduce((sum, v) => sum + v.amount, 0);\n  \n  const totalPayments = filteredVouchers\n    .filter(v => v.type === 'payment')\n    .reduce((sum, v) => sum + v.amount, 0);\n\n  const netCashFlow = totalReceipts - totalPayments;\n\n  return (\n    <div className=\"vouchers-container\">\n      <div className=\"vouchers-header\">\n        <h1>🧾 سندات القبض والصرف</h1>\n        <p>إدارة وتتبع سندات القبض والصرف النقدية</p>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>\n          {message}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card total\">\n          <div className=\"stat-icon\">🧾</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي السندات</h3>\n            <p>{vouchers.length} سند</p>\n          </div>\n        </div>\n        <div className=\"stat-card receipts\">\n          <div className=\"stat-icon\">📈</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي المقبوضات</h3>\n            <p>{totalReceipts.toLocaleString()} ريال</p>\n          </div>\n        </div>\n        <div className=\"stat-card payments\">\n          <div className=\"stat-icon\">📉</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي المدفوعات</h3>\n            <p>{totalPayments.toLocaleString()} ريال</p>\n          </div>\n        </div>\n        <div className=\"stat-card net-flow\">\n          <div className=\"stat-icon\">💰</div>\n          <div className=\"stat-info\">\n            <h3>صافي التدفق النقدي</h3>\n            <p className={netCashFlow >= 0 ? 'positive' : 'negative'}>\n              {netCashFlow.toLocaleString()} ريال\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"vouchers-controls\">\n        <div className=\"search-filters\">\n          <input\n            type=\"text\"\n            placeholder=\"🔍 البحث في السندات...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">جميع الأنواع</option>\n            {voucherTypes.map(type => (\n              <option key={type.value} value={type.value}>\n                {type.label}\n              </option>\n            ))}\n          </select>\n          <input\n            type=\"date\"\n            value={dateRange.start}\n            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n            className=\"date-input\"\n          />\n          <input\n            type=\"date\"\n            value={dateRange.end}\n            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n            className=\"date-input\"\n          />\n        </div>\n        <div className=\"action-buttons\">\n          <button\n            onClick={() => {\n              setNewVoucher(prev => ({ ...prev, type: 'receipt' }));\n              setShowAddForm(true);\n            }}\n            className=\"add-receipt-btn\"\n          >\n            ➕ سند قبض\n          </button>\n          <button\n            onClick={() => {\n              setNewVoucher(prev => ({ ...prev, type: 'payment' }));\n              setShowAddForm(true);\n            }}\n            className=\"add-payment-btn\"\n          >\n            ➕ سند صرف\n          </button>\n        </div>\n      </div>\n\n      {/* Add/Edit Form */}\n      {showAddForm && (\n        <div className=\"modal-overlay\">\n          <div className=\"voucher-form-modal\">\n            <div className=\"modal-header\">\n              <h3>\n                {editingVoucher ? \n                  `✏️ تعديل ${newVoucher.type === 'receipt' ? 'سند القبض' : 'سند الصرف'}` : \n                  `➕ إضافة ${newVoucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} جديد`\n                }\n              </h3>\n              <button onClick={resetForm} className=\"close-btn\">✕</button>\n            </div>\n            \n            <form onSubmit={handleSubmit} className=\"voucher-form\">\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label>نوع السند</label>\n                  <select\n                    name=\"type\"\n                    value={newVoucher.type}\n                    onChange={handleInputChange}\n                    required\n                  >\n                    {voucherTypes.map(type => (\n                      <option key={type.value} value={type.value}>\n                        {type.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>التاريخ</label>\n                  <input\n                    type=\"date\"\n                    name=\"voucherDate\"\n                    value={newVoucher.voucherDate}\n                    onChange={handleInputChange}\n                    required\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label>المبلغ</label>\n                  <input\n                    type=\"number\"\n                    name=\"amount\"\n                    value={newVoucher.amount}\n                    onChange={handleInputChange}\n                    step=\"0.01\"\n                    min=\"0\"\n                    required\n                    placeholder=\"0.00\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label>طريقة الدفع</label>\n                  <select\n                    name=\"paymentMethod\"\n                    value={newVoucher.paymentMethod}\n                    onChange={handleInputChange}\n                    required\n                  >\n                    {paymentMethods.map(method => (\n                      <option key={method.value} value={method.value}>\n                        {method.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>الحساب الرئيسي</label>\n                  <select\n                    name=\"accountId\"\n                    value={newVoucher.accountId}\n                    onChange={handleInputChange}\n                    required\n                  >\n                    <option value=\"\">اختر الحساب</option>\n                    {accounts.filter(acc => \n                      acc.type === 'asset' && \n                      (acc.name.includes('نقدية') || acc.name.includes('بنك') || acc.name.includes('صندوق'))\n                    ).map(account => (\n                      <option key={account.id} value={account.id}>\n                        {account.code} - {account.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {newVoucher.type === 'receipt' && (\n                  <div className=\"form-group\">\n                    <label>العميل (اختياري)</label>\n                    <select\n                      name=\"customerId\"\n                      value={newVoucher.customerId}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"\">اختر العميل</option>\n                      {customers.map(customer => (\n                        <option key={customer.id} value={customer.id}>\n                          {customer.name}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n\n                {newVoucher.type === 'payment' && (\n                  <div className=\"form-group\">\n                    <label>المورد (اختياري)</label>\n                    <select\n                      name=\"supplierId\"\n                      value={newVoucher.supplierId}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"\">اختر المورد</option>\n                      {suppliers.map(supplier => (\n                        <option key={supplier.id} value={supplier.id}>\n                          {supplier.name}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n\n                <div className=\"form-group\">\n                  <label>رقم المرجع</label>\n                  <input\n                    type=\"text\"\n                    name=\"reference\"\n                    value={newVoucher.reference}\n                    onChange={handleInputChange}\n                    placeholder=\"رقم الشيك، رقم التحويل، إلخ\"\n                  />\n                </div>\n\n                <div className=\"form-group full-width\">\n                  <label>البيان</label>\n                  <input\n                    type=\"text\"\n                    name=\"description\"\n                    value={newVoucher.description}\n                    onChange={handleInputChange}\n                    required\n                    placeholder=\"وصف مختصر للعملية\"\n                  />\n                </div>\n\n                <div className=\"form-group full-width\">\n                  <label>ملاحظات</label>\n                  <textarea\n                    name=\"notes\"\n                    value={newVoucher.notes}\n                    onChange={handleInputChange}\n                    rows=\"3\"\n                    placeholder=\"ملاحظات إضافية...\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"form-actions\">\n                <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                  إلغاء\n                </button>\n                <button type=\"submit\" disabled={loading} className=\"save-btn\">\n                  {loading ? '⏳ جاري الحفظ...' : (editingVoucher ? '💾 تحديث' : '💾 حفظ')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Vouchers Table */}\n      <div className=\"vouchers-table-container\">\n        <table className=\"vouchers-table\">\n          <thead>\n            <tr>\n              <th>رقم السند</th>\n              <th>النوع</th>\n              <th>التاريخ</th>\n              <th>البيان</th>\n              <th>الحساب</th>\n              <th>المبلغ</th>\n              <th>طريقة الدفع</th>\n              <th>الإجراءات</th>\n            </tr>\n          </thead>\n          <tbody>\n            {filteredVouchers.length === 0 ? (\n              <tr>\n                <td colSpan=\"8\" className=\"no-data\">\n                  لا توجد سندات مطابقة للبحث\n                </td>\n              </tr>\n            ) : (\n              filteredVouchers.map(voucher => {\n                const typeInfo = getTypeInfo(voucher.type);\n                return (\n                  <tr key={voucher.id}>\n                    <td className=\"voucher-number\">{voucher.voucherNumber}</td>\n                    <td>\n                      <span \n                        className=\"type-badge\"\n                        style={{ backgroundColor: typeInfo.color }}\n                      >\n                        {typeInfo.label}\n                      </span>\n                    </td>\n                    <td>{new Date(voucher.voucherDate).toLocaleDateString('ar-SA')}</td>\n                    <td>{voucher.description}</td>\n                    <td>{getAccountName(voucher.accountId)}</td>\n                    <td className={`amount ${voucher.type === 'receipt' ? 'positive' : 'negative'}`}>\n                      {voucher.type === 'receipt' ? '+' : '-'}{voucher.amount.toLocaleString()} ريال\n                    </td>\n                    <td>{getPaymentMethodLabel(voucher.paymentMethod)}</td>\n                    <td className=\"actions\">\n                      <button\n                        onClick={() => handleEdit(voucher)}\n                        className=\"edit-btn\"\n                        title=\"تعديل\"\n                      >\n                        ✏️\n                      </button>\n                      <button\n                        onClick={() => printVoucher(voucher)}\n                        className=\"print-btn\"\n                        title=\"طباعة\"\n                      >\n                        🖨️\n                      </button>\n                      <button\n                        onClick={() => handleDelete(voucher.id)}\n                        className=\"delete-btn\"\n                        title=\"حذف\"\n                      >\n                        🗑️\n                      </button>\n                    </td>\n                  </tr>\n                );\n              })\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default Vouchers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC;IACzCyB,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,GAAG,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC;IAC3CoC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnDU,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC5D;EAED,MAAMC,cAAc,GAAG,CACrB;IAAEH,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAW,CAAC,EACpC;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAClD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAEDjD,SAAS,CAAC,MAAM;IACdoD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI;MACF,MAAMC,YAAY,GAAGpD,QAAQ,CAACqD,WAAW,CAAC,CAAC;MAC3C,MAAMC,YAAY,GAAGtD,QAAQ,CAACuD,WAAW,CAAC,CAAC;MAC3C,MAAMC,aAAa,GAAGxD,QAAQ,CAACyD,YAAY,CAAC,CAAC;MAC7C,MAAMC,aAAa,GAAG1D,QAAQ,CAAC2D,YAAY,CAAC,CAAC;MAE7CrD,WAAW,CAAC8C,YAAY,CAAC;MACzB5C,WAAW,CAAC8C,YAAY,CAAC;MACzB5C,YAAY,CAAC8C,aAAa,CAAC;MAC3B5C,YAAY,CAAC8C,aAAa,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C7B,UAAU,CAAC,uBAAuB,CAAC;IACrC;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEjB;IAAM,CAAC,GAAGgB,CAAC,CAACE,MAAM;IAChChC,aAAa,CAACiC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGjB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBvC,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMsC,WAAW,GAAG;QAClB,GAAGrC,UAAU;QACbI,MAAM,EAAEkC,UAAU,CAACtC,UAAU,CAACI,MAAM,CAAC;QACrCG,UAAU,EAAEP,UAAU,CAACO,UAAU,GAAGgC,QAAQ,CAACvC,UAAU,CAACO,UAAU,CAAC,GAAG,IAAI;QAC1EC,UAAU,EAAER,UAAU,CAACQ,UAAU,GAAG+B,QAAQ,CAACvC,UAAU,CAACQ,UAAU,CAAC,GAAG,IAAI;QAC1EF,SAAS,EAAEiC,QAAQ,CAACvC,UAAU,CAACM,SAAS,CAAC;QACzCG,aAAa,EAAET,UAAU,CAACS,aAAa,GAAG8B,QAAQ,CAACvC,UAAU,CAACS,aAAa,CAAC,GAAG,IAAI;QACnFC,WAAW,EAAEV,UAAU,CAACU,WAAW,GAAG6B,QAAQ,CAACvC,UAAU,CAACU,WAAW,CAAC,GAAG;MAC3E,CAAC;MAED,IAAI3B,cAAc,EAAE;QAClB,MAAMf,QAAQ,CAACwE,aAAa,CAACzD,cAAc,CAAC0D,EAAE,EAAEJ,WAAW,CAAC;QAC5DtC,UAAU,CAAC,uBAAuB,CAAC;QACnCf,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAC,MAAM;QACL,MAAMhB,QAAQ,CAAC0E,UAAU,CAACL,WAAW,CAAC;QACtCtC,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEA4C,SAAS,CAAC,CAAC;MACXxB,QAAQ,CAAC,CAAC;MACVyB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC7B,UAAU,CAAC,kBAAkB,CAAC;IAChC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8C,SAAS,GAAGA,CAAA,KAAM;IACtB1C,aAAa,CAAC;MACZC,IAAI,EAAE,SAAS;MACfC,WAAW,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnDU,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE;IACT,CAAC,CAAC;IACF/B,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM6D,UAAU,GAAIC,OAAO,IAAK;IAC9B7C,aAAa,CAAC;MACZ,GAAG6C,OAAO;MACV3C,WAAW,EAAE2C,OAAO,CAAC3C,WAAW,CAACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC;IACFV,iBAAiB,CAAC8D,OAAO,CAAC;IAC1BhE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiE,YAAY,GAAG,MAAON,EAAE,IAAK;IACjC,IAAIO,MAAM,CAACC,OAAO,CAAC,gCAAgC,CAAC,EAAE;MACpD,IAAI;QACF,MAAMjF,QAAQ,CAACkF,aAAa,CAACT,EAAE,CAAC;QAChC1C,UAAU,CAAC,qBAAqB,CAAC;QACjCoB,QAAQ,CAAC,CAAC;QACVyB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC7B,UAAU,CAAC,kBAAkB,CAAC;MAChC;IACF;EACF,CAAC;EAED,MAAMoD,YAAY,GAAIL,OAAO,IAAK;IAAA,IAAAM,oBAAA;IAChC,MAAMC,QAAQ,GAAGrF,QAAQ,CAACsF,WAAW,CAAC,CAAC;IACvC,MAAMC,OAAO,GAAGhF,QAAQ,CAACiF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChB,EAAE,KAAKK,OAAO,CAACxC,SAAS,CAAC;IAClE,MAAMoD,QAAQ,GAAGjF,SAAS,CAAC+E,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKK,OAAO,CAACvC,UAAU,CAAC;IACjE,MAAMqD,QAAQ,GAAGjF,SAAS,CAAC6E,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKK,OAAO,CAACtC,UAAU,CAAC;IAEjE,MAAMsD,WAAW,GAAGd,MAAM,CAACe,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7C,MAAMC,YAAY,GAAG;AACzB;AACA;AACA,mBAAmBlB,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,MAAM4C,OAAO,CAACmB,aAAa;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC4C,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AACpF,4BAA4B4C,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AAC9E;AACA;AACA;AACA;AACA,uBAAuB4C,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBmD,QAAQ,CAACa,WAAW;AACxC;AACA,qBAAqBb,QAAQ,CAACc,OAAO;AACrC,2BAA2Bd,QAAQ,CAACe,KAAK,qBAAqBf,QAAQ,CAACgB,KAAK;AAC5E,oCAAoChB,QAAQ,CAACiB,SAAS;AACtD;AACA;AACA,kBAAkBxB,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,YAAY,GAAG,YAAY;AAC1E;AACA;AACA;AACA;AACA;AACA,iDAAiD4C,OAAO,CAACmB,aAAa;AACtE,+CAA+C,IAAIzE,IAAI,CAACsD,OAAO,CAAC3C,WAAW,CAAC,CAACoE,kBAAkB,CAAC,OAAO,CAAC;AACxG,mDAAmD,EAAAnB,oBAAA,GAAAlC,cAAc,CAACsC,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACzD,KAAK,KAAK+B,OAAO,CAACnC,aAAa,CAAC,cAAAyC,oBAAA,uBAA3DA,oBAAA,CAA6DpC,KAAK,KAAI8B,OAAO,CAACnC,aAAa;AAC9I;AACA;AACA,8CAA8C,CAAA4C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEvB,IAAI,KAAI,UAAU;AACzE,kBAAkB0B,QAAQ,GAAG,+BAA+BA,QAAQ,CAAC1B,IAAI,MAAM,GAAG,EAAE;AACpF,kBAAkB4B,QAAQ,GAAG,+BAA+BA,QAAQ,CAAC5B,IAAI,MAAM,GAAG,EAAE;AACpF,kBAAkBc,OAAO,CAAClC,SAAS,GAAG,+BAA+BkC,OAAO,CAAClC,SAAS,MAAM,GAAG,EAAE;AACjG;AACA;AACA;AACA;AACA;AACA,2CAA2CkC,OAAO,CAAC1C,MAAM,CAACqE,cAAc,CAAC,CAAC;AAC1E;AACA,oDAAoDC,oBAAoB,CAAC5B,OAAO,CAAC1C,MAAM,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,mBAAmB0C,OAAO,CAACzC,WAAW;AACtC,gBAAgByC,OAAO,CAACjC,KAAK,GAAG;AAChC;AACA,qBAAqBiC,OAAO,CAACjC,KAAK;AAClC,eAAe,GAAG,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqBiC,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,IAAIV,IAAI,CAAC,CAAC,CAACiF,cAAc,CAAC,OAAO,CAAC;AACpE;AACA;AACA;AACA;AACA,KAAK;IAEDX,WAAW,CAACa,QAAQ,CAACC,KAAK,CAACZ,YAAY,CAAC;IACxCF,WAAW,CAACa,QAAQ,CAACE,KAAK,CAAC,CAAC;IAC5BjC,UAAU,CAAC,MAAMkB,WAAW,CAACgB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C,CAAC;EAED,MAAMJ,oBAAoB,GAAIK,MAAM,IAAK;IACvC;IACA,MAAMC,IAAI,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC7F,MAAMC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC/F,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;IAEpI,IAAIH,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAC9B,IAAIA,MAAM,GAAG,EAAE,EAAE,OAAOC,IAAI,CAACD,MAAM,CAAC;IACpC,IAAIA,MAAM,GAAG,EAAE,EAAE,OAAOG,KAAK,CAACH,MAAM,GAAG,EAAE,CAAC;IAC1C,IAAIA,MAAM,GAAG,GAAG,EAAE;MAChB,MAAMI,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC;MACnC,MAAMO,GAAG,GAAGP,MAAM,GAAG,EAAE;MACvB,OAAOE,IAAI,CAACE,GAAG,CAAC,IAAIG,GAAG,GAAG,CAAC,GAAG,IAAI,GAAGN,IAAI,CAACM,GAAG,CAAC,GAAG,EAAE,CAAC;IACtD;;IAEA;IACA,OAAOP,MAAM,CAACN,cAAc,CAAC,CAAC;EAChC,CAAC;EAED,MAAMc,cAAc,GAAIjF,SAAS,IAAK;IACpC,MAAMiD,OAAO,GAAGhF,QAAQ,CAACiF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChB,EAAE,KAAKnC,SAAS,CAAC;IAC1D,OAAOiD,OAAO,GAAG,GAAGA,OAAO,CAACiC,IAAI,MAAMjC,OAAO,CAACvB,IAAI,EAAE,GAAG,UAAU;EACnE,CAAC;EAED,MAAMyD,eAAe,GAAIlF,UAAU,IAAK;IACtC,MAAMmD,QAAQ,GAAGjF,SAAS,CAAC+E,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKlC,UAAU,CAAC;IACzD,OAAOmD,QAAQ,GAAGA,QAAQ,CAAC1B,IAAI,GAAG,UAAU;EAC9C,CAAC;EAED,MAAM0D,eAAe,GAAIlF,UAAU,IAAK;IACtC,MAAMoD,QAAQ,GAAGjF,SAAS,CAAC6E,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKjC,UAAU,CAAC;IACzD,OAAOoD,QAAQ,GAAGA,QAAQ,CAAC5B,IAAI,GAAG,UAAU;EAC9C,CAAC;EAED,MAAM2D,WAAW,GAAIzF,IAAI,IAAK;IAC5B,MAAM0F,OAAO,GAAG9E,YAAY,CAAC0C,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAAC9E,KAAK,KAAKb,IAAI,CAAC;IACxD,OAAO0F,OAAO,IAAI;MAAE5E,KAAK,EAAEd,IAAI;MAAEe,KAAK,EAAE;IAAU,CAAC;EACrD,CAAC;EAED,MAAM6E,qBAAqB,GAAIC,MAAM,IAAK;IACxC,MAAMC,SAAS,GAAG9E,cAAc,CAACsC,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACzD,KAAK,KAAKgF,MAAM,CAAC;IAC9D,OAAOC,SAAS,GAAGA,SAAS,CAAChF,KAAK,GAAG+E,MAAM;EAC7C,CAAC;EAED,MAAME,gBAAgB,GAAG5H,QAAQ,CAAC6H,MAAM,CAACpD,OAAO,IAAI;IAClD,MAAMqD,aAAa,GACjBrD,OAAO,CAACmB,aAAa,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpH,UAAU,CAACmH,WAAW,CAAC,CAAC,CAAC,IACtEtD,OAAO,CAACzC,WAAW,CAAC+F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpH,UAAU,CAACmH,WAAW,CAAC,CAAC,CAAC,IACpEb,cAAc,CAACzC,OAAO,CAACxC,SAAS,CAAC,CAAC8F,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpH,UAAU,CAACmH,WAAW,CAAC,CAAC,CAAC;IAEpF,MAAME,WAAW,GAAGnH,UAAU,KAAK,KAAK,IAAI2D,OAAO,CAAC5C,IAAI,KAAKf,UAAU;IAEvE,MAAMgB,WAAW,GAAG,IAAIX,IAAI,CAACsD,OAAO,CAAC3C,WAAW,CAAC;IACjD,MAAMoG,SAAS,GAAG,IAAI/G,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMiH,OAAO,GAAG,IAAIhH,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IACvC,MAAM8G,WAAW,GAAGtG,WAAW,IAAIoG,SAAS,IAAIpG,WAAW,IAAIqG,OAAO;IAEtE,OAAOL,aAAa,IAAIG,WAAW,IAAIG,WAAW;EACpD,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGT,gBAAgB,CACnCC,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACzG,IAAI,KAAK,SAAS,CAAC,CACjC0G,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACvG,MAAM,EAAE,CAAC,CAAC;EAExC,MAAM0G,aAAa,GAAGb,gBAAgB,CACnCC,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACzG,IAAI,KAAK,SAAS,CAAC,CACjC0G,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACvG,MAAM,EAAE,CAAC,CAAC;EAExC,MAAM2G,WAAW,GAAGL,aAAa,GAAGI,aAAa;EAEjD,oBACE5I,OAAA;IAAK8I,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC/I,OAAA;MAAK8I,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/I,OAAA;QAAA+I,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BnJ,OAAA;QAAA+I,QAAA,EAAG;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAELvH,OAAO,iBACN5B,OAAA;MAAK8I,SAAS,EAAE,WAAWlH,OAAO,CAACuG,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAY,QAAA,EACxEnH;IAAO;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGDnJ,OAAA;MAAK8I,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/I,OAAA;QAAK8I,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/I,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCnJ,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/I,OAAA;YAAA+I,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBnJ,OAAA;YAAA+I,QAAA,GAAI5I,QAAQ,CAACiJ,MAAM,EAAC,qBAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnJ,OAAA;QAAK8I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/I,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCnJ,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/I,OAAA;YAAA+I,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnJ,OAAA;YAAA+I,QAAA,GAAIP,aAAa,CAACjC,cAAc,CAAC,CAAC,EAAC,2BAAK;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnJ,OAAA;QAAK8I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/I,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCnJ,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/I,OAAA;YAAA+I,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnJ,OAAA;YAAA+I,QAAA,GAAIH,aAAa,CAACrC,cAAc,CAAC,CAAC,EAAC,2BAAK;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnJ,OAAA;QAAK8I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/I,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCnJ,OAAA;UAAK8I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/I,OAAA;YAAA+I,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnJ,OAAA;YAAG8I,SAAS,EAAED,WAAW,IAAI,CAAC,GAAG,UAAU,GAAG,UAAW;YAAAE,QAAA,GACtDF,WAAW,CAACtC,cAAc,CAAC,CAAC,EAAC,2BAChC;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnJ,OAAA;MAAK8I,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/I,OAAA;QAAK8I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/I,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXqH,WAAW,EAAC,wGAAwB;UACpCxG,KAAK,EAAE9B,UAAW;UAClBuI,QAAQ,EAAGzF,CAAC,IAAK7C,aAAa,CAAC6C,CAAC,CAACE,MAAM,CAAClB,KAAK,CAAE;UAC/CiG,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACFnJ,OAAA;UACE6C,KAAK,EAAE5B,UAAW;UAClBqI,QAAQ,EAAGzF,CAAC,IAAK3C,aAAa,CAAC2C,CAAC,CAACE,MAAM,CAAClB,KAAK,CAAE;UAC/CiG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzB/I,OAAA;YAAQ6C,KAAK,EAAC,KAAK;YAAAkG,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCvG,YAAY,CAAC2G,GAAG,CAACvH,IAAI,iBACpBhC,OAAA;YAAyB6C,KAAK,EAAEb,IAAI,CAACa,KAAM;YAAAkG,QAAA,EACxC/G,IAAI,CAACc;UAAK,GADAd,IAAI,CAACa,KAAK;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTnJ,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXa,KAAK,EAAE1B,SAAS,CAACE,KAAM;UACvBiI,QAAQ,EAAGzF,CAAC,IAAKzC,YAAY,CAAC4C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE3C,KAAK,EAAEwC,CAAC,CAACE,MAAM,CAAClB;UAAM,CAAC,CAAC,CAAE;UAC5EiG,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFnJ,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXa,KAAK,EAAE1B,SAAS,CAACM,GAAI;UACrB6H,QAAQ,EAAGzF,CAAC,IAAKzC,YAAY,CAAC4C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEvC,GAAG,EAAEoC,CAAC,CAACE,MAAM,CAAClB;UAAM,CAAC,CAAC,CAAE;UAC1EiG,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnJ,OAAA;QAAK8I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/I,OAAA;UACEwJ,OAAO,EAAEA,CAAA,KAAM;YACbzH,aAAa,CAACiC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEhC,IAAI,EAAE;YAAU,CAAC,CAAC,CAAC;YACrDpB,cAAc,CAAC,IAAI,CAAC;UACtB,CAAE;UACFkI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnJ,OAAA;UACEwJ,OAAO,EAAEA,CAAA,KAAM;YACbzH,aAAa,CAACiC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEhC,IAAI,EAAE;YAAU,CAAC,CAAC,CAAC;YACrDpB,cAAc,CAAC,IAAI,CAAC;UACtB,CAAE;UACFkI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxI,WAAW,iBACVX,OAAA;MAAK8I,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B/I,OAAA;QAAK8I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/I,OAAA;UAAK8I,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/I,OAAA;YAAA+I,QAAA,EACGlI,cAAc,GACb,YAAYiB,UAAU,CAACE,IAAI,KAAK,SAAS,GAAG,WAAW,GAAG,WAAW,EAAE,GACvE,WAAWF,UAAU,CAACE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;UAAO;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvE,CAAC,eACLnJ,OAAA;YAAQwJ,OAAO,EAAE/E,SAAU;YAACqE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAENnJ,OAAA;UAAMyJ,QAAQ,EAAExF,YAAa;UAAC6E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACpD/I,OAAA;YAAK8I,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/I,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBnJ,OAAA;gBACE8D,IAAI,EAAC,MAAM;gBACXjB,KAAK,EAAEf,UAAU,CAACE,IAAK;gBACvBsH,QAAQ,EAAE1F,iBAAkB;gBAC5B8F,QAAQ;gBAAAX,QAAA,EAEPnG,YAAY,CAAC2G,GAAG,CAACvH,IAAI,iBACpBhC,OAAA;kBAAyB6C,KAAK,EAAEb,IAAI,CAACa,KAAM;kBAAAkG,QAAA,EACxC/G,IAAI,CAACc;gBAAK,GADAd,IAAI,CAACa,KAAK;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBnJ,OAAA;gBACEgC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,aAAa;gBAClBjB,KAAK,EAAEf,UAAU,CAACG,WAAY;gBAC9BqH,QAAQ,EAAE1F,iBAAkB;gBAC5B8F,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBnJ,OAAA;gBACEgC,IAAI,EAAC,QAAQ;gBACb8B,IAAI,EAAC,QAAQ;gBACbjB,KAAK,EAAEf,UAAU,CAACI,MAAO;gBACzBoH,QAAQ,EAAE1F,iBAAkB;gBAC5B+F,IAAI,EAAC,MAAM;gBACXC,GAAG,EAAC,GAAG;gBACPF,QAAQ;gBACRL,WAAW,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BnJ,OAAA;gBACE8D,IAAI,EAAC,eAAe;gBACpBjB,KAAK,EAAEf,UAAU,CAACW,aAAc;gBAChC6G,QAAQ,EAAE1F,iBAAkB;gBAC5B8F,QAAQ;gBAAAX,QAAA,EAEP/F,cAAc,CAACuG,GAAG,CAAC1B,MAAM,iBACxB7H,OAAA;kBAA2B6C,KAAK,EAAEgF,MAAM,CAAChF,KAAM;kBAAAkG,QAAA,EAC5ClB,MAAM,CAAC/E;gBAAK,GADF+E,MAAM,CAAChF,KAAK;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BnJ,OAAA;gBACE8D,IAAI,EAAC,WAAW;gBAChBjB,KAAK,EAAEf,UAAU,CAACM,SAAU;gBAC5BkH,QAAQ,EAAE1F,iBAAkB;gBAC5B8F,QAAQ;gBAAAX,QAAA,gBAER/I,OAAA;kBAAQ6C,KAAK,EAAC,EAAE;kBAAAkG,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACpC9I,QAAQ,CAAC2H,MAAM,CAACzC,GAAG,IAClBA,GAAG,CAACvD,IAAI,KAAK,OAAO,KACnBuD,GAAG,CAACzB,IAAI,CAACqE,QAAQ,CAAC,OAAO,CAAC,IAAI5C,GAAG,CAACzB,IAAI,CAACqE,QAAQ,CAAC,KAAK,CAAC,IAAI5C,GAAG,CAACzB,IAAI,CAACqE,QAAQ,CAAC,OAAO,CAAC,CACvF,CAAC,CAACoB,GAAG,CAAClE,OAAO,iBACXrF,OAAA;kBAAyB6C,KAAK,EAAEwC,OAAO,CAACd,EAAG;kBAAAwE,QAAA,GACxC1D,OAAO,CAACiC,IAAI,EAAC,KAAG,EAACjC,OAAO,CAACvB,IAAI;gBAAA,GADnBuB,OAAO,CAACd,EAAE;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELrH,UAAU,CAACE,IAAI,KAAK,SAAS,iBAC5BhC,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BnJ,OAAA;gBACE8D,IAAI,EAAC,YAAY;gBACjBjB,KAAK,EAAEf,UAAU,CAACO,UAAW;gBAC7BiH,QAAQ,EAAE1F,iBAAkB;gBAAAmF,QAAA,gBAE5B/I,OAAA;kBAAQ6C,KAAK,EAAC,EAAE;kBAAAkG,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACpC5I,SAAS,CAACgJ,GAAG,CAAC/D,QAAQ,iBACrBxF,OAAA;kBAA0B6C,KAAK,EAAE2C,QAAQ,CAACjB,EAAG;kBAAAwE,QAAA,EAC1CvD,QAAQ,CAAC1B;gBAAI,GADH0B,QAAQ,CAACjB,EAAE;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEArH,UAAU,CAACE,IAAI,KAAK,SAAS,iBAC5BhC,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BnJ,OAAA;gBACE8D,IAAI,EAAC,YAAY;gBACjBjB,KAAK,EAAEf,UAAU,CAACQ,UAAW;gBAC7BgH,QAAQ,EAAE1F,iBAAkB;gBAAAmF,QAAA,gBAE5B/I,OAAA;kBAAQ6C,KAAK,EAAC,EAAE;kBAAAkG,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACpC1I,SAAS,CAAC8I,GAAG,CAAC7D,QAAQ,iBACrB1F,OAAA;kBAA0B6C,KAAK,EAAE6C,QAAQ,CAACnB,EAAG;kBAAAwE,QAAA,EAC1CrD,QAAQ,CAAC5B;gBAAI,GADH4B,QAAQ,CAACnB,EAAE;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAEDnJ,OAAA;cAAK8I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/I,OAAA;gBAAA+I,QAAA,EAAO;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBnJ,OAAA;gBACEgC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,WAAW;gBAChBjB,KAAK,EAAEf,UAAU,CAACY,SAAU;gBAC5B4G,QAAQ,EAAE1F,iBAAkB;gBAC5ByF,WAAW,EAAC;cAA6B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC/I,OAAA;gBAAA+I,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBnJ,OAAA;gBACEgC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,aAAa;gBAClBjB,KAAK,EAAEf,UAAU,CAACK,WAAY;gBAC9BmH,QAAQ,EAAE1F,iBAAkB;gBAC5B8F,QAAQ;gBACRL,WAAW,EAAC;cAAmB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnJ,OAAA;cAAK8I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC/I,OAAA;gBAAA+I,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBnJ,OAAA;gBACE8D,IAAI,EAAC,OAAO;gBACZjB,KAAK,EAAEf,UAAU,CAACa,KAAM;gBACxB2G,QAAQ,EAAE1F,iBAAkB;gBAC5BiG,IAAI,EAAC,GAAG;gBACRR,WAAW,EAAC;cAAmB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnJ,OAAA;YAAK8I,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/I,OAAA;cAAQgC,IAAI,EAAC,QAAQ;cAACwH,OAAO,EAAE/E,SAAU;cAACqE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnJ,OAAA;cAAQgC,IAAI,EAAC,QAAQ;cAAC8H,QAAQ,EAAEpI,OAAQ;cAACoH,SAAS,EAAC,UAAU;cAAAC,QAAA,EAC1DrH,OAAO,GAAG,iBAAiB,GAAIb,cAAc,GAAG,UAAU,GAAG;YAAS;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnJ,OAAA;MAAK8I,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC/I,OAAA;QAAO8I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC/B/I,OAAA;UAAA+I,QAAA,eACE/I,OAAA;YAAA+I,QAAA,gBACE/I,OAAA;cAAA+I,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBnJ,OAAA;cAAA+I,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdnJ,OAAA;cAAA+I,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBnJ,OAAA;cAAA+I,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnJ,OAAA;cAAA+I,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnJ,OAAA;cAAA+I,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnJ,OAAA;cAAA+I,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBnJ,OAAA;cAAA+I,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRnJ,OAAA;UAAA+I,QAAA,EACGhB,gBAAgB,CAACqB,MAAM,KAAK,CAAC,gBAC5BpJ,OAAA;YAAA+I,QAAA,eACE/I,OAAA;cAAI+J,OAAO,EAAC,GAAG;cAACjB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELpB,gBAAgB,CAACwB,GAAG,CAAC3E,OAAO,IAAI;YAC9B,MAAMoF,QAAQ,GAAGvC,WAAW,CAAC7C,OAAO,CAAC5C,IAAI,CAAC;YAC1C,oBACEhC,OAAA;cAAA+I,QAAA,gBACE/I,OAAA;gBAAI8I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEnE,OAAO,CAACmB;cAAa;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DnJ,OAAA;gBAAA+I,QAAA,eACE/I,OAAA;kBACE8I,SAAS,EAAC,YAAY;kBACtBmB,KAAK,EAAE;oBAAEC,eAAe,EAAEF,QAAQ,CAACjH;kBAAM,CAAE;kBAAAgG,QAAA,EAE1CiB,QAAQ,CAAClH;gBAAK;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnJ,OAAA;gBAAA+I,QAAA,EAAK,IAAIzH,IAAI,CAACsD,OAAO,CAAC3C,WAAW,CAAC,CAACoE,kBAAkB,CAAC,OAAO;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpEnJ,OAAA;gBAAA+I,QAAA,EAAKnE,OAAO,CAACzC;cAAW;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BnJ,OAAA;gBAAA+I,QAAA,EAAK1B,cAAc,CAACzC,OAAO,CAACxC,SAAS;cAAC;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CnJ,OAAA;gBAAI8I,SAAS,EAAE,UAAUlE,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,UAAU,EAAG;gBAAA+G,QAAA,GAC7EnE,OAAO,CAAC5C,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG,EAAE4C,OAAO,CAAC1C,MAAM,CAACqE,cAAc,CAAC,CAAC,EAAC,2BAC3E;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnJ,OAAA;gBAAA+I,QAAA,EAAKnB,qBAAqB,CAAChD,OAAO,CAACnC,aAAa;cAAC;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDnJ,OAAA;gBAAI8I,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACrB/I,OAAA;kBACEwJ,OAAO,EAAEA,CAAA,KAAM7E,UAAU,CAACC,OAAO,CAAE;kBACnCkE,SAAS,EAAC,UAAU;kBACpBqB,KAAK,EAAC,gCAAO;kBAAApB,QAAA,EACd;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnJ,OAAA;kBACEwJ,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAACL,OAAO,CAAE;kBACrCkE,SAAS,EAAC,WAAW;kBACrBqB,KAAK,EAAC,gCAAO;kBAAApB,QAAA,EACd;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnJ,OAAA;kBACEwJ,OAAO,EAAEA,CAAA,KAAM3E,YAAY,CAACD,OAAO,CAACL,EAAE,CAAE;kBACxCuE,SAAS,EAAC,YAAY;kBACtBqB,KAAK,EAAC,oBAAK;kBAAApB,QAAA,EACZ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAvCEvE,OAAO,CAACL,EAAE;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwCf,CAAC;UAET,CAAC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjJ,EAAA,CAruBID,QAAQ;AAAAmK,EAAA,GAARnK,QAAQ;AAuuBd,eAAeA,QAAQ;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}