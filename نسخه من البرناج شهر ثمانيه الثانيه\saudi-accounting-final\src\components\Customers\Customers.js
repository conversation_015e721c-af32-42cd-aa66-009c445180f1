import React, { useState, useEffect } from 'react';
import './Customers.css';
import database from '../../utils/database';

const Customers = ({ user, onBack }) => {
  const [customers, setCustomers] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [isLoading, setIsLoading] = useState(false);

  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    taxNumber: '',
    type: 'individual',
    creditLimit: '0',
    paymentTerms: '30',
    discount: '0',
    notes: '',
    contactPerson: '',
    website: ''
  });

  const customerTypes = [
    { value: 'individual', label: 'فرد' },
    { value: 'company', label: 'شركة' },
    { value: 'government', label: 'جهة حكومية' },
    { value: 'nonprofit', label: 'جمعية خيرية' }
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = () => {
    try {
      const dbCustomers = database.getCustomers();
      setCustomers(dbCustomers);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  const handleAddCustomer = async () => {
    if (!newCustomer.name || !newCustomer.phone) {
      alert('يرجى ملء الحقول المطلوبة (الاسم والهاتف)');
      return;
    }

    setIsLoading(true);
    try {
      const customerData = {
        ...newCustomer,
        creditLimit: parseFloat(newCustomer.creditLimit) || 0,
        paymentTerms: parseInt(newCustomer.paymentTerms) || 30,
        discount: parseFloat(newCustomer.discount) || 0
      };

      const savedCustomer = database.addCustomer(customerData);
      setCustomers([...customers, savedCustomer]);
      
      setNewCustomer({
        name: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        taxNumber: '',
        type: 'individual',
        creditLimit: '0',
        paymentTerms: '30',
        discount: '0',
        notes: '',
        contactPerson: '',
        website: ''
      });
      
      setShowAddForm(false);
      alert('تم إضافة العميل بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      alert('فشل في إضافة العميل');
    }
    setIsLoading(false);
  };

  const handleEditCustomer = async () => {
    if (!editingCustomer.name || !editingCustomer.phone) {
      alert('يرجى ملء الحقول المطلوبة (الاسم والهاتف)');
      return;
    }

    setIsLoading(true);
    try {
      const customerData = {
        ...editingCustomer,
        creditLimit: parseFloat(editingCustomer.creditLimit) || 0,
        paymentTerms: parseInt(editingCustomer.paymentTerms) || 30,
        discount: parseFloat(editingCustomer.discount) || 0
      };

      const updatedCustomer = database.updateCustomer(editingCustomer.id, customerData);
      setCustomers(customers.map(c => c.id === editingCustomer.id ? updatedCustomer : c));
      
      setEditingCustomer(null);
      alert('تم تحديث العميل بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      alert('فشل في تحديث العميل');
    }
    setIsLoading(false);
  };

  const handleDeleteCustomer = (customerId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        database.deleteCustomer(customerId);
        setCustomers(customers.filter(c => c.id !== customerId));
        alert('تم حذف العميل بنجاح');
      } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        alert('فشل في حذف العميل');
      }
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !selectedType || customer.type === selectedType;
    return matchesSearch && matchesType;
  });

  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'type':
        return a.type.localeCompare(b.type);
      case 'creditLimit':
        return b.creditLimit - a.creditLimit;
      case 'createdAt':
        return new Date(b.createdAt) - new Date(a.createdAt);
      default:
        return 0;
    }
  });

  const getTotalCustomers = () => customers.length;
  const getCompanyCustomers = () => customers.filter(c => c.type === 'company').length;
  const getTotalCreditLimit = () => customers.reduce((total, c) => total + c.creditLimit, 0);
  const getAverageDiscount = () => {
    const totalDiscount = customers.reduce((total, c) => total + c.discount, 0);
    return customers.length > 0 ? (totalDiscount / customers.length).toFixed(2) : 0;
  };

  const getCustomerTypeLabel = (type) => {
    const typeObj = customerTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  return (
    <div className="customers-container">
      <div className="customers-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ← العودة للوحة التحكم
          </button>
          <h1>👥 إدارة العملاء</h1>
          <button 
            onClick={() => setShowAddForm(true)} 
            className="add-btn"
            disabled={isLoading}
          >
            ➕ إضافة عميل جديد
          </button>
        </div>
      </div>

      <div className="customers-stats">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-info">
            <h3>إجمالي العملاء</h3>
            <p>{getTotalCustomers()}</p>
          </div>
        </div>
        <div className="stat-card info">
          <div className="stat-icon">🏢</div>
          <div className="stat-info">
            <h3>عملاء الشركات</h3>
            <p>{getCompanyCustomers()}</p>
          </div>
        </div>
        <div className="stat-card success">
          <div className="stat-icon">💳</div>
          <div className="stat-info">
            <h3>إجمالي حدود الائتمان</h3>
            <p>{getTotalCreditLimit().toFixed(2)} ريال</p>
          </div>
        </div>
        <div className="stat-card warning">
          <div className="stat-icon">🎯</div>
          <div className="stat-info">
            <h3>متوسط الخصم</h3>
            <p>{getAverageDiscount()}%</p>
          </div>
        </div>
      </div>

      <div className="customers-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث بالاسم أو الهاتف أو الإيميل..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="filter-select"
        >
          <option value="">جميع الأنواع</option>
          {customerTypes.map(type => (
            <option key={type.value} value={type.value}>{type.label}</option>
          ))}
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="sort-select"
        >
          <option value="name">ترتيب بالاسم</option>
          <option value="type">ترتيب بالنوع</option>
          <option value="creditLimit">ترتيب بحد الائتمان</option>
          <option value="createdAt">ترتيب بتاريخ الإضافة</option>
        </select>
      </div>

      <div className="customers-table-container">
        <table className="customers-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>النوع</th>
              <th>الهاتف</th>
              <th>الإيميل</th>
              <th>المدينة</th>
              <th>حد الائتمان</th>
              <th>الخصم</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {sortedCustomers.map(customer => (
              <tr key={customer.id}>
                <td>
                  <div className="customer-name">
                    <strong>{customer.name}</strong>
                    {customer.contactPerson && <small>جهة الاتصال: {customer.contactPerson}</small>}
                  </div>
                </td>
                <td>
                  <span className={`customer-type ${customer.type}`}>
                    {getCustomerTypeLabel(customer.type)}
                  </span>
                </td>
                <td>{customer.phone}</td>
                <td>{customer.email || '-'}</td>
                <td>{customer.city || '-'}</td>
                <td>{customer.creditLimit.toFixed(2)} ريال</td>
                <td>{customer.discount}%</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      onClick={() => setEditingCustomer(customer)}
                      className="edit-btn"
                      disabled={isLoading}
                      title="تعديل"
                    >
                      ✏️
                    </button>
                    <button 
                      onClick={() => handleDeleteCustomer(customer.id)}
                      className="delete-btn"
                      disabled={isLoading}
                      title="حذف"
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {sortedCustomers.length === 0 && (
          <div className="no-customers">
            <p>لا توجد عملاء تطابق البحث</p>
          </div>
        )}
      </div>

      {showAddForm && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>إضافة عميل جديد</h2>
              <button onClick={() => setShowAddForm(false)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم العميل *</label>
                  <input
                    type="text"
                    value={newCustomer.name}
                    onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                    placeholder="أدخل اسم العميل"
                  />
                </div>

                <div className="form-group">
                  <label>نوع العميل</label>
                  <select
                    value={newCustomer.type}
                    onChange={(e) => setNewCustomer({...newCustomer, type: e.target.value})}
                  >
                    {customerTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف *</label>
                  <input
                    type="tel"
                    value={newCustomer.phone}
                    onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                    placeholder="05xxxxxxxx"
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={newCustomer.email}
                    onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="form-group">
                  <label>المدينة</label>
                  <input
                    type="text"
                    value={newCustomer.city}
                    onChange={(e) => setNewCustomer({...newCustomer, city: e.target.value})}
                    placeholder="الرياض"
                  />
                </div>

                <div className="form-group">
                  <label>الرقم الضريبي</label>
                  <input
                    type="text"
                    value={newCustomer.taxNumber}
                    onChange={(e) => setNewCustomer({...newCustomer, taxNumber: e.target.value})}
                    placeholder="300000000000003"
                  />
                </div>

                <div className="form-group">
                  <label>حد الائتمان (ريال)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newCustomer.creditLimit}
                    onChange={(e) => setNewCustomer({...newCustomer, creditLimit: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>مدة السداد (يوم)</label>
                  <input
                    type="number"
                    value={newCustomer.paymentTerms}
                    onChange={(e) => setNewCustomer({...newCustomer, paymentTerms: e.target.value})}
                    placeholder="30"
                  />
                </div>

                <div className="form-group">
                  <label>نسبة الخصم (%)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newCustomer.discount}
                    onChange={(e) => setNewCustomer({...newCustomer, discount: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>جهة الاتصال</label>
                  <input
                    type="text"
                    value={newCustomer.contactPerson}
                    onChange={(e) => setNewCustomer({...newCustomer, contactPerson: e.target.value})}
                    placeholder="اسم الشخص المسؤول"
                  />
                </div>

                <div className="form-group">
                  <label>الموقع الإلكتروني</label>
                  <input
                    type="url"
                    value={newCustomer.website}
                    onChange={(e) => setNewCustomer({...newCustomer, website: e.target.value})}
                    placeholder="https://example.com"
                  />
                </div>

                <div className="form-group full-width">
                  <label>العنوان</label>
                  <textarea
                    value={newCustomer.address}
                    onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                    placeholder="العنوان التفصيلي"
                    rows="2"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={newCustomer.notes}
                    onChange={(e) => setNewCustomer({...newCustomer, notes: e.target.value})}
                    placeholder="ملاحظات إضافية"
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowAddForm(false)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleAddCustomer} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري الحفظ...' : 'حفظ العميل'}
              </button>
            </div>
          </div>
        </div>
      )}

      {editingCustomer && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>تعديل العميل</h2>
              <button onClick={() => setEditingCustomer(null)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم العميل *</label>
                  <input
                    type="text"
                    value={editingCustomer.name}
                    onChange={(e) => setEditingCustomer({...editingCustomer, name: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>نوع العميل</label>
                  <select
                    value={editingCustomer.type}
                    onChange={(e) => setEditingCustomer({...editingCustomer, type: e.target.value})}
                  >
                    {customerTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم الهاتف *</label>
                  <input
                    type="tel"
                    value={editingCustomer.phone}
                    onChange={(e) => setEditingCustomer({...editingCustomer, phone: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={editingCustomer.email || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, email: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>المدينة</label>
                  <input
                    type="text"
                    value={editingCustomer.city || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, city: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الرقم الضريبي</label>
                  <input
                    type="text"
                    value={editingCustomer.taxNumber || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, taxNumber: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>حد الائتمان (ريال)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingCustomer.creditLimit}
                    onChange={(e) => setEditingCustomer({...editingCustomer, creditLimit: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>مدة السداد (يوم)</label>
                  <input
                    type="number"
                    value={editingCustomer.paymentTerms}
                    onChange={(e) => setEditingCustomer({...editingCustomer, paymentTerms: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>نسبة الخصم (%)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingCustomer.discount}
                    onChange={(e) => setEditingCustomer({...editingCustomer, discount: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>جهة الاتصال</label>
                  <input
                    type="text"
                    value={editingCustomer.contactPerson || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, contactPerson: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الموقع الإلكتروني</label>
                  <input
                    type="url"
                    value={editingCustomer.website || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, website: e.target.value})}
                  />
                </div>

                <div className="form-group full-width">
                  <label>العنوان</label>
                  <textarea
                    value={editingCustomer.address || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, address: e.target.value})}
                    rows="2"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={editingCustomer.notes || ''}
                    onChange={(e) => setEditingCustomer({...editingCustomer, notes: e.target.value})}
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setEditingCustomer(null)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleEditCustomer} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري التحديث...' : 'تحديث العميل'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Customers;
