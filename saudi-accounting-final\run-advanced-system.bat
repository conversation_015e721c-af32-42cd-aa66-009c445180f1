@echo off
chcp 65001 >nul
echo ===============================================
echo    نظام المحاسبة السعودي المتكامل والمتقدم
echo    Saudi Advanced Integrated Accounting System
echo ===============================================
echo.
echo 🚀 الإصدار المتقدم - Advanced Version
echo.
echo ✨ الميزات الجديدة المضافة:
echo ├── 📋 القيود اليومية المتقدمة
echo ├── ↩️  إدارة المرتجعات الشاملة
echo ├── 📊 إدارة الباركود المتقدمة
echo ├── 💼 عروض الأسعار الاحترافية
echo ├── 🧾 سندات القبض والصرف
echo └── 📈 التقارير المالية المتقدمة
echo.
echo 🔧 الميزات الأساسية:
echo ├── 🏪 نقطة البيع المتقدمة
echo ├── 📦 إدارة المخزون الذكية
echo ├── 🧾 الفواتير الإلكترونية
echo ├── 👥 إدارة العملاء والموردين
echo ├── 💾 النسخ الاحتياطي الآمن
echo ├── 🔔 نظام الإشعارات الذكي
echo └── 👨‍💼 إدارة الموظفين
echo.
echo ===============================================
echo 🔍 التحقق من متطلبات النظام...
echo.

REM إضافة Node.js للـ PATH مؤقتاً
set PATH=%PATH%;C:\Program Files\nodejs

REM التحقق من وجود Node.js
echo 🔍 التحقق من Node.js...
"C:\Program Files\nodejs\node.exe" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير موجود
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM التحقق من وجود npm
echo 🔍 التحقق من npm...
"C:\Program Files\nodejs\npm.cmd" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير موجود
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 📊 معلومات النظام:
"C:\Program Files\nodejs\node.exe" --version
"C:\Program Files\nodejs\npm.cmd" --version
echo.

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة للنظام المتقدم...
    echo ⏳ هذا قد يستغرق بضع دقائق...
    "C:\Program Files\nodejs\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🔧 جرب تشغيل الأمر التالي يدوياً: npm install
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
) else (
    echo ✅ المكتبات موجودة مسبقاً
    echo.
)

echo ===============================================
echo 🌐 بدء تشغيل النظام المتقدم...
echo.
echo 📍 سيتم فتح التطبيق في المتصفح على:
echo    http://localhost:3000
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🛑 للإيقاف: اضغط Ctrl+C
echo ===============================================
echo.

REM تعيين متغيرات البيئة
set NODE_PATH=C:\Program Files\nodejs
set PATH=%NODE_PATH%;%PATH%

REM تشغيل التطبيق مع المسار الكامل
echo 🚀 تشغيل النظام...
"%NODE_PATH%\node.exe" "%NODE_PATH%\node_modules\npm\bin\npm-cli.js" start

REM في حالة فشل التشغيل
echo.
echo ❌ توقف النظام
echo 🔧 للمساعدة، راجع ملف README.md
pause
