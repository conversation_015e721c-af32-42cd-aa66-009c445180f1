"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(exports,"__esModule",{value:!0});var t=e(require("leven")),r=e(require("jsonpointer"));function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}var n={enum:1,type:0},o=/"/g,s=/NOT/g,u=/\//g,l=function(e){var r=e.value,a=e.format,n=void 0===a?function(e){return"Did you mean '"+e+"'?"}:a;if(!r)return"";var o=e.suggestions.reduce((function(e,a){var n=t(r,a);return e.distance>n?{value:a,distance:n}:e}),{distance:Infinity,value:""});return o.distance<r.length?n(o.value):""},i=function(e){return e.split("/").pop()},p=function(e){var t=e.object,a=e.pnter,n=e.fallback;try{return r.get(t,a)}catch(e){return n}};exports.betterAjvErrors=function(e){var t=e.errors,r=e.data,c=e.schema,d=e.basePath,v=void 0===d?"{base}":d;return Array.isArray(t)&&0!==t.length?function(e){var t=e.reduce((function(e,t){var r,a,o,s,u,l,i=t.instancePath+(null!=(r=null!=(a=null==(o=t.params)?void 0:o.additionalProperty)?a:null==(s=t.params)?void 0:s.missingProperty)?r:""),p=e[i];return p?((null!=(u=n[t.keyword])?u:0)>(null!=(l=n[p.keyword])?l:0)&&(e[i]=t),e):(e[i]=t,e)}),{});return Object.values(t)}(t).map((function(e){var t,n=function(e){return e.replace(u,".")}(v+e.instancePath),d=i(e.instancePath),y={errorType:e.keyword},f=(d?"property '"+d+"'":n)+" "+e.message.replace(o,"'").replace(s,"not");switch(e.keyword){case"additionalProperties":var m=e.params.additionalProperty,g=e.schemaPath.replace("#","").replace("/additionalProperties",""),h=p({object:c,pnter:g,fallback:{properties:{}}}).properties;t={message:"'"+m+"' property is not expected to be here",suggestion:l({value:m,suggestions:Object.keys(null!=h?h:{}),format:function(e){return"Did you mean property '"+e+"'?"}}),path:n,context:y};break;case"enum":var b=e.params.allowedValues.map((function(e){return e.toString()})),P=i(e.instancePath),w=p({object:r,pnter:e.instancePath,fallback:""});t={message:"'"+P+"' property must be equal to one of the allowed values",suggestion:l({value:w,suggestions:b}),path:n,context:a({},y,{allowedValues:e.params.allowedValues})};break;case"type":t={message:"'"+i(e.instancePath)+"' property type must be "+e.params.type,path:n,context:y};break;case"required":t={message:n+" must have required property '"+e.params.missingProperty+"'",path:n,context:y};break;case"const":return{message:"'"+d+"' property must be equal to the allowed value",path:n,context:a({},y,{allowedValue:e.params.allowedValue})};default:return{message:f,path:n,context:y}}for(var j=0,k=Object.entries(t);j<k.length;j++){var x=k[j],O=x[1];null!=O&&""!==O||delete t[x[0]]}return t})):[]};
//# sourceMappingURL=better-ajv-errors.cjs.production.min.js.map
