{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\JournalEntries\\\\JournalEntries.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './JournalEntries.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JournalEntries = () => {\n  _s();\n  const [entries, setEntries] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingEntry, setEditingEntry] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [newEntry, setNewEntry] = useState({\n    date: new Date().toISOString().split('T')[0],\n    reference: '',\n    description: '',\n    type: 'manual',\n    status: 'pending',\n    debitEntries: [{\n      accountId: '',\n      amount: '',\n      description: ''\n    }],\n    creditEntries: [{\n      accountId: '',\n      amount: '',\n      description: ''\n    }],\n    attachments: [],\n    notes: ''\n  });\n  const entryTypes = [{\n    value: 'manual',\n    label: '📝 قيد يدوي',\n    color: '#667eea'\n  }, {\n    value: 'sales',\n    label: '💰 مبيعات',\n    color: '#10b981'\n  }, {\n    value: 'purchase',\n    label: '🛒 مشتريات',\n    color: '#f59e0b'\n  }, {\n    value: 'payment',\n    label: '💳 دفعة',\n    color: '#6366f1'\n  }, {\n    value: 'receipt',\n    label: '🧾 إيصال',\n    color: '#8b5cf6'\n  }, {\n    value: 'adjustment',\n    label: '⚖️ تسوية',\n    color: '#ef4444'\n  }, {\n    value: 'depreciation',\n    label: '📉 إهلاك',\n    color: '#64748b'\n  }, {\n    value: 'accrual',\n    label: '📊 استحقاق',\n    color: '#059669'\n  }];\n  const entryStatuses = [{\n    value: 'pending',\n    label: '⏳ معلق',\n    color: '#f59e0b'\n  }, {\n    value: 'approved',\n    label: '✅ معتمد',\n    color: '#10b981'\n  }, {\n    value: 'posted',\n    label: '📋 مرحل',\n    color: '#6366f1'\n  }, {\n    value: 'cancelled',\n    label: '❌ ملغي',\n    color: '#ef4444'\n  }];\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = () => {\n    try {\n      const entriesData = database.getJournalEntries();\n      const accountsData = database.getAccounts();\n      setEntries(entriesData);\n      setAccounts(accountsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewEntry(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleDebitChange = (index, field, value) => {\n    const updatedDebits = [...newEntry.debitEntries];\n    updatedDebits[index] = {\n      ...updatedDebits[index],\n      [field]: value\n    };\n    setNewEntry(prev => ({\n      ...prev,\n      debitEntries: updatedDebits\n    }));\n  };\n  const handleCreditChange = (index, field, value) => {\n    const updatedCredits = [...newEntry.creditEntries];\n    updatedCredits[index] = {\n      ...updatedCredits[index],\n      [field]: value\n    };\n    setNewEntry(prev => ({\n      ...prev,\n      creditEntries: updatedCredits\n    }));\n  };\n  const addDebitEntry = () => {\n    setNewEntry(prev => ({\n      ...prev,\n      debitEntries: [...prev.debitEntries, {\n        accountId: '',\n        amount: '',\n        description: ''\n      }]\n    }));\n  };\n  const addCreditEntry = () => {\n    setNewEntry(prev => ({\n      ...prev,\n      creditEntries: [...prev.creditEntries, {\n        accountId: '',\n        amount: '',\n        description: ''\n      }]\n    }));\n  };\n  const removeDebitEntry = index => {\n    if (newEntry.debitEntries.length > 1) {\n      const updatedDebits = newEntry.debitEntries.filter((_, i) => i !== index);\n      setNewEntry(prev => ({\n        ...prev,\n        debitEntries: updatedDebits\n      }));\n    }\n  };\n  const removeCreditEntry = index => {\n    if (newEntry.creditEntries.length > 1) {\n      const updatedCredits = newEntry.creditEntries.filter((_, i) => i !== index);\n      setNewEntry(prev => ({\n        ...prev,\n        creditEntries: updatedCredits\n      }));\n    }\n  };\n  const calculateTotals = () => {\n    const totalDebit = newEntry.debitEntries.reduce((sum, entry) => sum + (parseFloat(entry.amount) || 0), 0);\n    const totalCredit = newEntry.creditEntries.reduce((sum, entry) => sum + (parseFloat(entry.amount) || 0), 0);\n    return {\n      totalDebit,\n      totalCredit,\n      difference: totalDebit - totalCredit\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      const {\n        totalDebit,\n        totalCredit\n      } = calculateTotals();\n      if (Math.abs(totalDebit - totalCredit) > 0.01) {\n        setMessage('خطأ: إجمالي المدين يجب أن يساوي إجمالي الدائن');\n        setLoading(false);\n        return;\n      }\n      const entryData = {\n        ...newEntry,\n        debitEntries: newEntry.debitEntries.filter(entry => entry.accountId && entry.amount),\n        creditEntries: newEntry.creditEntries.filter(entry => entry.accountId && entry.amount),\n        totalAmount: totalDebit\n      };\n      if (editingEntry) {\n        await database.updateJournalEntry(editingEntry.id, entryData);\n        setMessage('تم تحديث القيد بنجاح!');\n        setEditingEntry(null);\n      } else {\n        await database.addJournalEntry(entryData);\n        setMessage('تم إضافة القيد بنجاح!');\n      }\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ القيد:', error);\n      setMessage('خطأ في حفظ القيد');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetForm = () => {\n    setNewEntry({\n      date: new Date().toISOString().split('T')[0],\n      reference: '',\n      description: '',\n      type: 'manual',\n      status: 'pending',\n      debitEntries: [{\n        accountId: '',\n        amount: '',\n        description: ''\n      }],\n      creditEntries: [{\n        accountId: '',\n        amount: '',\n        description: ''\n      }],\n      attachments: [],\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingEntry(null);\n  };\n  const handleEdit = entry => {\n    setNewEntry({\n      ...entry,\n      date: entry.date.split('T')[0]\n    });\n    setEditingEntry(entry);\n    setShowAddForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا القيد؟')) {\n      try {\n        await database.deleteJournalEntry(id);\n        setMessage('تم حذف القيد بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف القيد:', error);\n        setMessage('خطأ في حذف القيد');\n      }\n    }\n  };\n  const handlePost = async id => {\n    if (window.confirm('هل أنت متأكد من ترحيل هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.')) {\n      try {\n        await database.postJournalEntry(id);\n        setMessage('تم ترحيل القيد بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في ترحيل القيد:', error);\n        setMessage('خطأ في ترحيل القيد');\n      }\n    }\n  };\n  const getAccountName = accountId => {\n    const account = accounts.find(acc => acc.id === parseInt(accountId));\n    return account ? `${account.code} - ${account.name}` : 'غير محدد';\n  };\n  const getTypeInfo = type => {\n    const typeObj = entryTypes.find(t => t.value === type);\n    return typeObj || {\n      label: type,\n      color: '#64748b'\n    };\n  };\n  const getStatusInfo = status => {\n    const statusObj = entryStatuses.find(s => s.value === status);\n    return statusObj || {\n      label: status,\n      color: '#64748b'\n    };\n  };\n  const filteredEntries = entries.filter(entry => {\n    const matchesSearch = entry.reference.toLowerCase().includes(searchTerm.toLowerCase()) || entry.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || entry.type === filterType;\n    const entryDate = new Date(entry.date);\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const matchesDate = entryDate >= startDate && entryDate <= endDate;\n    return matchesSearch && matchesType && matchesDate;\n  });\n  const totals = calculateTotals();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"journal-entries-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"journal-entries-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F \\u0627\\u0644\\u064A\\u0648\\u0645\\u064A\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('خطأ') ? 'error' : 'success'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card total\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [entries.length, \" \\u0642\\u064A\\u062F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card pending\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0642\\u064A\\u0648\\u062F \\u0645\\u0639\\u0644\\u0642\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [entries.filter(e => e.status === 'pending').length, \" \\u0642\\u064A\\u062F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card posted\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0642\\u064A\\u0648\\u062F \\u0645\\u0631\\u062D\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [entries.filter(e => e.status === 'posted').length, \" \\u0642\\u064A\\u062F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card amount\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0627\\u0644\\u063A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [entries.reduce((sum, e) => sum + (e.totalAmount || 0), 0).toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"journal-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-filters\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0642\\u064A\\u0648\\u062F...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterType,\n          onChange: e => setFilterType(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), entryTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type.value,\n            children: type.label\n          }, type.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.start,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            start: e.target.value\n          })),\n          className: \"date-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.end,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            end: e.target.value\n          })),\n          className: \"date-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddForm(true),\n        className: \"add-entry-btn\",\n        children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u064A\\u062F \\u062C\\u062F\\u064A\\u062F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"entry-form-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: editingEntry ? '✏️ تعديل القيد' : '➕ إضافة قيد جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetForm,\n            className: \"close-btn\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"entry-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"date\",\n                  value: newEntry.date,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"reference\",\n                  value: newEntry.reference,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0642\\u064A\\u062F \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0642\\u064A\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: newEntry.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: entryTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type.value,\n                    children: type.label\n                  }, type.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"status\",\n                  value: newEntry.status,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: entryStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: status.value,\n                    children: status.label\n                  }, status.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0642\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"description\",\n                value: newEntry.description,\n                onChange: handleInputChange,\n                placeholder: \"\\u0648\\u0635\\u0641 \\u0645\\u062E\\u062A\\u0635\\u0631 \\u0644\\u0644\\u0642\\u064A\\u062F\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"entries-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"debit-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDD34 \\u0627\\u0644\\u062C\\u0627\\u0646\\u0628 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: addDebitEntry,\n                  className: \"add-line-btn\",\n                  children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0633\\u0637\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), newEntry.debitEntries.map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"entry-line\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: entry.accountId,\n                  onChange: e => handleDebitChange(index, 'accountId', e.target.value),\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 25\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: account.id,\n                    children: [account.code, \" - \", account.name]\n                  }, account.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\",\n                  value: entry.amount,\n                  onChange: e => handleDebitChange(index, 'amount', e.target.value),\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\",\n                  value: entry.description,\n                  onChange: e => handleDebitChange(index, 'description', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), newEntry.debitEntries.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeDebitEntry(index),\n                  className: \"remove-line-btn\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-total\",\n                children: [\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646: \", totals.totalDebit.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"credit-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDD35 \\u0627\\u0644\\u062C\\u0627\\u0646\\u0628 \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: addCreditEntry,\n                  className: \"add-line-btn\",\n                  children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0633\\u0637\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), newEntry.creditEntries.map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"entry-line\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: entry.accountId,\n                  onChange: e => handleCreditChange(index, 'accountId', e.target.value),\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 25\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: account.id,\n                    children: [account.code, \" - \", account.name]\n                  }, account.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\",\n                  value: entry.amount,\n                  onChange: e => handleCreditChange(index, 'amount', e.target.value),\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\",\n                  value: entry.description,\n                  onChange: e => handleCreditChange(index, 'description', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 23\n                }, this), newEntry.creditEntries.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeCreditEntry(index),\n                  className: \"remove-line-btn\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"section-total\",\n                children: [\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646: \", totals.totalCredit.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"balance-check\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `balance-indicator ${Math.abs(totals.difference) < 0.01 ? 'balanced' : 'unbalanced'}`,\n              children: Math.abs(totals.difference) < 0.01 ? '✅ القيد متوازن' : `⚠️ فرق التوازن: ${totals.difference.toLocaleString()} ريال`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"notes\",\n              value: newEntry.notes,\n              onChange: handleInputChange,\n              rows: \"3\",\n              placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: resetForm,\n              className: \"cancel-btn\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading || Math.abs(totals.difference) >= 0.01,\n              className: \"save-btn\",\n              children: loading ? '⏳ جاري الحفظ...' : editingEntry ? '💾 تحديث' : '💾 حفظ'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"entries-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"entries-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0646\\u0648\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEntries.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"7\",\n              className: \"no-data\",\n              children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0642\\u064A\\u0648\\u062F \\u0645\\u0637\\u0627\\u0628\\u0642\\u0629 \\u0644\\u0644\\u0628\\u062D\\u062B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this) : filteredEntries.map(entry => {\n            const typeInfo = getTypeInfo(entry.type);\n            const statusInfo = getStatusInfo(entry.status);\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(entry.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"reference\",\n                children: entry.reference\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: entry.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-badge\",\n                  style: {\n                    backgroundColor: typeInfo.color\n                  },\n                  children: typeInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"amount\",\n                children: [(entry.totalAmount || 0).toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: statusInfo.color\n                  },\n                  children: statusInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(entry),\n                  className: \"edit-btn\",\n                  title: \"\\u062A\\u0639\\u062F\\u064A\\u0644\",\n                  disabled: entry.status === 'posted',\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 23\n                }, this), entry.status === 'approved' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePost(entry.id),\n                  className: \"post-btn\",\n                  title: \"\\u062A\\u0631\\u062D\\u064A\\u0644\",\n                  children: \"\\uD83D\\uDCCB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(entry.id),\n                  className: \"delete-btn\",\n                  title: \"\\u062D\\u0630\\u0641\",\n                  disabled: entry.status === 'posted',\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)]\n            }, entry.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n};\n_s(JournalEntries, \"ewJQ2Ay2jys14fEmbF3KwToc5Go=\");\n_c = JournalEntries;\nexport default JournalEntries;\nvar _c;\n$RefreshReg$(_c, \"JournalEntries\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "JournalEntries", "_s", "entries", "setEntries", "accounts", "setAccounts", "showAddForm", "setShowAddForm", "editingEntry", "setEditingEntry", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "toISOString", "split", "end", "loading", "setLoading", "message", "setMessage", "newEntry", "setNewEntry", "date", "reference", "description", "type", "status", "debitEntries", "accountId", "amount", "creditEntries", "attachments", "notes", "entryTypes", "value", "label", "color", "entryStatuses", "loadData", "entriesData", "getJournalEntries", "accountsData", "getAccounts", "error", "console", "handleInputChange", "e", "name", "target", "prev", "handleDebitChange", "index", "field", "updatedDebits", "handleCreditChange", "updatedCredits", "addDebitEntry", "addCreditEntry", "removeDebitEntry", "length", "filter", "_", "i", "removeCreditEntry", "calculateTotals", "totalDebit", "reduce", "sum", "entry", "parseFloat", "totalCredit", "difference", "handleSubmit", "preventDefault", "Math", "abs", "entryData", "totalAmount", "updateJournalEntry", "id", "addJournalEntry", "resetForm", "setTimeout", "handleEdit", "handleDelete", "window", "confirm", "deleteJournalEntry", "handlePost", "postJournalEntry", "getAccountName", "account", "find", "acc", "parseInt", "code", "getTypeInfo", "typeObj", "t", "getStatusInfo", "statusObj", "s", "filteredEntries", "matchesSearch", "toLowerCase", "includes", "matchesType", "entryDate", "startDate", "endDate", "matchesDate", "totals", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "placeholder", "onChange", "map", "onClick", "onSubmit", "required", "step", "min", "rows", "disabled", "colSpan", "typeInfo", "statusInfo", "toLocaleDateString", "style", "backgroundColor", "title", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/JournalEntries/JournalEntries.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './JournalEntries.css';\n\nconst JournalEntries = () => {\n  const [entries, setEntries] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingEntry, setEditingEntry] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const [newEntry, setNewEntry] = useState({\n    date: new Date().toISOString().split('T')[0],\n    reference: '',\n    description: '',\n    type: 'manual',\n    status: 'pending',\n    debitEntries: [{ accountId: '', amount: '', description: '' }],\n    creditEntries: [{ accountId: '', amount: '', description: '' }],\n    attachments: [],\n    notes: ''\n  });\n\n  const entryTypes = [\n    { value: 'manual', label: '📝 قيد يدوي', color: '#667eea' },\n    { value: 'sales', label: '💰 مبيعات', color: '#10b981' },\n    { value: 'purchase', label: '🛒 مشتريات', color: '#f59e0b' },\n    { value: 'payment', label: '💳 دفعة', color: '#6366f1' },\n    { value: 'receipt', label: '🧾 إيصال', color: '#8b5cf6' },\n    { value: 'adjustment', label: '⚖️ تسوية', color: '#ef4444' },\n    { value: 'depreciation', label: '📉 إهلاك', color: '#64748b' },\n    { value: 'accrual', label: '📊 استحقاق', color: '#059669' }\n  ];\n\n  const entryStatuses = [\n    { value: 'pending', label: '⏳ معلق', color: '#f59e0b' },\n    { value: 'approved', label: '✅ معتمد', color: '#10b981' },\n    { value: 'posted', label: '📋 مرحل', color: '#6366f1' },\n    { value: 'cancelled', label: '❌ ملغي', color: '#ef4444' }\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = () => {\n    try {\n      const entriesData = database.getJournalEntries();\n      const accountsData = database.getAccounts();\n      \n      setEntries(entriesData);\n      setAccounts(accountsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewEntry(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleDebitChange = (index, field, value) => {\n    const updatedDebits = [...newEntry.debitEntries];\n    updatedDebits[index] = { ...updatedDebits[index], [field]: value };\n    setNewEntry(prev => ({ ...prev, debitEntries: updatedDebits }));\n  };\n\n  const handleCreditChange = (index, field, value) => {\n    const updatedCredits = [...newEntry.creditEntries];\n    updatedCredits[index] = { ...updatedCredits[index], [field]: value };\n    setNewEntry(prev => ({ ...prev, creditEntries: updatedCredits }));\n  };\n\n  const addDebitEntry = () => {\n    setNewEntry(prev => ({\n      ...prev,\n      debitEntries: [...prev.debitEntries, { accountId: '', amount: '', description: '' }]\n    }));\n  };\n\n  const addCreditEntry = () => {\n    setNewEntry(prev => ({\n      ...prev,\n      creditEntries: [...prev.creditEntries, { accountId: '', amount: '', description: '' }]\n    }));\n  };\n\n  const removeDebitEntry = (index) => {\n    if (newEntry.debitEntries.length > 1) {\n      const updatedDebits = newEntry.debitEntries.filter((_, i) => i !== index);\n      setNewEntry(prev => ({ ...prev, debitEntries: updatedDebits }));\n    }\n  };\n\n  const removeCreditEntry = (index) => {\n    if (newEntry.creditEntries.length > 1) {\n      const updatedCredits = newEntry.creditEntries.filter((_, i) => i !== index);\n      setNewEntry(prev => ({ ...prev, creditEntries: updatedCredits }));\n    }\n  };\n\n  const calculateTotals = () => {\n    const totalDebit = newEntry.debitEntries.reduce((sum, entry) => \n      sum + (parseFloat(entry.amount) || 0), 0);\n    const totalCredit = newEntry.creditEntries.reduce((sum, entry) => \n      sum + (parseFloat(entry.amount) || 0), 0);\n    \n    return { totalDebit, totalCredit, difference: totalDebit - totalCredit };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      const { totalDebit, totalCredit } = calculateTotals();\n      \n      if (Math.abs(totalDebit - totalCredit) > 0.01) {\n        setMessage('خطأ: إجمالي المدين يجب أن يساوي إجمالي الدائن');\n        setLoading(false);\n        return;\n      }\n\n      const entryData = {\n        ...newEntry,\n        debitEntries: newEntry.debitEntries.filter(entry => entry.accountId && entry.amount),\n        creditEntries: newEntry.creditEntries.filter(entry => entry.accountId && entry.amount),\n        totalAmount: totalDebit\n      };\n\n      if (editingEntry) {\n        await database.updateJournalEntry(editingEntry.id, entryData);\n        setMessage('تم تحديث القيد بنجاح!');\n        setEditingEntry(null);\n      } else {\n        await database.addJournalEntry(entryData);\n        setMessage('تم إضافة القيد بنجاح!');\n      }\n\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ القيد:', error);\n      setMessage('خطأ في حفظ القيد');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetForm = () => {\n    setNewEntry({\n      date: new Date().toISOString().split('T')[0],\n      reference: '',\n      description: '',\n      type: 'manual',\n      status: 'pending',\n      debitEntries: [{ accountId: '', amount: '', description: '' }],\n      creditEntries: [{ accountId: '', amount: '', description: '' }],\n      attachments: [],\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingEntry(null);\n  };\n\n  const handleEdit = (entry) => {\n    setNewEntry({\n      ...entry,\n      date: entry.date.split('T')[0]\n    });\n    setEditingEntry(entry);\n    setShowAddForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا القيد؟')) {\n      try {\n        await database.deleteJournalEntry(id);\n        setMessage('تم حذف القيد بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف القيد:', error);\n        setMessage('خطأ في حذف القيد');\n      }\n    }\n  };\n\n  const handlePost = async (id) => {\n    if (window.confirm('هل أنت متأكد من ترحيل هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.')) {\n      try {\n        await database.postJournalEntry(id);\n        setMessage('تم ترحيل القيد بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في ترحيل القيد:', error);\n        setMessage('خطأ في ترحيل القيد');\n      }\n    }\n  };\n\n  const getAccountName = (accountId) => {\n    const account = accounts.find(acc => acc.id === parseInt(accountId));\n    return account ? `${account.code} - ${account.name}` : 'غير محدد';\n  };\n\n  const getTypeInfo = (type) => {\n    const typeObj = entryTypes.find(t => t.value === type);\n    return typeObj || { label: type, color: '#64748b' };\n  };\n\n  const getStatusInfo = (status) => {\n    const statusObj = entryStatuses.find(s => s.value === status);\n    return statusObj || { label: status, color: '#64748b' };\n  };\n\n  const filteredEntries = entries.filter(entry => {\n    const matchesSearch = \n      entry.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      entry.description.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesType = filterType === 'all' || entry.type === filterType;\n    \n    const entryDate = new Date(entry.date);\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const matchesDate = entryDate >= startDate && entryDate <= endDate;\n    \n    return matchesSearch && matchesType && matchesDate;\n  });\n\n  const totals = calculateTotals();\n\n  return (\n    <div className=\"journal-entries-container\">\n      <div className=\"journal-entries-header\">\n        <h1>📋 القيود اليومية</h1>\n        <p>إدارة وتتبع جميع القيود المحاسبية</p>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>\n          {message}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card total\">\n          <div className=\"stat-icon\">📋</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي القيود</h3>\n            <p>{entries.length} قيد</p>\n          </div>\n        </div>\n        <div className=\"stat-card pending\">\n          <div className=\"stat-icon\">⏳</div>\n          <div className=\"stat-info\">\n            <h3>قيود معلقة</h3>\n            <p>{entries.filter(e => e.status === 'pending').length} قيد</p>\n          </div>\n        </div>\n        <div className=\"stat-card posted\">\n          <div className=\"stat-icon\">✅</div>\n          <div className=\"stat-info\">\n            <h3>قيود مرحلة</h3>\n            <p>{entries.filter(e => e.status === 'posted').length} قيد</p>\n          </div>\n        </div>\n        <div className=\"stat-card amount\">\n          <div className=\"stat-icon\">💰</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي المبالغ</h3>\n            <p>{entries.reduce((sum, e) => sum + (e.totalAmount || 0), 0).toLocaleString()} ريال</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"journal-controls\">\n        <div className=\"search-filters\">\n          <input\n            type=\"text\"\n            placeholder=\"🔍 البحث في القيود...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">جميع الأنواع</option>\n            {entryTypes.map(type => (\n              <option key={type.value} value={type.value}>\n                {type.label}\n              </option>\n            ))}\n          </select>\n          <input\n            type=\"date\"\n            value={dateRange.start}\n            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n            className=\"date-input\"\n          />\n          <input\n            type=\"date\"\n            value={dateRange.end}\n            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n            className=\"date-input\"\n          />\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"add-entry-btn\"\n        >\n          ➕ إضافة قيد جديد\n        </button>\n      </div>\n\n      {/* Add/Edit Form */}\n      {showAddForm && (\n        <div className=\"modal-overlay\">\n          <div className=\"entry-form-modal\">\n            <div className=\"modal-header\">\n              <h3>{editingEntry ? '✏️ تعديل القيد' : '➕ إضافة قيد جديد'}</h3>\n              <button onClick={resetForm} className=\"close-btn\">✕</button>\n            </div>\n            \n            <form onSubmit={handleSubmit} className=\"entry-form\">\n              <div className=\"form-header\">\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>التاريخ</label>\n                    <input\n                      type=\"date\"\n                      name=\"date\"\n                      value={newEntry.date}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>رقم المرجع</label>\n                    <input\n                      type=\"text\"\n                      name=\"reference\"\n                      value={newEntry.reference}\n                      onChange={handleInputChange}\n                      placeholder=\"رقم القيد أو المرجع\"\n                      required\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>نوع القيد</label>\n                    <select\n                      name=\"type\"\n                      value={newEntry.type}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {entryTypes.map(type => (\n                        <option key={type.value} value={type.value}>\n                          {type.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>الحالة</label>\n                    <select\n                      name=\"status\"\n                      value={newEntry.status}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {entryStatuses.map(status => (\n                        <option key={status.value} value={status.value}>\n                          {status.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n                <div className=\"form-group full-width\">\n                  <label>وصف القيد</label>\n                  <input\n                    type=\"text\"\n                    name=\"description\"\n                    value={newEntry.description}\n                    onChange={handleInputChange}\n                    placeholder=\"وصف مختصر للقيد\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"entries-section\">\n                {/* Debit Entries */}\n                <div className=\"debit-section\">\n                  <div className=\"section-header\">\n                    <h4>🔴 الجانب المدين</h4>\n                    <button type=\"button\" onClick={addDebitEntry} className=\"add-line-btn\">\n                      ➕ إضافة سطر\n                    </button>\n                  </div>\n                  {newEntry.debitEntries.map((entry, index) => (\n                    <div key={index} className=\"entry-line\">\n                      <select\n                        value={entry.accountId}\n                        onChange={(e) => handleDebitChange(index, 'accountId', e.target.value)}\n                        required\n                      >\n                        <option value=\"\">اختر الحساب</option>\n                        {accounts.map(account => (\n                          <option key={account.id} value={account.id}>\n                            {account.code} - {account.name}\n                          </option>\n                        ))}\n                      </select>\n                      <input\n                        type=\"number\"\n                        placeholder=\"المبلغ\"\n                        value={entry.amount}\n                        onChange={(e) => handleDebitChange(index, 'amount', e.target.value)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        required\n                      />\n                      <input\n                        type=\"text\"\n                        placeholder=\"البيان\"\n                        value={entry.description}\n                        onChange={(e) => handleDebitChange(index, 'description', e.target.value)}\n                      />\n                      {newEntry.debitEntries.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeDebitEntry(index)}\n                          className=\"remove-line-btn\"\n                        >\n                          🗑️\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                  <div className=\"section-total\">\n                    إجمالي المدين: {totals.totalDebit.toLocaleString()} ريال\n                  </div>\n                </div>\n\n                {/* Credit Entries */}\n                <div className=\"credit-section\">\n                  <div className=\"section-header\">\n                    <h4>🔵 الجانب الدائن</h4>\n                    <button type=\"button\" onClick={addCreditEntry} className=\"add-line-btn\">\n                      ➕ إضافة سطر\n                    </button>\n                  </div>\n                  {newEntry.creditEntries.map((entry, index) => (\n                    <div key={index} className=\"entry-line\">\n                      <select\n                        value={entry.accountId}\n                        onChange={(e) => handleCreditChange(index, 'accountId', e.target.value)}\n                        required\n                      >\n                        <option value=\"\">اختر الحساب</option>\n                        {accounts.map(account => (\n                          <option key={account.id} value={account.id}>\n                            {account.code} - {account.name}\n                          </option>\n                        ))}\n                      </select>\n                      <input\n                        type=\"number\"\n                        placeholder=\"المبلغ\"\n                        value={entry.amount}\n                        onChange={(e) => handleCreditChange(index, 'amount', e.target.value)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        required\n                      />\n                      <input\n                        type=\"text\"\n                        placeholder=\"البيان\"\n                        value={entry.description}\n                        onChange={(e) => handleCreditChange(index, 'description', e.target.value)}\n                      />\n                      {newEntry.creditEntries.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeCreditEntry(index)}\n                          className=\"remove-line-btn\"\n                        >\n                          🗑️\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                  <div className=\"section-total\">\n                    إجمالي الدائن: {totals.totalCredit.toLocaleString()} ريال\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"balance-check\">\n                <div className={`balance-indicator ${Math.abs(totals.difference) < 0.01 ? 'balanced' : 'unbalanced'}`}>\n                  {Math.abs(totals.difference) < 0.01 ? \n                    '✅ القيد متوازن' : \n                    `⚠️ فرق التوازن: ${totals.difference.toLocaleString()} ريال`\n                  }\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>ملاحظات</label>\n                <textarea\n                  name=\"notes\"\n                  value={newEntry.notes}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  placeholder=\"ملاحظات إضافية...\"\n                />\n              </div>\n\n              <div className=\"form-actions\">\n                <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                  إلغاء\n                </button>\n                <button \n                  type=\"submit\" \n                  disabled={loading || Math.abs(totals.difference) >= 0.01} \n                  className=\"save-btn\"\n                >\n                  {loading ? '⏳ جاري الحفظ...' : (editingEntry ? '💾 تحديث' : '💾 حفظ')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Entries Table */}\n      <div className=\"entries-table-container\">\n        <table className=\"entries-table\">\n          <thead>\n            <tr>\n              <th>التاريخ</th>\n              <th>رقم المرجع</th>\n              <th>الوصف</th>\n              <th>النوع</th>\n              <th>المبلغ</th>\n              <th>الحالة</th>\n              <th>الإجراءات</th>\n            </tr>\n          </thead>\n          <tbody>\n            {filteredEntries.length === 0 ? (\n              <tr>\n                <td colSpan=\"7\" className=\"no-data\">\n                  لا توجد قيود مطابقة للبحث\n                </td>\n              </tr>\n            ) : (\n              filteredEntries.map(entry => {\n                const typeInfo = getTypeInfo(entry.type);\n                const statusInfo = getStatusInfo(entry.status);\n                return (\n                  <tr key={entry.id}>\n                    <td>{new Date(entry.date).toLocaleDateString('ar-SA')}</td>\n                    <td className=\"reference\">{entry.reference}</td>\n                    <td>{entry.description}</td>\n                    <td>\n                      <span \n                        className=\"type-badge\"\n                        style={{ backgroundColor: typeInfo.color }}\n                      >\n                        {typeInfo.label}\n                      </span>\n                    </td>\n                    <td className=\"amount\">{(entry.totalAmount || 0).toLocaleString()} ريال</td>\n                    <td>\n                      <span \n                        className=\"status-badge\"\n                        style={{ backgroundColor: statusInfo.color }}\n                      >\n                        {statusInfo.label}\n                      </span>\n                    </td>\n                    <td className=\"actions\">\n                      <button\n                        onClick={() => handleEdit(entry)}\n                        className=\"edit-btn\"\n                        title=\"تعديل\"\n                        disabled={entry.status === 'posted'}\n                      >\n                        ✏️\n                      </button>\n                      {entry.status === 'approved' && (\n                        <button\n                          onClick={() => handlePost(entry.id)}\n                          className=\"post-btn\"\n                          title=\"ترحيل\"\n                        >\n                          📋\n                        </button>\n                      )}\n                      <button\n                        onClick={() => handleDelete(entry.id)}\n                        className=\"delete-btn\"\n                        title=\"حذف\"\n                        disabled={entry.status === 'posted'}\n                      >\n                        🗑️\n                      </button>\n                    </td>\n                  </tr>\n                );\n              })\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default JournalEntries;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IACzCqB,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,GAAG,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IACvCgC,IAAI,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CS,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEL,WAAW,EAAE;IAAG,CAAC,CAAC;IAC9DM,aAAa,EAAE,CAAC;MAAEF,SAAS,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEL,WAAW,EAAE;IAAG,CAAC,CAAC;IAC/DO,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3D;IAAEF,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAU,CAAC,EACxD;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACxD;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5D;IAAEF,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC5D;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC1D;EAED7C,SAAS,CAAC,MAAM;IACd+C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI;MACF,MAAMC,WAAW,GAAG/C,QAAQ,CAACgD,iBAAiB,CAAC,CAAC;MAChD,MAAMC,YAAY,GAAGjD,QAAQ,CAACkD,WAAW,CAAC,CAAC;MAE3C5C,UAAU,CAACyC,WAAW,CAAC;MACvBvC,WAAW,CAACyC,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxB,UAAU,CAAC,uBAAuB,CAAC;IACrC;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEb;IAAM,CAAC,GAAGY,CAAC,CAACE,MAAM;IAChC3B,WAAW,CAAC4B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGb;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAElB,KAAK,KAAK;IACjD,MAAMmB,aAAa,GAAG,CAAC,GAAGjC,QAAQ,CAACO,YAAY,CAAC;IAChD0B,aAAa,CAACF,KAAK,CAAC,GAAG;MAAE,GAAGE,aAAa,CAACF,KAAK,CAAC;MAAE,CAACC,KAAK,GAAGlB;IAAM,CAAC;IAClEb,WAAW,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,YAAY,EAAE0B;IAAc,CAAC,CAAC,CAAC;EACjE,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACH,KAAK,EAAEC,KAAK,EAAElB,KAAK,KAAK;IAClD,MAAMqB,cAAc,GAAG,CAAC,GAAGnC,QAAQ,CAACU,aAAa,CAAC;IAClDyB,cAAc,CAACJ,KAAK,CAAC,GAAG;MAAE,GAAGI,cAAc,CAACJ,KAAK,CAAC;MAAE,CAACC,KAAK,GAAGlB;IAAM,CAAC;IACpEb,WAAW,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnB,aAAa,EAAEyB;IAAe,CAAC,CAAC,CAAC;EACnE,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,WAAW,CAAC4B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,YAAY,EAAE,CAAC,GAAGsB,IAAI,CAACtB,YAAY,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEL,WAAW,EAAE;MAAG,CAAC;IACrF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC3BpC,WAAW,CAAC4B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPnB,aAAa,EAAE,CAAC,GAAGmB,IAAI,CAACnB,aAAa,EAAE;QAAEF,SAAS,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEL,WAAW,EAAE;MAAG,CAAC;IACvF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkC,gBAAgB,GAAIP,KAAK,IAAK;IAClC,IAAI/B,QAAQ,CAACO,YAAY,CAACgC,MAAM,GAAG,CAAC,EAAE;MACpC,MAAMN,aAAa,GAAGjC,QAAQ,CAACO,YAAY,CAACiC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKX,KAAK,CAAC;MACzE9B,WAAW,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,YAAY,EAAE0B;MAAc,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,MAAMU,iBAAiB,GAAIZ,KAAK,IAAK;IACnC,IAAI/B,QAAQ,CAACU,aAAa,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACrC,MAAMJ,cAAc,GAAGnC,QAAQ,CAACU,aAAa,CAAC8B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKX,KAAK,CAAC;MAC3E9B,WAAW,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEnB,aAAa,EAAEyB;MAAe,CAAC,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,UAAU,GAAG7C,QAAQ,CAACO,YAAY,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KACzDD,GAAG,IAAIE,UAAU,CAACD,KAAK,CAACvC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C,MAAMyC,WAAW,GAAGlD,QAAQ,CAACU,aAAa,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAC3DD,GAAG,IAAIE,UAAU,CAACD,KAAK,CAACvC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3C,OAAO;MAAEoC,UAAU;MAAEK,WAAW;MAAEC,UAAU,EAAEN,UAAU,GAAGK;IAAY,CAAC;EAC1E,CAAC;EAED,MAAME,YAAY,GAAG,MAAO1B,CAAC,IAAK;IAChCA,CAAC,CAAC2B,cAAc,CAAC,CAAC;IAClBxD,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM;QAAE8C,UAAU;QAAEK;MAAY,CAAC,GAAGN,eAAe,CAAC,CAAC;MAErD,IAAIU,IAAI,CAACC,GAAG,CAACV,UAAU,GAAGK,WAAW,CAAC,GAAG,IAAI,EAAE;QAC7CnD,UAAU,CAAC,+CAA+C,CAAC;QAC3DF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAM2D,SAAS,GAAG;QAChB,GAAGxD,QAAQ;QACXO,YAAY,EAAEP,QAAQ,CAACO,YAAY,CAACiC,MAAM,CAACQ,KAAK,IAAIA,KAAK,CAACxC,SAAS,IAAIwC,KAAK,CAACvC,MAAM,CAAC;QACpFC,aAAa,EAAEV,QAAQ,CAACU,aAAa,CAAC8B,MAAM,CAACQ,KAAK,IAAIA,KAAK,CAACxC,SAAS,IAAIwC,KAAK,CAACvC,MAAM,CAAC;QACtFgD,WAAW,EAAEZ;MACf,CAAC;MAED,IAAI9D,YAAY,EAAE;QAChB,MAAMX,QAAQ,CAACsF,kBAAkB,CAAC3E,YAAY,CAAC4E,EAAE,EAAEH,SAAS,CAAC;QAC7DzD,UAAU,CAAC,uBAAuB,CAAC;QACnCf,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACL,MAAMZ,QAAQ,CAACwF,eAAe,CAACJ,SAAS,CAAC;QACzCzD,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEA8D,SAAS,CAAC,CAAC;MACX3C,QAAQ,CAAC,CAAC;MACV4C,UAAU,CAAC,MAAM/D,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCxB,UAAU,CAAC,kBAAkB,CAAC;IAChC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,SAAS,GAAGA,CAAA,KAAM;IACtB5D,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CS,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEL,WAAW,EAAE;MAAG,CAAC,CAAC;MAC9DM,aAAa,EAAE,CAAC;QAAEF,SAAS,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEL,WAAW,EAAE;MAAG,CAAC,CAAC;MAC/DO,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE;IACT,CAAC,CAAC;IACF9B,cAAc,CAAC,KAAK,CAAC;IACrBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+E,UAAU,GAAIf,KAAK,IAAK;IAC5B/C,WAAW,CAAC;MACV,GAAG+C,KAAK;MACR9C,IAAI,EAAE8C,KAAK,CAAC9C,IAAI,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;IACFV,eAAe,CAACgE,KAAK,CAAC;IACtBlE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMkF,YAAY,GAAG,MAAOL,EAAE,IAAK;IACjC,IAAIM,MAAM,CAACC,OAAO,CAAC,gCAAgC,CAAC,EAAE;MACpD,IAAI;QACF,MAAM9F,QAAQ,CAAC+F,kBAAkB,CAACR,EAAE,CAAC;QACrC5D,UAAU,CAAC,qBAAqB,CAAC;QACjCmB,QAAQ,CAAC,CAAC;QACV4C,UAAU,CAAC,MAAM/D,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzCxB,UAAU,CAAC,kBAAkB,CAAC;MAChC;IACF;EACF,CAAC;EAED,MAAMqE,UAAU,GAAG,MAAOT,EAAE,IAAK;IAC/B,IAAIM,MAAM,CAACC,OAAO,CAAC,kEAAkE,CAAC,EAAE;MACtF,IAAI;QACF,MAAM9F,QAAQ,CAACiG,gBAAgB,CAACV,EAAE,CAAC;QACnC5D,UAAU,CAAC,uBAAuB,CAAC;QACnCmB,QAAQ,CAAC,CAAC;QACV4C,UAAU,CAAC,MAAM/D,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxB,UAAU,CAAC,oBAAoB,CAAC;MAClC;IACF;EACF,CAAC;EAED,MAAMuE,cAAc,GAAI9D,SAAS,IAAK;IACpC,MAAM+D,OAAO,GAAG5F,QAAQ,CAAC6F,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKe,QAAQ,CAAClE,SAAS,CAAC,CAAC;IACpE,OAAO+D,OAAO,GAAG,GAAGA,OAAO,CAACI,IAAI,MAAMJ,OAAO,CAAC5C,IAAI,EAAE,GAAG,UAAU;EACnE,CAAC;EAED,MAAMiD,WAAW,GAAIvE,IAAI,IAAK;IAC5B,MAAMwE,OAAO,GAAGhE,UAAU,CAAC2D,IAAI,CAACM,CAAC,IAAIA,CAAC,CAAChE,KAAK,KAAKT,IAAI,CAAC;IACtD,OAAOwE,OAAO,IAAI;MAAE9D,KAAK,EAAEV,IAAI;MAAEW,KAAK,EAAE;IAAU,CAAC;EACrD,CAAC;EAED,MAAM+D,aAAa,GAAIzE,MAAM,IAAK;IAChC,MAAM0E,SAAS,GAAG/D,aAAa,CAACuD,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACnE,KAAK,KAAKR,MAAM,CAAC;IAC7D,OAAO0E,SAAS,IAAI;MAAEjE,KAAK,EAAET,MAAM;MAAEU,KAAK,EAAE;IAAU,CAAC;EACzD,CAAC;EAED,MAAMkE,eAAe,GAAGzG,OAAO,CAAC+D,MAAM,CAACQ,KAAK,IAAI;IAC9C,MAAMmC,aAAa,GACjBnC,KAAK,CAAC7C,SAAS,CAACiF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpG,UAAU,CAACmG,WAAW,CAAC,CAAC,CAAC,IAChEpC,KAAK,CAAC5C,WAAW,CAACgF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpG,UAAU,CAACmG,WAAW,CAAC,CAAC,CAAC;IAEpE,MAAME,WAAW,GAAGnG,UAAU,KAAK,KAAK,IAAI6D,KAAK,CAAC3C,IAAI,KAAKlB,UAAU;IAErE,MAAMoG,SAAS,GAAG,IAAI/F,IAAI,CAACwD,KAAK,CAAC9C,IAAI,CAAC;IACtC,MAAMsF,SAAS,GAAG,IAAIhG,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMkG,OAAO,GAAG,IAAIjG,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IACvC,MAAM+F,WAAW,GAAGH,SAAS,IAAIC,SAAS,IAAID,SAAS,IAAIE,OAAO;IAElE,OAAON,aAAa,IAAIG,WAAW,IAAII,WAAW;EACpD,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG/C,eAAe,CAAC,CAAC;EAEhC,oBACEtE,OAAA;IAAKsH,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCvH,OAAA;MAAKsH,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCvH,OAAA;QAAAuH,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B3H,OAAA;QAAAuH,QAAA,EAAG;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,EAELnG,OAAO,iBACNxB,OAAA;MAAKsH,SAAS,EAAE,WAAW9F,OAAO,CAACuF,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAQ,QAAA,EACxE/F;IAAO;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD3H,OAAA;MAAKsH,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBvH,OAAA;QAAKsH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvH,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC3H,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvH,OAAA;YAAAuH,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB3H,OAAA;YAAAuH,QAAA,GAAIpH,OAAO,CAAC8D,MAAM,EAAC,qBAAI;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3H,OAAA;QAAKsH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvH,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClC3H,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvH,OAAA;YAAAuH,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB3H,OAAA;YAAAuH,QAAA,GAAIpH,OAAO,CAAC+D,MAAM,CAACd,CAAC,IAAIA,CAAC,CAACpB,MAAM,KAAK,SAAS,CAAC,CAACiC,MAAM,EAAC,qBAAI;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3H,OAAA;QAAKsH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BvH,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClC3H,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvH,OAAA;YAAAuH,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB3H,OAAA;YAAAuH,QAAA,GAAIpH,OAAO,CAAC+D,MAAM,CAACd,CAAC,IAAIA,CAAC,CAACpB,MAAM,KAAK,QAAQ,CAAC,CAACiC,MAAM,EAAC,qBAAI;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3H,OAAA;QAAKsH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BvH,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC3H,OAAA;UAAKsH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvH,OAAA;YAAAuH,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB3H,OAAA;YAAAuH,QAAA,GAAIpH,OAAO,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAErB,CAAC,KAAKqB,GAAG,IAAIrB,CAAC,CAAC+B,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACyC,cAAc,CAAC,CAAC,EAAC,2BAAK;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3H,OAAA;MAAKsH,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvH,OAAA;QAAKsH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvH,OAAA;UACE+B,IAAI,EAAC,MAAM;UACX8F,WAAW,EAAC,kGAAuB;UACnCrF,KAAK,EAAE7B,UAAW;UAClBmH,QAAQ,EAAG1E,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;UAC/C8E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACF3H,OAAA;UACEwC,KAAK,EAAE3B,UAAW;UAClBiH,QAAQ,EAAG1E,CAAC,IAAKtC,aAAa,CAACsC,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;UAC/C8E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBvH,OAAA;YAAQwC,KAAK,EAAC,KAAK;YAAA+E,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCpF,UAAU,CAACwF,GAAG,CAAChG,IAAI,iBAClB/B,OAAA;YAAyBwC,KAAK,EAAET,IAAI,CAACS,KAAM;YAAA+E,QAAA,EACxCxF,IAAI,CAACU;UAAK,GADAV,IAAI,CAACS,KAAK;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT3H,OAAA;UACE+B,IAAI,EAAC,MAAM;UACXS,KAAK,EAAEzB,SAAS,CAACE,KAAM;UACvB6G,QAAQ,EAAG1E,CAAC,IAAKpC,YAAY,CAACuC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEtC,KAAK,EAAEmC,CAAC,CAACE,MAAM,CAACd;UAAM,CAAC,CAAC,CAAE;UAC5E8E,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACF3H,OAAA;UACE+B,IAAI,EAAC,MAAM;UACXS,KAAK,EAAEzB,SAAS,CAACM,GAAI;UACrByG,QAAQ,EAAG1E,CAAC,IAAKpC,YAAY,CAACuC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAElC,GAAG,EAAE+B,CAAC,CAACE,MAAM,CAACd;UAAM,CAAC,CAAC,CAAE;UAC1E8E,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3H,OAAA;QACEgI,OAAO,EAAEA,CAAA,KAAMxH,cAAc,CAAC,IAAI,CAAE;QACpC8G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLpH,WAAW,iBACVP,OAAA;MAAKsH,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BvH,OAAA;QAAKsH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BvH,OAAA;UAAKsH,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvH,OAAA;YAAAuH,QAAA,EAAK9G,YAAY,GAAG,gBAAgB,GAAG;UAAkB;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/D3H,OAAA;YAAQgI,OAAO,EAAEzC,SAAU;YAAC+B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEN3H,OAAA;UAAMiI,QAAQ,EAAEnD,YAAa;UAACwC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAClDvH,OAAA;YAAKsH,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BvH,OAAA;cAAKsH,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBvH,OAAA;gBAAKsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvH,OAAA;kBAAAuH,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtB3H,OAAA;kBACE+B,IAAI,EAAC,MAAM;kBACXsB,IAAI,EAAC,MAAM;kBACXb,KAAK,EAAEd,QAAQ,CAACE,IAAK;kBACrBkG,QAAQ,EAAE3E,iBAAkB;kBAC5B+E,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3H,OAAA;gBAAKsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvH,OAAA;kBAAAuH,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzB3H,OAAA;kBACE+B,IAAI,EAAC,MAAM;kBACXsB,IAAI,EAAC,WAAW;kBAChBb,KAAK,EAAEd,QAAQ,CAACG,SAAU;kBAC1BiG,QAAQ,EAAE3E,iBAAkB;kBAC5B0E,WAAW,EAAC,qGAAqB;kBACjCK,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3H,OAAA;gBAAKsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvH,OAAA;kBAAAuH,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxB3H,OAAA;kBACEqD,IAAI,EAAC,MAAM;kBACXb,KAAK,EAAEd,QAAQ,CAACK,IAAK;kBACrB+F,QAAQ,EAAE3E,iBAAkB;kBAC5B+E,QAAQ;kBAAAX,QAAA,EAEPhF,UAAU,CAACwF,GAAG,CAAChG,IAAI,iBAClB/B,OAAA;oBAAyBwC,KAAK,EAAET,IAAI,CAACS,KAAM;oBAAA+E,QAAA,EACxCxF,IAAI,CAACU;kBAAK,GADAV,IAAI,CAACS,KAAK;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN3H,OAAA;gBAAKsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvH,OAAA;kBAAAuH,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB3H,OAAA;kBACEqD,IAAI,EAAC,QAAQ;kBACbb,KAAK,EAAEd,QAAQ,CAACM,MAAO;kBACvB8F,QAAQ,EAAE3E,iBAAkB;kBAC5B+E,QAAQ;kBAAAX,QAAA,EAEP5E,aAAa,CAACoF,GAAG,CAAC/F,MAAM,iBACvBhC,OAAA;oBAA2BwC,KAAK,EAAER,MAAM,CAACQ,KAAM;oBAAA+E,QAAA,EAC5CvF,MAAM,CAACS;kBAAK,GADFT,MAAM,CAACQ,KAAK;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3H,OAAA;cAAKsH,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCvH,OAAA;gBAAAuH,QAAA,EAAO;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxB3H,OAAA;gBACE+B,IAAI,EAAC,MAAM;gBACXsB,IAAI,EAAC,aAAa;gBAClBb,KAAK,EAAEd,QAAQ,CAACI,WAAY;gBAC5BgG,QAAQ,EAAE3E,iBAAkB;gBAC5B0E,WAAW,EAAC,kFAAiB;gBAC7BK,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3H,OAAA;YAAKsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAE9BvH,OAAA;cAAKsH,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvH,OAAA;gBAAKsH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvH,OAAA;kBAAAuH,QAAA,EAAI;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzB3H,OAAA;kBAAQ+B,IAAI,EAAC,QAAQ;kBAACiG,OAAO,EAAElE,aAAc;kBAACwD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAEvE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACLjG,QAAQ,CAACO,YAAY,CAAC8F,GAAG,CAAC,CAACrD,KAAK,EAAEjB,KAAK,kBACtCzD,OAAA;gBAAiBsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACrCvH,OAAA;kBACEwC,KAAK,EAAEkC,KAAK,CAACxC,SAAU;kBACvB4F,QAAQ,EAAG1E,CAAC,IAAKI,iBAAiB,CAACC,KAAK,EAAE,WAAW,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;kBACvE0F,QAAQ;kBAAAX,QAAA,gBAERvH,OAAA;oBAAQwC,KAAK,EAAC,EAAE;oBAAA+E,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCtH,QAAQ,CAAC0H,GAAG,CAAC9B,OAAO,iBACnBjG,OAAA;oBAAyBwC,KAAK,EAAEyD,OAAO,CAACZ,EAAG;oBAAAkC,QAAA,GACxCtB,OAAO,CAACI,IAAI,EAAC,KAAG,EAACJ,OAAO,CAAC5C,IAAI;kBAAA,GADnB4C,OAAO,CAACZ,EAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACT3H,OAAA;kBACE+B,IAAI,EAAC,QAAQ;kBACb8F,WAAW,EAAC,sCAAQ;kBACpBrF,KAAK,EAAEkC,KAAK,CAACvC,MAAO;kBACpB2F,QAAQ,EAAG1E,CAAC,IAAKI,iBAAiB,CAACC,KAAK,EAAE,QAAQ,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;kBACpE2F,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPF,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF3H,OAAA;kBACE+B,IAAI,EAAC,MAAM;kBACX8F,WAAW,EAAC,sCAAQ;kBACpBrF,KAAK,EAAEkC,KAAK,CAAC5C,WAAY;kBACzBgG,QAAQ,EAAG1E,CAAC,IAAKI,iBAAiB,CAACC,KAAK,EAAE,aAAa,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK;gBAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,EACDjG,QAAQ,CAACO,YAAY,CAACgC,MAAM,GAAG,CAAC,iBAC/BjE,OAAA;kBACE+B,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAACP,KAAK,CAAE;kBACvC6D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GApCOlE,KAAK;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCV,CACN,CAAC,eACF3H,OAAA;gBAAKsH,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,6EACd,EAACF,MAAM,CAAC9C,UAAU,CAACqD,cAAc,CAAC,CAAC,EAAC,2BACrD;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3H,OAAA;cAAKsH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvH,OAAA;gBAAKsH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvH,OAAA;kBAAAuH,QAAA,EAAI;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzB3H,OAAA;kBAAQ+B,IAAI,EAAC,QAAQ;kBAACiG,OAAO,EAAEjE,cAAe;kBAACuD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAExE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACLjG,QAAQ,CAACU,aAAa,CAAC2F,GAAG,CAAC,CAACrD,KAAK,EAAEjB,KAAK,kBACvCzD,OAAA;gBAAiBsH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACrCvH,OAAA;kBACEwC,KAAK,EAAEkC,KAAK,CAACxC,SAAU;kBACvB4F,QAAQ,EAAG1E,CAAC,IAAKQ,kBAAkB,CAACH,KAAK,EAAE,WAAW,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;kBACxE0F,QAAQ;kBAAAX,QAAA,gBAERvH,OAAA;oBAAQwC,KAAK,EAAC,EAAE;oBAAA+E,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCtH,QAAQ,CAAC0H,GAAG,CAAC9B,OAAO,iBACnBjG,OAAA;oBAAyBwC,KAAK,EAAEyD,OAAO,CAACZ,EAAG;oBAAAkC,QAAA,GACxCtB,OAAO,CAACI,IAAI,EAAC,KAAG,EAACJ,OAAO,CAAC5C,IAAI;kBAAA,GADnB4C,OAAO,CAACZ,EAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACT3H,OAAA;kBACE+B,IAAI,EAAC,QAAQ;kBACb8F,WAAW,EAAC,sCAAQ;kBACpBrF,KAAK,EAAEkC,KAAK,CAACvC,MAAO;kBACpB2F,QAAQ,EAAG1E,CAAC,IAAKQ,kBAAkB,CAACH,KAAK,EAAE,QAAQ,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK,CAAE;kBACrE2F,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPF,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF3H,OAAA;kBACE+B,IAAI,EAAC,MAAM;kBACX8F,WAAW,EAAC,sCAAQ;kBACpBrF,KAAK,EAAEkC,KAAK,CAAC5C,WAAY;kBACzBgG,QAAQ,EAAG1E,CAAC,IAAKQ,kBAAkB,CAACH,KAAK,EAAE,aAAa,EAAEL,CAAC,CAACE,MAAM,CAACd,KAAK;gBAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,EACDjG,QAAQ,CAACU,aAAa,CAAC6B,MAAM,GAAG,CAAC,iBAChCjE,OAAA;kBACE+B,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACZ,KAAK,CAAE;kBACxC6D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GApCOlE,KAAK;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCV,CACN,CAAC,eACF3H,OAAA;gBAAKsH,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,6EACd,EAACF,MAAM,CAACzC,WAAW,CAACgD,cAAc,CAAC,CAAC,EAAC,2BACtD;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3H,OAAA;YAAKsH,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BvH,OAAA;cAAKsH,SAAS,EAAE,qBAAqBtC,IAAI,CAACC,GAAG,CAACoC,MAAM,CAACxC,UAAU,CAAC,GAAG,IAAI,GAAG,UAAU,GAAG,YAAY,EAAG;cAAA0C,QAAA,EACnGvC,IAAI,CAACC,GAAG,CAACoC,MAAM,CAACxC,UAAU,CAAC,GAAG,IAAI,GACjC,gBAAgB,GAChB,mBAAmBwC,MAAM,CAACxC,UAAU,CAAC+C,cAAc,CAAC,CAAC;YAAO;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3H,OAAA;YAAKsH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCvH,OAAA;cAAAuH,QAAA,EAAO;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtB3H,OAAA;cACEqD,IAAI,EAAC,OAAO;cACZb,KAAK,EAAEd,QAAQ,CAACY,KAAM;cACtBwF,QAAQ,EAAE3E,iBAAkB;cAC5BkF,IAAI,EAAC,GAAG;cACRR,WAAW,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3H,OAAA;YAAKsH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvH,OAAA;cAAQ+B,IAAI,EAAC,QAAQ;cAACiG,OAAO,EAAEzC,SAAU;cAAC+B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3H,OAAA;cACE+B,IAAI,EAAC,QAAQ;cACbuG,QAAQ,EAAEhH,OAAO,IAAI0D,IAAI,CAACC,GAAG,CAACoC,MAAM,CAACxC,UAAU,CAAC,IAAI,IAAK;cACzDyC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAEnBjG,OAAO,GAAG,iBAAiB,GAAIb,YAAY,GAAG,UAAU,GAAG;YAAS;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3H,OAAA;MAAKsH,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCvH,OAAA;QAAOsH,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9BvH,OAAA;UAAAuH,QAAA,eACEvH,OAAA;YAAAuH,QAAA,gBACEvH,OAAA;cAAAuH,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB3H,OAAA;cAAAuH,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3H,OAAA;cAAAuH,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd3H,OAAA;cAAAuH,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd3H,OAAA;cAAAuH,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf3H,OAAA;cAAAuH,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf3H,OAAA;cAAAuH,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR3H,OAAA;UAAAuH,QAAA,EACGX,eAAe,CAAC3C,MAAM,KAAK,CAAC,gBAC3BjE,OAAA;YAAAuH,QAAA,eACEvH,OAAA;cAAIuI,OAAO,EAAC,GAAG;cAACjB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELf,eAAe,CAACmB,GAAG,CAACrD,KAAK,IAAI;YAC3B,MAAM8D,QAAQ,GAAGlC,WAAW,CAAC5B,KAAK,CAAC3C,IAAI,CAAC;YACxC,MAAM0G,UAAU,GAAGhC,aAAa,CAAC/B,KAAK,CAAC1C,MAAM,CAAC;YAC9C,oBACEhC,OAAA;cAAAuH,QAAA,gBACEvH,OAAA;gBAAAuH,QAAA,EAAK,IAAIrG,IAAI,CAACwD,KAAK,CAAC9C,IAAI,CAAC,CAAC8G,kBAAkB,CAAC,OAAO;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3D3H,OAAA;gBAAIsH,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE7C,KAAK,CAAC7C;cAAS;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD3H,OAAA;gBAAAuH,QAAA,EAAK7C,KAAK,CAAC5C;cAAW;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5B3H,OAAA;gBAAAuH,QAAA,eACEvH,OAAA;kBACEsH,SAAS,EAAC,YAAY;kBACtBqB,KAAK,EAAE;oBAAEC,eAAe,EAAEJ,QAAQ,CAAC9F;kBAAM,CAAE;kBAAA6E,QAAA,EAE1CiB,QAAQ,CAAC/F;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL3H,OAAA;gBAAIsH,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAAE,CAAC7C,KAAK,CAACS,WAAW,IAAI,CAAC,EAAEyC,cAAc,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5E3H,OAAA;gBAAAuH,QAAA,eACEvH,OAAA;kBACEsH,SAAS,EAAC,cAAc;kBACxBqB,KAAK,EAAE;oBAAEC,eAAe,EAAEH,UAAU,CAAC/F;kBAAM,CAAE;kBAAA6E,QAAA,EAE5CkB,UAAU,CAAChG;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL3H,OAAA;gBAAIsH,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACrBvH,OAAA;kBACEgI,OAAO,EAAEA,CAAA,KAAMvC,UAAU,CAACf,KAAK,CAAE;kBACjC4C,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,gCAAO;kBACbP,QAAQ,EAAE5D,KAAK,CAAC1C,MAAM,KAAK,QAAS;kBAAAuF,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRjD,KAAK,CAAC1C,MAAM,KAAK,UAAU,iBAC1BhC,OAAA;kBACEgI,OAAO,EAAEA,CAAA,KAAMlC,UAAU,CAACpB,KAAK,CAACW,EAAE,CAAE;kBACpCiC,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,gCAAO;kBAAAtB,QAAA,EACd;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACD3H,OAAA;kBACEgI,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAAChB,KAAK,CAACW,EAAE,CAAE;kBACtCiC,SAAS,EAAC,YAAY;kBACtBuB,KAAK,EAAC,oBAAK;kBACXP,QAAQ,EAAE5D,KAAK,CAAC1C,MAAM,KAAK,QAAS;kBAAAuF,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA/CEjD,KAAK,CAACW,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgDb,CAAC;UAET,CAAC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzH,EAAA,CA7nBID,cAAc;AAAA6I,EAAA,GAAd7I,cAAc;AA+nBpB,eAAeA,cAAc;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}