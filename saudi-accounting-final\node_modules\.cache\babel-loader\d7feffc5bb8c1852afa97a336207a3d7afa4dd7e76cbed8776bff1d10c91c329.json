{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Login\\\\EnhancedLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport { testDatabase } from '../../utils/testDatabase';\nimport './EnhancedLogin.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedLogin = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n  useEffect(() => {\n    // Test database on component mount\n    console.log('🔧 اختبار قاعدة البيانات عند تحميل المكون...');\n    testDatabase();\n\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({\n        ...prev,\n        username: rememberedUsername\n      }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 500));\n      console.log('محاولة تسجيل الدخول:', formData.username);\n      const user = database.authenticateUser(formData.username, formData.password);\n      console.log('نتيجة المصادقة:', user);\n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        try {\n          database.logActivity({\n            type: 'login',\n            userId: user.id,\n            username: user.username,\n            timestamp: new Date().toISOString(),\n            details: 'تسجيل دخول ناجح'\n          });\n        } catch (logError) {\n          console.warn('خطأ في تسجيل النشاط:', logError);\n        }\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDemoLogin = async () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n\n    // تسجيل دخول تلقائي\n    setTimeout(() => {\n      const form = document.querySelector('.login-form');\n      if (form) {\n        form.dispatchEvent(new Event('submit', {\n          bubbles: true,\n          cancelable: true\n        }));\n      }\n    }, 100);\n  };\n  const handleResetDatabase = () => {\n    try {\n      console.log('إعادة تهيئة قاعدة البيانات...');\n\n      // مسح البيانات الحالية\n      localStorage.removeItem('saudi_accounting_users');\n\n      // إعادة تهيئة قاعدة البيانات\n      database.initializeDatabase();\n\n      // اختبار قاعدة البيانات\n      testDatabase();\n      setError('');\n      setFormData({\n        username: 'admin',\n        password: 'admin123'\n      });\n      alert('تم إعادة تهيئة قاعدة البيانات بنجاح!\\nيمكنك الآن تسجيل الدخول باستخدام:\\nاسم المستخدم: admin\\nكلمة المرور: admin123');\n    } catch (error) {\n      console.error('خطأ في إعادة تهيئة قاعدة البيانات:', error);\n      setError('فشل في إعادة تهيئة قاعدة البيانات');\n    }\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"enhanced-login\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-background\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"background-pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shapes\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: \"\\uD83C\\uDFEA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Aronim EXP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-time\",\n            children: getCurrentTime()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0633\\u062C\\u0644 \\u062F\\u062E\\u0648\\u0644\\u0643 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"login-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"username\",\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  name: \"username\",\n                  value: formData.username,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-icon\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"current-password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"password-toggle\",\n                  onClick: () => setShowPassword(!showPassword),\n                  disabled: loading,\n                  children: showPassword ? '🙈' : '👁️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-options\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"checkbox-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: rememberMe,\n                  onChange: e => setRememberMe(e.target.checked),\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"checkmark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), \"\\u062A\\u0630\\u0643\\u0631\\u0646\\u064A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"login-button\",\n              disabled: loading,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"button-icon\",\n                  children: \"\\uD83D\\uDD10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divider\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0623\\u0648\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleDemoLogin,\n              className: \"demo-button\",\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), \"\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 (Demo)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleResetDatabase,\n              className: \"reset-button\",\n              disabled: loading,\n              title: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0647\\u064A\\u0626\\u0629 \\u0642\\u0627\\u0639\\u062F\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0641\\u064A \\u062D\\u0627\\u0644\\u0629 \\u0648\\u062C\\u0648\\u062F \\u0645\\u0634\\u0643\\u0644\\u0629\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-icon\",\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0647\\u064A\\u0626\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"system-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDD12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0622\\u0645\\u0646 \\u0648\\u0645\\u062D\\u0645\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u062C\\u0627\\u0648\\u0628 \\u0645\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u062C\\u0647\\u0632\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"copyright\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Aronim EXP. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0646\\u0633\\u062E\\u0629 2.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0646\\u0642\\u0637\\u0629 \\u0628\\u064A\\u0639 \\u0645\\u062A\\u0637\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0628\\u064A\\u0639 \\u0633\\u0631\\u064A\\u0639 \\u0648\\u0641\\u0639\\u0627\\u0644 \\u0645\\u0639 \\u062F\\u0639\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0645\\u0641\\u0635\\u0644\\u0629 \\u0648\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u062F\\u0642\\u064A\\u0642\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u062A\\u0628\\u0639 \\u062F\\u0642\\u064A\\u0642 \\u0644\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0648\\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0630\\u0643\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062D\\u0627\\u0633\\u0628\\u064A \\u0634\\u0627\\u0645\\u0644 \\u0645\\u0639 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedLogin, \"IjvILKq9CIJnPDg9lujxmrutoIc=\");\n_c = EnhancedLogin;\nexport default EnhancedLogin;\nvar _c;\n$RefreshReg$(_c, \"EnhancedLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "testDatabase", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "onLogin", "_s", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "showPassword", "setShowPassword", "rememberMe", "setRememberMe", "console", "log", "rememberedUsername", "localStorage", "getItem", "prev", "usernameField", "document", "getElementById", "focus", "handleInputChange", "e", "name", "value", "target", "handleSubmit", "preventDefault", "trim", "Promise", "resolve", "setTimeout", "user", "authenticateUser", "setItem", "removeItem", "logActivity", "type", "userId", "id", "timestamp", "Date", "toISOString", "details", "logError", "warn", "handleDemoLogin", "form", "querySelector", "dispatchEvent", "Event", "bubbles", "cancelable", "handleResetDatabase", "initializeDatabase", "alert", "getCurrentTime", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "onChange", "placeholder", "disabled", "autoComplete", "onClick", "checked", "title", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Login/EnhancedLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport { testDatabase } from '../../utils/testDatabase';\nimport './EnhancedLogin.css';\n\nconst EnhancedLogin = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n\n  useEffect(() => {\n    // Test database on component mount\n    console.log('🔧 اختبار قاعدة البيانات عند تحميل المكون...');\n    testDatabase();\n\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({ ...prev, username: rememberedUsername }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      console.log('محاولة تسجيل الدخول:', formData.username);\n\n      const user = database.authenticateUser(formData.username, formData.password);\n\n      console.log('نتيجة المصادقة:', user);\n\n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        try {\n          database.logActivity({\n            type: 'login',\n            userId: user.id,\n            username: user.username,\n            timestamp: new Date().toISOString(),\n            details: 'تسجيل دخول ناجح'\n          });\n        } catch (logError) {\n          console.warn('خطأ في تسجيل النشاط:', logError);\n        }\n\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoLogin = async () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n\n    // تسجيل دخول تلقائي\n    setTimeout(() => {\n      const form = document.querySelector('.login-form');\n      if (form) {\n        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));\n      }\n    }, 100);\n  };\n\n  const handleResetDatabase = () => {\n    try {\n      console.log('إعادة تهيئة قاعدة البيانات...');\n\n      // مسح البيانات الحالية\n      localStorage.removeItem('saudi_accounting_users');\n\n      // إعادة تهيئة قاعدة البيانات\n      database.initializeDatabase();\n\n      // اختبار قاعدة البيانات\n      testDatabase();\n\n      setError('');\n      setFormData({\n        username: 'admin',\n        password: 'admin123'\n      });\n\n      alert('تم إعادة تهيئة قاعدة البيانات بنجاح!\\nيمكنك الآن تسجيل الدخول باستخدام:\\nاسم المستخدم: admin\\nكلمة المرور: admin123');\n\n    } catch (error) {\n      console.error('خطأ في إعادة تهيئة قاعدة البيانات:', error);\n      setError('فشل في إعادة تهيئة قاعدة البيانات');\n    }\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"enhanced-login\">\n      <div className=\"login-background\">\n        <div className=\"background-pattern\"></div>\n        <div className=\"floating-shapes\">\n          <div className=\"shape shape-1\"></div>\n          <div className=\"shape shape-2\"></div>\n          <div className=\"shape shape-3\"></div>\n          <div className=\"shape shape-4\"></div>\n        </div>\n      </div>\n\n      <div className=\"login-container\">\n        <div className=\"login-card\">\n          <div className=\"login-header\">\n            <div className=\"company-logo\">\n              <div className=\"logo-icon\">🏪</div>\n              <div className=\"logo-text\">\n                <h1>Aronim EXP</h1>\n                <p>نظام المحاسبة المتطور</p>\n              </div>\n            </div>\n            <div className=\"current-time\">\n              {getCurrentTime()}\n            </div>\n          </div>\n\n          <div className=\"login-body\">\n            <div className=\"welcome-text\">\n              <h2>مرحباً بك</h2>\n              <p>سجل دخولك للوصول إلى نظام المحاسبة</p>\n            </div>\n\n            {error && (\n              <div className=\"error-message\">\n                <span className=\"error-icon\">⚠️</span>\n                {error}\n              </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"login-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"username\">اسم المستخدم</label>\n                <div className=\"input-container\">\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل اسم المستخدم\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"username\"\n                  />\n                  <span className=\"input-icon\">👤</span>\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\">كلمة المرور</label>\n                <div className=\"input-container\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل كلمة المرور\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"current-password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    disabled={loading}\n                  >\n                    {showPassword ? '🙈' : '👁️'}\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"form-options\">\n                <label className=\"checkbox-container\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    disabled={loading}\n                  />\n                  <span className=\"checkmark\"></span>\n                  تذكرني\n                </label>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"login-button\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    جاري تسجيل الدخول...\n                  </>\n                ) : (\n                  <>\n                    <span className=\"button-icon\">🔐</span>\n                    تسجيل الدخول\n                  </>\n                )}\n              </button>\n            </form>\n\n            <div className=\"demo-section\">\n              <div className=\"divider\">\n                <span>أو</span>\n              </div>\n              <button\n                type=\"button\"\n                onClick={handleDemoLogin}\n                className=\"demo-button\"\n                disabled={loading}\n              >\n                <span className=\"button-icon\">🎯</span>\n                تجربة النظام (Demo)\n              </button>\n\n              <button\n                type=\"button\"\n                onClick={handleResetDatabase}\n                className=\"reset-button\"\n                disabled={loading}\n                title=\"إعادة تهيئة قاعدة البيانات في حالة وجود مشكلة\"\n              >\n                <span className=\"button-icon\">🔄</span>\n                إعادة تهيئة النظام\n              </button>\n            </div>\n          </div>\n\n          <div className=\"login-footer\">\n            <div className=\"system-info\">\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🇸🇦</span>\n                <span>متوافق مع المتطلبات السعودية</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🔒</span>\n                <span>نظام آمن ومحمي</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">📱</span>\n                <span>متجاوب مع جميع الأجهزة</span>\n              </div>\n            </div>\n            \n            <div className=\"copyright\">\n              <p>© 2024 Aronim EXP. جميع الحقوق محفوظة.</p>\n              <p>نسخة 2.0.0</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"features-sidebar\">\n          <h3>مميزات النظام</h3>\n          <div className=\"features-list\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🛒</div>\n              <div className=\"feature-content\">\n                <h4>نقطة بيع متطورة</h4>\n                <p>نظام بيع سريع وفعال مع دعم الباركود</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📊</div>\n              <div className=\"feature-content\">\n                <h4>تقارير شاملة</h4>\n                <p>تقارير مالية مفصلة وإحصائيات دقيقة</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📦</div>\n              <div className=\"feature-content\">\n                <h4>إدارة المخزون</h4>\n                <p>تتبع دقيق للمخزون والتنبيهات الذكية</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <div className=\"feature-content\">\n                <h4>محاسبة متكاملة</h4>\n                <p>نظام محاسبي شامل مع ضريبة القيمة المضافة</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd;IACAsB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3DrB,YAAY,CAAC,CAAC;;IAEd;IACA,MAAMsB,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IACrE,IAAIF,kBAAkB,EAAE;MACtBb,WAAW,CAACgB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEf,QAAQ,EAAEY;MAAmB,CAAC,CAAC,CAAC;MAChEH,aAAa,CAAC,IAAI,CAAC;IACrB;;IAEA;IACA,MAAMO,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC;IACzD,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzB,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACO,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAInB,KAAK,EAAE;MACTC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC5B,QAAQ,CAACE,QAAQ,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAAC7B,QAAQ,CAACG,QAAQ,CAAC0B,IAAI,CAAC,CAAC,EAAE;MAC1DtB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEb,QAAQ,CAACE,QAAQ,CAAC;MAEtD,MAAM+B,IAAI,GAAG1C,QAAQ,CAAC2C,gBAAgB,CAAClC,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAE5ES,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoB,IAAI,CAAC;MAEpC,IAAIA,IAAI,EAAE;QACR;QACA,IAAIvB,UAAU,EAAE;UACdK,YAAY,CAACoB,OAAO,CAAC,oBAAoB,EAAEnC,QAAQ,CAACE,QAAQ,CAAC;QAC/D,CAAC,MAAM;UACLa,YAAY,CAACqB,UAAU,CAAC,oBAAoB,CAAC;QAC/C;;QAEA;QACA,IAAI;UACF7C,QAAQ,CAAC8C,WAAW,CAAC;YACnBC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAEN,IAAI,CAACO,EAAE;YACftC,QAAQ,EAAE+B,IAAI,CAAC/B,QAAQ;YACvBuC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACnCC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOC,QAAQ,EAAE;UACjBjC,OAAO,CAACkC,IAAI,CAAC,sBAAsB,EAAED,QAAQ,CAAC;QAChD;QAEA/C,OAAO,CAACmC,IAAI,CAAC;MACf,CAAC,MAAM;QACL1B,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC9C,WAAW,CAAC;MACVC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFI,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACAyB,UAAU,CAAC,MAAM;MACf,MAAMgB,IAAI,GAAG7B,QAAQ,CAAC8B,aAAa,CAAC,aAAa,CAAC;MAClD,IAAID,IAAI,EAAE;QACRA,IAAI,CAACE,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,EAAE;UAAEC,OAAO,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC,CAAC;MAC9E;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI;MACF1C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACAE,YAAY,CAACqB,UAAU,CAAC,wBAAwB,CAAC;;MAEjD;MACA7C,QAAQ,CAACgE,kBAAkB,CAAC,CAAC;;MAE7B;MACA/D,YAAY,CAAC,CAAC;MAEde,QAAQ,CAAC,EAAE,CAAC;MACZN,WAAW,CAAC;QACVC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFqD,KAAK,CAAC,qHAAqH,CAAC;IAE9H,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DC,QAAQ,CAAC,mCAAmC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIf,IAAI,CAAC,CAAC,CAACgB,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtE,OAAA;IAAKuE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BxE,OAAA;MAAKuE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxE,OAAA;QAAKuE,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1C5E,OAAA;QAAKuE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxE,OAAA;UAAKuE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrC5E,OAAA;UAAKuE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrC5E,OAAA;UAAKuE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrC5E,OAAA;UAAKuE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5E,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxE,OAAA;QAAKuE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxE,OAAA;UAAKuE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC5E,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxE,OAAA;gBAAAwE,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5E,OAAA;gBAAAwE,QAAA,EAAG;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BT,cAAc,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5E,OAAA;UAAKuE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAAwE,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB5E,OAAA;cAAAwE,QAAA,EAAG;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAELhE,KAAK,iBACJZ,OAAA;YAAKuE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxE,OAAA;cAAMuE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrChE,KAAK;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED5E,OAAA;YAAM6E,QAAQ,EAAE5C,YAAa;YAACsC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAClDxE,OAAA;cAAKuE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxE,OAAA;gBAAO8E,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9C5E,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXE,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEzB,QAAQ,CAACE,QAAS;kBACzBuE,QAAQ,EAAEnD,iBAAkB;kBAC5BoD,WAAW,EAAC,8FAAmB;kBAC/BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAEvE,OAAQ;kBAClBwE,YAAY,EAAC;gBAAU;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACF5E,OAAA;kBAAMuE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5E,OAAA;cAAKuE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxE,OAAA;gBAAO8E,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7C5E,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBACE4C,IAAI,EAAE9B,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCgC,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEzB,QAAQ,CAACG,QAAS;kBACzBsE,QAAQ,EAAEnD,iBAAkB;kBAC5BoD,WAAW,EAAC,wFAAkB;kBAC9BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAEvE,OAAQ;kBAClBwE,YAAY,EAAC;gBAAkB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACF5E,OAAA;kBACE4C,IAAI,EAAC,QAAQ;kBACb2B,SAAS,EAAC,iBAAiB;kBAC3BY,OAAO,EAAEA,CAAA,KAAMpE,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CmE,QAAQ,EAAEvE,OAAQ;kBAAA8D,QAAA,EAEjB1D,YAAY,GAAG,IAAI,GAAG;gBAAK;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5E,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BxE,OAAA;gBAAOuE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACnCxE,OAAA;kBACE4C,IAAI,EAAC,UAAU;kBACfwC,OAAO,EAAEpE,UAAW;kBACpB+D,QAAQ,EAAGlD,CAAC,IAAKZ,aAAa,CAACY,CAAC,CAACG,MAAM,CAACoD,OAAO,CAAE;kBACjDH,QAAQ,EAAEvE;gBAAQ;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACF5E,OAAA;kBAAMuE,SAAS,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,wCAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN5E,OAAA;cACE4C,IAAI,EAAC,QAAQ;cACb2B,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEvE,OAAQ;cAAA8D,QAAA,EAEjB9D,OAAO,gBACNV,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA;kBAAMuE,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mGAE3C;cAAA,eAAE,CAAC,gBAEH5E,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA;kBAAMuE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uEAEzC;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEP5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBxE,OAAA;gBAAAwE,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACN5E,OAAA;cACE4C,IAAI,EAAC,QAAQ;cACbuC,OAAO,EAAE9B,eAAgB;cACzBkB,SAAS,EAAC,aAAa;cACvBU,QAAQ,EAAEvE,OAAQ;cAAA8D,QAAA,gBAElBxE,OAAA;gBAAMuE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,8EAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5E,OAAA;cACE4C,IAAI,EAAC,QAAQ;cACbuC,OAAO,EAAEvB,mBAAoB;cAC7BW,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEvE,OAAQ;cAClB2E,KAAK,EAAC,6OAA+C;cAAAb,QAAA,gBAErDxE,OAAA;gBAAMuE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,sGAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5E,OAAA;UAAKuE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BxE,OAAA;YAAKuE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxE,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxE,OAAA;gBAAMuE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC5E,OAAA;gBAAAwE,QAAA,EAAM;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN5E,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxE,OAAA;gBAAMuE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC5E,OAAA;gBAAAwE,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACN5E,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxE,OAAA;gBAAMuE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC5E,OAAA;gBAAAwE,QAAA,EAAM;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxE,OAAA;cAAAwE,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7C5E,OAAA;cAAAwE,QAAA,EAAG;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5E,OAAA;QAAKuE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BxE,OAAA;UAAAwE,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtB5E,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC5E,OAAA;cAAKuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA;gBAAAwE,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB5E,OAAA;gBAAAwE,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC5E,OAAA;cAAKuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA;gBAAAwE,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB5E,OAAA;gBAAAwE,QAAA,EAAG;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC5E,OAAA;cAAKuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA;gBAAAwE,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB5E,OAAA;gBAAAwE,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxE,OAAA;cAAKuE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC5E,OAAA;cAAKuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA;gBAAAwE,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB5E,OAAA;gBAAAwE,QAAA,EAAG;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CAnWIF,aAAa;AAAAmF,EAAA,GAAbnF,aAAa;AAqWnB,eAAeA,aAAa;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}