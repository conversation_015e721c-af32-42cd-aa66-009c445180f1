import { useState, useEffect } from 'react';
import database from '../../utils/database';
import './AdvancedReports.css';

const AdvancedReports = () => {
  const [reportType, setReportType] = useState('income_statement');
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);

  const reportTypes = [
    { value: 'income_statement', label: '📈 قائمة الدخل', icon: '📈' },
    { value: 'balance_sheet', label: '⚖️ الميزانية العمومية', icon: '⚖️' },
    { value: 'cash_flow', label: '💸 التدفقات النقدية', icon: '💸' },
    { value: 'trial_balance', label: '📋 ميزان المراجعة', icon: '📋' },
    { value: 'daily_summary', label: '📅 الملخص اليومي', icon: '📅' },
    { value: 'tax_report', label: '🧾 التقرير الضريبي', icon: '🧾' },
    { value: 'account_statement', label: '📄 كشف حساب', icon: '📄' },
    { value: 'top_products', label: '🏆 المنتجات الأكثر مبيعاً', icon: '🏆' },
    { value: 'slow_products', label: '🐌 المنتجات بطيئة الحركة', icon: '🐌' },
    { value: 'profit_analysis', label: '💹 تحليل الربحية', icon: '💹' },
    { value: 'shift_closure', label: '🔒 إغلاق الوردية', icon: '🔒' },
    { value: 'sales_detailed', label: '📊 تقرير المبيعات التفصيلي', icon: '📊' },
    { value: 'purchases_detailed', label: '🛒 تقرير المشتريات التفصيلي', icon: '🛒' },
    { value: 'inventory_valuation', label: '💰 تقييم المخزون', icon: '💰' },
    { value: 'customer_aging', label: '👥 أعمار ديون العملاء', icon: '👥' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const customersData = database.getCustomers();
      const productsData = database.getProducts();
      
      setCustomers(customersData);
      setProducts(productsData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    }
  };

  const generateReport = async () => {
    setLoading(true);
    try {
      let data = null;
      
      switch (reportType) {
        case 'income_statement':
          data = generateIncomeStatement();
          break;
        case 'balance_sheet':
          data = generateBalanceSheet();
          break;
        case 'cash_flow':
          data = generateCashFlowStatement();
          break;
        case 'trial_balance':
          data = generateTrialBalance();
          break;
        case 'daily_summary':
          data = generateDailySummary();
          break;
        case 'tax_report':
          data = generateTaxReport();
          break;
        case 'account_statement':
          data = generateAccountStatement();
          break;
        case 'top_products':
          data = generateTopProductsReport();
          break;
        case 'slow_products':
          data = generateSlowProductsReport();
          break;
        case 'profit_analysis':
          data = generateProfitAnalysis();
          break;
        case 'shift_closure':
          data = generateShiftClosure();
          break;
        case 'sales_detailed':
          data = generateDetailedSalesReport();
          break;
        case 'purchases_detailed':
          data = generateDetailedPurchasesReport();
          break;
        case 'inventory_valuation':
          data = generateInventoryValuation();
          break;
        case 'customer_aging':
          data = generateCustomerAging();
          break;
        default:
          data = { error: 'نوع التقرير غير مدعوم' };
      }
      
      setReportData(data);
    } catch (error) {
      console.error('خطأ في إنشاء التقرير:', error);
      setReportData({ error: 'خطأ في إنشاء التقرير' });
    } finally {
      setLoading(false);
    }
  };

  const generateIncomeStatement = () => {
    const invoices = database.getInvoices();
    const expenses = database.getVouchers().filter(v => v.type === 'payment');
    
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    // الإيرادات
    const salesRevenue = invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .reduce((sum, inv) => sum + (inv.total - inv.vatAmount), 0);
    
    const vatCollected = invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .reduce((sum, inv) => sum + inv.vatAmount, 0);
    
    // تكلفة البضاعة المباعة
    const costOfGoodsSold = invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .reduce((sum, inv) => {
        return sum + inv.items.reduce((itemSum, item) => {
          const product = products.find(p => p.id === item.productId);
          return itemSum + (item.quantity * (product?.cost || product?.price * 0.7 || 0));
        }, 0);
      }, 0);
    
    // المصروفات
    const operatingExpenses = expenses
      .filter(exp => {
        const expDate = new Date(exp.voucherDate);
        return expDate >= startDate && expDate <= endDate;
      })
      .reduce((sum, exp) => sum + exp.amount, 0);
    
    const grossProfit = salesRevenue - costOfGoodsSold;
    const netProfit = grossProfit - operatingExpenses;
    
    return {
      title: 'قائمة الدخل',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        revenue: {
          salesRevenue,
          vatCollected,
          totalRevenue: salesRevenue + vatCollected
        },
        costs: {
          costOfGoodsSold,
          operatingExpenses,
          totalCosts: costOfGoodsSold + operatingExpenses
        },
        profit: {
          grossProfit,
          grossProfitMargin: salesRevenue > 0 ? (grossProfit / salesRevenue * 100) : 0,
          netProfit,
          netProfitMargin: salesRevenue > 0 ? (netProfit / salesRevenue * 100) : 0
        }
      }
    };
  };

  const generateBalanceSheet = () => {
    const accounts = database.getAccounts();
    const products = database.getProducts();
    const customers = database.getCustomers();
    const suppliers = database.getSuppliers();
    
    // الأصول
    const cashAccounts = accounts.filter(acc => 
      acc.type === 'asset' && 
      (acc.name.includes('نقدية') || acc.name.includes('صندوق') || acc.name.includes('بنك'))
    );
    const totalCash = cashAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    const inventoryValue = products.reduce((sum, product) => 
      sum + (product.stock * product.price), 0);
    
    const accountsReceivable = customers.reduce((sum, customer) => 
      sum + (customer.balance || 0), 0);
    
    const fixedAssets = accounts
      .filter(acc => acc.type === 'asset' && !acc.name.includes('نقدية') && !acc.name.includes('صندوق'))
      .reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    const totalAssets = totalCash + inventoryValue + accountsReceivable + fixedAssets;
    
    // الخصوم
    const accountsPayable = suppliers.reduce((sum, supplier) => 
      sum + (supplier.balance || 0), 0);
    
    const liabilityAccounts = accounts.filter(acc => acc.type === 'liability');
    const totalLiabilities = liabilityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0) + accountsPayable;
    
    // حقوق الملكية
    const equityAccounts = accounts.filter(acc => acc.type === 'equity');
    const totalEquity = equityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);
    
    const retainedEarnings = totalAssets - totalLiabilities - totalEquity;
    
    return {
      title: 'الميزانية العمومية',
      date: new Date().toLocaleDateString('ar-SA'),
      data: {
        assets: {
          currentAssets: {
            cash: totalCash,
            inventory: inventoryValue,
            accountsReceivable,
            total: totalCash + inventoryValue + accountsReceivable
          },
          fixedAssets,
          totalAssets
        },
        liabilities: {
          accountsPayable,
          otherLiabilities: totalLiabilities - accountsPayable,
          totalLiabilities
        },
        equity: {
          capital: totalEquity,
          retainedEarnings,
          totalEquity: totalEquity + retainedEarnings
        }
      }
    };
  };

  const generateCashFlowStatement = () => {
    const vouchers = database.getVouchers();
    
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    // التدفقات النقدية من الأنشطة التشغيلية
    const cashFromSales = vouchers
      .filter(v => v.type === 'receipt' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate)
      .reduce((sum, v) => sum + v.amount, 0);
    
    const cashToPurchases = vouchers
      .filter(v => v.type === 'payment' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate)
      .reduce((sum, v) => sum + v.amount, 0);
    
    const netCashFromOperations = cashFromSales - cashToPurchases;
    
    return {
      title: 'قائمة التدفقات النقدية',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        operating: {
          cashFromSales,
          cashToPurchases,
          netCashFromOperations
        },
        investing: {
          // يمكن إضافة الاستثمارات هنا
          netCashFromInvesting: 0
        },
        financing: {
          // يمكن إضافة التمويل هنا
          netCashFromFinancing: 0
        },
        netCashFlow: netCashFromOperations
      }
    };
  };

  const generateTrialBalance = () => {
    const accounts = database.getAccounts();
    const journalEntries = database.getJournalEntries().filter(entry => entry.status === 'posted');
    
    const trialBalance = accounts.map(account => {
      let debitTotal = 0;
      let creditTotal = 0;
      
      journalEntries.forEach(entry => {
        if (entry.debitEntries) {
          entry.debitEntries.forEach(debit => {
            if (parseInt(debit.accountId) === account.id) {
              debitTotal += parseFloat(debit.amount);
            }
          });
        }
        
        if (entry.creditEntries) {
          entry.creditEntries.forEach(credit => {
            if (parseInt(credit.accountId) === account.id) {
              creditTotal += parseFloat(credit.amount);
            }
          });
        }
      });
      
      return {
        accountCode: account.code,
        accountName: account.name,
        accountType: account.type,
        debitBalance: debitTotal > creditTotal ? debitTotal - creditTotal : 0,
        creditBalance: creditTotal > debitTotal ? creditTotal - debitTotal : 0
      };
    });
    
    const totalDebits = trialBalance.reduce((sum, acc) => sum + acc.debitBalance, 0);
    const totalCredits = trialBalance.reduce((sum, acc) => sum + acc.creditBalance, 0);
    
    return {
      title: 'ميزان المراجعة',
      date: new Date().toLocaleDateString('ar-SA'),
      data: {
        accounts: trialBalance,
        totals: {
          totalDebits,
          totalCredits,
          isBalanced: Math.abs(totalDebits - totalCredits) < 0.01
        }
      }
    };
  };

  const generateDailySummary = () => {
    const selectedDate = new Date(dateRange.start);
    const invoices = database.getInvoices().filter(inv => 
      new Date(inv.invoiceDate).toDateString() === selectedDate.toDateString()
    );
    const vouchers = database.getVouchers().filter(v => 
      new Date(v.voucherDate).toDateString() === selectedDate.toDateString()
    );
    
    const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);
    const totalReceipts = vouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);
    const totalPayments = vouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);
    const netCash = totalReceipts - totalPayments;
    
    return {
      title: 'الملخص اليومي',
      date: selectedDate.toLocaleDateString('ar-SA'),
      data: {
        sales: {
          invoicesCount: invoices.length,
          totalSales,
          averageInvoice: invoices.length > 0 ? totalSales / invoices.length : 0
        },
        cash: {
          receipts: totalReceipts,
          payments: totalPayments,
          netCash
        },
        topProducts: getTopProductsForDate(selectedDate)
      }
    };
  };

  const generateTaxReport = () => {
    const invoices = database.getInvoices();
    const purchases = database.getPurchaseInvoices();
    
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    const salesVAT = invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .reduce((sum, inv) => sum + inv.vatAmount, 0);
    
    const purchasesVAT = purchases
      .filter(pur => {
        const purDate = new Date(pur.invoiceDate);
        return purDate >= startDate && purDate <= endDate;
      })
      .reduce((sum, pur) => sum + (pur.vatAmount || 0), 0);
    
    const netVAT = salesVAT - purchasesVAT;
    
    return {
      title: 'التقرير الضريبي',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        salesVAT,
        purchasesVAT,
        netVAT,
        vatRate: 15,
        taxableAmount: salesVAT / 0.15
      }
    };
  };

  const generateTopProductsReport = () => {
    const invoices = database.getInvoices();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    const productSales = {};
    
    invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .forEach(invoice => {
        invoice.items.forEach(item => {
          const product = products.find(p => p.id === item.productId);
          if (product) {
            if (!productSales[product.id]) {
              productSales[product.id] = {
                name: product.name,
                quantity: 0,
                revenue: 0,
                profit: 0
              };
            }
            productSales[product.id].quantity += item.quantity;
            productSales[product.id].revenue += item.total;
            productSales[product.id].profit += item.total - (item.quantity * (product.cost || product.price * 0.7));
          }
        });
      });
    
    const sortedProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
    
    return {
      title: 'المنتجات الأكثر مبيعاً',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: sortedProducts
    };
  };

  const generateSlowProductsReport = () => {
    const invoices = database.getInvoices();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    const productSales = {};
    
    // حساب مبيعات كل منتج
    invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .forEach(invoice => {
        invoice.items.forEach(item => {
          if (!productSales[item.productId]) {
            productSales[item.productId] = 0;
          }
          productSales[item.productId] += item.quantity;
        });
      });
    
    // العثور على المنتجات بطيئة الحركة
    const slowProducts = products
      .filter(product => (productSales[product.id] || 0) < 5) // أقل من 5 قطع
      .map(product => ({
        name: product.name,
        stock: product.stock,
        soldQuantity: productSales[product.id] || 0,
        stockValue: product.stock * product.price
      }))
      .sort((a, b) => b.stockValue - a.stockValue);
    
    return {
      title: 'المنتجات بطيئة الحركة',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: slowProducts
    };
  };

  const generateProfitAnalysis = () => {
    const invoices = database.getInvoices();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    let totalRevenue = 0;
    let totalCost = 0;
    let totalProfit = 0;
    
    const productProfits = {};
    
    invoices
      .filter(inv => {
        const invDate = new Date(inv.invoiceDate);
        return invDate >= startDate && invDate <= endDate;
      })
      .forEach(invoice => {
        invoice.items.forEach(item => {
          const product = products.find(p => p.id === item.productId);
          if (product) {
            const revenue = item.total;
            const cost = item.quantity * (product.cost || product.price * 0.7);
            const profit = revenue - cost;
            
            totalRevenue += revenue;
            totalCost += cost;
            totalProfit += profit;
            
            if (!productProfits[product.id]) {
              productProfits[product.id] = {
                name: product.name,
                revenue: 0,
                cost: 0,
                profit: 0,
                margin: 0
              };
            }
            
            productProfits[product.id].revenue += revenue;
            productProfits[product.id].cost += cost;
            productProfits[product.id].profit += profit;
            productProfits[product.id].margin = 
              productProfits[product.id].revenue > 0 ? 
              (productProfits[product.id].profit / productProfits[product.id].revenue * 100) : 0;
          }
        });
      });
    
    const overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0;
    
    return {
      title: 'تحليل الربحية',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        overall: {
          totalRevenue,
          totalCost,
          totalProfit,
          overallMargin
        },
        products: Object.values(productProfits)
          .sort((a, b) => b.profit - a.profit)
          .slice(0, 10)
      }
    };
  };

  const generateShiftClosure = () => {
    const today = new Date().toDateString();
    const todayInvoices = database.getInvoices().filter(inv => 
      new Date(inv.invoiceDate).toDateString() === today
    );
    const todayVouchers = database.getVouchers().filter(v => 
      new Date(v.voucherDate).toDateString() === today
    );
    
    const totalSales = todayInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const totalReceipts = todayVouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);
    const totalPayments = todayVouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);
    
    // تجميع المبيعات حسب طريقة الدفع
    const paymentMethods = {};
    todayInvoices.forEach(invoice => {
      const method = invoice.paymentMethod || 'نقداً';
      if (!paymentMethods[method]) {
        paymentMethods[method] = 0;
      }
      paymentMethods[method] += invoice.total;
    });
    
    return {
      title: 'إغلاق الوردية',
      date: new Date().toLocaleDateString('ar-SA'),
      time: new Date().toLocaleTimeString('ar-SA'),
      data: {
        sales: {
          invoicesCount: todayInvoices.length,
          totalSales,
          paymentMethods
        },
        cash: {
          receipts: totalReceipts,
          payments: totalPayments,
          netCash: totalReceipts - totalPayments
        },
        summary: {
          openingBalance: 0, // يمكن إضافة رصيد افتتاحي
          totalIncome: totalSales + totalReceipts,
          totalExpenses: totalPayments,
          closingBalance: totalSales + totalReceipts - totalPayments
        }
      }
    };
  };

  const getTopProductsForDate = (date) => {
    const invoices = database.getInvoices().filter(inv =>
      new Date(inv.invoiceDate).toDateString() === date.toDateString()
    );

    const productSales = {};

    invoices.forEach(invoice => {
      invoice.items.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          if (!productSales[product.id]) {
            productSales[product.id] = {
              name: product.name,
              quantity: 0,
              revenue: 0
            };
          }
          productSales[product.id].quantity += item.quantity;
          productSales[product.id].revenue += item.total;
        }
      });
    });

    return Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  };

  const generateAccountStatement = () => {
    const accounts = database.getAccounts();
    const journalEntries = database.getJournalEntries();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);

    let selectedAccount = null;
    if (selectedCustomer) {
      selectedAccount = accounts.find(acc => acc.id === parseInt(selectedCustomer));
    }

    if (!selectedAccount) {
      return {
        title: 'كشف حساب',
        error: 'يرجى اختيار حساب لعرض كشف الحساب'
      };
    }

    const accountMovements = [];
    let runningBalance = 0;

    journalEntries
      .filter(entry => {
        const entryDate = new Date(entry.date);
        return entryDate >= startDate && entryDate <= endDate && entry.status === 'posted';
      })
      .forEach(entry => {
        // Check debit entries
        if (entry.debitEntries) {
          entry.debitEntries.forEach(debit => {
            if (parseInt(debit.accountId) === selectedAccount.id) {
              runningBalance += parseFloat(debit.amount);
              accountMovements.push({
                date: entry.date,
                description: entry.description,
                reference: entry.reference,
                debit: parseFloat(debit.amount),
                credit: 0,
                balance: runningBalance
              });
            }
          });
        }

        // Check credit entries
        if (entry.creditEntries) {
          entry.creditEntries.forEach(credit => {
            if (parseInt(credit.accountId) === selectedAccount.id) {
              runningBalance -= parseFloat(credit.amount);
              accountMovements.push({
                date: entry.date,
                description: entry.description,
                reference: entry.reference,
                debit: 0,
                credit: parseFloat(credit.amount),
                balance: runningBalance
              });
            }
          });
        }
      });

    return {
      title: `كشف حساب - ${selectedAccount.name}`,
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        account: selectedAccount,
        movements: accountMovements.sort((a, b) => new Date(a.date) - new Date(b.date)),
        openingBalance: 0,
        closingBalance: runningBalance
      }
    };
  };

  const generateDetailedSalesReport = () => {
    const invoices = database.getInvoices();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);

    const filteredInvoices = invoices.filter(inv => {
      const invDate = new Date(inv.invoiceDate);
      return invDate >= startDate && invDate <= endDate;
    });

    const salesData = filteredInvoices.map(invoice => {
      const customer = customers.find(c => c.id === invoice.customerId);
      return {
        invoiceNumber: invoice.invoiceNumber,
        date: invoice.invoiceDate,
        customerName: customer?.name || 'غير محدد',
        items: invoice.items.map(item => {
          const product = products.find(p => p.id === item.productId);
          return {
            productName: product?.name || 'غير محدد',
            quantity: item.quantity,
            price: item.price,
            total: item.total
          };
        }),
        subtotal: invoice.subtotal,
        vatAmount: invoice.vatAmount,
        total: invoice.total
      };
    });

    const totalSales = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const totalVAT = filteredInvoices.reduce((sum, inv) => sum + inv.vatAmount, 0);

    return {
      title: 'تقرير المبيعات التفصيلي',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        invoices: salesData,
        summary: {
          totalInvoices: filteredInvoices.length,
          totalSales,
          totalVAT,
          averageInvoice: filteredInvoices.length > 0 ? totalSales / filteredInvoices.length : 0
        }
      }
    };
  };

  const generateDetailedPurchasesReport = () => {
    const purchases = database.getPurchaseInvoices();
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);

    const filteredPurchases = purchases.filter(pur => {
      const purDate = new Date(pur.invoiceDate);
      return purDate >= startDate && purDate <= endDate;
    });

    const purchasesData = filteredPurchases.map(purchase => {
      const supplier = database.getSuppliers().find(s => s.id === purchase.supplierId);
      return {
        invoiceNumber: purchase.invoiceNumber,
        date: purchase.invoiceDate,
        supplierName: supplier?.name || 'غير محدد',
        items: purchase.items?.map(item => {
          const product = products.find(p => p.id === item.productId);
          return {
            productName: product?.name || 'غير محدد',
            quantity: item.quantity,
            price: item.price,
            total: item.total
          };
        }) || [],
        subtotal: purchase.subtotal || 0,
        vatAmount: purchase.vatAmount || 0,
        total: purchase.total || 0
      };
    });

    const totalPurchases = filteredPurchases.reduce((sum, pur) => sum + (pur.total || 0), 0);
    const totalVAT = filteredPurchases.reduce((sum, pur) => sum + (pur.vatAmount || 0), 0);

    return {
      title: 'تقرير المشتريات التفصيلي',
      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,
      data: {
        purchases: purchasesData,
        summary: {
          totalPurchases: filteredPurchases.length,
          totalAmount: totalPurchases,
          totalVAT,
          averagePurchase: filteredPurchases.length > 0 ? totalPurchases / filteredPurchases.length : 0
        }
      }
    };
  };

  const generateInventoryValuation = () => {
    const inventoryData = products.map(product => {
      const cost = product.cost || product.price * 0.7; // افتراض التكلفة 70% من السعر
      const totalValue = product.stock * product.price;
      const totalCost = product.stock * cost;
      const potentialProfit = totalValue - totalCost;

      return {
        name: product.name,
        category: product.category,
        stock: product.stock,
        unitPrice: product.price,
        unitCost: cost,
        totalValue,
        totalCost,
        potentialProfit,
        profitMargin: totalValue > 0 ? (potentialProfit / totalValue * 100) : 0
      };
    });

    const totalInventoryValue = inventoryData.reduce((sum, item) => sum + item.totalValue, 0);
    const totalInventoryCost = inventoryData.reduce((sum, item) => sum + item.totalCost, 0);
    const totalPotentialProfit = totalInventoryValue - totalInventoryCost;

    return {
      title: 'تقييم المخزون',
      date: new Date().toLocaleDateString('ar-SA'),
      data: {
        items: inventoryData.sort((a, b) => b.totalValue - a.totalValue),
        summary: {
          totalItems: products.length,
          totalInventoryValue,
          totalInventoryCost,
          totalPotentialProfit,
          overallMargin: totalInventoryValue > 0 ? (totalPotentialProfit / totalInventoryValue * 100) : 0
        }
      }
    };
  };

  const generateCustomerAging = () => {
    const invoices = database.getInvoices();
    const payments = database.getVouchers().filter(v => v.type === 'receipt');
    const today = new Date();

    const customerBalances = {};

    // حساب أرصدة العملاء من الفواتير
    invoices.forEach(invoice => {
      const customer = customers.find(c => c.id === invoice.customerId);
      if (customer) {
        if (!customerBalances[customer.id]) {
          customerBalances[customer.id] = {
            name: customer.name,
            phone: customer.phone,
            email: customer.email,
            totalBalance: 0,
            current: 0,
            days30: 0,
            days60: 0,
            days90: 0,
            over90: 0
          };
        }

        const invoiceDate = new Date(invoice.invoiceDate);
        const daysDiff = Math.floor((today - invoiceDate) / (1000 * 60 * 60 * 24));

        // تصنيف الديون حسب العمر
        if (daysDiff <= 30) {
          customerBalances[customer.id].current += invoice.total;
        } else if (daysDiff <= 60) {
          customerBalances[customer.id].days30 += invoice.total;
        } else if (daysDiff <= 90) {
          customerBalances[customer.id].days60 += invoice.total;
        } else {
          customerBalances[customer.id].over90 += invoice.total;
        }

        customerBalances[customer.id].totalBalance += invoice.total;
      }
    });

    // خصم المدفوعات
    payments.forEach(payment => {
      if (payment.customerId && customerBalances[payment.customerId]) {
        customerBalances[payment.customerId].totalBalance -= payment.amount;
        // توزيع الدفعة على الفترات (الأقدم أولاً)
        let remainingPayment = payment.amount;

        if (remainingPayment > 0 && customerBalances[payment.customerId].over90 > 0) {
          const deduction = Math.min(remainingPayment, customerBalances[payment.customerId].over90);
          customerBalances[payment.customerId].over90 -= deduction;
          remainingPayment -= deduction;
        }

        if (remainingPayment > 0 && customerBalances[payment.customerId].days60 > 0) {
          const deduction = Math.min(remainingPayment, customerBalances[payment.customerId].days60);
          customerBalances[payment.customerId].days60 -= deduction;
          remainingPayment -= deduction;
        }

        if (remainingPayment > 0 && customerBalances[payment.customerId].days30 > 0) {
          const deduction = Math.min(remainingPayment, customerBalances[payment.customerId].days30);
          customerBalances[payment.customerId].days30 -= deduction;
          remainingPayment -= deduction;
        }

        if (remainingPayment > 0 && customerBalances[payment.customerId].current > 0) {
          const deduction = Math.min(remainingPayment, customerBalances[payment.customerId].current);
          customerBalances[payment.customerId].current -= deduction;
        }
      }
    });

    const agingData = Object.values(customerBalances)
      .filter(customer => customer.totalBalance > 0)
      .sort((a, b) => b.totalBalance - a.totalBalance);

    const totals = agingData.reduce((sum, customer) => ({
      totalBalance: sum.totalBalance + customer.totalBalance,
      current: sum.current + customer.current,
      days30: sum.days30 + customer.days30,
      days60: sum.days60 + customer.days60,
      over90: sum.over90 + customer.over90
    }), { totalBalance: 0, current: 0, days30: 0, days60: 0, over90: 0 });

    return {
      title: 'أعمار ديون العملاء',
      date: today.toLocaleDateString('ar-SA'),
      data: {
        customers: agingData,
        totals,
        summary: {
          totalCustomers: agingData.length,
          averageBalance: agingData.length > 0 ? totals.totalBalance / agingData.length : 0
        }
      }
    };
  };

  const printReport = () => {
    if (!reportData) return;
    
    const printWindow = window.open('', '_blank');
    const settings = database.getSettings();
    
    let printContent = `
      <html>
        <head>
          <title>${reportData.title}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              direction: rtl; 
              background: white;
            }
            .header { 
              text-align: center; 
              border-bottom: 2px solid #333; 
              padding-bottom: 20px; 
              margin-bottom: 30px; 
            }
            .company-info { 
              text-align: center; 
              margin-bottom: 20px; 
            }
            .report-title {
              font-size: 1.8em;
              font-weight: bold;
              color: #333;
              margin: 15px 0;
            }
            .report-period {
              font-size: 1.1em;
              color: #666;
              margin-bottom: 20px;
            }
            .section {
              margin: 20px 0;
              padding: 15px;
              border: 1px solid #ddd;
              background: #f9f9f9;
            }
            .section h3 {
              margin: 0 0 15px 0;
              color: #333;
              border-bottom: 1px solid #ccc;
              padding-bottom: 5px;
            }
            .data-table {
              width: 100%;
              border-collapse: collapse;
              margin: 10px 0;
            }
            .data-table th,
            .data-table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: right;
            }
            .data-table th {
              background: #f5f5f5;
              font-weight: bold;
            }
            .total-row {
              font-weight: bold;
              background: #e9e9e9;
            }
            .positive { color: #10b981; }
            .negative { color: #ef4444; }
            @media print { 
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>${settings.companyName}</h2>
            <div class="company-info">
              <p>${settings.address}</p>
              <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>
              <p>الرقم الضريبي: ${settings.vatNumber}</p>
            </div>
            <div class="report-title">${reportData.title}</div>
            ${reportData.period ? `<div class="report-period">${reportData.period}</div>` : ''}
            ${reportData.date ? `<div class="report-period">بتاريخ: ${reportData.date}</div>` : ''}
          </div>
    `;
    
    // إضافة محتوى التقرير حسب النوع
    printContent += generateReportHTML(reportData);
    
    printContent += `
          <div style="margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;">
            <p>تم إنشاء هذا التقرير بواسطة نظام المحاسبة السعودي</p>
            <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
          </div>
        </body>
      </html>
    `;
    
    printWindow.document.documentElement.innerHTML = printContent;
    setTimeout(() => printWindow.print(), 500);
  };

  const generateReportHTML = (data) => {
    // هذه دالة مساعدة لتوليد HTML للتقرير
    // يمكن توسيعها حسب نوع التقرير
    return `
      <div class="section">
        <pre>${JSON.stringify(data.data, null, 2)}</pre>
      </div>
    `;
  };

  const exportToExcel = () => {
    if (!reportData) return;
    
    // تحويل البيانات إلى CSV
    const csvContent = convertToCSV(reportData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${reportData.title}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const convertToCSV = (data) => {
    // تحويل بسيط للبيانات إلى CSV
    return JSON.stringify(data.data, null, 2);
  };

  return (
    <div className="advanced-reports-container">
      <div className="reports-header">
        <h1>📊 التقارير المتقدمة</h1>
        <p>تقارير مالية ومحاسبية شاملة ومتقدمة</p>
      </div>

      {/* Report Selection */}
      <div className="report-selection">
        <div className="report-types-grid">
          {reportTypes.map(type => (
            <button
              key={type.value}
              className={`report-type-card ${reportType === type.value ? 'active' : ''}`}
              onClick={() => setReportType(type.value)}
            >
              <div className="report-icon">{type.icon}</div>
              <div className="report-label">{type.label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Report Controls */}
      <div className="report-controls">
        <div className="date-controls">
          <div className="form-group">
            <label>من تاريخ</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            />
          </div>
          <div className="form-group">
            <label>إلى تاريخ</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            />
          </div>
        </div>

        {(reportType === 'account_statement') && (
          <div className="filter-controls">
            <div className="form-group">
              <label>العميل</label>
              <select
                value={selectedCustomer}
                onChange={(e) => setSelectedCustomer(e.target.value)}
              >
                <option value="">جميع العملاء</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}

        <div className="action-controls">
          <button
            onClick={generateReport}
            disabled={loading}
            className="generate-btn"
          >
            {loading ? '⏳ جاري الإنشاء...' : '📊 إنشاء التقرير'}
          </button>
          {reportData && (
            <>
              <button onClick={printReport} className="print-btn">
                🖨️ طباعة
              </button>
              <button onClick={exportToExcel} className="export-btn">
                📤 تصدير Excel
              </button>
            </>
          )}
        </div>
      </div>

      {/* Report Display */}
      {reportData && (
        <div className="report-display">
          <div className="report-header-info">
            <h2>{reportData.title}</h2>
            {reportData.period && <p className="report-period">{reportData.period}</p>}
            {reportData.date && <p className="report-date">بتاريخ: {reportData.date}</p>}
          </div>

          <div className="report-content">
            {reportData.error ? (
              <div className="error-message">
                ❌ {reportData.error}
              </div>
            ) : (
              <div className="report-data">
                <pre>{JSON.stringify(reportData.data, null, 2)}</pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedReports;
