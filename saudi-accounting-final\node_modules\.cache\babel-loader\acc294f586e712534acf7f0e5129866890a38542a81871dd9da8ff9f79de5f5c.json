{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\AdvancedReports\\\\AdvancedReports.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './AdvancedReports.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdvancedReports = () => {\n  _s();\n  const [reportType, setReportType] = useState('income_statement');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [reportData, setReportData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [selectedCustomer, setSelectedCustomer] = useState('');\n  const [selectedProduct, setSelectedProduct] = useState('');\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const reportTypes = [{\n    value: 'income_statement',\n    label: '📈 قائمة الدخل',\n    icon: '📈'\n  }, {\n    value: 'balance_sheet',\n    label: '⚖️ الميزانية العمومية',\n    icon: '⚖️'\n  }, {\n    value: 'cash_flow',\n    label: '💸 التدفقات النقدية',\n    icon: '💸'\n  }, {\n    value: 'trial_balance',\n    label: '📋 ميزان المراجعة',\n    icon: '📋'\n  }, {\n    value: 'daily_summary',\n    label: '📅 الملخص اليومي',\n    icon: '📅'\n  }, {\n    value: 'tax_report',\n    label: '🧾 التقرير الضريبي',\n    icon: '🧾'\n  }, {\n    value: 'account_statement',\n    label: '📄 كشف حساب',\n    icon: '📄'\n  }, {\n    value: 'top_products',\n    label: '🏆 المنتجات الأكثر مبيعاً',\n    icon: '🏆'\n  }, {\n    value: 'slow_products',\n    label: '🐌 المنتجات بطيئة الحركة',\n    icon: '🐌'\n  }, {\n    value: 'profit_analysis',\n    label: '💹 تحليل الربحية',\n    icon: '💹'\n  }, {\n    value: 'shift_closure',\n    label: '🔒 إغلاق الوردية',\n    icon: '🔒'\n  }, {\n    value: 'sales_detailed',\n    label: '📊 تقرير المبيعات التفصيلي',\n    icon: '📊'\n  }, {\n    value: 'purchases_detailed',\n    label: '🛒 تقرير المشتريات التفصيلي',\n    icon: '🛒'\n  }, {\n    value: 'inventory_valuation',\n    label: '💰 تقييم المخزون',\n    icon: '💰'\n  }, {\n    value: 'customer_aging',\n    label: '👥 أعمار ديون العملاء',\n    icon: '👥'\n  }];\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = () => {\n    try {\n      const customersData = database.getCustomers();\n      const productsData = database.getProducts();\n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n    }\n  };\n  const generateReport = async () => {\n    setLoading(true);\n    try {\n      let data = null;\n      switch (reportType) {\n        case 'income_statement':\n          data = generateIncomeStatement();\n          break;\n        case 'balance_sheet':\n          data = generateBalanceSheet();\n          break;\n        case 'cash_flow':\n          data = generateCashFlowStatement();\n          break;\n        case 'trial_balance':\n          data = generateTrialBalance();\n          break;\n        case 'daily_summary':\n          data = generateDailySummary();\n          break;\n        case 'tax_report':\n          data = generateTaxReport();\n          break;\n        case 'account_statement':\n          data = generateAccountStatement();\n          break;\n        case 'top_products':\n          data = generateTopProductsReport();\n          break;\n        case 'slow_products':\n          data = generateSlowProductsReport();\n          break;\n        case 'profit_analysis':\n          data = generateProfitAnalysis();\n          break;\n        case 'shift_closure':\n          data = generateShiftClosure();\n          break;\n        case 'sales_detailed':\n          data = generateDetailedSalesReport();\n          break;\n        case 'purchases_detailed':\n          data = generateDetailedPurchasesReport();\n          break;\n        case 'inventory_valuation':\n          data = generateInventoryValuation();\n          break;\n        case 'customer_aging':\n          data = generateCustomerAging();\n          break;\n        default:\n          data = {\n            error: 'نوع التقرير غير مدعوم'\n          };\n      }\n      setReportData(data);\n    } catch (error) {\n      console.error('خطأ في إنشاء التقرير:', error);\n      setReportData({\n        error: 'خطأ في إنشاء التقرير'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateIncomeStatement = () => {\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    const expenses = database.getVouchers().filter(v => v.type === 'payment');\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n\n    // الإيرادات\n    const salesRevenue = invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).reduce((sum, inv) => sum + (inv.total - inv.vatAmount), 0);\n    const vatCollected = invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).reduce((sum, inv) => sum + inv.vatAmount, 0);\n\n    // تكلفة البضاعة المباعة\n    const costOfGoodsSold = invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).reduce((sum, inv) => {\n      return sum + inv.items.reduce((itemSum, item) => {\n        const product = products.find(p => p.id === item.productId);\n        return itemSum + item.quantity * ((product === null || product === void 0 ? void 0 : product.cost) || (product === null || product === void 0 ? void 0 : product.price) * 0.7 || 0);\n      }, 0);\n    }, 0);\n\n    // المصروفات\n    const operatingExpenses = expenses.filter(exp => {\n      const expDate = new Date(exp.voucherDate);\n      return expDate >= startDate && expDate <= endDate;\n    }).reduce((sum, exp) => sum + exp.amount, 0);\n    const grossProfit = salesRevenue - costOfGoodsSold;\n    const netProfit = grossProfit - operatingExpenses;\n    return {\n      title: 'قائمة الدخل',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        revenue: {\n          salesRevenue,\n          vatCollected,\n          totalRevenue: salesRevenue + vatCollected\n        },\n        costs: {\n          costOfGoodsSold,\n          operatingExpenses,\n          totalCosts: costOfGoodsSold + operatingExpenses\n        },\n        profit: {\n          grossProfit,\n          grossProfitMargin: salesRevenue > 0 ? grossProfit / salesRevenue * 100 : 0,\n          netProfit,\n          netProfitMargin: salesRevenue > 0 ? netProfit / salesRevenue * 100 : 0\n        }\n      }\n    };\n  };\n  const generateBalanceSheet = () => {\n    const accounts = database.getAccounts();\n    const products = database.getProducts();\n    const customers = database.getCustomers();\n    const suppliers = database.getSuppliers();\n\n    // الأصول\n    const cashAccounts = accounts.filter(acc => acc.type === 'asset' && (acc.name.includes('نقدية') || acc.name.includes('صندوق') || acc.name.includes('بنك')));\n    const totalCash = cashAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    const inventoryValue = products.reduce((sum, product) => sum + product.stock * product.price, 0);\n    const accountsReceivable = customers.reduce((sum, customer) => sum + (customer.balance || 0), 0);\n    const fixedAssets = accounts.filter(acc => acc.type === 'asset' && !acc.name.includes('نقدية') && !acc.name.includes('صندوق')).reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    const totalAssets = totalCash + inventoryValue + accountsReceivable + fixedAssets;\n\n    // الخصوم\n    const accountsPayable = suppliers.reduce((sum, supplier) => sum + (supplier.balance || 0), 0);\n    const liabilityAccounts = accounts.filter(acc => acc.type === 'liability');\n    const totalLiabilities = liabilityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0) + accountsPayable;\n\n    // حقوق الملكية\n    const equityAccounts = accounts.filter(acc => acc.type === 'equity');\n    const totalEquity = equityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    const retainedEarnings = totalAssets - totalLiabilities - totalEquity;\n    return {\n      title: 'الميزانية العمومية',\n      date: new Date().toLocaleDateString('ar-SA'),\n      data: {\n        assets: {\n          currentAssets: {\n            cash: totalCash,\n            inventory: inventoryValue,\n            accountsReceivable,\n            total: totalCash + inventoryValue + accountsReceivable\n          },\n          fixedAssets,\n          totalAssets\n        },\n        liabilities: {\n          accountsPayable,\n          otherLiabilities: totalLiabilities - accountsPayable,\n          totalLiabilities\n        },\n        equity: {\n          capital: totalEquity,\n          retainedEarnings,\n          totalEquity: totalEquity + retainedEarnings\n        }\n      }\n    };\n  };\n  const generateCashFlowStatement = () => {\n    const vouchers = database.getVouchers();\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n\n    // التدفقات النقدية من الأنشطة التشغيلية\n    const cashFromSales = vouchers.filter(v => v.type === 'receipt' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate).reduce((sum, v) => sum + v.amount, 0);\n    const cashToPurchases = vouchers.filter(v => v.type === 'payment' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate).reduce((sum, v) => sum + v.amount, 0);\n    const netCashFromOperations = cashFromSales - cashToPurchases;\n    return {\n      title: 'قائمة التدفقات النقدية',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        operating: {\n          cashFromSales,\n          cashToPurchases,\n          netCashFromOperations\n        },\n        investing: {\n          // يمكن إضافة الاستثمارات هنا\n          netCashFromInvesting: 0\n        },\n        financing: {\n          // يمكن إضافة التمويل هنا\n          netCashFromFinancing: 0\n        },\n        netCashFlow: netCashFromOperations\n      }\n    };\n  };\n  const generateTrialBalance = () => {\n    const accounts = database.getAccounts();\n    const journalEntries = database.getJournalEntries().filter(entry => entry.status === 'posted');\n    const trialBalance = accounts.map(account => {\n      let debitTotal = 0;\n      let creditTotal = 0;\n      journalEntries.forEach(entry => {\n        if (entry.debitEntries) {\n          entry.debitEntries.forEach(debit => {\n            if (parseInt(debit.accountId) === account.id) {\n              debitTotal += parseFloat(debit.amount);\n            }\n          });\n        }\n        if (entry.creditEntries) {\n          entry.creditEntries.forEach(credit => {\n            if (parseInt(credit.accountId) === account.id) {\n              creditTotal += parseFloat(credit.amount);\n            }\n          });\n        }\n      });\n      return {\n        accountCode: account.code,\n        accountName: account.name,\n        accountType: account.type,\n        debitBalance: debitTotal > creditTotal ? debitTotal - creditTotal : 0,\n        creditBalance: creditTotal > debitTotal ? creditTotal - debitTotal : 0\n      };\n    });\n    const totalDebits = trialBalance.reduce((sum, acc) => sum + acc.debitBalance, 0);\n    const totalCredits = trialBalance.reduce((sum, acc) => sum + acc.creditBalance, 0);\n    return {\n      title: 'ميزان المراجعة',\n      date: new Date().toLocaleDateString('ar-SA'),\n      data: {\n        accounts: trialBalance,\n        totals: {\n          totalDebits,\n          totalCredits,\n          isBalanced: Math.abs(totalDebits - totalCredits) < 0.01\n        }\n      }\n    };\n  };\n  const generateDailySummary = () => {\n    const selectedDate = new Date(dateRange.start);\n    const invoices = database.getInvoices().filter(inv => new Date(inv.invoiceDate).toDateString() === selectedDate.toDateString());\n    const vouchers = database.getVouchers().filter(v => new Date(v.voucherDate).toDateString() === selectedDate.toDateString());\n    const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalReceipts = vouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);\n    const totalPayments = vouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);\n    const netCash = totalReceipts - totalPayments;\n    return {\n      title: 'الملخص اليومي',\n      date: selectedDate.toLocaleDateString('ar-SA'),\n      data: {\n        sales: {\n          invoicesCount: invoices.length,\n          totalSales,\n          averageInvoice: invoices.length > 0 ? totalSales / invoices.length : 0\n        },\n        cash: {\n          receipts: totalReceipts,\n          payments: totalPayments,\n          netCash\n        },\n        topProducts: getTopProductsForDate(selectedDate)\n      }\n    };\n  };\n  const generateTaxReport = () => {\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const salesVAT = invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).reduce((sum, inv) => sum + inv.vatAmount, 0);\n    const purchasesVAT = purchases.filter(pur => {\n      const purDate = new Date(pur.invoiceDate);\n      return purDate >= startDate && purDate <= endDate;\n    }).reduce((sum, pur) => sum + (pur.vatAmount || 0), 0);\n    const netVAT = salesVAT - purchasesVAT;\n    return {\n      title: 'التقرير الضريبي',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        salesVAT,\n        purchasesVAT,\n        netVAT,\n        vatRate: 15,\n        taxableAmount: salesVAT / 0.15\n      }\n    };\n  };\n  const generateTopProductsReport = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const productSales = {};\n    invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).forEach(invoice => {\n      invoice.items.forEach(item => {\n        const product = products.find(p => p.id === item.productId);\n        if (product) {\n          if (!productSales[product.id]) {\n            productSales[product.id] = {\n              name: product.name,\n              quantity: 0,\n              revenue: 0,\n              profit: 0\n            };\n          }\n          productSales[product.id].quantity += item.quantity;\n          productSales[product.id].revenue += item.total;\n          productSales[product.id].profit += item.total - item.quantity * (product.cost || product.price * 0.7);\n        }\n      });\n    });\n    const sortedProducts = Object.values(productSales).sort((a, b) => b.revenue - a.revenue).slice(0, 10);\n    return {\n      title: 'المنتجات الأكثر مبيعاً',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: sortedProducts\n    };\n  };\n  const generateSlowProductsReport = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    const productSales = {};\n\n    // حساب مبيعات كل منتج\n    invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).forEach(invoice => {\n      invoice.items.forEach(item => {\n        if (!productSales[item.productId]) {\n          productSales[item.productId] = 0;\n        }\n        productSales[item.productId] += item.quantity;\n      });\n    });\n\n    // العثور على المنتجات بطيئة الحركة\n    const slowProducts = products.filter(product => (productSales[product.id] || 0) < 5) // أقل من 5 قطع\n    .map(product => ({\n      name: product.name,\n      stock: product.stock,\n      soldQuantity: productSales[product.id] || 0,\n      stockValue: product.stock * product.price\n    })).sort((a, b) => b.stockValue - a.stockValue);\n    return {\n      title: 'المنتجات بطيئة الحركة',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: slowProducts\n    };\n  };\n  const generateProfitAnalysis = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    let totalRevenue = 0;\n    let totalCost = 0;\n    let totalProfit = 0;\n    const productProfits = {};\n    invoices.filter(inv => {\n      const invDate = new Date(inv.invoiceDate);\n      return invDate >= startDate && invDate <= endDate;\n    }).forEach(invoice => {\n      invoice.items.forEach(item => {\n        const product = products.find(p => p.id === item.productId);\n        if (product) {\n          const revenue = item.total;\n          const cost = item.quantity * (product.cost || product.price * 0.7);\n          const profit = revenue - cost;\n          totalRevenue += revenue;\n          totalCost += cost;\n          totalProfit += profit;\n          if (!productProfits[product.id]) {\n            productProfits[product.id] = {\n              name: product.name,\n              revenue: 0,\n              cost: 0,\n              profit: 0,\n              margin: 0\n            };\n          }\n          productProfits[product.id].revenue += revenue;\n          productProfits[product.id].cost += cost;\n          productProfits[product.id].profit += profit;\n          productProfits[product.id].margin = productProfits[product.id].revenue > 0 ? productProfits[product.id].profit / productProfits[product.id].revenue * 100 : 0;\n        }\n      });\n    });\n    const overallMargin = totalRevenue > 0 ? totalProfit / totalRevenue * 100 : 0;\n    return {\n      title: 'تحليل الربحية',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        overall: {\n          totalRevenue,\n          totalCost,\n          totalProfit,\n          overallMargin\n        },\n        products: Object.values(productProfits).sort((a, b) => b.profit - a.profit).slice(0, 10)\n      }\n    };\n  };\n  const generateShiftClosure = () => {\n    const today = new Date().toDateString();\n    const todayInvoices = database.getInvoices().filter(inv => new Date(inv.invoiceDate).toDateString() === today);\n    const todayVouchers = database.getVouchers().filter(v => new Date(v.voucherDate).toDateString() === today);\n    const totalSales = todayInvoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalReceipts = todayVouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);\n    const totalPayments = todayVouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);\n\n    // تجميع المبيعات حسب طريقة الدفع\n    const paymentMethods = {};\n    todayInvoices.forEach(invoice => {\n      const method = invoice.paymentMethod || 'نقداً';\n      if (!paymentMethods[method]) {\n        paymentMethods[method] = 0;\n      }\n      paymentMethods[method] += invoice.total;\n    });\n    return {\n      title: 'إغلاق الوردية',\n      date: new Date().toLocaleDateString('ar-SA'),\n      time: new Date().toLocaleTimeString('ar-SA'),\n      data: {\n        sales: {\n          invoicesCount: todayInvoices.length,\n          totalSales,\n          paymentMethods\n        },\n        cash: {\n          receipts: totalReceipts,\n          payments: totalPayments,\n          netCash: totalReceipts - totalPayments\n        },\n        summary: {\n          openingBalance: 0,\n          // يمكن إضافة رصيد افتتاحي\n          totalIncome: totalSales + totalReceipts,\n          totalExpenses: totalPayments,\n          closingBalance: totalSales + totalReceipts - totalPayments\n        }\n      }\n    };\n  };\n  const getTopProductsForDate = date => {\n    const invoices = database.getInvoices().filter(inv => new Date(inv.invoiceDate).toDateString() === date.toDateString());\n    const productSales = {};\n    invoices.forEach(invoice => {\n      invoice.items.forEach(item => {\n        const product = products.find(p => p.id === item.productId);\n        if (product) {\n          if (!productSales[product.id]) {\n            productSales[product.id] = {\n              name: product.name,\n              quantity: 0,\n              revenue: 0\n            };\n          }\n          productSales[product.id].quantity += item.quantity;\n          productSales[product.id].revenue += item.total;\n        }\n      });\n    });\n    return Object.values(productSales).sort((a, b) => b.revenue - a.revenue).slice(0, 5);\n  };\n  const printReport = () => {\n    if (!reportData) return;\n    const printWindow = window.open('', '_blank');\n    const settings = database.getSettings();\n    let printContent = `\n      <html>\n        <head>\n          <title>${reportData.title}</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 20px; \n              direction: rtl; \n              background: white;\n            }\n            .header { \n              text-align: center; \n              border-bottom: 2px solid #333; \n              padding-bottom: 20px; \n              margin-bottom: 30px; \n            }\n            .company-info { \n              text-align: center; \n              margin-bottom: 20px; \n            }\n            .report-title {\n              font-size: 1.8em;\n              font-weight: bold;\n              color: #333;\n              margin: 15px 0;\n            }\n            .report-period {\n              font-size: 1.1em;\n              color: #666;\n              margin-bottom: 20px;\n            }\n            .section {\n              margin: 20px 0;\n              padding: 15px;\n              border: 1px solid #ddd;\n              background: #f9f9f9;\n            }\n            .section h3 {\n              margin: 0 0 15px 0;\n              color: #333;\n              border-bottom: 1px solid #ccc;\n              padding-bottom: 5px;\n            }\n            .data-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 10px 0;\n            }\n            .data-table th,\n            .data-table td {\n              border: 1px solid #ddd;\n              padding: 8px;\n              text-align: right;\n            }\n            .data-table th {\n              background: #f5f5f5;\n              font-weight: bold;\n            }\n            .total-row {\n              font-weight: bold;\n              background: #e9e9e9;\n            }\n            .positive { color: #10b981; }\n            .negative { color: #ef4444; }\n            @media print { \n              body { margin: 0; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h2>${settings.companyName}</h2>\n            <div class=\"company-info\">\n              <p>${settings.address}</p>\n              <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>\n              <p>الرقم الضريبي: ${settings.vatNumber}</p>\n            </div>\n            <div class=\"report-title\">${reportData.title}</div>\n            ${reportData.period ? `<div class=\"report-period\">${reportData.period}</div>` : ''}\n            ${reportData.date ? `<div class=\"report-period\">بتاريخ: ${reportData.date}</div>` : ''}\n          </div>\n    `;\n\n    // إضافة محتوى التقرير حسب النوع\n    printContent += generateReportHTML(reportData);\n    printContent += `\n          <div style=\"margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;\">\n            <p>تم إنشاء هذا التقرير بواسطة نظام المحاسبة السعودي</p>\n            <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `;\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n  const generateReportHTML = data => {\n    // هذه دالة مساعدة لتوليد HTML للتقرير\n    // يمكن توسيعها حسب نوع التقرير\n    return `\n      <div class=\"section\">\n        <pre>${JSON.stringify(data.data, null, 2)}</pre>\n      </div>\n    `;\n  };\n  const exportToExcel = () => {\n    if (!reportData) return;\n\n    // تحويل البيانات إلى CSV\n    const csvContent = convertToCSV(reportData);\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${reportData.title}-${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n  const convertToCSV = data => {\n    // تحويل بسيط للبيانات إلى CSV\n    return JSON.stringify(data.data, null, 2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"advanced-reports-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reports-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0648\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0648\\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"report-selection\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-types-grid\",\n        children: reportTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `report-type-card ${reportType === type.value ? 'active' : ''}`,\n          onClick: () => setReportType(type.value),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"report-icon\",\n            children: type.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"report-label\",\n            children: type.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this)]\n        }, type.value, true, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"report-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"date-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0645\\u0646 \\u062A\\u0627\\u0631\\u064A\\u062E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: dateRange.start,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              start: e.target.value\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0625\\u0644\\u0649 \\u062A\\u0627\\u0631\\u064A\\u062E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: dateRange.end,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              end: e.target.value\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this), reportType === 'account_statement' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-controls\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCustomer,\n            onChange: e => setSelectedCustomer(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 17\n            }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: customer.id,\n              children: customer.name\n            }, customer.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: generateReport,\n          disabled: loading,\n          className: \"generate-btn\",\n          children: loading ? '⏳ جاري الإنشاء...' : '📊 إنشاء التقرير'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 11\n        }, this), reportData && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: printReport,\n            className: \"print-btn\",\n            children: \"\\uD83D\\uDDA8\\uFE0F \\u0637\\u0628\\u0627\\u0639\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: exportToExcel,\n            className: \"export-btn\",\n            children: \"\\uD83D\\uDCE4 \\u062A\\u0635\\u062F\\u064A\\u0631 Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 803,\n      columnNumber: 7\n    }, this), reportData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"report-display\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-header-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: reportData.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 13\n        }, this), reportData.period && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"report-period\",\n          children: reportData.period\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 35\n        }, this), reportData.date && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"report-date\",\n          children: [\"\\u0628\\u062A\\u0627\\u0631\\u064A\\u062E: \", reportData.date]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-content\",\n        children: reportData.error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [\"\\u274C \", reportData.error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-data\",\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            children: JSON.stringify(reportData.data, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 878,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 780,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedReports, \"aNDyj8YNAFW+as5y8NE0lAEOeco=\");\n_c = AdvancedReports;\nexport default AdvancedReports;\nvar _c;\n$RefreshReg$(_c, \"AdvancedReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdvancedReports", "_s", "reportType", "setReportType", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "toISOString", "split", "end", "reportData", "setReportData", "loading", "setLoading", "selectedCustomer", "setSelectedCustomer", "selectedProduct", "setSelectedProduct", "customers", "setCustomers", "products", "setProducts", "reportTypes", "value", "label", "icon", "loadData", "customersData", "getCustomers", "productsData", "getProducts", "error", "console", "generateReport", "data", "generateIncomeStatement", "generateBalanceSheet", "generateCashFlowStatement", "generateTrialBalance", "generateDailySummary", "generateTaxReport", "generateAccountStatement", "generateTopProductsReport", "generateSlowProductsReport", "generateProfitAnalysis", "generateShiftClosure", "generateDetailedSalesReport", "generateDetailedPurchasesReport", "generateInventoryValuation", "generateCustomerAging", "invoices", "getInvoices", "purchases", "getPurchaseInvoices", "expenses", "getVouchers", "filter", "v", "type", "startDate", "endDate", "salesRevenue", "inv", "invDate", "invoiceDate", "reduce", "sum", "total", "vatAmount", "vatCollected", "costOfGoodsSold", "items", "itemSum", "item", "product", "find", "p", "id", "productId", "quantity", "cost", "price", "operatingExpenses", "exp", "expDate", "voucherDate", "amount", "grossProfit", "netProfit", "title", "period", "toLocaleDateString", "revenue", "totalRevenue", "costs", "totalCosts", "profit", "grossProfitMargin", "netProfitMargin", "accounts", "getAccounts", "suppliers", "getSuppliers", "cashAccounts", "acc", "name", "includes", "totalCash", "balance", "inventoryValue", "stock", "accountsReceivable", "customer", "fixedAssets", "totalAssets", "accountsPayable", "supplier", "liabilityAccounts", "totalLiabilities", "equityAccounts", "totalEquity", "retainedEarnings", "date", "assets", "currentAssets", "cash", "inventory", "liabilities", "otherLiabilities", "equity", "capital", "vouchers", "cashFromSales", "cashToPurchases", "netCashFromOperations", "operating", "investing", "netCashFromInvesting", "financing", "netCashFromFinancing", "netCashFlow", "journalEntries", "getJournalEntries", "entry", "status", "trialBalance", "map", "account", "debitTotal", "creditTotal", "for<PERSON>ach", "debitEntries", "debit", "parseInt", "accountId", "parseFloat", "creditEntries", "credit", "accountCode", "code", "accountName", "accountType", "debitBalance", "creditBalance", "totalDebits", "totalCredits", "totals", "isBalanced", "Math", "abs", "selectedDate", "toDateString", "totalSales", "totalReceipts", "totalPayments", "netCash", "sales", "invoicesCount", "length", "averageInvoice", "receipts", "payments", "topProducts", "getTopProductsForDate", "salesVAT", "purchasesVAT", "pur", "purDate", "netVAT", "vatRate", "taxableAmount", "productSales", "invoice", "sortedProducts", "Object", "values", "sort", "a", "b", "slice", "slowProducts", "soldQuantity", "stockValue", "totalCost", "totalProfit", "productProfits", "margin", "<PERSON><PERSON><PERSON><PERSON>", "overall", "today", "todayInvoices", "todayVouchers", "paymentMethods", "method", "paymentMethod", "time", "toLocaleTimeString", "summary", "openingBalance", "totalIncome", "totalExpenses", "closingBalance", "printReport", "printWindow", "window", "open", "settings", "getSettings", "printContent", "companyName", "address", "phone", "email", "vatNumber", "generateReportHTML", "toLocaleString", "document", "write", "close", "setTimeout", "print", "JSON", "stringify", "exportToExcel", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "link", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "e", "prev", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/AdvancedReports/AdvancedReports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './AdvancedReports.css';\n\nconst AdvancedReports = () => {\n  const [reportType, setReportType] = useState('income_statement');\n  const [dateRange, setDateRange] = useState({\n    start: new Date().toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n  const [reportData, setReportData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [selectedCustomer, setSelectedCustomer] = useState('');\n  const [selectedProduct, setSelectedProduct] = useState('');\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n\n  const reportTypes = [\n    { value: 'income_statement', label: '📈 قائمة الدخل', icon: '📈' },\n    { value: 'balance_sheet', label: '⚖️ الميزانية العمومية', icon: '⚖️' },\n    { value: 'cash_flow', label: '💸 التدفقات النقدية', icon: '💸' },\n    { value: 'trial_balance', label: '📋 ميزان المراجعة', icon: '📋' },\n    { value: 'daily_summary', label: '📅 الملخص اليومي', icon: '📅' },\n    { value: 'tax_report', label: '🧾 التقرير الضريبي', icon: '🧾' },\n    { value: 'account_statement', label: '📄 كشف حساب', icon: '📄' },\n    { value: 'top_products', label: '🏆 المنتجات الأكثر مبيعاً', icon: '🏆' },\n    { value: 'slow_products', label: '🐌 المنتجات بطيئة الحركة', icon: '🐌' },\n    { value: 'profit_analysis', label: '💹 تحليل الربحية', icon: '💹' },\n    { value: 'shift_closure', label: '🔒 إغلاق الوردية', icon: '🔒' },\n    { value: 'sales_detailed', label: '📊 تقرير المبيعات التفصيلي', icon: '📊' },\n    { value: 'purchases_detailed', label: '🛒 تقرير المشتريات التفصيلي', icon: '🛒' },\n    { value: 'inventory_valuation', label: '💰 تقييم المخزون', icon: '💰' },\n    { value: 'customer_aging', label: '👥 أعمار ديون العملاء', icon: '👥' }\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = () => {\n    try {\n      const customersData = database.getCustomers();\n      const productsData = database.getProducts();\n      \n      setCustomers(customersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n    }\n  };\n\n  const generateReport = async () => {\n    setLoading(true);\n    try {\n      let data = null;\n      \n      switch (reportType) {\n        case 'income_statement':\n          data = generateIncomeStatement();\n          break;\n        case 'balance_sheet':\n          data = generateBalanceSheet();\n          break;\n        case 'cash_flow':\n          data = generateCashFlowStatement();\n          break;\n        case 'trial_balance':\n          data = generateTrialBalance();\n          break;\n        case 'daily_summary':\n          data = generateDailySummary();\n          break;\n        case 'tax_report':\n          data = generateTaxReport();\n          break;\n        case 'account_statement':\n          data = generateAccountStatement();\n          break;\n        case 'top_products':\n          data = generateTopProductsReport();\n          break;\n        case 'slow_products':\n          data = generateSlowProductsReport();\n          break;\n        case 'profit_analysis':\n          data = generateProfitAnalysis();\n          break;\n        case 'shift_closure':\n          data = generateShiftClosure();\n          break;\n        case 'sales_detailed':\n          data = generateDetailedSalesReport();\n          break;\n        case 'purchases_detailed':\n          data = generateDetailedPurchasesReport();\n          break;\n        case 'inventory_valuation':\n          data = generateInventoryValuation();\n          break;\n        case 'customer_aging':\n          data = generateCustomerAging();\n          break;\n        default:\n          data = { error: 'نوع التقرير غير مدعوم' };\n      }\n      \n      setReportData(data);\n    } catch (error) {\n      console.error('خطأ في إنشاء التقرير:', error);\n      setReportData({ error: 'خطأ في إنشاء التقرير' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateIncomeStatement = () => {\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    const expenses = database.getVouchers().filter(v => v.type === 'payment');\n    \n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    // الإيرادات\n    const salesRevenue = invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .reduce((sum, inv) => sum + (inv.total - inv.vatAmount), 0);\n    \n    const vatCollected = invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .reduce((sum, inv) => sum + inv.vatAmount, 0);\n    \n    // تكلفة البضاعة المباعة\n    const costOfGoodsSold = invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .reduce((sum, inv) => {\n        return sum + inv.items.reduce((itemSum, item) => {\n          const product = products.find(p => p.id === item.productId);\n          return itemSum + (item.quantity * (product?.cost || product?.price * 0.7 || 0));\n        }, 0);\n      }, 0);\n    \n    // المصروفات\n    const operatingExpenses = expenses\n      .filter(exp => {\n        const expDate = new Date(exp.voucherDate);\n        return expDate >= startDate && expDate <= endDate;\n      })\n      .reduce((sum, exp) => sum + exp.amount, 0);\n    \n    const grossProfit = salesRevenue - costOfGoodsSold;\n    const netProfit = grossProfit - operatingExpenses;\n    \n    return {\n      title: 'قائمة الدخل',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        revenue: {\n          salesRevenue,\n          vatCollected,\n          totalRevenue: salesRevenue + vatCollected\n        },\n        costs: {\n          costOfGoodsSold,\n          operatingExpenses,\n          totalCosts: costOfGoodsSold + operatingExpenses\n        },\n        profit: {\n          grossProfit,\n          grossProfitMargin: salesRevenue > 0 ? (grossProfit / salesRevenue * 100) : 0,\n          netProfit,\n          netProfitMargin: salesRevenue > 0 ? (netProfit / salesRevenue * 100) : 0\n        }\n      }\n    };\n  };\n\n  const generateBalanceSheet = () => {\n    const accounts = database.getAccounts();\n    const products = database.getProducts();\n    const customers = database.getCustomers();\n    const suppliers = database.getSuppliers();\n    \n    // الأصول\n    const cashAccounts = accounts.filter(acc => \n      acc.type === 'asset' && \n      (acc.name.includes('نقدية') || acc.name.includes('صندوق') || acc.name.includes('بنك'))\n    );\n    const totalCash = cashAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    \n    const inventoryValue = products.reduce((sum, product) => \n      sum + (product.stock * product.price), 0);\n    \n    const accountsReceivable = customers.reduce((sum, customer) => \n      sum + (customer.balance || 0), 0);\n    \n    const fixedAssets = accounts\n      .filter(acc => acc.type === 'asset' && !acc.name.includes('نقدية') && !acc.name.includes('صندوق'))\n      .reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    \n    const totalAssets = totalCash + inventoryValue + accountsReceivable + fixedAssets;\n    \n    // الخصوم\n    const accountsPayable = suppliers.reduce((sum, supplier) => \n      sum + (supplier.balance || 0), 0);\n    \n    const liabilityAccounts = accounts.filter(acc => acc.type === 'liability');\n    const totalLiabilities = liabilityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0) + accountsPayable;\n    \n    // حقوق الملكية\n    const equityAccounts = accounts.filter(acc => acc.type === 'equity');\n    const totalEquity = equityAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);\n    \n    const retainedEarnings = totalAssets - totalLiabilities - totalEquity;\n    \n    return {\n      title: 'الميزانية العمومية',\n      date: new Date().toLocaleDateString('ar-SA'),\n      data: {\n        assets: {\n          currentAssets: {\n            cash: totalCash,\n            inventory: inventoryValue,\n            accountsReceivable,\n            total: totalCash + inventoryValue + accountsReceivable\n          },\n          fixedAssets,\n          totalAssets\n        },\n        liabilities: {\n          accountsPayable,\n          otherLiabilities: totalLiabilities - accountsPayable,\n          totalLiabilities\n        },\n        equity: {\n          capital: totalEquity,\n          retainedEarnings,\n          totalEquity: totalEquity + retainedEarnings\n        }\n      }\n    };\n  };\n\n  const generateCashFlowStatement = () => {\n    const vouchers = database.getVouchers();\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    \n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    // التدفقات النقدية من الأنشطة التشغيلية\n    const cashFromSales = vouchers\n      .filter(v => v.type === 'receipt' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate)\n      .reduce((sum, v) => sum + v.amount, 0);\n    \n    const cashToPurchases = vouchers\n      .filter(v => v.type === 'payment' && new Date(v.voucherDate) >= startDate && new Date(v.voucherDate) <= endDate)\n      .reduce((sum, v) => sum + v.amount, 0);\n    \n    const netCashFromOperations = cashFromSales - cashToPurchases;\n    \n    return {\n      title: 'قائمة التدفقات النقدية',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        operating: {\n          cashFromSales,\n          cashToPurchases,\n          netCashFromOperations\n        },\n        investing: {\n          // يمكن إضافة الاستثمارات هنا\n          netCashFromInvesting: 0\n        },\n        financing: {\n          // يمكن إضافة التمويل هنا\n          netCashFromFinancing: 0\n        },\n        netCashFlow: netCashFromOperations\n      }\n    };\n  };\n\n  const generateTrialBalance = () => {\n    const accounts = database.getAccounts();\n    const journalEntries = database.getJournalEntries().filter(entry => entry.status === 'posted');\n    \n    const trialBalance = accounts.map(account => {\n      let debitTotal = 0;\n      let creditTotal = 0;\n      \n      journalEntries.forEach(entry => {\n        if (entry.debitEntries) {\n          entry.debitEntries.forEach(debit => {\n            if (parseInt(debit.accountId) === account.id) {\n              debitTotal += parseFloat(debit.amount);\n            }\n          });\n        }\n        \n        if (entry.creditEntries) {\n          entry.creditEntries.forEach(credit => {\n            if (parseInt(credit.accountId) === account.id) {\n              creditTotal += parseFloat(credit.amount);\n            }\n          });\n        }\n      });\n      \n      return {\n        accountCode: account.code,\n        accountName: account.name,\n        accountType: account.type,\n        debitBalance: debitTotal > creditTotal ? debitTotal - creditTotal : 0,\n        creditBalance: creditTotal > debitTotal ? creditTotal - debitTotal : 0\n      };\n    });\n    \n    const totalDebits = trialBalance.reduce((sum, acc) => sum + acc.debitBalance, 0);\n    const totalCredits = trialBalance.reduce((sum, acc) => sum + acc.creditBalance, 0);\n    \n    return {\n      title: 'ميزان المراجعة',\n      date: new Date().toLocaleDateString('ar-SA'),\n      data: {\n        accounts: trialBalance,\n        totals: {\n          totalDebits,\n          totalCredits,\n          isBalanced: Math.abs(totalDebits - totalCredits) < 0.01\n        }\n      }\n    };\n  };\n\n  const generateDailySummary = () => {\n    const selectedDate = new Date(dateRange.start);\n    const invoices = database.getInvoices().filter(inv => \n      new Date(inv.invoiceDate).toDateString() === selectedDate.toDateString()\n    );\n    const vouchers = database.getVouchers().filter(v => \n      new Date(v.voucherDate).toDateString() === selectedDate.toDateString()\n    );\n    \n    const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalReceipts = vouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);\n    const totalPayments = vouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);\n    const netCash = totalReceipts - totalPayments;\n    \n    return {\n      title: 'الملخص اليومي',\n      date: selectedDate.toLocaleDateString('ar-SA'),\n      data: {\n        sales: {\n          invoicesCount: invoices.length,\n          totalSales,\n          averageInvoice: invoices.length > 0 ? totalSales / invoices.length : 0\n        },\n        cash: {\n          receipts: totalReceipts,\n          payments: totalPayments,\n          netCash\n        },\n        topProducts: getTopProductsForDate(selectedDate)\n      }\n    };\n  };\n\n  const generateTaxReport = () => {\n    const invoices = database.getInvoices();\n    const purchases = database.getPurchaseInvoices();\n    \n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    const salesVAT = invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .reduce((sum, inv) => sum + inv.vatAmount, 0);\n    \n    const purchasesVAT = purchases\n      .filter(pur => {\n        const purDate = new Date(pur.invoiceDate);\n        return purDate >= startDate && purDate <= endDate;\n      })\n      .reduce((sum, pur) => sum + (pur.vatAmount || 0), 0);\n    \n    const netVAT = salesVAT - purchasesVAT;\n    \n    return {\n      title: 'التقرير الضريبي',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        salesVAT,\n        purchasesVAT,\n        netVAT,\n        vatRate: 15,\n        taxableAmount: salesVAT / 0.15\n      }\n    };\n  };\n\n  const generateTopProductsReport = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    const productSales = {};\n    \n    invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .forEach(invoice => {\n        invoice.items.forEach(item => {\n          const product = products.find(p => p.id === item.productId);\n          if (product) {\n            if (!productSales[product.id]) {\n              productSales[product.id] = {\n                name: product.name,\n                quantity: 0,\n                revenue: 0,\n                profit: 0\n              };\n            }\n            productSales[product.id].quantity += item.quantity;\n            productSales[product.id].revenue += item.total;\n            productSales[product.id].profit += item.total - (item.quantity * (product.cost || product.price * 0.7));\n          }\n        });\n      });\n    \n    const sortedProducts = Object.values(productSales)\n      .sort((a, b) => b.revenue - a.revenue)\n      .slice(0, 10);\n    \n    return {\n      title: 'المنتجات الأكثر مبيعاً',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: sortedProducts\n    };\n  };\n\n  const generateSlowProductsReport = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    const productSales = {};\n    \n    // حساب مبيعات كل منتج\n    invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .forEach(invoice => {\n        invoice.items.forEach(item => {\n          if (!productSales[item.productId]) {\n            productSales[item.productId] = 0;\n          }\n          productSales[item.productId] += item.quantity;\n        });\n      });\n    \n    // العثور على المنتجات بطيئة الحركة\n    const slowProducts = products\n      .filter(product => (productSales[product.id] || 0) < 5) // أقل من 5 قطع\n      .map(product => ({\n        name: product.name,\n        stock: product.stock,\n        soldQuantity: productSales[product.id] || 0,\n        stockValue: product.stock * product.price\n      }))\n      .sort((a, b) => b.stockValue - a.stockValue);\n    \n    return {\n      title: 'المنتجات بطيئة الحركة',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: slowProducts\n    };\n  };\n\n  const generateProfitAnalysis = () => {\n    const invoices = database.getInvoices();\n    const startDate = new Date(dateRange.start);\n    const endDate = new Date(dateRange.end);\n    \n    let totalRevenue = 0;\n    let totalCost = 0;\n    let totalProfit = 0;\n    \n    const productProfits = {};\n    \n    invoices\n      .filter(inv => {\n        const invDate = new Date(inv.invoiceDate);\n        return invDate >= startDate && invDate <= endDate;\n      })\n      .forEach(invoice => {\n        invoice.items.forEach(item => {\n          const product = products.find(p => p.id === item.productId);\n          if (product) {\n            const revenue = item.total;\n            const cost = item.quantity * (product.cost || product.price * 0.7);\n            const profit = revenue - cost;\n            \n            totalRevenue += revenue;\n            totalCost += cost;\n            totalProfit += profit;\n            \n            if (!productProfits[product.id]) {\n              productProfits[product.id] = {\n                name: product.name,\n                revenue: 0,\n                cost: 0,\n                profit: 0,\n                margin: 0\n              };\n            }\n            \n            productProfits[product.id].revenue += revenue;\n            productProfits[product.id].cost += cost;\n            productProfits[product.id].profit += profit;\n            productProfits[product.id].margin = \n              productProfits[product.id].revenue > 0 ? \n              (productProfits[product.id].profit / productProfits[product.id].revenue * 100) : 0;\n          }\n        });\n      });\n    \n    const overallMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0;\n    \n    return {\n      title: 'تحليل الربحية',\n      period: `من ${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`,\n      data: {\n        overall: {\n          totalRevenue,\n          totalCost,\n          totalProfit,\n          overallMargin\n        },\n        products: Object.values(productProfits)\n          .sort((a, b) => b.profit - a.profit)\n          .slice(0, 10)\n      }\n    };\n  };\n\n  const generateShiftClosure = () => {\n    const today = new Date().toDateString();\n    const todayInvoices = database.getInvoices().filter(inv => \n      new Date(inv.invoiceDate).toDateString() === today\n    );\n    const todayVouchers = database.getVouchers().filter(v => \n      new Date(v.voucherDate).toDateString() === today\n    );\n    \n    const totalSales = todayInvoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalReceipts = todayVouchers.filter(v => v.type === 'receipt').reduce((sum, v) => sum + v.amount, 0);\n    const totalPayments = todayVouchers.filter(v => v.type === 'payment').reduce((sum, v) => sum + v.amount, 0);\n    \n    // تجميع المبيعات حسب طريقة الدفع\n    const paymentMethods = {};\n    todayInvoices.forEach(invoice => {\n      const method = invoice.paymentMethod || 'نقداً';\n      if (!paymentMethods[method]) {\n        paymentMethods[method] = 0;\n      }\n      paymentMethods[method] += invoice.total;\n    });\n    \n    return {\n      title: 'إغلاق الوردية',\n      date: new Date().toLocaleDateString('ar-SA'),\n      time: new Date().toLocaleTimeString('ar-SA'),\n      data: {\n        sales: {\n          invoicesCount: todayInvoices.length,\n          totalSales,\n          paymentMethods\n        },\n        cash: {\n          receipts: totalReceipts,\n          payments: totalPayments,\n          netCash: totalReceipts - totalPayments\n        },\n        summary: {\n          openingBalance: 0, // يمكن إضافة رصيد افتتاحي\n          totalIncome: totalSales + totalReceipts,\n          totalExpenses: totalPayments,\n          closingBalance: totalSales + totalReceipts - totalPayments\n        }\n      }\n    };\n  };\n\n  const getTopProductsForDate = (date) => {\n    const invoices = database.getInvoices().filter(inv => \n      new Date(inv.invoiceDate).toDateString() === date.toDateString()\n    );\n    \n    const productSales = {};\n    \n    invoices.forEach(invoice => {\n      invoice.items.forEach(item => {\n        const product = products.find(p => p.id === item.productId);\n        if (product) {\n          if (!productSales[product.id]) {\n            productSales[product.id] = {\n              name: product.name,\n              quantity: 0,\n              revenue: 0\n            };\n          }\n          productSales[product.id].quantity += item.quantity;\n          productSales[product.id].revenue += item.total;\n        }\n      });\n    });\n    \n    return Object.values(productSales)\n      .sort((a, b) => b.revenue - a.revenue)\n      .slice(0, 5);\n  };\n\n  const printReport = () => {\n    if (!reportData) return;\n    \n    const printWindow = window.open('', '_blank');\n    const settings = database.getSettings();\n    \n    let printContent = `\n      <html>\n        <head>\n          <title>${reportData.title}</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 20px; \n              direction: rtl; \n              background: white;\n            }\n            .header { \n              text-align: center; \n              border-bottom: 2px solid #333; \n              padding-bottom: 20px; \n              margin-bottom: 30px; \n            }\n            .company-info { \n              text-align: center; \n              margin-bottom: 20px; \n            }\n            .report-title {\n              font-size: 1.8em;\n              font-weight: bold;\n              color: #333;\n              margin: 15px 0;\n            }\n            .report-period {\n              font-size: 1.1em;\n              color: #666;\n              margin-bottom: 20px;\n            }\n            .section {\n              margin: 20px 0;\n              padding: 15px;\n              border: 1px solid #ddd;\n              background: #f9f9f9;\n            }\n            .section h3 {\n              margin: 0 0 15px 0;\n              color: #333;\n              border-bottom: 1px solid #ccc;\n              padding-bottom: 5px;\n            }\n            .data-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 10px 0;\n            }\n            .data-table th,\n            .data-table td {\n              border: 1px solid #ddd;\n              padding: 8px;\n              text-align: right;\n            }\n            .data-table th {\n              background: #f5f5f5;\n              font-weight: bold;\n            }\n            .total-row {\n              font-weight: bold;\n              background: #e9e9e9;\n            }\n            .positive { color: #10b981; }\n            .negative { color: #ef4444; }\n            @media print { \n              body { margin: 0; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h2>${settings.companyName}</h2>\n            <div class=\"company-info\">\n              <p>${settings.address}</p>\n              <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>\n              <p>الرقم الضريبي: ${settings.vatNumber}</p>\n            </div>\n            <div class=\"report-title\">${reportData.title}</div>\n            ${reportData.period ? `<div class=\"report-period\">${reportData.period}</div>` : ''}\n            ${reportData.date ? `<div class=\"report-period\">بتاريخ: ${reportData.date}</div>` : ''}\n          </div>\n    `;\n    \n    // إضافة محتوى التقرير حسب النوع\n    printContent += generateReportHTML(reportData);\n    \n    printContent += `\n          <div style=\"margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;\">\n            <p>تم إنشاء هذا التقرير بواسطة نظام المحاسبة السعودي</p>\n            <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `;\n    \n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => printWindow.print(), 500);\n  };\n\n  const generateReportHTML = (data) => {\n    // هذه دالة مساعدة لتوليد HTML للتقرير\n    // يمكن توسيعها حسب نوع التقرير\n    return `\n      <div class=\"section\">\n        <pre>${JSON.stringify(data.data, null, 2)}</pre>\n      </div>\n    `;\n  };\n\n  const exportToExcel = () => {\n    if (!reportData) return;\n    \n    // تحويل البيانات إلى CSV\n    const csvContent = convertToCSV(reportData);\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${reportData.title}-${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  const convertToCSV = (data) => {\n    // تحويل بسيط للبيانات إلى CSV\n    return JSON.stringify(data.data, null, 2);\n  };\n\n  return (\n    <div className=\"advanced-reports-container\">\n      <div className=\"reports-header\">\n        <h1>📊 التقارير المتقدمة</h1>\n        <p>تقارير مالية ومحاسبية شاملة ومتقدمة</p>\n      </div>\n\n      {/* Report Selection */}\n      <div className=\"report-selection\">\n        <div className=\"report-types-grid\">\n          {reportTypes.map(type => (\n            <button\n              key={type.value}\n              className={`report-type-card ${reportType === type.value ? 'active' : ''}`}\n              onClick={() => setReportType(type.value)}\n            >\n              <div className=\"report-icon\">{type.icon}</div>\n              <div className=\"report-label\">{type.label}</div>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Report Controls */}\n      <div className=\"report-controls\">\n        <div className=\"date-controls\">\n          <div className=\"form-group\">\n            <label>من تاريخ</label>\n            <input\n              type=\"date\"\n              value={dateRange.start}\n              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>إلى تاريخ</label>\n            <input\n              type=\"date\"\n              value={dateRange.end}\n              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n            />\n          </div>\n        </div>\n\n        {(reportType === 'account_statement') && (\n          <div className=\"filter-controls\">\n            <div className=\"form-group\">\n              <label>العميل</label>\n              <select\n                value={selectedCustomer}\n                onChange={(e) => setSelectedCustomer(e.target.value)}\n              >\n                <option value=\"\">جميع العملاء</option>\n                {customers.map(customer => (\n                  <option key={customer.id} value={customer.id}>\n                    {customer.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n        )}\n\n        <div className=\"action-controls\">\n          <button\n            onClick={generateReport}\n            disabled={loading}\n            className=\"generate-btn\"\n          >\n            {loading ? '⏳ جاري الإنشاء...' : '📊 إنشاء التقرير'}\n          </button>\n          {reportData && (\n            <>\n              <button onClick={printReport} className=\"print-btn\">\n                🖨️ طباعة\n              </button>\n              <button onClick={exportToExcel} className=\"export-btn\">\n                📤 تصدير Excel\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Report Display */}\n      {reportData && (\n        <div className=\"report-display\">\n          <div className=\"report-header-info\">\n            <h2>{reportData.title}</h2>\n            {reportData.period && <p className=\"report-period\">{reportData.period}</p>}\n            {reportData.date && <p className=\"report-date\">بتاريخ: {reportData.date}</p>}\n          </div>\n\n          <div className=\"report-content\">\n            {reportData.error ? (\n              <div className=\"error-message\">\n                ❌ {reportData.error}\n              </div>\n            ) : (\n              <div className=\"report-data\">\n                <pre>{JSON.stringify(reportData.data, null, 2)}</pre>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdvancedReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,kBAAkB,CAAC;EAChE,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC;IACzCa,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7CC,GAAG,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM8B,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClE;IAAEF,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACtE;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChE;IAAEF,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClE;IAAEF,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChE;IAAEF,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChE;IAAEF,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE,2BAA2B;IAAEC,IAAI,EAAE;EAAK,CAAC,EACzE;IAAEF,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,0BAA0B;IAAEC,IAAI,EAAE;EAAK,CAAC,EACzE;IAAEF,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnE;IAAEF,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjE;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,4BAA4B;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC5E;IAAEF,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,6BAA6B;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjF;IAAEF,KAAK,EAAE,qBAAqB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACvE;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,IAAI,EAAE;EAAK,CAAC,CACxE;EAEDhC,SAAS,CAAC,MAAM;IACdiC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI;MACF,MAAMC,aAAa,GAAGjC,QAAQ,CAACkC,YAAY,CAAC,CAAC;MAC7C,MAAMC,YAAY,GAAGnC,QAAQ,CAACoC,WAAW,CAAC,CAAC;MAE3CX,YAAY,CAACQ,aAAa,CAAC;MAC3BN,WAAW,CAACQ,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCpB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIqB,IAAI,GAAG,IAAI;MAEf,QAAQjC,UAAU;QAChB,KAAK,kBAAkB;UACrBiC,IAAI,GAAGC,uBAAuB,CAAC,CAAC;UAChC;QACF,KAAK,eAAe;UAClBD,IAAI,GAAGE,oBAAoB,CAAC,CAAC;UAC7B;QACF,KAAK,WAAW;UACdF,IAAI,GAAGG,yBAAyB,CAAC,CAAC;UAClC;QACF,KAAK,eAAe;UAClBH,IAAI,GAAGI,oBAAoB,CAAC,CAAC;UAC7B;QACF,KAAK,eAAe;UAClBJ,IAAI,GAAGK,oBAAoB,CAAC,CAAC;UAC7B;QACF,KAAK,YAAY;UACfL,IAAI,GAAGM,iBAAiB,CAAC,CAAC;UAC1B;QACF,KAAK,mBAAmB;UACtBN,IAAI,GAAGO,wBAAwB,CAAC,CAAC;UACjC;QACF,KAAK,cAAc;UACjBP,IAAI,GAAGQ,yBAAyB,CAAC,CAAC;UAClC;QACF,KAAK,eAAe;UAClBR,IAAI,GAAGS,0BAA0B,CAAC,CAAC;UACnC;QACF,KAAK,iBAAiB;UACpBT,IAAI,GAAGU,sBAAsB,CAAC,CAAC;UAC/B;QACF,KAAK,eAAe;UAClBV,IAAI,GAAGW,oBAAoB,CAAC,CAAC;UAC7B;QACF,KAAK,gBAAgB;UACnBX,IAAI,GAAGY,2BAA2B,CAAC,CAAC;UACpC;QACF,KAAK,oBAAoB;UACvBZ,IAAI,GAAGa,+BAA+B,CAAC,CAAC;UACxC;QACF,KAAK,qBAAqB;UACxBb,IAAI,GAAGc,0BAA0B,CAAC,CAAC;UACnC;QACF,KAAK,gBAAgB;UACnBd,IAAI,GAAGe,qBAAqB,CAAC,CAAC;UAC9B;QACF;UACEf,IAAI,GAAG;YAAEH,KAAK,EAAE;UAAwB,CAAC;MAC7C;MAEApB,aAAa,CAACuB,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CpB,aAAa,CAAC;QAAEoB,KAAK,EAAE;MAAuB,CAAC,CAAC;IAClD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMe,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAG1D,QAAQ,CAAC2D,mBAAmB,CAAC,CAAC;IAChD,MAAMC,QAAQ,GAAG5D,QAAQ,CAAC6D,WAAW,CAAC,CAAC,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC;IAEzE,MAAMC,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;;IAEvC;IACA,MAAMoD,YAAY,GAAGX,QAAQ,CAC1BM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAKI,GAAG,IAAIJ,GAAG,CAACK,KAAK,GAAGL,GAAG,CAACM,SAAS,CAAC,EAAE,CAAC,CAAC;IAE7D,MAAMC,YAAY,GAAGnB,QAAQ,CAC1BM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAKI,GAAG,GAAGJ,GAAG,CAACM,SAAS,EAAE,CAAC,CAAC;;IAE/C;IACA,MAAME,eAAe,GAAGpB,QAAQ,CAC7BM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAK;MACpB,OAAOI,GAAG,GAAGJ,GAAG,CAACS,KAAK,CAACN,MAAM,CAAC,CAACO,OAAO,EAAEC,IAAI,KAAK;QAC/C,MAAMC,OAAO,GAAGtD,QAAQ,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACK,SAAS,CAAC;QAC3D,OAAON,OAAO,GAAIC,IAAI,CAACM,QAAQ,IAAI,CAAAL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,IAAI,KAAI,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,KAAK,IAAG,GAAG,IAAI,CAAC,CAAE;MACjF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,EAAE,CAAC,CAAC;;IAEP;IACA,MAAMC,iBAAiB,GAAG5B,QAAQ,CAC/BE,MAAM,CAAC2B,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAI9E,IAAI,CAAC6E,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIzB,SAAS,IAAIyB,OAAO,IAAIxB,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEiB,GAAG,KAAKjB,GAAG,GAAGiB,GAAG,CAACG,MAAM,EAAE,CAAC,CAAC;IAE5C,MAAMC,WAAW,GAAG1B,YAAY,GAAGS,eAAe;IAClD,MAAMkB,SAAS,GAAGD,WAAW,GAAGL,iBAAiB;IAEjD,OAAO;MACLO,KAAK,EAAE,aAAa;MACpBC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAE;QACJ0D,OAAO,EAAE;UACP/B,YAAY;UACZQ,YAAY;UACZwB,YAAY,EAAEhC,YAAY,GAAGQ;QAC/B,CAAC;QACDyB,KAAK,EAAE;UACLxB,eAAe;UACfY,iBAAiB;UACjBa,UAAU,EAAEzB,eAAe,GAAGY;QAChC,CAAC;QACDc,MAAM,EAAE;UACNT,WAAW;UACXU,iBAAiB,EAAEpC,YAAY,GAAG,CAAC,GAAI0B,WAAW,GAAG1B,YAAY,GAAG,GAAG,GAAI,CAAC;UAC5E2B,SAAS;UACTU,eAAe,EAAErC,YAAY,GAAG,CAAC,GAAI2B,SAAS,GAAG3B,YAAY,GAAG,GAAG,GAAI;QACzE;MACF;IACF,CAAC;EACH,CAAC;EAED,MAAMzB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM+D,QAAQ,GAAGzG,QAAQ,CAAC0G,WAAW,CAAC,CAAC;IACvC,MAAMhF,QAAQ,GAAG1B,QAAQ,CAACoC,WAAW,CAAC,CAAC;IACvC,MAAMZ,SAAS,GAAGxB,QAAQ,CAACkC,YAAY,CAAC,CAAC;IACzC,MAAMyE,SAAS,GAAG3G,QAAQ,CAAC4G,YAAY,CAAC,CAAC;;IAEzC;IACA,MAAMC,YAAY,GAAGJ,QAAQ,CAAC3C,MAAM,CAACgD,GAAG,IACtCA,GAAG,CAAC9C,IAAI,KAAK,OAAO,KACnB8C,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIF,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIF,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,CACvF,CAAC;IACD,MAAMC,SAAS,GAAGJ,YAAY,CAACtC,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,IAAIsC,GAAG,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEhF,MAAMC,cAAc,GAAGzF,QAAQ,CAAC6C,MAAM,CAAC,CAACC,GAAG,EAAEQ,OAAO,KAClDR,GAAG,GAAIQ,OAAO,CAACoC,KAAK,GAAGpC,OAAO,CAACO,KAAM,EAAE,CAAC,CAAC;IAE3C,MAAM8B,kBAAkB,GAAG7F,SAAS,CAAC+C,MAAM,CAAC,CAACC,GAAG,EAAE8C,QAAQ,KACxD9C,GAAG,IAAI8C,QAAQ,CAACJ,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEnC,MAAMK,WAAW,GAAGd,QAAQ,CACzB3C,MAAM,CAACgD,GAAG,IAAIA,GAAG,CAAC9C,IAAI,KAAK,OAAO,IAAI,CAAC8C,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACF,GAAG,CAACC,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC,CACjGzC,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,IAAIsC,GAAG,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEpD,MAAMM,WAAW,GAAGP,SAAS,GAAGE,cAAc,GAAGE,kBAAkB,GAAGE,WAAW;;IAEjF;IACA,MAAME,eAAe,GAAGd,SAAS,CAACpC,MAAM,CAAC,CAACC,GAAG,EAAEkD,QAAQ,KACrDlD,GAAG,IAAIkD,QAAQ,CAACR,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEnC,MAAMS,iBAAiB,GAAGlB,QAAQ,CAAC3C,MAAM,CAACgD,GAAG,IAAIA,GAAG,CAAC9C,IAAI,KAAK,WAAW,CAAC;IAC1E,MAAM4D,gBAAgB,GAAGD,iBAAiB,CAACpD,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,IAAIsC,GAAG,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGO,eAAe;;IAE9G;IACA,MAAMI,cAAc,GAAGpB,QAAQ,CAAC3C,MAAM,CAACgD,GAAG,IAAIA,GAAG,CAAC9C,IAAI,KAAK,QAAQ,CAAC;IACpE,MAAM8D,WAAW,GAAGD,cAAc,CAACtD,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,IAAIsC,GAAG,CAACI,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEpF,MAAMa,gBAAgB,GAAGP,WAAW,GAAGI,gBAAgB,GAAGE,WAAW;IAErE,OAAO;MACL/B,KAAK,EAAE,oBAAoB;MAC3BiC,IAAI,EAAE,IAAIpH,IAAI,CAAC,CAAC,CAACqF,kBAAkB,CAAC,OAAO,CAAC;MAC5CzD,IAAI,EAAE;QACJyF,MAAM,EAAE;UACNC,aAAa,EAAE;YACbC,IAAI,EAAElB,SAAS;YACfmB,SAAS,EAAEjB,cAAc;YACzBE,kBAAkB;YAClB5C,KAAK,EAAEwC,SAAS,GAAGE,cAAc,GAAGE;UACtC,CAAC;UACDE,WAAW;UACXC;QACF,CAAC;QACDa,WAAW,EAAE;UACXZ,eAAe;UACfa,gBAAgB,EAAEV,gBAAgB,GAAGH,eAAe;UACpDG;QACF,CAAC;QACDW,MAAM,EAAE;UACNC,OAAO,EAAEV,WAAW;UACpBC,gBAAgB;UAChBD,WAAW,EAAEA,WAAW,GAAGC;QAC7B;MACF;IACF,CAAC;EACH,CAAC;EAED,MAAMpF,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAM8F,QAAQ,GAAGzI,QAAQ,CAAC6D,WAAW,CAAC,CAAC;IACvC,MAAML,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAG1D,QAAQ,CAAC2D,mBAAmB,CAAC,CAAC;IAEhD,MAAMM,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;;IAEvC;IACA,MAAM2H,aAAa,GAAGD,QAAQ,CAC3B3E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,IAAI,IAAIpD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,IAAI1B,SAAS,IAAI,IAAIrD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,IAAIzB,OAAO,CAAC,CAC/GK,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;IAExC,MAAM+C,eAAe,GAAGF,QAAQ,CAC7B3E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,IAAI,IAAIpD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,IAAI1B,SAAS,IAAI,IAAIrD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,IAAIzB,OAAO,CAAC,CAC/GK,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;IAExC,MAAMgD,qBAAqB,GAAGF,aAAa,GAAGC,eAAe;IAE7D,OAAO;MACL5C,KAAK,EAAE,wBAAwB;MAC/BC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAE;QACJqG,SAAS,EAAE;UACTH,aAAa;UACbC,eAAe;UACfC;QACF,CAAC;QACDE,SAAS,EAAE;UACT;UACAC,oBAAoB,EAAE;QACxB,CAAC;QACDC,SAAS,EAAE;UACT;UACAC,oBAAoB,EAAE;QACxB,CAAC;QACDC,WAAW,EAAEN;MACf;IACF,CAAC;EACH,CAAC;EAED,MAAMhG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM6D,QAAQ,GAAGzG,QAAQ,CAAC0G,WAAW,CAAC,CAAC;IACvC,MAAMyC,cAAc,GAAGnJ,QAAQ,CAACoJ,iBAAiB,CAAC,CAAC,CAACtF,MAAM,CAACuF,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,QAAQ,CAAC;IAE9F,MAAMC,YAAY,GAAG9C,QAAQ,CAAC+C,GAAG,CAACC,OAAO,IAAI;MAC3C,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAIC,WAAW,GAAG,CAAC;MAEnBR,cAAc,CAACS,OAAO,CAACP,KAAK,IAAI;QAC9B,IAAIA,KAAK,CAACQ,YAAY,EAAE;UACtBR,KAAK,CAACQ,YAAY,CAACD,OAAO,CAACE,KAAK,IAAI;YAClC,IAAIC,QAAQ,CAACD,KAAK,CAACE,SAAS,CAAC,KAAKP,OAAO,CAACtE,EAAE,EAAE;cAC5CuE,UAAU,IAAIO,UAAU,CAACH,KAAK,CAAClE,MAAM,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;QAEA,IAAIyD,KAAK,CAACa,aAAa,EAAE;UACvBb,KAAK,CAACa,aAAa,CAACN,OAAO,CAACO,MAAM,IAAI;YACpC,IAAIJ,QAAQ,CAACI,MAAM,CAACH,SAAS,CAAC,KAAKP,OAAO,CAACtE,EAAE,EAAE;cAC7CwE,WAAW,IAAIM,UAAU,CAACE,MAAM,CAACvE,MAAM,CAAC;YAC1C;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,OAAO;QACLwE,WAAW,EAAEX,OAAO,CAACY,IAAI;QACzBC,WAAW,EAAEb,OAAO,CAAC1C,IAAI;QACzBwD,WAAW,EAAEd,OAAO,CAACzF,IAAI;QACzBwG,YAAY,EAAEd,UAAU,GAAGC,WAAW,GAAGD,UAAU,GAAGC,WAAW,GAAG,CAAC;QACrEc,aAAa,EAAEd,WAAW,GAAGD,UAAU,GAAGC,WAAW,GAAGD,UAAU,GAAG;MACvE,CAAC;IACH,CAAC,CAAC;IAEF,MAAMgB,WAAW,GAAGnB,YAAY,CAAChF,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,GAAGsC,GAAG,CAAC0D,YAAY,EAAE,CAAC,CAAC;IAChF,MAAMG,YAAY,GAAGpB,YAAY,CAAChF,MAAM,CAAC,CAACC,GAAG,EAAEsC,GAAG,KAAKtC,GAAG,GAAGsC,GAAG,CAAC2D,aAAa,EAAE,CAAC,CAAC;IAElF,OAAO;MACL1E,KAAK,EAAE,gBAAgB;MACvBiC,IAAI,EAAE,IAAIpH,IAAI,CAAC,CAAC,CAACqF,kBAAkB,CAAC,OAAO,CAAC;MAC5CzD,IAAI,EAAE;QACJiE,QAAQ,EAAE8C,YAAY;QACtBqB,MAAM,EAAE;UACNF,WAAW;UACXC,YAAY;UACZE,UAAU,EAAEC,IAAI,CAACC,GAAG,CAACL,WAAW,GAAGC,YAAY,CAAC,GAAG;QACrD;MACF;IACF,CAAC;EACH,CAAC;EAED,MAAM9H,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMmI,YAAY,GAAG,IAAIpK,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC9C,MAAM6C,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC,CAACK,MAAM,CAACM,GAAG,IAChD,IAAIxD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC,CAAC2G,YAAY,CAAC,CAAC,KAAKD,YAAY,CAACC,YAAY,CAAC,CACzE,CAAC;IACD,MAAMxC,QAAQ,GAAGzI,QAAQ,CAAC6D,WAAW,CAAC,CAAC,CAACC,MAAM,CAACC,CAAC,IAC9C,IAAInD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,CAACsF,YAAY,CAAC,CAAC,KAAKD,YAAY,CAACC,YAAY,CAAC,CACvE,CAAC;IAED,MAAMC,UAAU,GAAG1H,QAAQ,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAKI,GAAG,GAAGJ,GAAG,CAACK,KAAK,EAAE,CAAC,CAAC;IACpE,MAAM0G,aAAa,GAAG1C,QAAQ,CAAC3E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;IACtG,MAAMwF,aAAa,GAAG3C,QAAQ,CAAC3E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;IACtG,MAAMyF,OAAO,GAAGF,aAAa,GAAGC,aAAa;IAE7C,OAAO;MACLrF,KAAK,EAAE,eAAe;MACtBiC,IAAI,EAAEgD,YAAY,CAAC/E,kBAAkB,CAAC,OAAO,CAAC;MAC9CzD,IAAI,EAAE;QACJ8I,KAAK,EAAE;UACLC,aAAa,EAAE/H,QAAQ,CAACgI,MAAM;UAC9BN,UAAU;UACVO,cAAc,EAAEjI,QAAQ,CAACgI,MAAM,GAAG,CAAC,GAAGN,UAAU,GAAG1H,QAAQ,CAACgI,MAAM,GAAG;QACvE,CAAC;QACDrD,IAAI,EAAE;UACJuD,QAAQ,EAAEP,aAAa;UACvBQ,QAAQ,EAAEP,aAAa;UACvBC;QACF,CAAC;QACDO,WAAW,EAAEC,qBAAqB,CAACb,YAAY;MACjD;IACF,CAAC;EACH,CAAC;EAED,MAAMlI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMU,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAG1D,QAAQ,CAAC2D,mBAAmB,CAAC,CAAC;IAEhD,MAAMM,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IAEvC,MAAM+K,QAAQ,GAAGtI,QAAQ,CACtBM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAKI,GAAG,GAAGJ,GAAG,CAACM,SAAS,EAAE,CAAC,CAAC;IAE/C,MAAMqH,YAAY,GAAGrI,SAAS,CAC3BI,MAAM,CAACkI,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIrL,IAAI,CAACoL,GAAG,CAAC1H,WAAW,CAAC;MACzC,OAAO2H,OAAO,IAAIhI,SAAS,IAAIgI,OAAO,IAAI/H,OAAO;IACnD,CAAC,CAAC,CACDK,MAAM,CAAC,CAACC,GAAG,EAAEwH,GAAG,KAAKxH,GAAG,IAAIwH,GAAG,CAACtH,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtD,MAAMwH,MAAM,GAAGJ,QAAQ,GAAGC,YAAY;IAEtC,OAAO;MACLhG,KAAK,EAAE,iBAAiB;MACxBC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAE;QACJsJ,QAAQ;QACRC,YAAY;QACZG,MAAM;QACNC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAEN,QAAQ,GAAG;MAC5B;IACF,CAAC;EACH,CAAC;EAED,MAAM9I,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMQ,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMQ,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IAEvC,MAAMsL,YAAY,GAAG,CAAC,CAAC;IAEvB7I,QAAQ,CACLM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACD0F,OAAO,CAAC0C,OAAO,IAAI;MAClBA,OAAO,CAACzH,KAAK,CAAC+E,OAAO,CAAC7E,IAAI,IAAI;QAC5B,MAAMC,OAAO,GAAGtD,QAAQ,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACK,SAAS,CAAC;QAC3D,IAAIJ,OAAO,EAAE;UACX,IAAI,CAACqH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,EAAE;YAC7BkH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,GAAG;cACzB4B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI;cAClB1B,QAAQ,EAAE,CAAC;cACXa,OAAO,EAAE,CAAC;cACVI,MAAM,EAAE;YACV,CAAC;UACH;UACA+F,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,CAACE,QAAQ,IAAIN,IAAI,CAACM,QAAQ;UAClDgH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,CAACe,OAAO,IAAInB,IAAI,CAACN,KAAK;UAC9C4H,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,CAACmB,MAAM,IAAIvB,IAAI,CAACN,KAAK,GAAIM,IAAI,CAACM,QAAQ,IAAIL,OAAO,CAACM,IAAI,IAAIN,OAAO,CAACO,KAAK,GAAG,GAAG,CAAE;QACzG;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ,MAAMgH,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACJ,YAAY,CAAC,CAC/CK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC1G,OAAO,GAAGyG,CAAC,CAACzG,OAAO,CAAC,CACrC2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAEf,OAAO;MACL9G,KAAK,EAAE,wBAAwB;MAC/BC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAE+J;IACR,CAAC;EACH,CAAC;EAED,MAAMtJ,0BAA0B,GAAGA,CAAA,KAAM;IACvC,MAAMO,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMQ,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IAEvC,MAAMsL,YAAY,GAAG,CAAC,CAAC;;IAEvB;IACA7I,QAAQ,CACLM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACD0F,OAAO,CAAC0C,OAAO,IAAI;MAClBA,OAAO,CAACzH,KAAK,CAAC+E,OAAO,CAAC7E,IAAI,IAAI;QAC5B,IAAI,CAACsH,YAAY,CAACtH,IAAI,CAACK,SAAS,CAAC,EAAE;UACjCiH,YAAY,CAACtH,IAAI,CAACK,SAAS,CAAC,GAAG,CAAC;QAClC;QACAiH,YAAY,CAACtH,IAAI,CAACK,SAAS,CAAC,IAAIL,IAAI,CAACM,QAAQ;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEJ;IACA,MAAMyH,YAAY,GAAGpL,QAAQ,CAC1BoC,MAAM,CAACkB,OAAO,IAAI,CAACqH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAAA,CACvDqE,GAAG,CAACxE,OAAO,KAAK;MACf+B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI;MAClBK,KAAK,EAAEpC,OAAO,CAACoC,KAAK;MACpB2F,YAAY,EAAEV,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,IAAI,CAAC;MAC3C6H,UAAU,EAAEhI,OAAO,CAACoC,KAAK,GAAGpC,OAAO,CAACO;IACtC,CAAC,CAAC,CAAC,CACFmH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACI,UAAU,GAAGL,CAAC,CAACK,UAAU,CAAC;IAE9C,OAAO;MACLjH,KAAK,EAAE,uBAAuB;MAC9BC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAEsK;IACR,CAAC;EACH,CAAC;EAED,MAAM5J,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMM,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC;IACvC,MAAMQ,SAAS,GAAG,IAAIrD,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC;IAC3C,MAAMuD,OAAO,GAAG,IAAItD,IAAI,CAACH,SAAS,CAACM,GAAG,CAAC;IAEvC,IAAIoF,YAAY,GAAG,CAAC;IACpB,IAAI8G,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG,CAAC;IAEnB,MAAMC,cAAc,GAAG,CAAC,CAAC;IAEzB3J,QAAQ,CACLM,MAAM,CAACM,GAAG,IAAI;MACb,MAAMC,OAAO,GAAG,IAAIzD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC;MACzC,OAAOD,OAAO,IAAIJ,SAAS,IAAII,OAAO,IAAIH,OAAO;IACnD,CAAC,CAAC,CACD0F,OAAO,CAAC0C,OAAO,IAAI;MAClBA,OAAO,CAACzH,KAAK,CAAC+E,OAAO,CAAC7E,IAAI,IAAI;QAC5B,MAAMC,OAAO,GAAGtD,QAAQ,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACK,SAAS,CAAC;QAC3D,IAAIJ,OAAO,EAAE;UACX,MAAMkB,OAAO,GAAGnB,IAAI,CAACN,KAAK;UAC1B,MAAMa,IAAI,GAAGP,IAAI,CAACM,QAAQ,IAAIL,OAAO,CAACM,IAAI,IAAIN,OAAO,CAACO,KAAK,GAAG,GAAG,CAAC;UAClE,MAAMe,MAAM,GAAGJ,OAAO,GAAGZ,IAAI;UAE7Ba,YAAY,IAAID,OAAO;UACvB+G,SAAS,IAAI3H,IAAI;UACjB4H,WAAW,IAAI5G,MAAM;UAErB,IAAI,CAAC6G,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,EAAE;YAC/BgI,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,GAAG;cAC3B4B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI;cAClBb,OAAO,EAAE,CAAC;cACVZ,IAAI,EAAE,CAAC;cACPgB,MAAM,EAAE,CAAC;cACT8G,MAAM,EAAE;YACV,CAAC;UACH;UAEAD,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACe,OAAO,IAAIA,OAAO;UAC7CiH,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACG,IAAI,IAAIA,IAAI;UACvC6H,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACmB,MAAM,IAAIA,MAAM;UAC3C6G,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACiI,MAAM,GAC/BD,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACe,OAAO,GAAG,CAAC,GACrCiH,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACmB,MAAM,GAAG6G,cAAc,CAACnI,OAAO,CAACG,EAAE,CAAC,CAACe,OAAO,GAAG,GAAG,GAAI,CAAC;QACtF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ,MAAMmH,aAAa,GAAGlH,YAAY,GAAG,CAAC,GAAI+G,WAAW,GAAG/G,YAAY,GAAG,GAAG,GAAI,CAAC;IAE/E,OAAO;MACLJ,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE,MAAM/B,SAAS,CAACgC,kBAAkB,CAAC,OAAO,CAAC,QAAQ/B,OAAO,CAAC+B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAChGzD,IAAI,EAAE;QACJ8K,OAAO,EAAE;UACPnH,YAAY;UACZ8G,SAAS;UACTC,WAAW;UACXG;QACF,CAAC;QACD3L,QAAQ,EAAE8K,MAAM,CAACC,MAAM,CAACU,cAAc,CAAC,CACpCT,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACtG,MAAM,GAAGqG,CAAC,CAACrG,MAAM,CAAC,CACnCuG,KAAK,CAAC,CAAC,EAAE,EAAE;MAChB;IACF,CAAC;EACH,CAAC;EAED,MAAM1J,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMoK,KAAK,GAAG,IAAI3M,IAAI,CAAC,CAAC,CAACqK,YAAY,CAAC,CAAC;IACvC,MAAMuC,aAAa,GAAGxN,QAAQ,CAACyD,WAAW,CAAC,CAAC,CAACK,MAAM,CAACM,GAAG,IACrD,IAAIxD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC,CAAC2G,YAAY,CAAC,CAAC,KAAKsC,KAC/C,CAAC;IACD,MAAME,aAAa,GAAGzN,QAAQ,CAAC6D,WAAW,CAAC,CAAC,CAACC,MAAM,CAACC,CAAC,IACnD,IAAInD,IAAI,CAACmD,CAAC,CAAC4B,WAAW,CAAC,CAACsF,YAAY,CAAC,CAAC,KAAKsC,KAC7C,CAAC;IAED,MAAMrC,UAAU,GAAGsC,aAAa,CAACjJ,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAKI,GAAG,GAAGJ,GAAG,CAACK,KAAK,EAAE,CAAC,CAAC;IACzE,MAAM0G,aAAa,GAAGsC,aAAa,CAAC3J,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;IAC3G,MAAMwF,aAAa,GAAGqC,aAAa,CAAC3J,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAET,CAAC,KAAKS,GAAG,GAAGT,CAAC,CAAC6B,MAAM,EAAE,CAAC,CAAC;;IAE3G;IACA,MAAM8H,cAAc,GAAG,CAAC,CAAC;IACzBF,aAAa,CAAC5D,OAAO,CAAC0C,OAAO,IAAI;MAC/B,MAAMqB,MAAM,GAAGrB,OAAO,CAACsB,aAAa,IAAI,OAAO;MAC/C,IAAI,CAACF,cAAc,CAACC,MAAM,CAAC,EAAE;QAC3BD,cAAc,CAACC,MAAM,CAAC,GAAG,CAAC;MAC5B;MACAD,cAAc,CAACC,MAAM,CAAC,IAAIrB,OAAO,CAAC7H,KAAK;IACzC,CAAC,CAAC;IAEF,OAAO;MACLsB,KAAK,EAAE,eAAe;MACtBiC,IAAI,EAAE,IAAIpH,IAAI,CAAC,CAAC,CAACqF,kBAAkB,CAAC,OAAO,CAAC;MAC5C4H,IAAI,EAAE,IAAIjN,IAAI,CAAC,CAAC,CAACkN,kBAAkB,CAAC,OAAO,CAAC;MAC5CtL,IAAI,EAAE;QACJ8I,KAAK,EAAE;UACLC,aAAa,EAAEiC,aAAa,CAAChC,MAAM;UACnCN,UAAU;UACVwC;QACF,CAAC;QACDvF,IAAI,EAAE;UACJuD,QAAQ,EAAEP,aAAa;UACvBQ,QAAQ,EAAEP,aAAa;UACvBC,OAAO,EAAEF,aAAa,GAAGC;QAC3B,CAAC;QACD2C,OAAO,EAAE;UACPC,cAAc,EAAE,CAAC;UAAE;UACnBC,WAAW,EAAE/C,UAAU,GAAGC,aAAa;UACvC+C,aAAa,EAAE9C,aAAa;UAC5B+C,cAAc,EAAEjD,UAAU,GAAGC,aAAa,GAAGC;QAC/C;MACF;IACF,CAAC;EACH,CAAC;EAED,MAAMS,qBAAqB,GAAI7D,IAAI,IAAK;IACtC,MAAMxE,QAAQ,GAAGxD,QAAQ,CAACyD,WAAW,CAAC,CAAC,CAACK,MAAM,CAACM,GAAG,IAChD,IAAIxD,IAAI,CAACwD,GAAG,CAACE,WAAW,CAAC,CAAC2G,YAAY,CAAC,CAAC,KAAKjD,IAAI,CAACiD,YAAY,CAAC,CACjE,CAAC;IAED,MAAMoB,YAAY,GAAG,CAAC,CAAC;IAEvB7I,QAAQ,CAACoG,OAAO,CAAC0C,OAAO,IAAI;MAC1BA,OAAO,CAACzH,KAAK,CAAC+E,OAAO,CAAC7E,IAAI,IAAI;QAC5B,MAAMC,OAAO,GAAGtD,QAAQ,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACK,SAAS,CAAC;QAC3D,IAAIJ,OAAO,EAAE;UACX,IAAI,CAACqH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,EAAE;YAC7BkH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,GAAG;cACzB4B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI;cAClB1B,QAAQ,EAAE,CAAC;cACXa,OAAO,EAAE;YACX,CAAC;UACH;UACAmG,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,CAACE,QAAQ,IAAIN,IAAI,CAACM,QAAQ;UAClDgH,YAAY,CAACrH,OAAO,CAACG,EAAE,CAAC,CAACe,OAAO,IAAInB,IAAI,CAACN,KAAK;QAChD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO+H,MAAM,CAACC,MAAM,CAACJ,YAAY,CAAC,CAC/BK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC1G,OAAO,GAAGyG,CAAC,CAACzG,OAAO,CAAC,CACrC2G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,MAAMuB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACpN,UAAU,EAAE;IAEjB,MAAMqN,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7C,MAAMC,QAAQ,GAAGxO,QAAQ,CAACyO,WAAW,CAAC,CAAC;IAEvC,IAAIC,YAAY,GAAG;AACvB;AACA;AACA,mBAAmB1N,UAAU,CAAC+E,KAAK;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkByI,QAAQ,CAACG,WAAW;AACtC;AACA,mBAAmBH,QAAQ,CAACI,OAAO;AACnC,yBAAyBJ,QAAQ,CAACK,KAAK,qBAAqBL,QAAQ,CAACM,KAAK;AAC1E,kCAAkCN,QAAQ,CAACO,SAAS;AACpD;AACA,wCAAwC/N,UAAU,CAAC+E,KAAK;AACxD,cAAc/E,UAAU,CAACgF,MAAM,GAAG,8BAA8BhF,UAAU,CAACgF,MAAM,QAAQ,GAAG,EAAE;AAC9F,cAAchF,UAAU,CAACgH,IAAI,GAAG,sCAAsChH,UAAU,CAACgH,IAAI,QAAQ,GAAG,EAAE;AAClG;AACA,KAAK;;IAED;IACA0G,YAAY,IAAIM,kBAAkB,CAAChO,UAAU,CAAC;IAE9C0N,YAAY,IAAI;AACpB;AACA;AACA,gCAAgC,IAAI9N,IAAI,CAAC,CAAC,CAACqO,cAAc,CAAC,OAAO,CAAC;AAClE;AACA;AACA;AACA,KAAK;IAEDZ,WAAW,CAACa,QAAQ,CAACC,KAAK,CAACT,YAAY,CAAC;IACxCL,WAAW,CAACa,QAAQ,CAACE,KAAK,CAAC,CAAC;IAC5BC,UAAU,CAAC,MAAMhB,WAAW,CAACiB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C,CAAC;EAED,MAAMN,kBAAkB,GAAIxM,IAAI,IAAK;IACnC;IACA;IACA,OAAO;AACX;AACA,eAAe+M,IAAI,CAACC,SAAS,CAAChN,IAAI,CAACA,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACjD;AACA,KAAK;EACH,CAAC;EAED,MAAMiN,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACzO,UAAU,EAAE;;IAEjB;IACA,MAAM0O,UAAU,GAAGC,YAAY,CAAC3O,UAAU,CAAC;IAC3C,MAAM4O,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAE1L,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAM8L,IAAI,GAAGZ,QAAQ,CAACa,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrCE,IAAI,CAACK,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BF,IAAI,CAACK,YAAY,CAAC,UAAU,EAAE,GAAGnP,UAAU,CAAC+E,KAAK,IAAI,IAAInF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAClGgP,IAAI,CAACM,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCnB,QAAQ,CAACoB,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC;IACZtB,QAAQ,CAACoB,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;EACjC,CAAC;EAED,MAAMH,YAAY,GAAInN,IAAI,IAAK;IAC7B;IACA,OAAO+M,IAAI,CAACC,SAAS,CAAChN,IAAI,CAACA,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;EAC3C,CAAC;EAED,oBACEtC,OAAA;IAAKwQ,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBACzCzQ,OAAA;MAAKwQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzQ,OAAA;QAAAyQ,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B7Q,OAAA;QAAAyQ,QAAA,EAAG;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAGN7Q,OAAA;MAAKwQ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BzQ,OAAA;QAAKwQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/B/O,WAAW,CAAC4H,GAAG,CAACxF,IAAI,iBACnB9D,OAAA;UAEEwQ,SAAS,EAAE,oBAAoBnQ,UAAU,KAAKyD,IAAI,CAACnC,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3EmP,OAAO,EAAEA,CAAA,KAAMxQ,aAAa,CAACwD,IAAI,CAACnC,KAAK,CAAE;UAAA8O,QAAA,gBAEzCzQ,OAAA;YAAKwQ,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE3M,IAAI,CAACjC;UAAI;YAAA6O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9C7Q,OAAA;YAAKwQ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE3M,IAAI,CAAClC;UAAK;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAL3C/M,IAAI,CAACnC,KAAK;UAAA+O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMT,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7Q,OAAA;MAAKwQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BzQ,OAAA;QAAKwQ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzQ,OAAA;UAAKwQ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzQ,OAAA;YAAAyQ,QAAA,EAAO;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvB7Q,OAAA;YACE8D,IAAI,EAAC,MAAM;YACXnC,KAAK,EAAEpB,SAAS,CAACE,KAAM;YACvBsQ,QAAQ,EAAGC,CAAC,IAAKxQ,YAAY,CAACyQ,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAExQ,KAAK,EAAEuQ,CAAC,CAACE,MAAM,CAACvP;YAAM,CAAC,CAAC;UAAE;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7Q,OAAA;UAAKwQ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzQ,OAAA;YAAAyQ,QAAA,EAAO;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxB7Q,OAAA;YACE8D,IAAI,EAAC,MAAM;YACXnC,KAAK,EAAEpB,SAAS,CAACM,GAAI;YACrBkQ,QAAQ,EAAGC,CAAC,IAAKxQ,YAAY,CAACyQ,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEpQ,GAAG,EAAEmQ,CAAC,CAACE,MAAM,CAACvP;YAAM,CAAC,CAAC;UAAE;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEJxQ,UAAU,KAAK,mBAAmB,iBAClCL,OAAA;QAAKwQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzQ,OAAA;UAAKwQ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzQ,OAAA;YAAAyQ,QAAA,EAAO;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrB7Q,OAAA;YACE2B,KAAK,EAAET,gBAAiB;YACxB6P,QAAQ,EAAGC,CAAC,IAAK7P,mBAAmB,CAAC6P,CAAC,CAACE,MAAM,CAACvP,KAAK,CAAE;YAAA8O,QAAA,gBAErDzQ,OAAA;cAAQ2B,KAAK,EAAC,EAAE;cAAA8O,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACrCvP,SAAS,CAACgI,GAAG,CAAClC,QAAQ,iBACrBpH,OAAA;cAA0B2B,KAAK,EAAEyF,QAAQ,CAACnC,EAAG;cAAAwL,QAAA,EAC1CrJ,QAAQ,CAACP;YAAI,GADHO,QAAQ,CAACnC,EAAE;cAAAyL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED7Q,OAAA;QAAKwQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzQ,OAAA;UACE8Q,OAAO,EAAEzO,cAAe;UACxB8O,QAAQ,EAAEnQ,OAAQ;UAClBwP,SAAS,EAAC,cAAc;UAAAC,QAAA,EAEvBzP,OAAO,GAAG,mBAAmB,GAAG;QAAkB;UAAA0P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EACR/P,UAAU,iBACTd,OAAA,CAAAE,SAAA;UAAAuQ,QAAA,gBACEzQ,OAAA;YAAQ8Q,OAAO,EAAE5C,WAAY;YAACsC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7Q,OAAA;YAAQ8Q,OAAO,EAAEvB,aAAc;YAACiB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/P,UAAU,iBACTd,OAAA;MAAKwQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzQ,OAAA;QAAKwQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzQ,OAAA;UAAAyQ,QAAA,EAAK3P,UAAU,CAAC+E;QAAK;UAAA6K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC1B/P,UAAU,CAACgF,MAAM,iBAAI9F,OAAA;UAAGwQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE3P,UAAU,CAACgF;QAAM;UAAA4K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzE/P,UAAU,CAACgH,IAAI,iBAAI9H,OAAA;UAAGwQ,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,wCAAQ,EAAC3P,UAAU,CAACgH,IAAI;QAAA;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAEN7Q,OAAA;QAAKwQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5B3P,UAAU,CAACqB,KAAK,gBACfnC,OAAA;UAAKwQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,SAC3B,EAAC3P,UAAU,CAACqB,KAAK;QAAA;UAAAuO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,gBAEN7Q,OAAA;UAAKwQ,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BzQ,OAAA;YAAAyQ,QAAA,EAAMpB,IAAI,CAACC,SAAS,CAACxO,UAAU,CAACwB,IAAI,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAoO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzQ,EAAA,CAl3BID,eAAe;AAAAiR,EAAA,GAAfjR,eAAe;AAo3BrB,eAAeA,eAAe;AAAC,IAAAiR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}