import React, { useState, useEffect } from 'react';
import Layout from './components/Layout/Layout';
import EnhancedLogin from './components/Login/EnhancedLogin';
import EnhancedDashboard from './components/Dashboard/EnhancedDashboard';
import POS from './components/POS/POS';
import Products from './components/Products/Products';
import Customers from './components/Customers/Customers';
import Suppliers from './components/Suppliers/Suppliers';
import Invoices from './components/Invoices/Invoices';
import Accounting from './components/Accounting/Accounting';
import Reports from './components/Reports/Reports';
import Settings from './components/Settings/Settings';
import Backup from './components/Backup/Backup';
import Employees from './components/Employees/Employees';
import Notifications from './components/Notifications/Notifications';
import JournalEntries from './components/JournalEntries/JournalEntries';
import Returns from './components/Returns/Returns';
import BarcodeManager from './components/Barcode/BarcodeManager';
import Quotations from './components/Quotations/Quotations';
import Vouchers from './components/Vouchers/Vouchers';
import AdvancedReports from './components/AdvancedReports/AdvancedReports';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('login');
  const [user, setUser] = useState(null);

  useEffect(() => {
    // التحقق من وجود جلسة مستخدم محفوظة
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setCurrentView('pos'); // Start with POS as main screen
    }
  }, []);

  const handleLogin = (userData) => {
    setUser(userData);
    localStorage.setItem('currentUser', JSON.stringify(userData));
    setCurrentView('pos'); // Go directly to POS after login
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
    setCurrentView('login');
  };

  const handleNavigate = (view) => {
    setCurrentView(view);
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
  };

  // عرض صفحة تسجيل الدخول
  if (currentView === 'login') {
    return <EnhancedLogin onLogin={handleLogin} />;
  }

  // إذا كان المستخدم مسجل دخول، عرض التطبيق مع Layout
  if (user) {
    let currentComponent;

    switch (currentView) {
      case 'dashboard':
        currentComponent = <EnhancedDashboard user={user} onLogout={handleLogout} onNavigate={handleNavigate} />;
        break;
      case 'pos':
        currentComponent = <POS user={user} onBack={handleBackToDashboard} />;
        break;
      case 'reports':
        currentComponent = <Reports user={user} onBack={handleBackToDashboard} />;
        break;
      case 'products':
        currentComponent = <Products user={user} onBack={handleBackToDashboard} />;
        break;
      case 'customers':
        currentComponent = <Customers user={user} onBack={handleBackToDashboard} />;
        break;
      case 'suppliers':
        currentComponent = <Suppliers user={user} onBack={handleBackToDashboard} />;
        break;
      case 'accounting':
        currentComponent = <Accounting user={user} onBack={handleBackToDashboard} />;
        break;
      case 'settings':
        currentComponent = <Settings user={user} onBack={handleBackToDashboard} />;
        break;
      case 'employees':
        currentComponent = <Employees user={user} onBack={handleBackToDashboard} />;
        break;
      case 'backup':
        currentComponent = <Backup user={user} onBack={handleBackToDashboard} />;
        break;
      case 'notifications':
        currentComponent = <Notifications user={user} onBack={handleBackToDashboard} />;
        break;
      case 'journal-entries':
        currentComponent = <JournalEntries user={user} onBack={handleBackToDashboard} />;
        break;
      case 'returns':
        currentComponent = <Returns user={user} onBack={handleBackToDashboard} />;
        break;
      case 'barcode':
        currentComponent = <BarcodeManager user={user} onBack={handleBackToDashboard} />;
        break;
      case 'quotations':
        currentComponent = <Quotations user={user} onBack={handleBackToDashboard} />;
        break;
      case 'vouchers':
        currentComponent = <Vouchers user={user} onBack={handleBackToDashboard} />;
        break;
      case 'advanced-reports':
        currentComponent = <AdvancedReports user={user} onBack={handleBackToDashboard} />;
        break;
      case 'invoices':
        currentComponent = <Invoices user={user} onBack={handleBackToDashboard} />;
        break;
      default:
        currentComponent = <EnhancedDashboard user={user} onLogout={handleLogout} onNavigate={handleNavigate} />;
    }

    return (
      <Layout
        currentView={currentView}
        onNavigate={handleNavigate}
        user={user}
        onLogout={handleLogout}
      >
        {currentComponent}
      </Layout>
    );
  }

  // إذا لم يكن هناك مستخدم مسجل دخول، عرض صفحة تسجيل الدخول
  return <EnhancedLogin onLogin={handleLogin} />;
}

export default App;
