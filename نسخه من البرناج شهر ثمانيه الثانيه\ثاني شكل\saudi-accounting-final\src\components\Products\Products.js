import React, { useState, useEffect } from 'react';
import './Products.css';
import database from '../../utils/database';

const Products = ({ user, onBack }) => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState(['إلكترونيات', 'مكتبية', 'منزلية', 'ملابس', 'أغذية']);
  const [units, setUnits] = useState(['قطعة', 'كيلو', 'لتر', 'متر', 'علبة', 'كرتون']);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [isLoading, setIsLoading] = useState(false);

  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: '',
    stock: '',
    category: '',
    unit: 'قطعة',
    barcode: '',
    minStock: '5',
    vatRate: 0.15,
    cost: '',
    supplier: '',
    location: ''
  });

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = () => {
    try {
      const dbProducts = database.getProducts();
      setProducts(dbProducts);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
    }
  };

  const generateBarcode = () => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return timestamp.slice(-10) + random;
  };

  const handleAddProduct = async () => {
    if (!newProduct.name || !newProduct.price || !newProduct.stock) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setIsLoading(true);
    try {
      const productData = {
        ...newProduct,
        price: parseFloat(newProduct.price),
        stock: parseInt(newProduct.stock),
        minStock: parseInt(newProduct.minStock),
        cost: parseFloat(newProduct.cost) || 0,
        barcode: newProduct.barcode || generateBarcode()
      };

      const savedProduct = database.addProduct(productData);
      setProducts([...products, savedProduct]);
      
      setNewProduct({
        name: '',
        description: '',
        price: '',
        stock: '',
        category: '',
        unit: 'قطعة',
        barcode: '',
        minStock: '5',
        vatRate: 0.15,
        cost: '',
        supplier: '',
        location: ''
      });
      
      setShowAddForm(false);
      alert('تم إضافة المنتج بنجاح');
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      alert('فشل في إضافة المنتج');
    }
    setIsLoading(false);
  };

  const handleEditProduct = async () => {
    if (!editingProduct.name || !editingProduct.price || !editingProduct.stock) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setIsLoading(true);
    try {
      const productData = {
        ...editingProduct,
        price: parseFloat(editingProduct.price),
        stock: parseInt(editingProduct.stock),
        minStock: parseInt(editingProduct.minStock),
        cost: parseFloat(editingProduct.cost) || 0
      };

      const updatedProduct = database.updateProduct(editingProduct.id, productData);
      setProducts(products.map(p => p.id === editingProduct.id ? updatedProduct : p));
      
      setEditingProduct(null);
      alert('تم تحديث المنتج بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      alert('فشل في تحديث المنتج');
    }
    setIsLoading(false);
  };

  const handleDeleteProduct = (productId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        database.deleteProduct(productId);
        setProducts(products.filter(p => p.id !== productId));
        alert('تم حذف المنتج بنجاح');
      } catch (error) {
        console.error('خطأ في حذف المنتج:', error);
        alert('فشل في حذف المنتج');
      }
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode.includes(searchTerm) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'price':
        return a.price - b.price;
      case 'stock':
        return a.stock - b.stock;
      case 'category':
        return a.category.localeCompare(b.category);
      default:
        return 0;
    }
  });

  const getLowStockProducts = () => {
    return products.filter(product => product.stock <= product.minStock);
  };

  const getTotalValue = () => {
    return products.reduce((total, product) => total + (product.price * product.stock), 0);
  };

  return (
    <div className="products-container">
      <div className="products-header">
        <div className="header-content">
          <button onClick={onBack} className="back-btn">
            ← العودة للوحة التحكم
          </button>
          <h1>📦 إدارة المنتجات</h1>
          <button 
            onClick={() => setShowAddForm(true)} 
            className="add-btn"
            disabled={isLoading}
          >
            ➕ إضافة منتج جديد
          </button>
        </div>
      </div>

      <div className="products-stats">
        <div className="stat-card">
          <div className="stat-icon">📦</div>
          <div className="stat-info">
            <h3>إجمالي المنتجات</h3>
            <p>{products.length}</p>
          </div>
        </div>
        <div className="stat-card warning">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <h3>منتجات منخفضة المخزون</h3>
            <p>{getLowStockProducts().length}</p>
          </div>
        </div>
        <div className="stat-card success">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>قيمة المخزون الإجمالية</h3>
            <p>{getTotalValue().toFixed(2)} ريال</p>
          </div>
        </div>
        <div className="stat-card info">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <h3>الفئات</h3>
            <p>{categories.length}</p>
          </div>
        </div>
      </div>

      <div className="products-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث بالاسم أو الباركود أو الوصف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="filter-select"
        >
          <option value="">جميع الفئات</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="sort-select"
        >
          <option value="name">ترتيب بالاسم</option>
          <option value="price">ترتيب بالسعر</option>
          <option value="stock">ترتيب بالمخزون</option>
          <option value="category">ترتيب بالفئة</option>
        </select>
      </div>

      <div className="products-table-container">
        <table className="products-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>الفئة</th>
              <th>السعر</th>
              <th>المخزون</th>
              <th>الوحدة</th>
              <th>الباركود</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {sortedProducts.map(product => (
              <tr key={product.id} className={product.stock <= product.minStock ? 'low-stock' : ''}>
                <td>
                  <div className="product-name">
                    <strong>{product.name}</strong>
                    {product.description && <small>{product.description}</small>}
                  </div>
                </td>
                <td>{product.category}</td>
                <td>{product.price.toFixed(2)} ريال</td>
                <td>
                  <span className={product.stock <= product.minStock ? 'stock-warning' : 'stock-normal'}>
                    {product.stock}
                  </span>
                </td>
                <td>{product.unit}</td>
                <td>{product.barcode}</td>
                <td>
                  <span className={`status ${product.stock > product.minStock ? 'available' : 'low'}`}>
                    {product.stock > product.minStock ? 'متوفر' : 'منخفض'}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button 
                      onClick={() => setEditingProduct(product)}
                      className="edit-btn"
                      disabled={isLoading}
                    >
                      ✏️
                    </button>
                    <button 
                      onClick={() => handleDeleteProduct(product.id)}
                      className="delete-btn"
                      disabled={isLoading}
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {sortedProducts.length === 0 && (
          <div className="no-products">
            <p>لا توجد منتجات تطابق البحث</p>
          </div>
        )}
      </div>

      {showAddForm && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>إضافة منتج جديد</h2>
              <button onClick={() => setShowAddForm(false)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم المنتج *</label>
                  <input
                    type="text"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={newProduct.category}
                    onChange={(e) => setNewProduct({...newProduct, category: e.target.value})}
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>السعر *</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct({...newProduct, price: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>تكلفة الشراء</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newProduct.cost}
                    onChange={(e) => setNewProduct({...newProduct, cost: e.target.value})}
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>الكمية *</label>
                  <input
                    type="number"
                    value={newProduct.stock}
                    onChange={(e) => setNewProduct({...newProduct, stock: e.target.value})}
                    placeholder="0"
                  />
                </div>

                <div className="form-group">
                  <label>الحد الأدنى للمخزون</label>
                  <input
                    type="number"
                    value={newProduct.minStock}
                    onChange={(e) => setNewProduct({...newProduct, minStock: e.target.value})}
                    placeholder="5"
                  />
                </div>

                <div className="form-group">
                  <label>الوحدة</label>
                  <select
                    value={newProduct.unit}
                    onChange={(e) => setNewProduct({...newProduct, unit: e.target.value})}
                  >
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الباركود</label>
                  <div className="barcode-input">
                    <input
                      type="text"
                      value={newProduct.barcode}
                      onChange={(e) => setNewProduct({...newProduct, barcode: e.target.value})}
                      placeholder="سيتم إنشاؤه تلقائياً"
                    />
                    <button 
                      type="button" 
                      onClick={() => setNewProduct({...newProduct, barcode: generateBarcode()})}
                      className="generate-btn"
                    >
                      إنشاء
                    </button>
                  </div>
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newProduct.description}
                    onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                    placeholder="وصف المنتج (اختياري)"
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>المورد</label>
                  <input
                    type="text"
                    value={newProduct.supplier}
                    onChange={(e) => setNewProduct({...newProduct, supplier: e.target.value})}
                    placeholder="اسم المورد"
                  />
                </div>

                <div className="form-group">
                  <label>الموقع</label>
                  <input
                    type="text"
                    value={newProduct.location}
                    onChange={(e) => setNewProduct({...newProduct, location: e.target.value})}
                    placeholder="موقع المنتج في المخزن"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowAddForm(false)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleAddProduct} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري الحفظ...' : 'حفظ المنتج'}
              </button>
            </div>
          </div>
        </div>
      )}

      {editingProduct && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>تعديل المنتج</h2>
              <button onClick={() => setEditingProduct(null)} className="close-btn">×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم المنتج *</label>
                  <input
                    type="text"
                    value={editingProduct.name}
                    onChange={(e) => setEditingProduct({...editingProduct, name: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={editingProduct.category}
                    onChange={(e) => setEditingProduct({...editingProduct, category: e.target.value})}
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>السعر *</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingProduct.price}
                    onChange={(e) => setEditingProduct({...editingProduct, price: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>تكلفة الشراء</label>
                  <input
                    type="number"
                    step="0.01"
                    value={editingProduct.cost || ''}
                    onChange={(e) => setEditingProduct({...editingProduct, cost: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الكمية *</label>
                  <input
                    type="number"
                    value={editingProduct.stock}
                    onChange={(e) => setEditingProduct({...editingProduct, stock: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الحد الأدنى للمخزون</label>
                  <input
                    type="number"
                    value={editingProduct.minStock}
                    onChange={(e) => setEditingProduct({...editingProduct, minStock: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الوحدة</label>
                  <select
                    value={editingProduct.unit}
                    onChange={(e) => setEditingProduct({...editingProduct, unit: e.target.value})}
                  >
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الباركود</label>
                  <input
                    type="text"
                    value={editingProduct.barcode}
                    onChange={(e) => setEditingProduct({...editingProduct, barcode: e.target.value})}
                  />
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={editingProduct.description || ''}
                    onChange={(e) => setEditingProduct({...editingProduct, description: e.target.value})}
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label>المورد</label>
                  <input
                    type="text"
                    value={editingProduct.supplier || ''}
                    onChange={(e) => setEditingProduct({...editingProduct, supplier: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label>الموقع</label>
                  <input
                    type="text"
                    value={editingProduct.location || ''}
                    onChange={(e) => setEditingProduct({...editingProduct, location: e.target.value})}
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setEditingProduct(null)} className="cancel-btn">
                إلغاء
              </button>
              <button onClick={handleEditProduct} className="save-btn" disabled={isLoading}>
                {isLoading ? 'جاري التحديث...' : 'تحديث المنتج'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
