{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = ({\n  user,\n  onLogout,\n  onNavigate\n}) => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // تحديث الوقت كل ثانية\n  React.useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // بيانات وهمية للإحصائيات\n  const stats = {\n    totalSales: 125000,\n    todaySales: 8500,\n    totalCustomers: 342,\n    pendingInvoices: 15\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const formatTime = date => {\n    return date.toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"dashboard-title\",\n            children: \"\\uD83E\\uDDEE \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"current-time\",\n            children: formatTime(currentTime)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-name\",\n              children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-role\",\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onLogout,\n            className: \"logout-btn\",\n            children: \"\\uD83D\\uDEAA \\u062A\\u0633\\u062C\\u064A\\u0644 \\u062E\\u0631\\u0648\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"stats-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card sales\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: formatCurrency(stats.totalSales)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card today\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: formatCurrency(stats.todaySales)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+8.2%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card customers\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: stats.totalCustomers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+5 \\u062C\\u062F\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card invoices\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0645\\u0639\\u0644\\u0642\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: stats.pendingInvoices\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change neutral\",\n                children: \"\\u0628\\u062D\\u0627\\u062C\\u0629 \\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"actions-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"\\u26A1 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"actions-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0639\\u0631\\u0636 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0648\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCB3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"f3elDUct5ap4W3FuLtGG73aMsLc=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Dashboard", "user", "onLogout", "onNavigate", "_s", "currentTime", "setCurrentTime", "Date", "useEffect", "timer", "setInterval", "clearInterval", "stats", "totalSales", "todaySales", "totalCustomers", "pendingInvoices", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatTime", "date", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "second", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Dashboard/Dashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Dashboard.css';\n\nconst Dashboard = ({ user, onLogout, onNavigate }) => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // تحديث الوقت كل ثانية\n  React.useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  // بيانات وهمية للإحصائيات\n  const stats = {\n    totalSales: 125000,\n    todaySales: 8500,\n    totalCustomers: 342,\n    pendingInvoices: 15\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      {/* شريط التنقل العلوي */}\n      <header className=\"dashboard-header\">\n        <div className=\"header-content\">\n          <div className=\"header-left\">\n            <h1 className=\"dashboard-title\">🧮 نظام المحاسبة السعودي</h1>\n            <p className=\"current-time\">{formatTime(currentTime)}</p>\n          </div>\n          <div className=\"header-right\">\n            <div className=\"user-info\">\n              <span className=\"user-name\">مرحباً، {user.name}</span>\n              <span className=\"user-role\">{user.role}</span>\n            </div>\n            <button onClick={onLogout} className=\"logout-btn\">\n              🚪 تسجيل خروج\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* المحتوى الرئيسي */}\n      <main className=\"dashboard-main\">\n        {/* بطاقات الإحصائيات */}\n        <section className=\"stats-section\">\n          <h2 className=\"section-title\">📊 الإحصائيات السريعة</h2>\n          <div className=\"stats-grid\">\n            <div className=\"stat-card sales\">\n              <div className=\"stat-icon\">💰</div>\n              <div className=\"stat-content\">\n                <h3>إجمالي المبيعات</h3>\n                <p className=\"stat-value\">{formatCurrency(stats.totalSales)}</p>\n                <span className=\"stat-change positive\">+12.5%</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card today\">\n              <div className=\"stat-icon\">📈</div>\n              <div className=\"stat-content\">\n                <h3>مبيعات اليوم</h3>\n                <p className=\"stat-value\">{formatCurrency(stats.todaySales)}</p>\n                <span className=\"stat-change positive\">+8.2%</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card customers\">\n              <div className=\"stat-icon\">👥</div>\n              <div className=\"stat-content\">\n                <h3>إجمالي العملاء</h3>\n                <p className=\"stat-value\">{stats.totalCustomers}</p>\n                <span className=\"stat-change positive\">+5 جديد</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card invoices\">\n              <div className=\"stat-icon\">📋</div>\n              <div className=\"stat-content\">\n                <h3>فواتير معلقة</h3>\n                <p className=\"stat-value\">{stats.pendingInvoices}</p>\n                <span className=\"stat-change neutral\">بحاجة متابعة</span>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* الإجراءات السريعة */}\n        <section className=\"actions-section\">\n          <h2 className=\"section-title\">⚡ الإجراءات السريعة</h2>\n          <div className=\"actions-grid\">\n            <button className=\"action-card\">\n              <div className=\"action-icon\">🛒</div>\n              <h3>نقطة البيع</h3>\n              <p>إنشاء فاتورة جديدة</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">📦</div>\n              <h3>إدارة المخزون</h3>\n              <p>عرض وإدارة المنتجات</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">👤</div>\n              <h3>إدارة العملاء</h3>\n              <p>إضافة وتعديل العملاء</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">📊</div>\n              <h3>التقارير</h3>\n              <p>عرض التقارير المالية</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">⚙️</div>\n              <h3>الإعدادات</h3>\n              <p>إعدادات النظام</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">💳</div>\n              <h3>المدفوعات</h3>\n              <p>إدارة المدفوعات</p>\n            </button>\n          </div>\n        </section>\n      </main>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,IAAIU,IAAI,CAAC,CAAC,CAAC;;EAE1D;EACAX,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BJ,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMI,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,KAAK,GAAG;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,GAAG;IACnBC,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAClCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACElC,OAAA;IAAKmC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCpC,OAAA;MAAQmC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAClCpC,OAAA;QAAKmC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAImC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DxC,OAAA;YAAGmC,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEX,UAAU,CAACnB,WAAW;UAAC;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNxC,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpC,OAAA;cAAMmC,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,6CAAQ,EAAClC,IAAI,CAACuC,IAAI;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDxC,OAAA;cAAMmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAElC,IAAI,CAACwC;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNxC,OAAA;YAAQ2C,OAAO,EAAExC,QAAS;YAACgC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTxC,OAAA;MAAMmC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE9BpC,OAAA;QAASmC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAChCpC,OAAA;UAAImC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDxC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAKmC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCxC,OAAA;cAAKmC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpC,OAAA;gBAAAoC,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBxC,OAAA;gBAAGmC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElB,cAAc,CAACL,KAAK,CAACC,UAAU;cAAC;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChExC,OAAA;gBAAMmC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCxC,OAAA;cAAKmC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpC,OAAA;gBAAAoC,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBxC,OAAA;gBAAGmC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElB,cAAc,CAACL,KAAK,CAACE,UAAU;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChExC,OAAA;gBAAMmC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCxC,OAAA;cAAKmC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpC,OAAA;gBAAAoC,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBxC,OAAA;gBAAGmC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEvB,KAAK,CAACG;cAAc;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDxC,OAAA;gBAAMmC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCxC,OAAA;cAAKmC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpC,OAAA;gBAAAoC,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBxC,OAAA;gBAAGmC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEvB,KAAK,CAACI;cAAe;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDxC,OAAA;gBAAMmC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVxC,OAAA;QAASmC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAClCpC,OAAA;UAAImC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDxC,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBxC,OAAA;cAAAoC,QAAA,EAAG;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAETxC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBxC,OAAA;cAAAoC,QAAA,EAAG;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAETxC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBxC,OAAA;cAAAoC,QAAA,EAAG;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAETxC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBxC,OAAA;cAAAoC,QAAA,EAAG;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAETxC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBxC,OAAA;cAAAoC,QAAA,EAAG;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAETxC,OAAA;YAAQmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCxC,OAAA;cAAAoC,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBxC,OAAA;cAAAoC,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnC,EAAA,CApJIJ,SAAS;AAAA2C,EAAA,GAAT3C,SAAS;AAsJf,eAAeA,SAAS;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}