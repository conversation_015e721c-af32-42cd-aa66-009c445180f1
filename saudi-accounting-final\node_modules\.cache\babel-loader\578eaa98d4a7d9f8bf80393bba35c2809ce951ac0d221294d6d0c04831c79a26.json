{"ast": null, "code": "// Test database functionality\nimport database from './database';\n\n// Test function to verify database works\nexport const testDatabase = () => {\n  console.log('🧪 اختبار قاعدة البيانات...');\n  try {\n    // Initialize database\n    database.initializeDatabase();\n    console.log('✅ تم تهيئة قاعدة البيانات');\n\n    // Test users\n    const users = database.getUsers();\n    console.log('👥 المستخدمين:', users);\n\n    // Test authentication\n    const user = database.authenticateUser('admin', 'admin123');\n    console.log('🔐 اختبار المصادقة:', user);\n    if (user) {\n      console.log('✅ تسجيل الدخول نجح!');\n      return true;\n    } else {\n      console.log('❌ فشل تسجيل الدخول');\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ خطأ في اختبار قاعدة البيانات:', error);\n    return false;\n  }\n};\n\n// Run test immediately\ntestDatabase();", "map": {"version": 3, "names": ["database", "testDatabase", "console", "log", "initializeDatabase", "users", "getUsers", "user", "authenticateUser", "error"], "sources": ["D:/aronim/saudi-accounting-final/src/utils/testDatabase.js"], "sourcesContent": ["// Test database functionality\nimport database from './database';\n\n// Test function to verify database works\nexport const testDatabase = () => {\n  console.log('🧪 اختبار قاعدة البيانات...');\n  \n  try {\n    // Initialize database\n    database.initializeDatabase();\n    console.log('✅ تم تهيئة قاعدة البيانات');\n    \n    // Test users\n    const users = database.getUsers();\n    console.log('👥 المستخدمين:', users);\n    \n    // Test authentication\n    const user = database.authenticateUser('admin', 'admin123');\n    console.log('🔐 اختبار المصادقة:', user);\n    \n    if (user) {\n      console.log('✅ تسجيل الدخول نجح!');\n      return true;\n    } else {\n      console.log('❌ فشل تسجيل الدخول');\n      return false;\n    }\n    \n  } catch (error) {\n    console.error('❌ خطأ في اختبار قاعدة البيانات:', error);\n    return false;\n  }\n};\n\n// Run test immediately\ntestDatabase();\n"], "mappings": "AAAA;AACA,OAAOA,QAAQ,MAAM,YAAY;;AAEjC;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAChCC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAE1C,IAAI;IACF;IACAH,QAAQ,CAACI,kBAAkB,CAAC,CAAC;IAC7BF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;IAExC;IACA,MAAME,KAAK,GAAGL,QAAQ,CAACM,QAAQ,CAAC,CAAC;IACjCJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,KAAK,CAAC;;IAEpC;IACA,MAAME,IAAI,GAAGP,QAAQ,CAACQ,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC;IAC3DN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,IAAI,CAAC;IAExC,IAAIA,IAAI,EAAE;MACRL,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,OAAO,IAAI;IACb,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC,OAAO,KAAK;IACd;EAEF,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAR,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}