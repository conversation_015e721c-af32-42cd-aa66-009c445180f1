import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Vouchers.css';

const Vouchers = () => {
  const [vouchers, setVouchers] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingVoucher, setEditingVoucher] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newVoucher, setNewVoucher] = useState({
    type: 'receipt',
    voucherDate: new Date().toISOString().split('T')[0],
    amount: '',
    description: '',
    accountId: '',
    customerId: '',
    supplierId: '',
    fromAccountId: '',
    toAccountId: '',
    paymentMethod: 'cash',
    reference: '',
    notes: ''
  });

  const voucherTypes = [
    { value: 'receipt', label: '🧾 سند قبض', color: '#10b981' },
    { value: 'payment', label: '💸 سند صرف', color: '#ef4444' }
  ];

  const paymentMethods = [
    { value: 'cash', label: '💵 نقداً' },
    { value: 'bank_transfer', label: '🏦 تحويل بنكي' },
    { value: 'check', label: '📝 شيك' },
    { value: 'credit_card', label: '💳 بطاقة ائتمان' },
    { value: 'digital_wallet', label: '📱 محفظة رقمية' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const vouchersData = database.getVouchers();
      const accountsData = database.getAccounts();
      const customersData = database.getCustomers();
      const suppliersData = database.getSuppliers();
      
      setVouchers(vouchersData);
      setAccounts(accountsData);
      setCustomers(customersData);
      setSuppliers(suppliersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewVoucher(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const voucherData = {
        ...newVoucher,
        amount: parseFloat(newVoucher.amount),
        customerId: newVoucher.customerId ? parseInt(newVoucher.customerId) : null,
        supplierId: newVoucher.supplierId ? parseInt(newVoucher.supplierId) : null,
        accountId: parseInt(newVoucher.accountId),
        fromAccountId: newVoucher.fromAccountId ? parseInt(newVoucher.fromAccountId) : null,
        toAccountId: newVoucher.toAccountId ? parseInt(newVoucher.toAccountId) : null
      };

      if (editingVoucher) {
        await database.updateVoucher(editingVoucher.id, voucherData);
        setMessage('تم تحديث السند بنجاح!');
        setEditingVoucher(null);
      } else {
        await database.addVoucher(voucherData);
        setMessage('تم إضافة السند بنجاح!');
      }

      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ السند:', error);
      setMessage('خطأ في حفظ السند');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewVoucher({
      type: 'receipt',
      voucherDate: new Date().toISOString().split('T')[0],
      amount: '',
      description: '',
      accountId: '',
      customerId: '',
      supplierId: '',
      fromAccountId: '',
      toAccountId: '',
      paymentMethod: 'cash',
      reference: '',
      notes: ''
    });
    setShowAddForm(false);
    setEditingVoucher(null);
  };

  const handleEdit = (voucher) => {
    setNewVoucher({
      ...voucher,
      voucherDate: voucher.voucherDate.split('T')[0]
    });
    setEditingVoucher(voucher);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا السند؟')) {
      try {
        await database.deleteVoucher(id);
        setMessage('تم حذف السند بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف السند:', error);
        setMessage('خطأ في حذف السند');
      }
    }
  };

  const printVoucher = (voucher) => {
    const settings = database.getSettings();
    const account = accounts.find(acc => acc.id === voucher.accountId);
    const customer = customers.find(c => c.id === voucher.customerId);
    const supplier = suppliers.find(s => s.id === voucher.supplierId);
    
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>${voucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} - ${voucher.voucherNumber}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              direction: rtl; 
              background: white;
            }
            .voucher-container {
              max-width: 600px;
              margin: 0 auto;
              border: 2px solid #333;
              padding: 20px;
              background: white;
            }
            .header { 
              text-align: center; 
              border-bottom: 2px solid #333; 
              padding-bottom: 15px; 
              margin-bottom: 20px; 
            }
            .company-info { 
              text-align: center; 
              margin-bottom: 20px; 
              font-size: 0.9em;
            }
            .voucher-title {
              font-size: 1.8em;
              font-weight: bold;
              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};
              margin: 10px 0;
            }
            .voucher-info { 
              display: flex; 
              justify-content: space-between; 
              margin-bottom: 20px; 
              border: 1px solid #ddd;
              padding: 15px;
              background: #f9f9f9;
            }
            .amount-section {
              text-align: center;
              margin: 30px 0;
              padding: 20px;
              border: 2px solid ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};
              background: ${voucher.type === 'receipt' ? '#f0fdf4' : '#fef2f2'};
            }
            .amount-number {
              font-size: 2em;
              font-weight: bold;
              color: ${voucher.type === 'receipt' ? '#10b981' : '#ef4444'};
              margin: 10px 0;
            }
            .amount-words {
              font-style: italic;
              margin-top: 10px;
              padding: 10px;
              background: white;
              border: 1px solid #ddd;
            }
            .details-section {
              margin: 20px 0;
              padding: 15px;
              border: 1px solid #ddd;
            }
            .signature-section {
              margin-top: 40px;
              display: flex;
              justify-content: space-between;
            }
            .signature-box {
              text-align: center;
              width: 200px;
              border-top: 1px solid #333;
              padding-top: 10px;
            }
            @media print { 
              body { margin: 0; }
              .voucher-container { border: 2px solid #000; }
            }
          </style>
        </head>
        <body>
          <div class="voucher-container">
            <div class="header">
              <h2>${settings.companyName}</h2>
              <div class="company-info">
                <p>${settings.address}</p>
                <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>
                <p>الرقم الضريبي: ${settings.vatNumber}</p>
              </div>
              <div class="voucher-title">
                ${voucher.type === 'receipt' ? '🧾 سند قبض' : '💸 سند صرف'}
              </div>
            </div>

            <div class="voucher-info">
              <div>
                <p><strong>رقم السند:</strong> ${voucher.voucherNumber}</p>
                <p><strong>التاريخ:</strong> ${new Date(voucher.voucherDate).toLocaleDateString('ar-SA')}</p>
                <p><strong>طريقة الدفع:</strong> ${paymentMethods.find(m => m.value === voucher.paymentMethod)?.label || voucher.paymentMethod}</p>
              </div>
              <div>
                <p><strong>الحساب:</strong> ${account?.name || 'غير محدد'}</p>
                ${customer ? `<p><strong>العميل:</strong> ${customer.name}</p>` : ''}
                ${supplier ? `<p><strong>المورد:</strong> ${supplier.name}</p>` : ''}
                ${voucher.reference ? `<p><strong>المرجع:</strong> ${voucher.reference}</p>` : ''}
              </div>
            </div>

            <div class="amount-section">
              <h3>المبلغ</h3>
              <div class="amount-number">${voucher.amount.toLocaleString()} ريال سعودي</div>
              <div class="amount-words">
                <strong>المبلغ بالكلمات:</strong> ${convertNumberToWords(voucher.amount)} ريال سعودي فقط لا غير
              </div>
            </div>

            <div class="details-section">
              <h4>البيان:</h4>
              <p>${voucher.description}</p>
              ${voucher.notes ? `
                <h4>ملاحظات:</h4>
                <p>${voucher.notes}</p>
              ` : ''}
            </div>

            <div class="signature-section">
              <div class="signature-box">
                <p>المحاسب</p>
                <br><br>
                <p>التوقيع: ________________</p>
              </div>
              <div class="signature-box">
                <p>${voucher.type === 'receipt' ? 'المستلم' : 'المستفيد'}</p>
                <br><br>
                <p>التوقيع: ________________</p>
              </div>
            </div>

            <div style="margin-top: 30px; text-align: center; font-size: 0.8em; color: #666;">
              <p>تم إنشاء هذا السند بواسطة نظام المحاسبة السعودي</p>
              <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
            </div>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    setTimeout(() => printWindow.print(), 500);
  };

  const convertNumberToWords = (number) => {
    // تحويل الرقم إلى كلمات - تطبيق مبسط
    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    
    if (number === 0) return 'صفر';
    if (number < 10) return ones[number];
    if (number < 20) return teens[number - 10];
    if (number < 100) {
      const ten = Math.floor(number / 10);
      const one = number % 10;
      return tens[ten] + (one > 0 ? ' و' + ones[one] : '');
    }
    
    // للأرقام الأكبر، نعيد تمثيل مبسط
    return number.toLocaleString();
  };

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? `${account.code} - ${account.name}` : 'غير محدد';
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
  };

  const getSupplierName = (supplierId) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'غير محدد';
  };

  const getTypeInfo = (type) => {
    const typeObj = voucherTypes.find(t => t.value === type);
    return typeObj || { label: type, color: '#64748b' };
  };

  const getPaymentMethodLabel = (method) => {
    const methodObj = paymentMethods.find(m => m.value === method);
    return methodObj ? methodObj.label : method;
  };

  const filteredVouchers = vouchers.filter(voucher => {
    const matchesSearch = 
      voucher.voucherNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getAccountName(voucher.accountId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || voucher.type === filterType;
    
    const voucherDate = new Date(voucher.voucherDate);
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    const matchesDate = voucherDate >= startDate && voucherDate <= endDate;
    
    return matchesSearch && matchesType && matchesDate;
  });

  const totalReceipts = filteredVouchers
    .filter(v => v.type === 'receipt')
    .reduce((sum, v) => sum + v.amount, 0);
  
  const totalPayments = filteredVouchers
    .filter(v => v.type === 'payment')
    .reduce((sum, v) => sum + v.amount, 0);

  const netCashFlow = totalReceipts - totalPayments;

  return (
    <div className="vouchers-container">
      <div className="vouchers-header">
        <h1>🧾 سندات القبض والصرف</h1>
        <p>إدارة وتتبع سندات القبض والصرف النقدية</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">🧾</div>
          <div className="stat-info">
            <h3>إجمالي السندات</h3>
            <p>{vouchers.length} سند</p>
          </div>
        </div>
        <div className="stat-card receipts">
          <div className="stat-icon">📈</div>
          <div className="stat-info">
            <h3>إجمالي المقبوضات</h3>
            <p>{totalReceipts.toLocaleString()} ريال</p>
          </div>
        </div>
        <div className="stat-card payments">
          <div className="stat-icon">📉</div>
          <div className="stat-info">
            <h3>إجمالي المدفوعات</h3>
            <p>{totalPayments.toLocaleString()} ريال</p>
          </div>
        </div>
        <div className="stat-card net-flow">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>صافي التدفق النقدي</h3>
            <p className={netCashFlow >= 0 ? 'positive' : 'negative'}>
              {netCashFlow.toLocaleString()} ريال
            </p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="vouchers-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في السندات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الأنواع</option>
            {voucherTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            className="date-input"
          />
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            className="date-input"
          />
        </div>
        <div className="action-buttons">
          <button
            onClick={() => {
              setNewVoucher(prev => ({ ...prev, type: 'receipt' }));
              setShowAddForm(true);
            }}
            className="add-receipt-btn"
          >
            ➕ سند قبض
          </button>
          <button
            onClick={() => {
              setNewVoucher(prev => ({ ...prev, type: 'payment' }));
              setShowAddForm(true);
            }}
            className="add-payment-btn"
          >
            ➕ سند صرف
          </button>
        </div>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="voucher-form-modal">
            <div className="modal-header">
              <h3>
                {editingVoucher ? 
                  `✏️ تعديل ${newVoucher.type === 'receipt' ? 'سند القبض' : 'سند الصرف'}` : 
                  `➕ إضافة ${newVoucher.type === 'receipt' ? 'سند قبض' : 'سند صرف'} جديد`
                }
              </h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="voucher-form">
              <div className="form-grid">
                <div className="form-group">
                  <label>نوع السند</label>
                  <select
                    name="type"
                    value={newVoucher.type}
                    onChange={handleInputChange}
                    required
                  >
                    {voucherTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>التاريخ</label>
                  <input
                    type="date"
                    name="voucherDate"
                    value={newVoucher.voucherDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label>المبلغ</label>
                  <input
                    type="number"
                    name="amount"
                    value={newVoucher.amount}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    required
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع</label>
                  <select
                    name="paymentMethod"
                    value={newVoucher.paymentMethod}
                    onChange={handleInputChange}
                    required
                  >
                    {paymentMethods.map(method => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الحساب الرئيسي</label>
                  <select
                    name="accountId"
                    value={newVoucher.accountId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر الحساب</option>
                    {accounts.filter(acc => 
                      acc.type === 'asset' && 
                      (acc.name.includes('نقدية') || acc.name.includes('بنك') || acc.name.includes('صندوق'))
                    ).map(account => (
                      <option key={account.id} value={account.id}>
                        {account.code} - {account.name}
                      </option>
                    ))}
                  </select>
                </div>

                {newVoucher.type === 'receipt' && (
                  <div className="form-group">
                    <label>العميل (اختياري)</label>
                    <select
                      name="customerId"
                      value={newVoucher.customerId}
                      onChange={handleInputChange}
                    >
                      <option value="">اختر العميل</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {newVoucher.type === 'payment' && (
                  <div className="form-group">
                    <label>المورد (اختياري)</label>
                    <select
                      name="supplierId"
                      value={newVoucher.supplierId}
                      onChange={handleInputChange}
                    >
                      <option value="">اختر المورد</option>
                      {suppliers.map(supplier => (
                        <option key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="form-group">
                  <label>رقم المرجع</label>
                  <input
                    type="text"
                    name="reference"
                    value={newVoucher.reference}
                    onChange={handleInputChange}
                    placeholder="رقم الشيك، رقم التحويل، إلخ"
                  />
                </div>

                <div className="form-group full-width">
                  <label>البيان</label>
                  <input
                    type="text"
                    name="description"
                    value={newVoucher.description}
                    onChange={handleInputChange}
                    required
                    placeholder="وصف مختصر للعملية"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    name="notes"
                    value={newVoucher.notes}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : (editingVoucher ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Vouchers Table */}
      <div className="vouchers-table-container">
        <table className="vouchers-table">
          <thead>
            <tr>
              <th>رقم السند</th>
              <th>النوع</th>
              <th>التاريخ</th>
              <th>البيان</th>
              <th>الحساب</th>
              <th>المبلغ</th>
              <th>طريقة الدفع</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredVouchers.length === 0 ? (
              <tr>
                <td colSpan="8" className="no-data">
                  لا توجد سندات مطابقة للبحث
                </td>
              </tr>
            ) : (
              filteredVouchers.map(voucher => {
                const typeInfo = getTypeInfo(voucher.type);
                return (
                  <tr key={voucher.id}>
                    <td className="voucher-number">{voucher.voucherNumber}</td>
                    <td>
                      <span 
                        className="type-badge"
                        style={{ backgroundColor: typeInfo.color }}
                      >
                        {typeInfo.label}
                      </span>
                    </td>
                    <td>{new Date(voucher.voucherDate).toLocaleDateString('ar-SA')}</td>
                    <td>{voucher.description}</td>
                    <td>{getAccountName(voucher.accountId)}</td>
                    <td className={`amount ${voucher.type === 'receipt' ? 'positive' : 'negative'}`}>
                      {voucher.type === 'receipt' ? '+' : '-'}{voucher.amount.toLocaleString()} ريال
                    </td>
                    <td>{getPaymentMethodLabel(voucher.paymentMethod)}</td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(voucher)}
                        className="edit-btn"
                        title="تعديل"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => printVoucher(voucher)}
                        className="print-btn"
                        title="طباعة"
                      >
                        🖨️
                      </button>
                      <button
                        onClick={() => handleDelete(voucher.id)}
                        className="delete-btn"
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Vouchers;
