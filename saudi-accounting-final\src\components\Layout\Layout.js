import React, { useState } from 'react';
import './Layout.css';

const Layout = ({ children, currentView, onNavigate, user, onLogout }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const navigationItems = [
    {
      id: 'pos',
      label: 'نقطة البيع',
      icon: '🛒',
      isActive: currentView === 'pos'
    },
    {
      id: 'dashboard',
      label: 'لوحة التحكم',
      icon: '📊',
      isActive: currentView === 'dashboard'
    },
    {
      id: 'products',
      label: 'المنتجات',
      icon: '📦',
      isActive: currentView === 'products'
    },
    {
      id: 'customers',
      label: 'العملاء',
      icon: '👥',
      isActive: currentView === 'customers'
    },
    {
      id: 'suppliers',
      label: 'الموردين',
      icon: '🏢',
      isActive: currentView === 'suppliers'
    },
    {
      id: 'invoices',
      label: 'الفواتير',
      icon: '🧾',
      isActive: currentView === 'invoices'
    },
    {
      id: 'quotations',
      label: 'عروض الأسعار',
      icon: '💼',
      isActive: currentView === 'quotations'
    },
    {
      id: 'returns',
      label: 'المرتجعات',
      icon: '↩️',
      isActive: currentView === 'returns'
    },
    {
      id: 'vouchers',
      label: 'السندات',
      icon: '🧾',
      isActive: currentView === 'vouchers'
    },
    {
      id: 'journal-entries',
      label: 'القيود اليومية',
      icon: '📋',
      isActive: currentView === 'journal-entries'
    },
    {
      id: 'accounting',
      label: 'النظام المحاسبي',
      icon: '💰',
      isActive: currentView === 'accounting'
    },
    {
      id: 'advanced-reports',
      label: 'التقارير المتقدمة',
      icon: '📈',
      isActive: currentView === 'advanced-reports'
    },
    {
      id: 'reports',
      label: 'التقارير',
      icon: '📊',
      isActive: currentView === 'reports'
    },
    {
      id: 'barcode',
      label: 'إدارة الباركود',
      icon: '📊',
      isActive: currentView === 'barcode'
    },
    {
      id: 'employees',
      label: 'الموظفين',
      icon: '👨‍💼',
      isActive: currentView === 'employees'
    },
    {
      id: 'backup',
      label: 'النسخ الاحتياطي',
      icon: '💾',
      isActive: currentView === 'backup'
    },
    {
      id: 'settings',
      label: 'الإعدادات',
      icon: '⚙️',
      isActive: currentView === 'settings'
    }
  ];

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const getCurrentDateTime = () => {
    const now = new Date();
    return {
      date: now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const { date, time } = getCurrentDateTime();

  const getPageTitle = () => {
    const item = navigationItems.find(item => item.isActive);
    return item ? item.label : 'نظام المحاسبة السعودي';
  };

  return (
    <div className="app-layout">
      {/* Sidebar */}
      <div className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <a href="#" className="sidebar-logo" onClick={(e) => e.preventDefault()}>
            <span className="logo-icon">🏪</span>
            {!sidebarCollapsed && <span>Aronim EXP</span>}
          </a>
        </div>

        <nav className="sidebar-nav">
          {navigationItems.map((item) => (
            <div key={item.id} className="nav-item">
              <a
                href="#"
                className={`nav-link ${item.isActive ? 'active' : ''}`}
                onClick={(e) => {
                  e.preventDefault();
                  onNavigate(item.id);
                }}
                title={sidebarCollapsed ? item.label : ''}
              >
                <span className="nav-icon">{item.icon}</span>
                {!sidebarCollapsed && <span>{item.label}</span>}
              </a>
            </div>
          ))}
        </nav>

        <div className="sidebar-footer">
          <button
            className="nav-link"
            onClick={onLogout}
            title={sidebarCollapsed ? 'تسجيل الخروج' : ''}
          >
            <span className="nav-icon">🚪</span>
            {!sidebarCollapsed && <span>تسجيل الخروج</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className={`main-content ${sidebarCollapsed ? 'expanded' : ''}`}>
        {/* Header */}
        <div className="content-header">
          <div className="header-top">
            <div className="header-title">
              <button
                className="sidebar-toggle"
                onClick={toggleSidebar}
                title={sidebarCollapsed ? 'توسيع الشريط الجانبي' : 'طي الشريط الجانبي'}
              >
                {sidebarCollapsed ? '☰' : '✕'}
              </button>
              <span>{getPageTitle()}</span>
            </div>

            <div className="header-actions">
              <div className="datetime-info">
                <div className="date">{date}</div>
                <div className="time">{time}</div>
              </div>
              
              <div className="user-info">
                <span className="user-icon">👤</span>
                <span className="user-name">{user?.username || 'المستخدم'}</span>
              </div>

              <button
                className="notifications-btn"
                onClick={() => onNavigate('notifications')}
                title="الإشعارات"
              >
                🔔
                <span className="notification-badge">3</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content Body */}
        <div className="content-body">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Layout;
