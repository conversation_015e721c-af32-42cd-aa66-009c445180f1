/* Invoices Styles - <PERSON><PERSON><PERSON> ERP Design */

.invoices-container {
  padding: var(--spacing-xl);
  background: var(--gray-50);
  min-height: 100vh;
}

.invoices-header {
  margin-bottom: var(--spacing-xl);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.header-top h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card.success {
  border-left: 4px solid var(--success-color);
}

.stat-card.warning {
  border-left: 4px solid var(--warning-color);
}

.stat-card.danger {
  border-left: 4px solid var(--danger-color);
}

.stat-icon {
  font-size: var(--font-size-2xl);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-top: var(--spacing-xs);
}

/* Filters Section */
.filters-section {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-xl);
}

.search-box {
  margin-bottom: var(--spacing-lg);
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filters-row {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background: var(--white);
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Content */
.invoices-content {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--gray-500);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--gray-700);
}

.empty-state p {
  font-size: var(--font-size-base);
  margin: 0;
}

/* Summary */
.invoices-summary {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--gray-700);
}

/* Table */
.invoices-table-container {
  overflow-x: auto;
}

.invoices-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.invoices-table th {
  background: var(--gray-50);
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: right;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  white-space: nowrap;
}

.invoices-table td {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-900);
}

.invoices-table tbody tr:hover {
  background: var(--gray-50);
}

.invoice-number {
  font-weight: 600;
  color: var(--primary-color);
}

.amount {
  font-weight: 600;
  color: var(--success-color);
}

/* Status Badge */
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
}

.status-badge.success {
  background: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.danger {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.secondary {
  background: var(--gray-200);
  color: var(--gray-700);
}

/* Actions */
.actions-buttons {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.actions-buttons .btn {
  padding: var(--spacing-xs);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.invoice-details-modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-xl);
}

/* Invoice Info Grid */
.invoice-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-item label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
}

.info-item span {
  font-size: var(--font-size-base);
  color: var(--gray-900);
}

/* Items Section */
.items-section {
  margin-bottom: var(--spacing-xl);
}

.items-section h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.items-table th {
  background: var(--gray-50);
  padding: var(--spacing-md);
  text-align: right;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

.items-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-900);
}

.items-table tbody tr:last-child td {
  border-bottom: none;
}

/* Totals Section */
.totals-section {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.total-row:last-child {
  margin-bottom: 0;
}

.total-row.final-total {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
  border-top: 2px solid var(--gray-300);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* Animations */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .invoices-container {
    padding: var(--spacing-md);
  }
  
  .header-top {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .filters-row {
    flex-direction: column;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .invoices-table {
    font-size: var(--font-size-xs);
  }
  
  .invoices-table th,
  .invoices-table td {
    padding: var(--spacing-sm);
  }
  
  .actions-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .invoice-info-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}
