import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Returns.css';

const Returns = () => {
  const [returns, setReturns] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [products, setProducts] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingReturn, setEditingReturn] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newReturn, setNewReturn] = useState({
    type: 'sales_return',
    originalInvoiceId: '',
    customerId: '',
    supplierId: '',
    returnNumber: '',
    returnDate: new Date().toISOString().split('T')[0],
    reason: '',
    status: 'pending',
    items: [{ productId: '', quantity: '', price: '', total: '', reason: '' }],
    subtotal: 0,
    vatAmount: 0,
    total: 0,
    notes: ''
  });

  const returnTypes = [
    { value: 'sales_return', label: '↩️ مرتجع مبيعات', color: '#ef4444' },
    { value: 'purchase_return', label: '📤 مرتجع مشتريات', color: '#f59e0b' },
    { value: 'damaged_return', label: '💔 مرتجع تالف', color: '#8b5cf6' },
    { value: 'expired_return', label: '⏰ مرتجع منتهي الصلاحية', color: '#6b7280' },
    { value: 'quality_return', label: '🔍 مرتجع جودة', color: '#dc2626' }
  ];

  const returnStatuses = [
    { value: 'pending', label: '⏳ معلق', color: '#f59e0b' },
    { value: 'approved', label: '✅ معتمد', color: '#10b981' },
    { value: 'processed', label: '🔄 تم المعالجة', color: '#6366f1' },
    { value: 'completed', label: '✅ مكتمل', color: '#059669' },
    { value: 'cancelled', label: '❌ ملغي', color: '#ef4444' }
  ];

  const returnReasons = [
    'عيب في المنتج',
    'منتج خاطئ',
    'تلف أثناء الشحن',
    'انتهاء صلاحية',
    'عدم مطابقة المواصفات',
    'طلب العميل',
    'خطأ في الكمية',
    'خطأ في السعر',
    'منتج معيب',
    'أخرى'
  ];

  useEffect(() => {
    loadData();
    generateReturnNumber();
  }, []);

  const loadData = () => {
    try {
      const returnsData = database.getReturns();
      const invoicesData = database.getInvoices();
      const customersData = database.getCustomers();
      const suppliersData = database.getSuppliers();
      const productsData = database.getProducts();
      
      setReturns(returnsData);
      setInvoices(invoicesData);
      setCustomers(customersData);
      setSuppliers(suppliersData);
      setProducts(productsData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const generateReturnNumber = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    const returnNumber = `RET-${year}${month}${day}-${random}`;
    setNewReturn(prev => ({ ...prev, returnNumber }));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewReturn(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-fill customer/supplier when original invoice is selected
    if (name === 'originalInvoiceId' && value) {
      const selectedInvoice = invoices.find(inv => inv.id === parseInt(value));
      if (selectedInvoice) {
        setNewReturn(prev => ({
          ...prev,
          customerId: selectedInvoice.customerId || '',
          supplierId: selectedInvoice.supplierId || ''
        }));
      }
    }
  };

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...newReturn.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Calculate total for the item
    if (field === 'quantity' || field === 'price') {
      const quantity = parseFloat(updatedItems[index].quantity) || 0;
      const price = parseFloat(updatedItems[index].price) || 0;
      updatedItems[index].total = quantity * price;
    }

    // Auto-fill product price
    if (field === 'productId' && value) {
      const selectedProduct = products.find(p => p.id === parseInt(value));
      if (selectedProduct) {
        updatedItems[index].price = selectedProduct.price;
        const quantity = parseFloat(updatedItems[index].quantity) || 0;
        updatedItems[index].total = quantity * selectedProduct.price;
      }
    }

    setNewReturn(prev => ({ ...prev, items: updatedItems }));
    calculateTotals(updatedItems);
  };

  const calculateTotals = (items = newReturn.items) => {
    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
    const vatRate = 0.15; // 15% VAT
    const vatAmount = subtotal * vatRate;
    const total = subtotal + vatAmount;

    setNewReturn(prev => ({
      ...prev,
      subtotal,
      vatAmount,
      total
    }));
  };

  const addItem = () => {
    setNewReturn(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', quantity: '', price: '', total: '', reason: '' }]
    }));
  };

  const removeItem = (index) => {
    if (newReturn.items.length > 1) {
      const updatedItems = newReturn.items.filter((_, i) => i !== index);
      setNewReturn(prev => ({ ...prev, items: updatedItems }));
      calculateTotals(updatedItems);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const returnData = {
        ...newReturn,
        items: newReturn.items.filter(item => item.productId && item.quantity),
        customerId: parseInt(newReturn.customerId) || null,
        supplierId: parseInt(newReturn.supplierId) || null,
        originalInvoiceId: parseInt(newReturn.originalInvoiceId) || null
      };

      if (editingReturn) {
        await database.updateReturn(editingReturn.id, returnData);
        setMessage('تم تحديث المرتجع بنجاح!');
        setEditingReturn(null);
      } else {
        await database.addReturn(returnData);
        setMessage('تم إضافة المرتجع بنجاح!');
      }

      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ المرتجع:', error);
      setMessage('خطأ في حفظ المرتجع');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewReturn({
      type: 'sales_return',
      originalInvoiceId: '',
      customerId: '',
      supplierId: '',
      returnNumber: '',
      returnDate: new Date().toISOString().split('T')[0],
      reason: '',
      status: 'pending',
      items: [{ productId: '', quantity: '', price: '', total: '', reason: '' }],
      subtotal: 0,
      vatAmount: 0,
      total: 0,
      notes: ''
    });
    setShowAddForm(false);
    setEditingReturn(null);
    generateReturnNumber();
  };

  const handleEdit = (returnItem) => {
    setNewReturn({
      ...returnItem,
      returnDate: returnItem.returnDate.split('T')[0]
    });
    setEditingReturn(returnItem);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المرتجع؟')) {
      try {
        await database.deleteReturn(id);
        setMessage('تم حذف المرتجع بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف المرتجع:', error);
        setMessage('خطأ في حذف المرتجع');
      }
    }
  };

  const handleProcess = async (id) => {
    if (window.confirm('هل أنت متأكد من معالجة هذا المرتجع؟')) {
      try {
        await database.processReturn(id);
        setMessage('تم معالجة المرتجع بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في معالجة المرتجع:', error);
        setMessage('خطأ في معالجة المرتجع');
      }
    }
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
  };

  const getSupplierName = (supplierId) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'غير محدد';
  };

  const getProductName = (productId) => {
    const product = products.find(p => p.id === parseInt(productId));
    return product ? product.name : 'غير محدد';
  };

  const getInvoiceNumber = (invoiceId) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    return invoice ? invoice.invoiceNumber : 'غير محدد';
  };

  const getTypeInfo = (type) => {
    const typeObj = returnTypes.find(t => t.value === type);
    return typeObj || { label: type, color: '#64748b' };
  };

  const getStatusInfo = (status) => {
    const statusObj = returnStatuses.find(s => s.value === status);
    return statusObj || { label: status, color: '#64748b' };
  };

  const filteredReturns = returns.filter(returnItem => {
    const matchesSearch = 
      returnItem.returnNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getCustomerName(returnItem.customerId).toLowerCase().includes(searchTerm.toLowerCase()) ||
      getSupplierName(returnItem.supplierId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || returnItem.type === filterType;
    const matchesStatus = filterStatus === 'all' || returnItem.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const totalReturns = filteredReturns.reduce((sum, ret) => sum + ret.total, 0);
  const salesReturns = filteredReturns.filter(r => r.type === 'sales_return');
  const purchaseReturns = filteredReturns.filter(r => r.type === 'purchase_return');

  return (
    <div className="returns-container">
      <div className="returns-header">
        <h1>↩️ إدارة المرتجعات</h1>
        <p>إدارة مرتجعات المبيعات والمشتريات</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">↩️</div>
          <div className="stat-info">
            <h3>إجمالي المرتجعات</h3>
            <p>{returns.length} مرتجع</p>
          </div>
        </div>
        <div className="stat-card sales">
          <div className="stat-icon">📤</div>
          <div className="stat-info">
            <h3>مرتجعات المبيعات</h3>
            <p>{salesReturns.length} مرتجع</p>
          </div>
        </div>
        <div className="stat-card purchase">
          <div className="stat-icon">📥</div>
          <div className="stat-info">
            <h3>مرتجعات المشتريات</h3>
            <p>{purchaseReturns.length} مرتجع</p>
          </div>
        </div>
        <div className="stat-card amount">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي القيمة</h3>
            <p>{totalReturns.toLocaleString()} ريال</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="returns-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في المرتجعات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الأنواع</option>
            {returnTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الحالات</option>
            {returnStatuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="add-return-btn"
        >
          ➕ إضافة مرتجع جديد
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="return-form-modal">
            <div className="modal-header">
              <h3>{editingReturn ? '✏️ تعديل المرتجع' : '➕ إضافة مرتجع جديد'}</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="return-form">
              <div className="form-header">
                <div className="form-grid">
                  <div className="form-group">
                    <label>نوع المرتجع</label>
                    <select
                      name="type"
                      value={newReturn.type}
                      onChange={handleInputChange}
                      required
                    >
                      {returnTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>رقم المرتجع</label>
                    <input
                      type="text"
                      name="returnNumber"
                      value={newReturn.returnNumber}
                      onChange={handleInputChange}
                      required
                      readOnly
                    />
                  </div>

                  <div className="form-group">
                    <label>تاريخ المرتجع</label>
                    <input
                      type="date"
                      name="returnDate"
                      value={newReturn.returnDate}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label>الحالة</label>
                    <select
                      name="status"
                      value={newReturn.status}
                      onChange={handleInputChange}
                      required
                    >
                      {returnStatuses.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>الفاتورة الأصلية</label>
                    <select
                      name="originalInvoiceId"
                      value={newReturn.originalInvoiceId}
                      onChange={handleInputChange}
                    >
                      <option value="">اختر الفاتورة (اختياري)</option>
                      {invoices.map(invoice => (
                        <option key={invoice.id} value={invoice.id}>
                          {invoice.invoiceNumber} - {getCustomerName(invoice.customerId)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {newReturn.type === 'sales_return' && (
                    <div className="form-group">
                      <label>العميل</label>
                      <select
                        name="customerId"
                        value={newReturn.customerId}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">اختر العميل</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {newReturn.type === 'purchase_return' && (
                    <div className="form-group">
                      <label>المورد</label>
                      <select
                        name="supplierId"
                        value={newReturn.supplierId}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">اختر المورد</option>
                        {suppliers.map(supplier => (
                          <option key={supplier.id} value={supplier.id}>
                            {supplier.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div className="form-group">
                    <label>سبب المرتجع</label>
                    <select
                      name="reason"
                      value={newReturn.reason}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">اختر السبب</option>
                      {returnReasons.map(reason => (
                        <option key={reason} value={reason}>
                          {reason}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Items Section */}
              <div className="items-section">
                <div className="section-header">
                  <h4>📦 أصناف المرتجع</h4>
                  <button type="button" onClick={addItem} className="add-item-btn">
                    ➕ إضافة صنف
                  </button>
                </div>

                <div className="items-table">
                  <div className="items-header">
                    <div>الصنف</div>
                    <div>الكمية</div>
                    <div>السعر</div>
                    <div>الإجمالي</div>
                    <div>السبب</div>
                    <div>إجراءات</div>
                  </div>

                  {newReturn.items.map((item, index) => (
                    <div key={index} className="item-row">
                      <select
                        value={item.productId}
                        onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                        required
                      >
                        <option value="">اختر الصنف</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name}
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        placeholder="الكمية"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                        min="1"
                        required
                      />
                      <input
                        type="number"
                        placeholder="السعر"
                        value={item.price}
                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                        step="0.01"
                        min="0"
                        required
                      />
                      <input
                        type="number"
                        value={item.total}
                        readOnly
                        className="total-field"
                      />
                      <input
                        type="text"
                        placeholder="سبب المرتجع"
                        value={item.reason}
                        onChange={(e) => handleItemChange(index, 'reason', e.target.value)}
                      />
                      {newReturn.items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="remove-item-btn"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                <div className="totals-section">
                  <div className="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{newReturn.subtotal.toLocaleString()} ريال</span>
                  </div>
                  <div className="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{newReturn.vatAmount.toLocaleString()} ريال</span>
                  </div>
                  <div className="total-row final-total">
                    <span>الإجمالي:</span>
                    <span>{newReturn.total.toLocaleString()} ريال</span>
                  </div>
                </div>
              </div>

              <div className="form-group full-width">
                <label>ملاحظات</label>
                <textarea
                  name="notes"
                  value={newReturn.notes}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="ملاحظات إضافية..."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : (editingReturn ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Returns Table */}
      <div className="returns-table-container">
        <table className="returns-table">
          <thead>
            <tr>
              <th>رقم المرتجع</th>
              <th>النوع</th>
              <th>التاريخ</th>
              <th>العميل/المورد</th>
              <th>السبب</th>
              <th>المبلغ</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredReturns.length === 0 ? (
              <tr>
                <td colSpan="8" className="no-data">
                  لا توجد مرتجعات مطابقة للبحث
                </td>
              </tr>
            ) : (
              filteredReturns.map(returnItem => {
                const typeInfo = getTypeInfo(returnItem.type);
                const statusInfo = getStatusInfo(returnItem.status);
                const clientName = returnItem.type === 'sales_return' ? 
                  getCustomerName(returnItem.customerId) : 
                  getSupplierName(returnItem.supplierId);
                
                return (
                  <tr key={returnItem.id}>
                    <td className="return-number">{returnItem.returnNumber}</td>
                    <td>
                      <span 
                        className="type-badge"
                        style={{ backgroundColor: typeInfo.color }}
                      >
                        {typeInfo.label}
                      </span>
                    </td>
                    <td>{new Date(returnItem.returnDate).toLocaleDateString('ar-SA')}</td>
                    <td>{clientName}</td>
                    <td>{returnItem.reason}</td>
                    <td className="amount">{returnItem.total.toLocaleString()} ريال</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: statusInfo.color }}
                      >
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(returnItem)}
                        className="edit-btn"
                        title="تعديل"
                        disabled={returnItem.status === 'completed'}
                      >
                        ✏️
                      </button>
                      {returnItem.status === 'approved' && (
                        <button
                          onClick={() => handleProcess(returnItem.id)}
                          className="process-btn"
                          title="معالجة"
                        >
                          🔄
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(returnItem.id)}
                        className="delete-btn"
                        title="حذف"
                        disabled={returnItem.status === 'completed'}
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Returns;
