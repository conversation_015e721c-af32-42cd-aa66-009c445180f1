.barcode-manager-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.barcode-manager-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.barcode-manager-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.barcode-manager-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.message {
  padding: 15px 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  font-weight: 600;
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.scanner-section {
  background: white;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.scanner-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.scan-btn {
  padding: 12px 25px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scan-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.scan-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.scanned-result {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
  background: #f0fdf4;
  border: 1px solid #10b981;
  border-radius: 8px;
}

.search-scanned-btn {
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-scanned-btn:hover {
  background: #059669;
}

.barcode-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 12px 18px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.select-all-btn,
.clear-selection-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.select-all-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.select-all-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.clear-selection-btn {
  background: #f3f4f6;
  color: #374151;
}

.clear-selection-btn:hover {
  background: #e5e7eb;
}

.selection-count {
  font-weight: 600;
  color: #6366f1;
  padding: 8px 12px;
  background: #e0e7ff;
  border-radius: 6px;
}

.print-settings {
  background: white;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.print-settings h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.3rem;
  font-weight: 700;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
}

.setting-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.setting-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.checkbox-group label {
  margin-bottom: 0;
  cursor: pointer;
}

.setting-group input,
.setting-group select {
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.setting-group input:focus,
.setting-group select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.print-btn,
.export-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.print-btn {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.print-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.export-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.export-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.print-btn:disabled,
.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
  font-style: italic;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-card.selected {
  border-color: #6366f1;
  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.product-header h4 {
  margin: 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 700;
}

.selection-indicator {
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.product-info {
  margin-bottom: 15px;
}

.product-info p {
  margin: 5px 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.product-info strong {
  color: #374151;
}

.barcode-preview {
  text-align: center;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.barcode-preview img {
  max-width: 100%;
  height: auto;
  margin-bottom: 5px;
}

.barcode-text {
  font-family: monospace;
  font-size: 0.8rem;
  color: #374151;
  font-weight: 600;
}

.product-actions {
  text-align: center;
}

.quick-print-btn {
  padding: 8px 15px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-print-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.scanner-modal {
  background: white;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.scanner-content h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.5rem;
}

.scanner-animation {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 20px auto;
  border: 2px solid #6366f1;
  border-radius: 10px;
  overflow: hidden;
}

.scanner-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #ef4444;
  animation: scan 2s infinite;
}

@keyframes scan {
  0% { top: 0; }
  50% { top: calc(100% - 2px); }
  100% { top: 0; }
}

.scanner-animation p {
  margin-top: 20px;
  color: #6b7280;
}

.cancel-scan-btn {
  padding: 10px 20px;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.cancel-scan-btn:hover {
  background: #e5e7eb;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .barcode-manager-container {
    padding: 15px;
  }
  
  .barcode-manager-header h1 {
    font-size: 2.2rem;
  }
  
  .barcode-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .selection-controls {
    justify-content: center;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .scanner-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .scanned-result {
    flex-direction: column;
    text-align: center;
  }
}
