import React, { useState, useEffect, useRef } from 'react';
import database from '../../utils/database';
import './UltimatePOS.css';

const UltimatePOS = ({ user, onBack }) => {
  const [products, setProducts] = useState([]);
  const [cart, setCart] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [cashReceived, setCashReceived] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCart, setShowCart] = useState(true);
  const searchInputRef = useRef(null);

  useEffect(() => {
    loadProducts();
    generateInvoiceNumber();
    
    // Keyboard shortcuts
    const handleKeyPress = (e) => {
      if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        handleCheckout();
      } else if (e.ctrlKey && e.key === 'Backspace') {
        e.preventDefault();
        clearCart();
      } else if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const loadProducts = () => {
    try {
      const allProducts = database.getProducts();
      setProducts(allProducts);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
      showMessage('خطأ في تحميل المنتجات', 'error');
    }
  };

  const generateInvoiceNumber = () => {
    const settings = database.getSettings();
    setCurrentInvoiceNumber(settings.nextInvoiceNumber.toString().padStart(6, '0'));
  };

  const showMessage = (text, type = 'success') => {
    setMessage({ text, type });
    setTimeout(() => setMessage(''), 3000);
  };

  const addToCart = (product) => {
    if (product.stock <= 0) {
      showMessage('المنتج غير متوفر في المخزون', 'error');
      return;
    }

    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      if (existingItem.quantity >= product.stock) {
        showMessage('الكمية المطلوبة تتجاوز المخزون المتاح', 'error');
        return;
      }
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity: 1 }]);
    }
    showMessage(`تم إضافة ${product.name}`, 'success');
  };

  const updateQuantity = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    const product = products.find(p => p.id === productId);
    if (newQuantity > product.stock) {
      showMessage('الكمية المطلوبة تتجاوز المخزون المتاح', 'error');
      return;
    }

    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const removeFromCart = (productId) => {
    setCart(cart.filter(item => item.id !== productId));
    showMessage('تم حذف المنتج من السلة', 'info');
  };

  const clearCart = () => {
    setCart([]);
    showMessage('تم مسح السلة', 'info');
  };

  const calculateTotal = () => {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const vat = subtotal * 0.15;
    return { subtotal, vat, total: subtotal + vat };
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      showMessage('السلة فارغة', 'error');
      return;
    }
    setShowPaymentModal(true);
  };

  const processPayment = async () => {
    try {
      setLoading(true);
      const { total } = calculateTotal();
      
      let finalAmount = total;
      if (paymentMethod === 'cash') {
        const cash = parseFloat(cashReceived);
        if (cash < total) {
          showMessage('المبلغ المدفوع أقل من إجمالي الفاتورة', 'error');
          return;
        }
        finalAmount = cash;
      }

      const invoiceData = {
        invoiceNumber: currentInvoiceNumber,
        items: cart,
        ...calculateTotal(),
        cashReceived: paymentMethod === 'cash' ? finalAmount : total,
        change: paymentMethod === 'cash' ? finalAmount - total : 0,
        cashier: user.name,
        paymentMethod
      };

      database.addInvoice(invoiceData);
      
      // Update inventory
      cart.forEach(item => {
        database.setProductStock(item.id, item.stock - item.quantity);
      });

      showMessage('تم إتمام البيع بنجاح', 'success');
      setCart([]);
      setCashReceived('');
      setShowPaymentModal(false);
      generateInvoiceNumber();
      loadProducts();

    } catch (error) {
      console.error('خطأ في معالجة الدفع:', error);
      showMessage('خطأ في معالجة الدفع', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchValue) => {
    const product = products.find(p => 
      p.barcode === searchValue || 
      p.name.toLowerCase().includes(searchValue.toLowerCase())
    );
    
    if (product) {
      addToCart(product);
      setSearchTerm('');
    } else {
      showMessage('لم يتم العثور على منتج', 'error');
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode?.includes(searchTerm);
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ['all', ...new Set(products.map(p => p.category).filter(Boolean))];
  const { subtotal, vat, total } = calculateTotal();

  return (
    <div className="ultimate-pos">
      {/* Top Bar */}
      <div className="pos-topbar">
        <div className="topbar-left">
          <button className="back-button" onClick={onBack}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            العودة
          </button>
          <div className="pos-info">
            <h1>نقطة البيع</h1>
            <span className="invoice-info">فاتورة #{currentInvoiceNumber}</span>
          </div>
        </div>
        
        <div className="topbar-center">
          <div className="search-bar">
            <svg className="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="البحث في المنتجات أو مسح الباركود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchTerm) {
                  handleSearch(searchTerm);
                }
              }}
              className="search-input"
            />
            {searchTerm && (
              <button 
                className="clear-search"
                onClick={() => setSearchTerm('')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            )}
          </div>
        </div>
        
        <div className="topbar-right">
          <div className="user-info">
            <div className="user-avatar">
              {user.name.charAt(0)}
            </div>
            <div className="user-details">
              <span className="user-name">{user.name}</span>
              <span className="current-time">
                {new Date().toLocaleTimeString('ar-SA', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            </div>
          </div>
          <button 
            className="toggle-cart"
            onClick={() => setShowCart(!showCart)}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17A2 2 0 0115 19H9A2 2 0 017 17V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            {cart.length > 0 && <span className="cart-badge">{cart.length}</span>}
          </button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`pos-message ${message.type}`}>
          <span>{message.text}</span>
          <button onClick={() => setMessage('')}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2"/>
              <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </button>
        </div>
      )}

      {/* Main Content */}
      <div className="pos-content">
        {/* Products Area */}
        <div className={`products-area ${!showCart ? 'full-width' : ''}`}>
          {/* Categories */}
          <div className="categories-tabs">
            {categories.map(category => (
              <button
                key={category}
                className={`category-tab ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category === 'all' ? 'الكل' : category}
              </button>
            ))}
          </div>

          {/* Products List */}
          <div className="products-list">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className={`product-item ${product.stock <= 0 ? 'out-of-stock' : ''}`}
                onClick={() => addToCart(product)}
              >
                <div className="product-image">
                  {product.image ? (
                    <img src={product.image} alt={product.name} />
                  ) : (
                    <div className="placeholder-image">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
                        <polyline points="21,15 16,10 5,21" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    </div>
                  )}
                  {product.stock <= 5 && product.stock > 0 && (
                    <div className="low-stock-badge">قليل</div>
                  )}
                  {product.stock <= 0 && (
                    <div className="out-of-stock-badge">نفد</div>
                  )}
                </div>
                <div className="product-details">
                  <h3 className="product-name">{product.name}</h3>
                  <p className="product-description">{product.description}</p>
                  <div className="product-footer">
                    <span className="product-price">{product.price.toFixed(2)} ر.س</span>
                    <span className="product-stock">المخزون: {product.stock}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cart Sidebar */}
        {showCart && (
          <div className="cart-sidebar">
            <div className="cart-header">
              <h2>سلة المشتريات</h2>
              <span className="items-count">{cart.length} منتج</span>
            </div>

            <div className="cart-content">
              {cart.length === 0 ? (
                <div className="empty-cart">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17A2 2 0 0115 19H9A2 2 0 017 17V13" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                  <p>السلة فارغة</p>
                  <span>ابدأ بإضافة المنتجات</span>
                </div>
              ) : (
                <>
                  <div className="cart-items">
                    {cart.map(item => (
                      <div key={item.id} className="cart-item">
                        <div className="item-image">
                          {item.image ? (
                            <img src={item.image} alt={item.name} />
                          ) : (
                            <div className="placeholder">
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
                              </svg>
                            </div>
                          )}
                        </div>
                        <div className="item-details">
                          <h4>{item.name}</h4>
                          <span className="item-price">{item.price.toFixed(2)} ر.س</span>
                        </div>
                        <div className="item-controls">
                          <button
                            className="qty-btn minus"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                          </button>
                          <span className="quantity">{item.quantity}</span>
                          <button
                            className="qty-btn plus"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" strokeWidth="2"/>
                              <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                          </button>
                          <button
                            className="remove-btn"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="2"/>
                              <path d="M19,6V20A2,2 0 0,1 17,22H7A2,2 0 0,1 5,20V6M8,6V4A2,2 0 0,1 10,2H14A2,2 0 0,1 16,4V6" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                          </button>
                        </div>
                        <div className="item-total">
                          {(item.price * item.quantity).toFixed(2)} ر.س
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="cart-summary">
                    <div className="summary-line">
                      <span>المجموع الفرعي</span>
                      <span>{subtotal.toFixed(2)} ر.س</span>
                    </div>
                    <div className="summary-line">
                      <span>ضريبة القيمة المضافة (15%)</span>
                      <span>{vat.toFixed(2)} ر.س</span>
                    </div>
                    <div className="summary-line total">
                      <span>الإجمالي</span>
                      <span>{total.toFixed(2)} ر.س</span>
                    </div>
                  </div>

                  <div className="cart-actions">
                    <button className="clear-cart-btn" onClick={clearCart}>
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="2"/>
                        <path d="M19,6V20A2,2 0 0,1 17,22H7A2,2 0 0,1 5,20V6M8,6V4A2,2 0 0,1 10,2H14A2,2 0 0,1 16,4V6" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                      مسح الكل
                    </button>
                    <button className="checkout-btn" onClick={handleCheckout}>
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <rect x="1" y="3" width="15" height="13" stroke="currentColor" strokeWidth="2"/>
                        <path d="M16 8L20 12L16 16" stroke="currentColor" strokeWidth="2"/>
                        <path d="M20 12H9" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                      إتمام الدفع
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="payment-overlay">
          <div className="payment-modal">
            <div className="payment-header">
              <h2>إتمام عملية الدفع</h2>
              <button
                className="close-payment"
                onClick={() => setShowPaymentModal(false)}
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2"/>
                  <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </button>
            </div>
            
            <div className="payment-body">
              <div className="payment-summary">
                <h3>ملخص الطلب</h3>
                <div className="summary-details">
                  <div className="detail-line">
                    <span>عدد المنتجات:</span>
                    <span>{cart.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  <div className="detail-line">
                    <span>المجموع الفرعي:</span>
                    <span>{subtotal.toFixed(2)} ر.س</span>
                  </div>
                  <div className="detail-line">
                    <span>ضريبة القيمة المضافة:</span>
                    <span>{vat.toFixed(2)} ر.س</span>
                  </div>
                  <div className="detail-line total-line">
                    <span>الإجمالي:</span>
                    <span>{total.toFixed(2)} ر.س</span>
                  </div>
                </div>
              </div>

              <div className="payment-methods">
                <h3>طريقة الدفع</h3>
                <div className="method-options">
                  <label className={`method-option ${paymentMethod === 'cash' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      value="cash"
                      checked={paymentMethod === 'cash'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <div className="method-content">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <line x1="12" y1="1" x2="12" y2="23" stroke="currentColor" strokeWidth="2"/>
                        <path d="M17 5H9.5A3.5 3.5 0 0 0 6 8.5V8.5A3.5 3.5 0 0 0 9.5 12H14.5A3.5 3.5 0 0 1 18 15.5V15.5A3.5 3.5 0 0 1 14.5 19H6" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                      <span>نقداً</span>
                    </div>
                  </label>
                  <label className={`method-option ${paymentMethod === 'card' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <div className="method-content">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                        <line x1="1" y1="10" x2="23" y2="10" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                      <span>بطاقة ائتمان</span>
                    </div>
                  </label>
                </div>
              </div>

              {paymentMethod === 'cash' && (
                <div className="cash-payment">
                  <label>المبلغ المستلم:</label>
                  <input
                    type="number"
                    value={cashReceived}
                    onChange={(e) => setCashReceived(e.target.value)}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    autoFocus
                  />
                  {cashReceived && parseFloat(cashReceived) >= total && (
                    <div className="change-display">
                      <span>المبلغ المرتجع:</span>
                      <span className="change-amount">
                        {(parseFloat(cashReceived) - total).toFixed(2)} ر.س
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="payment-footer">
              <button
                className="cancel-payment"
                onClick={() => setShowPaymentModal(false)}
              >
                إلغاء
              </button>
              <button
                className="confirm-payment"
                onClick={processPayment}
                disabled={
                  loading || 
                  (paymentMethod === 'cash' && (!cashReceived || parseFloat(cashReceived) < total))
                }
              >
                {loading ? (
                  <>
                    <div className="spinner"></div>
                    جاري المعالجة...
                  </>
                ) : (
                  'تأكيد الدفع'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UltimatePOS;
