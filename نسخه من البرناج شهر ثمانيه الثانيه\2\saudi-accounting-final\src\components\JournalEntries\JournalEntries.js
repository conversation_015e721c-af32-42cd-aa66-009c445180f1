import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './JournalEntries.css';

const JournalEntries = () => {
  const [entries, setEntries] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEntry, setEditingEntry] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newEntry, setNewEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    reference: '',
    description: '',
    type: 'manual',
    status: 'pending',
    debitEntries: [{ accountId: '', amount: '', description: '' }],
    creditEntries: [{ accountId: '', amount: '', description: '' }],
    attachments: [],
    notes: ''
  });

  const entryTypes = [
    { value: 'manual', label: '📝 قيد يدوي', color: '#667eea' },
    { value: 'sales', label: '💰 مبيعات', color: '#10b981' },
    { value: 'purchase', label: '🛒 مشتريات', color: '#f59e0b' },
    { value: 'payment', label: '💳 دفعة', color: '#6366f1' },
    { value: 'receipt', label: '🧾 إيصال', color: '#8b5cf6' },
    { value: 'adjustment', label: '⚖️ تسوية', color: '#ef4444' },
    { value: 'depreciation', label: '📉 إهلاك', color: '#64748b' },
    { value: 'accrual', label: '📊 استحقاق', color: '#059669' }
  ];

  const entryStatuses = [
    { value: 'pending', label: '⏳ معلق', color: '#f59e0b' },
    { value: 'approved', label: '✅ معتمد', color: '#10b981' },
    { value: 'posted', label: '📋 مرحل', color: '#6366f1' },
    { value: 'cancelled', label: '❌ ملغي', color: '#ef4444' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const entriesData = database.getJournalEntries();
      const accountsData = database.getAccounts();
      
      setEntries(entriesData);
      setAccounts(accountsData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewEntry(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDebitChange = (index, field, value) => {
    const updatedDebits = [...newEntry.debitEntries];
    updatedDebits[index] = { ...updatedDebits[index], [field]: value };
    setNewEntry(prev => ({ ...prev, debitEntries: updatedDebits }));
  };

  const handleCreditChange = (index, field, value) => {
    const updatedCredits = [...newEntry.creditEntries];
    updatedCredits[index] = { ...updatedCredits[index], [field]: value };
    setNewEntry(prev => ({ ...prev, creditEntries: updatedCredits }));
  };

  const addDebitEntry = () => {
    setNewEntry(prev => ({
      ...prev,
      debitEntries: [...prev.debitEntries, { accountId: '', amount: '', description: '' }]
    }));
  };

  const addCreditEntry = () => {
    setNewEntry(prev => ({
      ...prev,
      creditEntries: [...prev.creditEntries, { accountId: '', amount: '', description: '' }]
    }));
  };

  const removeDebitEntry = (index) => {
    if (newEntry.debitEntries.length > 1) {
      const updatedDebits = newEntry.debitEntries.filter((_, i) => i !== index);
      setNewEntry(prev => ({ ...prev, debitEntries: updatedDebits }));
    }
  };

  const removeCreditEntry = (index) => {
    if (newEntry.creditEntries.length > 1) {
      const updatedCredits = newEntry.creditEntries.filter((_, i) => i !== index);
      setNewEntry(prev => ({ ...prev, creditEntries: updatedCredits }));
    }
  };

  const calculateTotals = () => {
    const totalDebit = newEntry.debitEntries.reduce((sum, entry) => 
      sum + (parseFloat(entry.amount) || 0), 0);
    const totalCredit = newEntry.creditEntries.reduce((sum, entry) => 
      sum + (parseFloat(entry.amount) || 0), 0);
    
    return { totalDebit, totalCredit, difference: totalDebit - totalCredit };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const { totalDebit, totalCredit } = calculateTotals();
      
      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        setMessage('خطأ: إجمالي المدين يجب أن يساوي إجمالي الدائن');
        setLoading(false);
        return;
      }

      const entryData = {
        ...newEntry,
        debitEntries: newEntry.debitEntries.filter(entry => entry.accountId && entry.amount),
        creditEntries: newEntry.creditEntries.filter(entry => entry.accountId && entry.amount),
        totalAmount: totalDebit
      };

      if (editingEntry) {
        await database.updateJournalEntry(editingEntry.id, entryData);
        setMessage('تم تحديث القيد بنجاح!');
        setEditingEntry(null);
      } else {
        await database.addJournalEntry(entryData);
        setMessage('تم إضافة القيد بنجاح!');
      }

      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ القيد:', error);
      setMessage('خطأ في حفظ القيد');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewEntry({
      date: new Date().toISOString().split('T')[0],
      reference: '',
      description: '',
      type: 'manual',
      status: 'pending',
      debitEntries: [{ accountId: '', amount: '', description: '' }],
      creditEntries: [{ accountId: '', amount: '', description: '' }],
      attachments: [],
      notes: ''
    });
    setShowAddForm(false);
    setEditingEntry(null);
  };

  const handleEdit = (entry) => {
    setNewEntry({
      ...entry,
      date: entry.date.split('T')[0]
    });
    setEditingEntry(entry);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا القيد؟')) {
      try {
        await database.deleteJournalEntry(id);
        setMessage('تم حذف القيد بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف القيد:', error);
        setMessage('خطأ في حذف القيد');
      }
    }
  };

  const handlePost = async (id) => {
    if (window.confirm('هل أنت متأكد من ترحيل هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.')) {
      try {
        await database.postJournalEntry(id);
        setMessage('تم ترحيل القيد بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في ترحيل القيد:', error);
        setMessage('خطأ في ترحيل القيد');
      }
    }
  };

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === parseInt(accountId));
    return account ? `${account.code} - ${account.name}` : 'غير محدد';
  };

  const getTypeInfo = (type) => {
    const typeObj = entryTypes.find(t => t.value === type);
    return typeObj || { label: type, color: '#64748b' };
  };

  const getStatusInfo = (status) => {
    const statusObj = entryStatuses.find(s => s.value === status);
    return statusObj || { label: status, color: '#64748b' };
  };

  const filteredEntries = entries.filter(entry => {
    const matchesSearch = 
      entry.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || entry.type === filterType;
    
    const entryDate = new Date(entry.date);
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    const matchesDate = entryDate >= startDate && entryDate <= endDate;
    
    return matchesSearch && matchesType && matchesDate;
  });

  const totals = calculateTotals();

  return (
    <div className="journal-entries-container">
      <div className="journal-entries-header">
        <h1>📋 القيود اليومية</h1>
        <p>إدارة وتتبع جميع القيود المحاسبية</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">📋</div>
          <div className="stat-info">
            <h3>إجمالي القيود</h3>
            <p>{entries.length} قيد</p>
          </div>
        </div>
        <div className="stat-card pending">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <h3>قيود معلقة</h3>
            <p>{entries.filter(e => e.status === 'pending').length} قيد</p>
          </div>
        </div>
        <div className="stat-card posted">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <h3>قيود مرحلة</h3>
            <p>{entries.filter(e => e.status === 'posted').length} قيد</p>
          </div>
        </div>
        <div className="stat-card amount">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي المبالغ</h3>
            <p>{entries.reduce((sum, e) => sum + (e.totalAmount || 0), 0).toLocaleString()} ريال</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="journal-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في القيود..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الأنواع</option>
            {entryTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            className="date-input"
          />
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            className="date-input"
          />
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="add-entry-btn"
        >
          ➕ إضافة قيد جديد
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="entry-form-modal">
            <div className="modal-header">
              <h3>{editingEntry ? '✏️ تعديل القيد' : '➕ إضافة قيد جديد'}</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="entry-form">
              <div className="form-header">
                <div className="form-grid">
                  <div className="form-group">
                    <label>التاريخ</label>
                    <input
                      type="date"
                      name="date"
                      value={newEntry.date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>رقم المرجع</label>
                    <input
                      type="text"
                      name="reference"
                      value={newEntry.reference}
                      onChange={handleInputChange}
                      placeholder="رقم القيد أو المرجع"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>نوع القيد</label>
                    <select
                      name="type"
                      value={newEntry.type}
                      onChange={handleInputChange}
                      required
                    >
                      {entryTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="form-group">
                    <label>الحالة</label>
                    <select
                      name="status"
                      value={newEntry.status}
                      onChange={handleInputChange}
                      required
                    >
                      {entryStatuses.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="form-group full-width">
                  <label>وصف القيد</label>
                  <input
                    type="text"
                    name="description"
                    value={newEntry.description}
                    onChange={handleInputChange}
                    placeholder="وصف مختصر للقيد"
                    required
                  />
                </div>
              </div>

              <div className="entries-section">
                {/* Debit Entries */}
                <div className="debit-section">
                  <div className="section-header">
                    <h4>🔴 الجانب المدين</h4>
                    <button type="button" onClick={addDebitEntry} className="add-line-btn">
                      ➕ إضافة سطر
                    </button>
                  </div>
                  {newEntry.debitEntries.map((entry, index) => (
                    <div key={index} className="entry-line">
                      <select
                        value={entry.accountId}
                        onChange={(e) => handleDebitChange(index, 'accountId', e.target.value)}
                        required
                      >
                        <option value="">اختر الحساب</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.code} - {account.name}
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        placeholder="المبلغ"
                        value={entry.amount}
                        onChange={(e) => handleDebitChange(index, 'amount', e.target.value)}
                        step="0.01"
                        min="0"
                        required
                      />
                      <input
                        type="text"
                        placeholder="البيان"
                        value={entry.description}
                        onChange={(e) => handleDebitChange(index, 'description', e.target.value)}
                      />
                      {newEntry.debitEntries.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeDebitEntry(index)}
                          className="remove-line-btn"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  ))}
                  <div className="section-total">
                    إجمالي المدين: {totals.totalDebit.toLocaleString()} ريال
                  </div>
                </div>

                {/* Credit Entries */}
                <div className="credit-section">
                  <div className="section-header">
                    <h4>🔵 الجانب الدائن</h4>
                    <button type="button" onClick={addCreditEntry} className="add-line-btn">
                      ➕ إضافة سطر
                    </button>
                  </div>
                  {newEntry.creditEntries.map((entry, index) => (
                    <div key={index} className="entry-line">
                      <select
                        value={entry.accountId}
                        onChange={(e) => handleCreditChange(index, 'accountId', e.target.value)}
                        required
                      >
                        <option value="">اختر الحساب</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.code} - {account.name}
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        placeholder="المبلغ"
                        value={entry.amount}
                        onChange={(e) => handleCreditChange(index, 'amount', e.target.value)}
                        step="0.01"
                        min="0"
                        required
                      />
                      <input
                        type="text"
                        placeholder="البيان"
                        value={entry.description}
                        onChange={(e) => handleCreditChange(index, 'description', e.target.value)}
                      />
                      {newEntry.creditEntries.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeCreditEntry(index)}
                          className="remove-line-btn"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  ))}
                  <div className="section-total">
                    إجمالي الدائن: {totals.totalCredit.toLocaleString()} ريال
                  </div>
                </div>
              </div>

              <div className="balance-check">
                <div className={`balance-indicator ${Math.abs(totals.difference) < 0.01 ? 'balanced' : 'unbalanced'}`}>
                  {Math.abs(totals.difference) < 0.01 ? 
                    '✅ القيد متوازن' : 
                    `⚠️ فرق التوازن: ${totals.difference.toLocaleString()} ريال`
                  }
                </div>
              </div>

              <div className="form-group full-width">
                <label>ملاحظات</label>
                <textarea
                  name="notes"
                  value={newEntry.notes}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="ملاحظات إضافية..."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button 
                  type="submit" 
                  disabled={loading || Math.abs(totals.difference) >= 0.01} 
                  className="save-btn"
                >
                  {loading ? '⏳ جاري الحفظ...' : (editingEntry ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Entries Table */}
      <div className="entries-table-container">
        <table className="entries-table">
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>رقم المرجع</th>
              <th>الوصف</th>
              <th>النوع</th>
              <th>المبلغ</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredEntries.length === 0 ? (
              <tr>
                <td colSpan="7" className="no-data">
                  لا توجد قيود مطابقة للبحث
                </td>
              </tr>
            ) : (
              filteredEntries.map(entry => {
                const typeInfo = getTypeInfo(entry.type);
                const statusInfo = getStatusInfo(entry.status);
                return (
                  <tr key={entry.id}>
                    <td>{new Date(entry.date).toLocaleDateString('ar-SA')}</td>
                    <td className="reference">{entry.reference}</td>
                    <td>{entry.description}</td>
                    <td>
                      <span 
                        className="type-badge"
                        style={{ backgroundColor: typeInfo.color }}
                      >
                        {typeInfo.label}
                      </span>
                    </td>
                    <td className="amount">{(entry.totalAmount || 0).toLocaleString()} ريال</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: statusInfo.color }}
                      >
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(entry)}
                        className="edit-btn"
                        title="تعديل"
                        disabled={entry.status === 'posted'}
                      >
                        ✏️
                      </button>
                      {entry.status === 'approved' && (
                        <button
                          onClick={() => handlePost(entry.id)}
                          className="post-btn"
                          title="ترحيل"
                        >
                          📋
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(entry.id)}
                        className="delete-btn"
                        title="حذف"
                        disabled={entry.status === 'posted'}
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default JournalEntries;
