import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Employees.css';

const Employees = () => {
  const [employees, setEmployees] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newEmployee, setNewEmployee] = useState({
    name: '',
    email: '',
    phone: '',
    nationalId: '',
    position: '',
    department: '',
    salary: '',
    hireDate: new Date().toISOString().split('T')[0],
    status: 'active',
    address: '',
    emergencyContact: '',
    bankAccount: '',
    notes: ''
  });

  const departments = [
    'المحاسبة',
    'المبيعات',
    'المشتريات',
    'المخازن',
    'الموارد البشرية',
    'تقنية المعلومات',
    'الإدارة العامة',
    'خدمة العملاء',
    'التسويق',
    'الأمن والسلامة'
  ];

  const positions = [
    'مدير عام',
    'مدير قسم',
    'محاسب',
    'مساعد محاسب',
    'مندوب مبيعات',
    'أمين مخزن',
    'موظف استقبال',
    'سكرتير',
    'مطور برمجيات',
    'فني دعم',
    'مشرف',
    'موظف'
  ];

  const employeeStatuses = [
    { value: 'active', label: '✅ نشط', color: '#10b981' },
    { value: 'inactive', label: '⏸️ غير نشط', color: '#f59e0b' },
    { value: 'terminated', label: '❌ منتهي الخدمة', color: '#ef4444' },
    { value: 'on_leave', label: '🏖️ في إجازة', color: '#6366f1' }
  ];

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = () => {
    try {
      const employeesData = database.getEmployees();
      setEmployees(employeesData);
    } catch (error) {
      console.error('خطأ في تحميل الموظفين:', error);
      setMessage('خطأ في تحميل الموظفين');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewEmployee(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const employeeData = {
        ...newEmployee,
        salary: parseFloat(newEmployee.salary)
      };

      if (editingEmployee) {
        await database.updateEmployee(editingEmployee.id, employeeData);
        setMessage('تم تحديث بيانات الموظف بنجاح!');
        setEditingEmployee(null);
      } else {
        await database.addEmployee(employeeData);
        setMessage('تم إضافة الموظف بنجاح!');
      }

      resetForm();
      loadEmployees();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الموظف:', error);
      setMessage('خطأ في حفظ الموظف');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewEmployee({
      name: '',
      email: '',
      phone: '',
      nationalId: '',
      position: '',
      department: '',
      salary: '',
      hireDate: new Date().toISOString().split('T')[0],
      status: 'active',
      address: '',
      emergencyContact: '',
      bankAccount: '',
      notes: ''
    });
    setShowAddForm(false);
    setEditingEmployee(null);
  };

  const handleEdit = (employee) => {
    setNewEmployee({
      ...employee,
      hireDate: employee.hireDate.split('T')[0]
    });
    setEditingEmployee(employee);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      try {
        await database.deleteEmployee(id);
        setMessage('تم حذف الموظف بنجاح!');
        loadEmployees();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف الموظف:', error);
        setMessage('خطأ في حذف الموظف');
      }
    }
  };

  const getStatusInfo = (status) => {
    const statusObj = employeeStatuses.find(s => s.value === status);
    return statusObj || { label: status, color: '#64748b' };
  };

  const getDepartments = () => {
    const usedDepartments = [...new Set(employees.map(emp => emp.department))];
    return usedDepartments.filter(dept => dept);
  };

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = 
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.phone.includes(searchTerm) ||
      employee.nationalId.includes(searchTerm) ||
      employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = filterDepartment === 'all' || employee.department === filterDepartment;
    const matchesStatus = filterStatus === 'all' || employee.status === filterStatus;
    
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const totalSalaries = activeEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0);

  return (
    <div className="employees-container">
      <div className="employees-header">
        <h1>👥 إدارة الموظفين</h1>
        <p>إدارة بيانات الموظفين والموارد البشرية</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">👥</div>
          <div className="stat-info">
            <h3>إجمالي الموظفين</h3>
            <p>{employees.length} موظف</p>
          </div>
        </div>
        <div className="stat-card active">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <h3>الموظفين النشطين</h3>
            <p>{activeEmployees.length} موظف</p>
          </div>
        </div>
        <div className="stat-card departments">
          <div className="stat-icon">🏢</div>
          <div className="stat-info">
            <h3>الأقسام</h3>
            <p>{getDepartments().length} قسم</p>
          </div>
        </div>
        <div className="stat-card salaries">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي الرواتب</h3>
            <p>{totalSalaries.toLocaleString()} ريال</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="employees-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في الموظفين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterDepartment}
            onChange={(e) => setFilterDepartment(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الأقسام</option>
            {getDepartments().map(dept => (
              <option key={dept} value={dept}>
                {dept}
              </option>
            ))}
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الحالات</option>
            {employeeStatuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="add-employee-btn"
        >
          ➕ إضافة موظف جديد
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="employee-form-modal">
            <div className="modal-header">
              <h3>{editingEmployee ? '✏️ تعديل الموظف' : '➕ إضافة موظف جديد'}</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="employee-form">
              <div className="form-sections">
                <div className="form-section">
                  <h4>📋 المعلومات الأساسية</h4>
                  <div className="form-grid">
                    <div className="form-group">
                      <label>الاسم الكامل</label>
                      <input
                        type="text"
                        name="name"
                        value={newEmployee.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label>رقم الهوية الوطنية</label>
                      <input
                        type="text"
                        name="nationalId"
                        value={newEmployee.nationalId}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label>البريد الإلكتروني</label>
                      <input
                        type="email"
                        name="email"
                        value={newEmployee.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label>رقم الهاتف</label>
                      <input
                        type="tel"
                        name="phone"
                        value={newEmployee.phone}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group full-width">
                      <label>العنوان</label>
                      <textarea
                        name="address"
                        value={newEmployee.address}
                        onChange={handleInputChange}
                        rows="2"
                      />
                    </div>

                    <div className="form-group">
                      <label>جهة الاتصال في الطوارئ</label>
                      <input
                        type="text"
                        name="emergencyContact"
                        value={newEmployee.emergencyContact}
                        onChange={handleInputChange}
                        placeholder="الاسم ورقم الهاتف"
                      />
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4>💼 معلومات العمل</h4>
                  <div className="form-grid">
                    <div className="form-group">
                      <label>المنصب</label>
                      <select
                        name="position"
                        value={newEmployee.position}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">اختر المنصب</option>
                        {positions.map(position => (
                          <option key={position} value={position}>
                            {position}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label>القسم</label>
                      <select
                        name="department"
                        value={newEmployee.department}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">اختر القسم</option>
                        {departments.map(dept => (
                          <option key={dept} value={dept}>
                            {dept}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label>الراتب الأساسي (ريال)</label>
                      <input
                        type="number"
                        name="salary"
                        value={newEmployee.salary}
                        onChange={handleInputChange}
                        step="0.01"
                        min="0"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label>تاريخ التوظيف</label>
                      <input
                        type="date"
                        name="hireDate"
                        value={newEmployee.hireDate}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label>الحالة</label>
                      <select
                        name="status"
                        value={newEmployee.status}
                        onChange={handleInputChange}
                        required
                      >
                        {employeeStatuses.map(status => (
                          <option key={status.value} value={status.value}>
                            {status.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label>رقم الحساب البنكي</label>
                      <input
                        type="text"
                        name="bankAccount"
                        value={newEmployee.bankAccount}
                        onChange={handleInputChange}
                        placeholder="IBAN أو رقم الحساب"
                      />
                    </div>

                    <div className="form-group full-width">
                      <label>ملاحظات</label>
                      <textarea
                        name="notes"
                        value={newEmployee.notes}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="ملاحظات إضافية..."
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : (editingEmployee ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Employees Table */}
      <div className="employees-table-container">
        <table className="employees-table">
          <thead>
            <tr>
              <th>الاسم</th>
              <th>رقم الهوية</th>
              <th>المنصب</th>
              <th>القسم</th>
              <th>الهاتف</th>
              <th>الراتب</th>
              <th>تاريخ التوظيف</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredEmployees.length === 0 ? (
              <tr>
                <td colSpan="9" className="no-data">
                  لا توجد موظفين مطابقين للبحث
                </td>
              </tr>
            ) : (
              filteredEmployees.map(employee => {
                const statusInfo = getStatusInfo(employee.status);
                return (
                  <tr key={employee.id}>
                    <td className="employee-name">{employee.name}</td>
                    <td>{employee.nationalId}</td>
                    <td>{employee.position}</td>
                    <td>{employee.department}</td>
                    <td>{employee.phone}</td>
                    <td className="salary">{employee.salary?.toLocaleString()} ريال</td>
                    <td>{new Date(employee.hireDate).toLocaleDateString('ar-SA')}</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: statusInfo.color }}
                      >
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(employee)}
                        className="edit-btn"
                        title="تعديل"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDelete(employee.id)}
                        className="delete-btn"
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Employees;
