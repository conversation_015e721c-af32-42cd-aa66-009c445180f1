/* Enhanced Login Styles - <PERSON><PERSON><PERSON>P Professional Design */

.enhanced-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Cairo', sans-serif;
}

/* Background */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, #1e40af 100%);
  z-index: -2;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
  z-index: -1;
}

.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 70%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Main Container */
.login-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  gap: var(--spacing-2xl);
  align-items: center;
  justify-content: center;
}

/* Login Card */
.login-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  width: 100%;
  max-width: 450px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  padding: var(--spacing-xl);
  text-align: center;
  border-bottom: 1px solid var(--gray-200);
}

.company-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.logo-icon {
  font-size: var(--font-size-4xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.logo-text p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
  font-weight: 500;
}

.current-time {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  font-weight: 500;
  background: var(--gray-100);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  display: inline-block;
}

/* Login Body */
.login-body {
  padding: var(--spacing-xl);
}

.welcome-text {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.welcome-text h2 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.welcome-text p {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  margin: 0;
}

/* Error Message */
.error-message {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: var(--danger-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border: 1px solid #fecaca;
  font-weight: 500;
}

.error-icon {
  font-size: var(--font-size-lg);
}

/* Form Styles */
.login-form {
  margin-bottom: var(--spacing-xl);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-right: 3rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
  background: var(--white);
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.form-input:disabled {
  background-color: var(--gray-100);
  cursor: not-allowed;
  opacity: 0.7;
}

.input-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--font-size-lg);
  color: var(--gray-400);
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.password-toggle:hover {
  color: var(--gray-600);
  background: var(--gray-100);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Form Options */
.form-options {
  margin-bottom: var(--spacing-xl);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  font-weight: 500;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: var(--white);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: var(--white);
  font-size: var(--font-size-sm);
  font-weight: 700;
}

/* Buttons */
.login-button,
.demo-button {
  width: 100%;
  padding: var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-family: inherit;
}

.login-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.demo-button {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.demo-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button-icon {
  font-size: var(--font-size-lg);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Demo Section */
.demo-section {
  margin-bottom: var(--spacing-lg);
}

.divider {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gray-300);
}

.divider span {
  background: var(--white);
  padding: 0 var(--spacing-md);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Login Footer */
.login-footer {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.info-icon {
  font-size: var(--font-size-base);
}

.copyright {
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.copyright p {
  margin: 0;
  margin-bottom: var(--spacing-xs);
}

/* Features Sidebar */
.features-sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
}

.features-sidebar h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.feature-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-lg);
  color: var(--white);
}

.feature-content h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.feature-content p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
    gap: var(--spacing-xl);
  }
  
  .features-sidebar {
    max-width: 100%;
    order: -1;
  }
  
  .features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .enhanced-login {
    padding: var(--spacing-md);
  }
  
  .login-container {
    padding: var(--spacing-md);
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .login-header,
  .login-body,
  .login-footer {
    padding: var(--spacing-lg);
  }
  
  .company-logo {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .logo-icon {
    font-size: var(--font-size-3xl);
  }
  
  .features-sidebar {
    padding: var(--spacing-lg);
  }
  
  .features-list {
    grid-template-columns: 1fr;
  }
  
  .system-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .form-input {
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 2.5rem;
  }
  
  .login-button,
  .demo-button {
    padding: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
  
  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-xl);
  }
}
