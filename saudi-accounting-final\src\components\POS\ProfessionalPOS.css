/* Professional POS Styles */

.professional-pos {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  font-family: 'Cairo', sans-serif;
}

/* Header */
.pos-header {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.pos-title h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.invoice-number {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 0.25rem;
}

.header-center {
  flex: 1;
  max-width: 500px;
  margin: 0 2rem;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.time-display {
  font-size: 1.1rem;
  font-weight: 600;
}

.cashier-info {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Message */
.pos-message {
  padding: 1rem 2rem;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  animation: slideDown 0.3s ease;
}

.pos-message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.pos-message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.close-message {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-message:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.pos-main {
  flex: 1;
  display: flex;
  gap: 1rem;
  padding: 1rem;
  overflow: hidden;
}

/* Products Section */
.products-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.categories-bar {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  overflow-x: auto;
}

.category-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.category-btn:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.category-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.products-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1rem;
  overflow-y: auto;
}

.product-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 200px;
  overflow: hidden;
}

.product-card:hover:not(.out-of-stock) {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.product-card:active:not(.out-of-stock) {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.product-card.out-of-stock {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-image {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  background: #f8fafc;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.no-image {
  font-size: 2rem;
  color: #94a3b8;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  line-height: 1.3;
}

.product-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 0.25rem;
}

.product-stock {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.product-barcode {
  font-size: 0.7rem;
  color: #94a3b8;
  font-family: monospace;
}

.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 12px;
}

/* Cart Section */
.cart-section {
  flex: 1;
  min-width: 350px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cart-header {
  padding: 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cart-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #1e293b;
}

.cart-count {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.empty-cart {
  text-align: center;
  color: #94a3b8;
  font-size: 1.1rem;
  padding: 2rem;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: #fafafa;
}

.item-info {
  flex: 1;
}

.item-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: #1e293b;
}

.item-price {
  font-size: 0.8rem;
  color: #059669;
  font-weight: 600;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.qty-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #1e293b;
}

.remove-btn {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #fecaca;
}

.item-total {
  font-weight: 700;
  color: #1e293b;
  min-width: 80px;
  text-align: right;
}

.cart-summary {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.summary-row.total {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  border-top: 2px solid #e2e8f0;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.cart-actions {
  padding: 1rem;
  display: flex;
  gap: 0.5rem;
}

.clear-btn {
  flex: 1;
  padding: 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #e5e7eb;
}

.checkout-btn {
  flex: 2;
  padding: 0.75rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkout-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

/* Payment Modal */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.payment-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
  animation: modalSlideIn 0.3s ease;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.modal-content {
  padding: 1.5rem;
}

.payment-summary {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #059669;
}

.payment-input {
  margin-bottom: 1rem;
}

.payment-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.payment-input input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.payment-input input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.change-amount {
  background: #ecfdf5;
  border: 1px solid #bbf7d0;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.change {
  font-size: 1.2rem;
  font-weight: 700;
  color: #059669;
}

.modal-actions {
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 1rem;
}

.cancel-btn {
  flex: 1;
  padding: 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  flex: 2;
  padding: 0.75rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Animations */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 1024px) {
  .pos-main {
    flex-direction: column;
    gap: 0.5rem;
  }

  .products-section {
    flex: none;
    height: 55vh;
  }

  .cart-section {
    flex: none;
    min-width: auto;
    height: 45vh;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .pos-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .header-left,
  .header-center,
  .header-right {
    width: 100%;
  }

  .header-center {
    margin: 0;
  }

  .header-right {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .product-card {
    height: 150px;
    padding: 0.75rem;
  }

  .product-name {
    font-size: 0.8rem;
  }

  .product-price {
    font-size: 1rem;
  }

  .cart-section {
    min-width: auto;
  }

  .cart-item {
    padding: 0.5rem;
    gap: 0.75rem;
  }

  .item-controls {
    gap: 0.25rem;
  }

  .qty-btn {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .categories-bar {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .category-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .pos-main {
    padding: 0.5rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .product-card {
    height: 130px;
    padding: 0.5rem;
  }

  .product-image {
    height: 60px;
  }

  .no-image {
    font-size: 1.5rem;
  }

  .product-name {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  .product-price {
    font-size: 0.9rem;
  }

  .product-stock,
  .product-barcode {
    font-size: 0.7rem;
  }

  .cart-header {
    padding: 0.75rem;
  }

  .cart-header h2 {
    font-size: 1rem;
  }

  .cart-items {
    padding: 0.75rem;
  }

  .cart-item {
    padding: 0.5rem;
    margin-bottom: 0.4rem;
  }

  .item-info h4 {
    font-size: 0.8rem;
  }

  .item-price {
    font-size: 0.75rem;
  }

  .quantity {
    min-width: 30px;
    font-size: 0.9rem;
  }

  .item-total {
    font-size: 0.85rem;
    min-width: 60px;
  }

  .cart-summary {
    padding: 0.75rem;
  }

  .summary-row {
    font-size: 0.8rem;
  }

  .summary-row.total {
    font-size: 1rem;
  }

  .cart-actions {
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .clear-btn,
  .checkout-btn {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}
