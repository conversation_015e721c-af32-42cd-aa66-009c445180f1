import React, { useState, useEffect, useRef } from 'react';
import database from '../../utils/database';
import './ModernPOS.css';

const ModernPOS = ({ user, onBack }) => {
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [cart, setCart] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [discount, setDiscount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [cashReceived, setCashReceived] = useState('');
  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');
  const searchInputRef = useRef(null);

  const paymentMethods = [
    { value: 'cash', label: '💵 نقداً', icon: '💵' },
    { value: 'card', label: '💳 بطاقة', icon: '💳' },
    { value: 'bank_transfer', label: '🏦 تحويل بنكي', icon: '🏦' },
    { value: 'digital_wallet', label: '📱 محفظة رقمية', icon: '📱' },
    { value: 'credit', label: '📝 آجل', icon: '📝' }
  ];

  useEffect(() => {
    loadData();
    generateInvoiceNumber();
    
    // Focus on search input
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }

    // Keyboard shortcuts
    const handleKeyPress = (e) => {
      if (e.key === 'F2') {
        e.preventDefault();
        if (cart.length > 0) {
          handleCheckout();
        }
      } else if (e.key === 'F3') {
        e.preventDefault();
        clearCart();
      } else if (e.key === 'F4') {
        e.preventDefault();
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const loadData = () => {
    try {
      const productsData = database.getProducts();
      const customersData = database.getCustomers();
      
      setProducts(productsData);
      setCustomers(customersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const generateInvoiceNumber = () => {
    const invoices = database.getInvoices();
    const nextNumber = invoices.length + 1;
    const invoiceNumber = `INV-${nextNumber.toString().padStart(6, '0')}`;
    setCurrentInvoiceNumber(invoiceNumber);
  };

  const addToCart = (product) => {
    if (product.stock <= 0) {
      setMessage('⚠️ المنتج غير متوفر في المخزون');
      setTimeout(() => setMessage(''), 3000);
      return;
    }

    const existingItem = cart.find(item => item.productId === product.id);
    
    if (existingItem) {
      if (existingItem.quantity >= product.stock) {
        setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');
        setTimeout(() => setMessage(''), 3000);
        return;
      }
      
      setCart(cart.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ));
    } else {
      const newItem = {
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        total: product.price,
        category: product.category
      };
      setCart([...cart, newItem]);
    }

    // Success feedback
    setMessage(`✅ تم إضافة ${product.name} إلى السلة`);
    setTimeout(() => setMessage(''), 2000);
  };

  const updateQuantity = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    const product = products.find(p => p.id === productId);
    if (newQuantity > product.stock) {
      setMessage('⚠️ الكمية المطلوبة تتجاوز المخزون المتاح');
      setTimeout(() => setMessage(''), 3000);
      return;
    }

    setCart(cart.map(item =>
      item.productId === productId
        ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
        : item
    ));
  };

  const removeFromCart = (productId) => {
    setCart(cart.filter(item => item.productId !== productId));
  };

  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setDiscount(0);
    setPaymentMethod('cash');
    setCashReceived('');
    generateInvoiceNumber();
    setMessage('🗑️ تم مسح السلة');
    setTimeout(() => setMessage(''), 2000);
  };

  const calculateTotals = () => {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = (subtotal * discount) / 100;
    const afterDiscount = subtotal - discountAmount;
    const vatAmount = afterDiscount * 0.15;
    const total = afterDiscount + vatAmount;

    return {
      subtotal,
      discountAmount,
      afterDiscount,
      vatAmount,
      total
    };
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      setMessage('⚠️ السلة فارغة');
      setTimeout(() => setMessage(''), 3000);
      return;
    }

    setShowPaymentModal(true);
  };

  const processPayment = async () => {
    setLoading(true);
    
    try {
      const totals = calculateTotals();
      
      if (paymentMethod === 'cash') {
        const received = parseFloat(cashReceived) || 0;
        if (received < totals.total) {
          setMessage('⚠️ المبلغ المستلم أقل من إجمالي الفاتورة');
          setLoading(false);
          return;
        }
      }

      const invoiceData = {
        invoiceNumber: currentInvoiceNumber,
        invoiceDate: new Date().toISOString(),
        customerId: selectedCustomer?.id || null,
        customerName: selectedCustomer?.name || 'عميل نقدي',
        items: cart,
        subtotal: totals.subtotal,
        discountPercentage: discount,
        discountAmount: totals.discountAmount,
        vatAmount: totals.vatAmount,
        total: totals.total,
        paymentMethod,
        status: 'paid',
        cashierId: user?.id,
        cashierName: user?.username
      };

      const savedInvoice = await database.addInvoice(invoiceData);
      
      // Update product stock
      cart.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          database.updateProduct(product.id, {
            stock: product.stock - item.quantity
          });
        }
      });

      // Create payment voucher if cash
      if (paymentMethod === 'cash') {
        const voucherData = {
          type: 'receipt',
          voucherDate: new Date().toISOString(),
          amount: totals.total,
          description: `قبض نقدي - فاتورة ${currentInvoiceNumber}`,
          accountId: 1, // Cash account
          customerId: selectedCustomer?.id || null,
          paymentMethod: 'cash',
          reference: currentInvoiceNumber
        };
        
        await database.addVoucher(voucherData);
      }

      setMessage('✅ تم إتمام البيع بنجاح!');
      
      // Print invoice
      printInvoice(savedInvoice, totals);
      
      // Reset form
      clearCart();
      setShowPaymentModal(false);
      loadData(); // Refresh data
      
      setTimeout(() => setMessage(''), 3000);
      
    } catch (error) {
      console.error('خطأ في معالجة الدفع:', error);
      setMessage('❌ خطأ في معالجة الدفع');
      setTimeout(() => setMessage(''), 3000);
    } finally {
      setLoading(false);
    }
  };

  const printInvoice = (invoice, totals) => {
    const settings = database.getSettings();
    const change = paymentMethod === 'cash' ? (parseFloat(cashReceived) - totals.total) : 0;
    
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>فاتورة - ${invoice.invoiceNumber}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              direction: rtl; 
              font-size: 14px;
            }
            .invoice-header { 
              text-align: center; 
              border-bottom: 2px solid #333; 
              padding-bottom: 20px; 
              margin-bottom: 20px; 
            }
            .invoice-details { 
              display: flex; 
              justify-content: space-between; 
              margin-bottom: 20px; 
            }
            .items-table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-bottom: 20px; 
            }
            .items-table th, .items-table td { 
              border: 1px solid #ddd; 
              padding: 8px; 
              text-align: right; 
            }
            .items-table th { 
              background: #f5f5f5; 
            }
            .totals { 
              text-align: left; 
              margin-top: 20px; 
            }
            .total-row { 
              display: flex; 
              justify-content: space-between; 
              margin: 5px 0; 
            }
            .final-total { 
              font-weight: bold; 
              font-size: 18px; 
              border-top: 2px solid #333; 
              padding-top: 10px; 
            }
            @media print { 
              body { margin: 0; } 
            }
          </style>
        </head>
        <body>
          <div class="invoice-header">
            <h2>${settings.companyName}</h2>
            <p>${settings.address}</p>
            <p>هاتف: ${settings.phone} | بريد إلكتروني: ${settings.email}</p>
            <p>الرقم الضريبي: ${settings.vatNumber}</p>
          </div>

          <div class="invoice-details">
            <div>
              <p><strong>رقم الفاتورة:</strong> ${invoice.invoiceNumber}</p>
              <p><strong>التاريخ:</strong> ${new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')}</p>
              <p><strong>الوقت:</strong> ${new Date(invoice.invoiceDate).toLocaleTimeString('ar-SA')}</p>
            </div>
            <div>
              <p><strong>العميل:</strong> ${invoice.customerName}</p>
              <p><strong>الكاشير:</strong> ${invoice.cashierName}</p>
              <p><strong>طريقة الدفع:</strong> ${paymentMethods.find(m => m.value === paymentMethod)?.label}</p>
            </div>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th>الصنف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              ${cart.map(item => `
                <tr>
                  <td>${item.name}</td>
                  <td>${item.quantity}</td>
                  <td>${item.price.toFixed(2)} ريال</td>
                  <td>${item.total.toFixed(2)} ريال</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="totals">
            <div class="total-row">
              <span>المجموع الفرعي:</span>
              <span>${totals.subtotal.toFixed(2)} ريال</span>
            </div>
            ${discount > 0 ? `
              <div class="total-row">
                <span>الخصم (${discount}%):</span>
                <span>-${totals.discountAmount.toFixed(2)} ريال</span>
              </div>
            ` : ''}
            <div class="total-row">
              <span>ضريبة القيمة المضافة (15%):</span>
              <span>${totals.vatAmount.toFixed(2)} ريال</span>
            </div>
            <div class="total-row final-total">
              <span>الإجمالي النهائي:</span>
              <span>${totals.total.toFixed(2)} ريال</span>
            </div>
            ${paymentMethod === 'cash' ? `
              <div class="total-row">
                <span>المبلغ المستلم:</span>
                <span>${parseFloat(cashReceived).toFixed(2)} ريال</span>
              </div>
              <div class="total-row">
                <span>الباقي:</span>
                <span>${change.toFixed(2)} ريال</span>
              </div>
            ` : ''}
          </div>

          <div style="text-align: center; margin-top: 30px; font-size: 12px;">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام Aronim EXP</p>
          </div>
        </body>
      </html>
    `;

    printWindow.document.documentElement.innerHTML = printContent;
    setTimeout(() => printWindow.print(), 500);
  };

  const handleBarcodeSearch = (barcode) => {
    const product = products.find(p => p.barcode === barcode);
    if (product) {
      addToCart(product);
    } else {
      setMessage('⚠️ لم يتم العثور على منتج بهذا الباركود');
      setTimeout(() => setMessage(''), 3000);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode?.includes(searchTerm);
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(products.map(p => p.category))].filter(Boolean);
  const totals = calculateTotals();

  return (
    <div className="modern-pos">
      {/* Header */}
      <div className="pos-header">
        <div className="pos-title">
          <h1>🛒 نقطة البيع</h1>
          <div className="invoice-info">
            <span className="invoice-number">فاتورة رقم: {currentInvoiceNumber}</span>
            <span className="current-time">
              {new Date().toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </span>
          </div>
        </div>
        
        <div className="pos-actions">
          <button className="shortcut-btn" title="F2 - إتمام البيع">
            F2 💳
          </button>
          <button className="shortcut-btn" title="F3 - مسح السلة" onClick={clearCart}>
            F3 🗑️
          </button>
          <button className="shortcut-btn" title="F4 - البحث" onClick={() => searchInputRef.current?.focus()}>
            F4 🔍
          </button>
        </div>
      </div>

      {message && (
        <div className={`pos-message ${message.includes('❌') || message.includes('⚠️') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <div className="pos-content">
        {/* Products Section */}
        <div className="products-section">
          <div className="products-header">
            <div className="search-section">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="🔍 البحث عن منتج أو مسح الباركود..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && searchTerm) {
                    handleBarcodeSearch(searchTerm);
                  }
                }}
                className="search-input"
              />
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="category-filter"
              >
                <option value="all">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="products-grid">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className={`product-card ${product.stock <= 0 ? 'out-of-stock' : ''}`}
                onClick={() => addToCart(product)}
              >
                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  <div className="product-price">{product.price.toFixed(2)} ريال</div>
                  <div className={`product-stock ${product.stock <= 5 ? 'low-stock' : ''}`}>
                    المخزون: {product.stock}
                  </div>
                </div>
                <div className="product-actions">
                  <button className="add-btn" disabled={product.stock <= 0}>
                    {product.stock <= 0 ? '❌' : '➕'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cart Section */}
        <div className="cart-section">
          <div className="cart-header">
            <h2>🛒 السلة ({cart.length})</h2>
            {cart.length > 0 && (
              <button onClick={clearCart} className="clear-cart-btn">
                🗑️ مسح الكل
              </button>
            )}
          </div>

          <div className="customer-section">
            <select
              value={selectedCustomer?.id || ''}
              onChange={(e) => {
                const customer = customers.find(c => c.id === parseInt(e.target.value));
                setSelectedCustomer(customer || null);
              }}
              className="customer-select"
            >
              <option value="">عميل نقدي</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>

          <div className="cart-items">
            {cart.length === 0 ? (
              <div className="empty-cart">
                <div className="empty-cart-icon">🛒</div>
                <p>السلة فارغة</p>
                <p>اختر المنتجات لإضافتها</p>
              </div>
            ) : (
              cart.map(item => (
                <div key={item.productId} className="cart-item">
                  <div className="item-info">
                    <h4>{item.name}</h4>
                    <div className="item-price">{item.price.toFixed(2)} ريال</div>
                  </div>
                  <div className="item-controls">
                    <button
                      onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                      className="qty-btn"
                    >
                      ➖
                    </button>
                    <span className="quantity">{item.quantity}</span>
                    <button
                      onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                      className="qty-btn"
                    >
                      ➕
                    </button>
                  </div>
                  <div className="item-total">
                    {item.total.toFixed(2)} ريال
                  </div>
                  <button
                    onClick={() => removeFromCart(item.productId)}
                    className="remove-btn"
                  >
                    ❌
                  </button>
                </div>
              ))
            )}
          </div>

          {cart.length > 0 && (
            <>
              <div className="discount-section">
                <label>الخصم (%)</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={discount}
                  onChange={(e) => setDiscount(Math.max(0, Math.min(100, parseFloat(e.target.value) || 0)))}
                  className="discount-input"
                />
              </div>

              <div className="cart-totals">
                <div className="total-row">
                  <span>المجموع الفرعي:</span>
                  <span>{totals.subtotal.toFixed(2)} ريال</span>
                </div>
                {discount > 0 && (
                  <div className="total-row discount">
                    <span>الخصم ({discount}%):</span>
                    <span>-{totals.discountAmount.toFixed(2)} ريال</span>
                  </div>
                )}
                <div className="total-row">
                  <span>ضريبة القيمة المضافة:</span>
                  <span>{totals.vatAmount.toFixed(2)} ريال</span>
                </div>
                <div className="total-row final">
                  <span>الإجمالي:</span>
                  <span>{totals.total.toFixed(2)} ريال</span>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                className="checkout-btn"
                disabled={loading}
              >
                {loading ? '⏳ جاري المعالجة...' : '💳 إتمام البيع (F2)'}
              </button>
            </>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="modal-overlay">
          <div className="payment-modal">
            <div className="modal-header">
              <h3>💳 إتمام عملية الدفع</h3>
              <button onClick={() => setShowPaymentModal(false)} className="close-btn">
                ✕
              </button>
            </div>

            <div className="modal-body">
              <div className="payment-summary">
                <div className="summary-row">
                  <span>إجمالي الفاتورة:</span>
                  <span className="amount">{totals.total.toFixed(2)} ريال</span>
                </div>
              </div>

              <div className="payment-methods">
                <label>طريقة الدفع:</label>
                <div className="payment-options">
                  {paymentMethods.map(method => (
                    <button
                      key={method.value}
                      className={`payment-option ${paymentMethod === method.value ? 'active' : ''}`}
                      onClick={() => setPaymentMethod(method.value)}
                    >
                      <span className="method-icon">{method.icon}</span>
                      <span className="method-label">{method.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {paymentMethod === 'cash' && (
                <div className="cash-payment">
                  <label>المبلغ المستلم:</label>
                  <input
                    type="number"
                    value={cashReceived}
                    onChange={(e) => setCashReceived(e.target.value)}
                    placeholder="أدخل المبلغ المستلم"
                    className="cash-input"
                    min={totals.total}
                    step="0.01"
                  />
                  {cashReceived && parseFloat(cashReceived) >= totals.total && (
                    <div className="change-amount">
                      الباقي: {(parseFloat(cashReceived) - totals.total).toFixed(2)} ريال
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="cancel-btn"
              >
                إلغاء
              </button>
              <button
                onClick={processPayment}
                disabled={loading || (paymentMethod === 'cash' && parseFloat(cashReceived) < totals.total)}
                className="confirm-payment-btn"
              >
                {loading ? '⏳ جاري المعالجة...' : '✅ تأكيد الدفع'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernPOS;
