{"ast": null, "code": "// نظام قاعدة البيانات المحلية للنظام المحاسبي السعودي\n// Local Database System for Saudi Accounting System\n\nclass LocalDatabase {\n  constructor() {\n    this.initializeDatabase();\n  }\n\n  // تهيئة قاعدة البيانات\n  initializeDatabase() {\n    // إنشاء الجداول الأساسية إذا لم تكن موجودة\n    if (!localStorage.getItem('saudi_accounting_invoices')) {\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_products')) {\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(this.getDefaultProducts()));\n    }\n    if (!localStorage.getItem('saudi_accounting_customers')) {\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_settings')) {\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(this.getDefaultSettings()));\n    }\n    if (!localStorage.getItem('saudi_accounting_payments')) {\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_inventory_movements')) {\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_employees')) {\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_returns')) {\n      localStorage.setItem('saudi_accounting_returns', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_quotations')) {\n      localStorage.setItem('saudi_accounting_quotations', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_purchase_invoices')) {\n      localStorage.setItem('saudi_accounting_purchase_invoices', JSON.stringify([]));\n    }\n    if (!localStorage.getItem('saudi_accounting_vouchers')) {\n      localStorage.setItem('saudi_accounting_vouchers', JSON.stringify([]));\n    }\n  }\n\n  // المنتجات الافتراضية\n  getDefaultProducts() {\n    return [{\n      id: 1,\n      name: 'لابتوب Dell',\n      price: 2500,\n      stock: 10,\n      category: 'إلكترونيات',\n      barcode: '*************',\n      description: 'لابتوب Dell Inspiron 15',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 2,\n      name: 'ماوس لاسلكي',\n      price: 85,\n      stock: 25,\n      category: 'إلكترونيات',\n      barcode: '*************',\n      description: 'ماوس لاسلكي عالي الجودة',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 3,\n      name: 'كيبورد ميكانيكي',\n      price: 150,\n      stock: 15,\n      category: 'إلكترونيات',\n      barcode: '*************',\n      description: 'كيبورد ميكانيكي للألعاب',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 4,\n      name: 'شاشة 24 بوصة',\n      price: 800,\n      stock: 8,\n      category: 'إلكترونيات',\n      barcode: '1234567890126',\n      description: 'شاشة LED 24 بوصة Full HD',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 5,\n      name: 'طابعة HP',\n      price: 450,\n      stock: 5,\n      category: 'مكتبية',\n      barcode: '1234567890127',\n      description: 'طابعة HP LaserJet',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 6,\n      name: 'كاميرا ويب',\n      price: 120,\n      stock: 20,\n      category: 'إلكترونيات',\n      barcode: '1234567890128',\n      description: 'كاميرا ويب HD 1080p',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 7,\n      name: 'سماعات بلوتوث',\n      price: 200,\n      stock: 12,\n      category: 'إلكترونيات',\n      barcode: '1234567890129',\n      description: 'سماعات بلوتوث لاسلكية',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }, {\n      id: 8,\n      name: 'قلم رقمي',\n      price: 75,\n      stock: 30,\n      category: 'مكتبية',\n      barcode: '1234567890130',\n      description: 'قلم رقمي للرسم والكتابة',\n      vatRate: 0.15,\n      createdAt: new Date().toISOString()\n    }];\n  }\n\n  // الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      companyName: 'شركة نظام المحاسبة السعودي المحدودة',\n      vatNumber: '310122393500003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      vatRate: 0.15,\n      currency: 'ريال سعودي',\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1001,\n      createdAt: new Date().toISOString()\n    };\n  }\n\n  // === إدارة الفواتير ===\n\n  // حفظ فاتورة جديدة\n  saveInvoice(invoice) {\n    try {\n      const invoices = this.getInvoices();\n      const newInvoice = {\n        ...invoice,\n        id: this.generateInvoiceId(),\n        createdAt: new Date().toISOString(),\n        status: 'paid'\n      };\n      invoices.push(newInvoice);\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n\n      // تحديث المخزون\n      this.updateStockAfterSale(invoice.items);\n\n      // تحديث رقم الفاتورة التالي\n      this.incrementInvoiceNumber();\n      return newInvoice;\n    } catch (error) {\n      console.error('خطأ في حفظ الفاتورة:', error);\n      throw new Error('فشل في حفظ الفاتورة');\n    }\n  }\n\n  // جلب جميع الفواتير\n  getInvoices() {\n    try {\n      const invoices = localStorage.getItem('saudi_accounting_invoices');\n      return invoices ? JSON.parse(invoices) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الفواتير:', error);\n      return [];\n    }\n  }\n\n  // جلب فاتورة بالرقم\n  getInvoiceById(id) {\n    const invoices = this.getInvoices();\n    return invoices.find(invoice => invoice.id === id);\n  }\n\n  // جلب فواتير بتاريخ محدد\n  getInvoicesByDate(date) {\n    const invoices = this.getInvoices();\n    return invoices.filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt).toDateString();\n      const searchDate = new Date(date).toDateString();\n      return invoiceDate === searchDate;\n    });\n  }\n\n  // === إدارة المنتجات ===\n\n  // جلب جميع المنتجات\n  getProducts() {\n    try {\n      const products = localStorage.getItem('saudi_accounting_products');\n      return products ? JSON.parse(products) : this.getDefaultProducts();\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات:', error);\n      return this.getDefaultProducts();\n    }\n  }\n\n  // إضافة منتج جديد\n  addProduct(product) {\n    try {\n      const products = this.getProducts();\n      const newProduct = {\n        ...product,\n        id: this.generateProductId(),\n        createdAt: new Date().toISOString()\n      };\n      products.push(newProduct);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      return newProduct;\n    } catch (error) {\n      console.error('خطأ في إضافة المنتج:', error);\n      throw new Error('فشل في إضافة المنتج');\n    }\n  }\n\n  // تحديث منتج\n  updateProduct(id, updates) {\n    try {\n      const products = this.getProducts();\n      const index = products.findIndex(product => product.id === id);\n      if (index !== -1) {\n        products[index] = {\n          ...products[index],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n        return products[index];\n      }\n      throw new Error('المنتج غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المنتج:', error);\n      throw new Error('فشل في تحديث المنتج');\n    }\n  }\n\n  // حذف منتج\n  deleteProduct(id) {\n    try {\n      const products = this.getProducts();\n      const filteredProducts = products.filter(product => product.id !== id);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(filteredProducts));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المنتج:', error);\n      throw new Error('فشل في حذف المنتج');\n    }\n  }\n\n  // تحديث المخزون بعد البيع\n  updateStockAfterSale(items) {\n    try {\n      const products = this.getProducts();\n      items.forEach(item => {\n        const productIndex = products.findIndex(product => product.id === item.id);\n        if (productIndex !== -1) {\n          products[productIndex].stock -= item.quantity;\n          if (products[productIndex].stock < 0) {\n            products[productIndex].stock = 0;\n          }\n        }\n      });\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n    } catch (error) {\n      console.error('خطأ في تحديث المخزون:', error);\n    }\n  }\n\n  // === إدارة العملاء ===\n\n  // جلب جميع العملاء\n  getCustomers() {\n    try {\n      const customers = localStorage.getItem('saudi_accounting_customers');\n      return customers ? JSON.parse(customers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب العملاء:', error);\n      return [];\n    }\n  }\n\n  // إضافة عميل جديد\n  addCustomer(customer) {\n    try {\n      const customers = this.getCustomers();\n      const newCustomer = {\n        ...customer,\n        id: this.generateCustomerId(),\n        createdAt: new Date().toISOString()\n      };\n      customers.push(newCustomer);\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify(customers));\n      return newCustomer;\n    } catch (error) {\n      console.error('خطأ في إضافة العميل:', error);\n      throw new Error('فشل في إضافة العميل');\n    }\n  }\n\n  // === مولدات الأرقام ===\n\n  generateInvoiceId() {\n    const settings = this.getSettings();\n    return `${settings.invoicePrefix}-${settings.nextInvoiceNumber}`;\n  }\n  generateProductId() {\n    const products = this.getProducts();\n    return products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;\n  }\n  generateCustomerId() {\n    const customers = this.getCustomers();\n    return customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;\n  }\n\n  // === إدارة الموردين ===\n\n  // جلب جميع الموردين\n  getSuppliers() {\n    try {\n      const suppliers = localStorage.getItem('saudi_accounting_suppliers');\n      return suppliers ? JSON.parse(suppliers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموردين:', error);\n      return [];\n    }\n  }\n\n  // إضافة مورد جديد\n  addSupplier(supplier) {\n    try {\n      const suppliers = this.getSuppliers();\n      const newSupplier = {\n        ...supplier,\n        id: this.generateSupplierId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      suppliers.push(newSupplier);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n      return newSupplier;\n    } catch (error) {\n      console.error('خطأ في إضافة المورد:', error);\n      throw new Error('فشل في إضافة المورد');\n    }\n  }\n\n  // تحديث مورد\n  updateSupplier(id, updates) {\n    try {\n      const suppliers = this.getSuppliers();\n      const index = suppliers.findIndex(supplier => supplier.id === id);\n      if (index !== -1) {\n        suppliers[index] = {\n          ...suppliers[index],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n        return suppliers[index];\n      }\n      throw new Error('المورد غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المورد:', error);\n      throw new Error('فشل في تحديث المورد');\n    }\n  }\n\n  // حذف مورد\n  deleteSupplier(id) {\n    try {\n      const suppliers = this.getSuppliers();\n      const filteredSuppliers = suppliers.filter(supplier => supplier.id !== id);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(filteredSuppliers));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المورد:', error);\n      throw new Error('فشل في حذف المورد');\n    }\n  }\n\n  // توليد معرف مورد\n  generateSupplierId() {\n    const suppliers = this.getSuppliers();\n    return suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 1;\n  }\n\n  // === إدارة المدفوعات ===\n\n  // جلب جميع المدفوعات\n  getPayments() {\n    try {\n      const payments = localStorage.getItem('saudi_accounting_payments');\n      return payments ? JSON.parse(payments) : [];\n    } catch (error) {\n      console.error('خطأ في جلب المدفوعات:', error);\n      return [];\n    }\n  }\n\n  // إضافة دفعة جديدة\n  addPayment(payment) {\n    try {\n      const payments = this.getPayments();\n      const newPayment = {\n        ...payment,\n        id: this.generatePaymentId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      payments.push(newPayment);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n\n      // تحديث حالة الفاتورة إذا كانت الدفعة مكتملة\n      if (payment.status === 'completed') {\n        this.updateInvoicePaymentStatus(payment.invoiceId, payment.amount);\n      }\n      return newPayment;\n    } catch (error) {\n      console.error('خطأ في إضافة الدفعة:', error);\n      throw new Error('فشل في إضافة الدفعة');\n    }\n  }\n\n  // تحديث دفعة\n  updatePayment(id, updates) {\n    try {\n      const payments = this.getPayments();\n      const paymentIndex = payments.findIndex(p => p.id === id);\n      if (paymentIndex !== -1) {\n        payments[paymentIndex] = {\n          ...payments[paymentIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n        return payments[paymentIndex];\n      }\n      throw new Error('الدفعة غير موجودة');\n    } catch (error) {\n      console.error('خطأ في تحديث الدفعة:', error);\n      throw new Error('فشل في تحديث الدفعة');\n    }\n  }\n\n  // حذف دفعة\n  deletePayment(id) {\n    try {\n      const payments = this.getPayments();\n      const filteredPayments = payments.filter(p => p.id !== id);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(filteredPayments));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الدفعة:', error);\n      throw new Error('فشل في حذف الدفعة');\n    }\n  }\n\n  // توليد معرف دفعة\n  generatePaymentId() {\n    const payments = this.getPayments();\n    return payments.length > 0 ? Math.max(...payments.map(p => p.id)) + 1 : 1;\n  }\n\n  // تحديث حالة دفع الفاتورة\n  updateInvoicePaymentStatus(invoiceId, paidAmount) {\n    try {\n      const invoices = this.getInvoices();\n      const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);\n      if (invoiceIndex !== -1) {\n        const invoice = invoices[invoiceIndex];\n        const totalPaid = (invoice.paidAmount || 0) + paidAmount;\n        let paymentStatus = 'unpaid';\n        if (totalPaid >= invoice.total) {\n          paymentStatus = 'paid';\n        } else if (totalPaid > 0) {\n          paymentStatus = 'partial';\n        }\n        invoices[invoiceIndex] = {\n          ...invoice,\n          paidAmount: totalPaid,\n          paymentStatus: paymentStatus,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث حالة دفع الفاتورة:', error);\n    }\n  }\n\n  // جلب مدفوعات عميل معين\n  getCustomerPayments(customerId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.customerId === customerId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات العميل:', error);\n      return [];\n    }\n  }\n\n  // جلب مدفوعات فاتورة معينة\n  getInvoicePayments(invoiceId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.invoiceId === invoiceId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات الفاتورة:', error);\n      return [];\n    }\n  }\n\n  // === إدارة حركات المخزون ===\n\n  // جلب جميع حركات المخزون\n  getInventoryMovements() {\n    try {\n      const movements = localStorage.getItem('saudi_accounting_inventory_movements');\n      return movements ? JSON.parse(movements) : [];\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون:', error);\n      return [];\n    }\n  }\n\n  // إضافة حركة مخزون جديدة\n  addInventoryMovement(movement) {\n    try {\n      const movements = this.getInventoryMovements();\n      const newMovement = {\n        ...movement,\n        id: this.generateMovementId(),\n        createdAt: new Date().toISOString()\n      };\n      movements.push(newMovement);\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(movements));\n\n      // تحديث مخزون المنتج\n      this.updateProductStock(movement.productId, movement.quantity, movement.type);\n      return newMovement;\n    } catch (error) {\n      console.error('خطأ في إضافة حركة المخزون:', error);\n      throw new Error('فشل في إضافة حركة المخزون');\n    }\n  }\n\n  // تحديث مخزون المنتج\n  updateProductStock(productId, quantity, movementType) {\n    try {\n      const products = this.getProducts();\n      const productIndex = products.findIndex(p => p.id === productId);\n      if (productIndex !== -1) {\n        const currentStock = products[productIndex].stock || 0;\n        let newStock = currentStock;\n        if (movementType === 'in') {\n          newStock = currentStock + quantity;\n        } else if (movementType === 'out' || movementType === 'damaged' || movementType === 'expired') {\n          newStock = Math.max(0, currentStock - quantity);\n        } else if (movementType === 'adjustment') {\n          newStock = quantity; // التعديل يحدد الكمية الجديدة مباشرة\n        }\n        products[productIndex] = {\n          ...products[productIndex],\n          stock: newStock,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث مخزون المنتج:', error);\n    }\n  }\n\n  // توليد معرف حركة مخزون\n  generateMovementId() {\n    const movements = this.getInventoryMovements();\n    return movements.length > 0 ? Math.max(...movements.map(m => m.id)) + 1 : 1;\n  }\n\n  // جلب حركات مخزون منتج معين\n  getProductMovements(productId) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => movement.productId === productId);\n    } catch (error) {\n      console.error('خطأ في جلب حركات المنتج:', error);\n      return [];\n    }\n  }\n\n  // جلب تقرير حركات المخزون لفترة معينة\n  getInventoryMovementsByDateRange(startDate, endDate) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => {\n        const movementDate = new Date(movement.date);\n        return movementDate >= new Date(startDate) && movementDate <= new Date(endDate);\n      });\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون للفترة:', error);\n      return [];\n    }\n  }\n\n  // حساب قيمة المخزون الإجمالية\n  calculateTotalInventoryValue() {\n    try {\n      const products = this.getProducts();\n      return products.reduce((total, product) => {\n        return total + product.stock * product.price;\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب قيمة المخزون:', error);\n      return 0;\n    }\n  }\n\n  // جلب المنتجات منخفضة المخزون\n  getLowStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock <= (product.minStock || 5));\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);\n      return [];\n    }\n  }\n\n  // جلب المنتجات نافدة المخزون\n  getOutOfStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock === 0);\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات نافدة المخزون:', error);\n      return [];\n    }\n  }\n\n  // === إدارة الموظفين ===\n\n  // جلب جميع الموظفين\n  getEmployees() {\n    try {\n      const employees = localStorage.getItem('saudi_accounting_employees');\n      return employees ? JSON.parse(employees) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين:', error);\n      return [];\n    }\n  }\n\n  // إضافة موظف جديد\n  addEmployee(employee) {\n    try {\n      const employees = this.getEmployees();\n      const newEmployee = {\n        ...employee,\n        id: this.generateEmployeeId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      employees.push(newEmployee);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n      return newEmployee;\n    } catch (error) {\n      console.error('خطأ في إضافة الموظف:', error);\n      throw new Error('فشل في إضافة الموظف');\n    }\n  }\n\n  // تحديث موظف\n  updateEmployee(id, updates) {\n    try {\n      const employees = this.getEmployees();\n      const employeeIndex = employees.findIndex(emp => emp.id === id);\n      if (employeeIndex !== -1) {\n        employees[employeeIndex] = {\n          ...employees[employeeIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n        return employees[employeeIndex];\n      }\n      throw new Error('الموظف غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث الموظف:', error);\n      throw new Error('فشل في تحديث الموظف');\n    }\n  }\n\n  // حذف موظف\n  deleteEmployee(id) {\n    try {\n      const employees = this.getEmployees();\n      const filteredEmployees = employees.filter(emp => emp.id !== id);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(filteredEmployees));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الموظف:', error);\n      throw new Error('فشل في حذف الموظف');\n    }\n  }\n\n  // توليد معرف موظف\n  generateEmployeeId() {\n    const employees = this.getEmployees();\n    return employees.length > 0 ? Math.max(...employees.map(emp => emp.id)) + 1 : 1;\n  }\n\n  // جلب الموظفين النشطين\n  getActiveEmployees() {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.status === 'active');\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين النشطين:', error);\n      return [];\n    }\n  }\n\n  // جلب موظفين حسب القسم\n  getEmployeesByDepartment(department) {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.department === department);\n    } catch (error) {\n      console.error('خطأ في جلب موظفين القسم:', error);\n      return [];\n    }\n  }\n\n  // حساب إجمالي الرواتب\n  calculateTotalSalaries() {\n    try {\n      const activeEmployees = this.getActiveEmployees();\n      return activeEmployees.reduce((total, employee) => {\n        return total + (employee.salary || 0);\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب إجمالي الرواتب:', error);\n      return 0;\n    }\n  }\n\n  // === إدارة الإعدادات المحسنة ===\n\n  // جلب الإعدادات الافتراضية المحسنة\n  getDefaultSettings() {\n    return {\n      // بيانات الشركة\n      companyName: 'شركة المحاسبة السعودية',\n      vatNumber: '300000000000003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      website: 'www.company.com',\n      // إعدادات الفواتير\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1,\n      vatRate: 15,\n      currency: 'SAR',\n      defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n      autoSaveInvoices: true,\n      // إعدادات النظام\n      language: 'ar',\n      timezone: 'Asia/Riyadh',\n      dateFormat: 'DD/MM/YYYY',\n      enableNotifications: true,\n      enableDarkMode: false,\n      enableAutoBackup: false,\n      // إعدادات إضافية\n      fiscalYearStart: '01/01',\n      backupFrequency: 'weekly',\n      maxInvoiceItems: 50,\n      lowStockThreshold: 5,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n  }\n\n  // تحديث الإعدادات\n  updateSettings(newSettings) {\n    try {\n      const currentSettings = this.getSettings();\n      const updatedSettings = {\n        ...currentSettings,\n        ...newSettings,\n        updatedAt: new Date().toISOString()\n      };\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(updatedSettings));\n      return updatedSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // إعادة تعيين الإعدادات للقيم الافتراضية\n  resetSettings() {\n    try {\n      const defaultSettings = this.getDefaultSettings();\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(defaultSettings));\n      return defaultSettings;\n    } catch (error) {\n      console.error('خطأ في إعادة تعيين الإعدادات:', error);\n      throw new Error('فشل في إعادة تعيين الإعدادات');\n    }\n  }\n\n  // تصدير جميع البيانات\n  exportAllData() {\n    try {\n      const data = {\n        settings: this.getSettings(),\n        invoices: this.getInvoices(),\n        products: this.getProducts(),\n        customers: this.getCustomers(),\n        suppliers: this.getSuppliers(),\n        accounts: this.getAccounts(),\n        journalEntries: this.getJournalEntries(),\n        payments: this.getPayments(),\n        inventoryMovements: this.getInventoryMovements(),\n        employees: this.getEmployees(),\n        exportDate: new Date().toISOString(),\n        version: '1.0.0'\n      };\n      return data;\n    } catch (error) {\n      console.error('خطأ في تصدير البيانات:', error);\n      throw new Error('فشل في تصدير البيانات');\n    }\n  }\n\n  // استيراد جميع البيانات\n  importAllData(data) {\n    try {\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.suppliers) {\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(data.suppliers));\n      }\n      if (data.accounts) {\n        localStorage.setItem('saudi_accounting_accounts', JSON.stringify(data.accounts));\n      }\n      if (data.journalEntries) {\n        localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(data.journalEntries));\n      }\n      if (data.payments) {\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(data.payments));\n      }\n      if (data.inventoryMovements) {\n        localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(data.inventoryMovements));\n      }\n      if (data.employees) {\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(data.employees));\n      }\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // === المحاسبة المالية ===\n\n  // جلب جميع الحسابات\n  getAccounts() {\n    try {\n      const accounts = localStorage.getItem('saudi_accounting_accounts');\n      return accounts ? JSON.parse(accounts) : this.getDefaultAccounts();\n    } catch (error) {\n      console.error('خطأ في جلب الحسابات:', error);\n      return this.getDefaultAccounts();\n    }\n  }\n\n  // الحسابات الافتراضية\n  getDefaultAccounts() {\n    return [{\n      id: 1,\n      code: '1001',\n      name: 'النقدية',\n      type: 'asset',\n      balance: 0,\n      description: 'النقدية في الصندوق'\n    }, {\n      id: 2,\n      code: '1002',\n      name: 'البنك',\n      type: 'asset',\n      balance: 0,\n      description: 'الحساب الجاري في البنك'\n    }, {\n      id: 3,\n      code: '1101',\n      name: 'العملاء',\n      type: 'asset',\n      balance: 0,\n      description: 'مدينو العملاء'\n    }, {\n      id: 4,\n      code: '1201',\n      name: 'المخزون',\n      type: 'asset',\n      balance: 0,\n      description: 'مخزون البضائع'\n    }, {\n      id: 5,\n      code: '2001',\n      name: 'الموردون',\n      type: 'liability',\n      balance: 0,\n      description: 'دائنو الموردين'\n    }, {\n      id: 6,\n      code: '2101',\n      name: 'ضريبة القيمة المضافة',\n      type: 'liability',\n      balance: 0,\n      description: 'ضريبة القيمة المضافة المستحقة'\n    }, {\n      id: 7,\n      code: '3001',\n      name: 'رأس المال',\n      type: 'equity',\n      balance: 0,\n      description: 'رأس مال المالك'\n    }, {\n      id: 8,\n      code: '4001',\n      name: 'المبيعات',\n      type: 'revenue',\n      balance: 0,\n      description: 'إيرادات المبيعات'\n    }, {\n      id: 9,\n      code: '5001',\n      name: 'تكلفة البضاعة المباعة',\n      type: 'expense',\n      balance: 0,\n      description: 'تكلفة البضائع المباعة'\n    }, {\n      id: 10,\n      code: '5101',\n      name: 'مصاريف التشغيل',\n      type: 'expense',\n      balance: 0,\n      description: 'المصاريف التشغيلية'\n    }];\n  }\n\n  // إضافة حساب جديد\n  addAccount(account) {\n    try {\n      const accounts = this.getAccounts();\n      const newAccount = {\n        ...account,\n        id: this.generateAccountId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      accounts.push(newAccount);\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n      return newAccount;\n    } catch (error) {\n      console.error('خطأ في إضافة الحساب:', error);\n      throw new Error('فشل في إضافة الحساب');\n    }\n  }\n\n  // توليد معرف حساب\n  generateAccountId() {\n    const accounts = this.getAccounts();\n    return accounts.length > 0 ? Math.max(...accounts.map(a => a.id)) + 1 : 1;\n  }\n\n  // جلب جميع القيود المحاسبية\n  getJournalEntries() {\n    try {\n      const entries = localStorage.getItem('saudi_accounting_journal_entries');\n      return entries ? JSON.parse(entries) : [];\n    } catch (error) {\n      console.error('خطأ في جلب القيود:', error);\n      return [];\n    }\n  }\n\n  // إضافة قيد محاسبي\n  addJournalEntry(entry) {\n    try {\n      const entries = this.getJournalEntries();\n      const newEntry = {\n        ...entry,\n        id: this.generateJournalEntryId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n      entries.push(newEntry);\n      localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(entries));\n\n      // تحديث أرصدة الحسابات\n      this.updateAccountBalances(newEntry);\n      return newEntry;\n    } catch (error) {\n      console.error('خطأ في إضافة القيد:', error);\n      throw new Error('فشل في إضافة القيد');\n    }\n  }\n\n  // تحديث أرصدة الحسابات\n  updateAccountBalances(entry) {\n    try {\n      const accounts = this.getAccounts();\n      entry.entries.forEach(line => {\n        const accountIndex = accounts.findIndex(a => a.id === parseInt(line.accountId));\n        if (accountIndex !== -1) {\n          // للأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص\n          // للخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص\n          if (accounts[accountIndex].type === 'asset' || accounts[accountIndex].type === 'expense') {\n            accounts[accountIndex].balance += line.debit - line.credit;\n          } else {\n            accounts[accountIndex].balance += line.credit - line.debit;\n          }\n          accounts[accountIndex].updatedAt = new Date().toISOString();\n        }\n      });\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n    } catch (error) {\n      console.error('خطأ في تحديث أرصدة الحسابات:', error);\n    }\n  }\n\n  // توليد معرف قيد محاسبي\n  generateJournalEntryId() {\n    const entries = this.getJournalEntries();\n    return entries.length > 0 ? Math.max(...entries.map(e => e.id)) + 1 : 1;\n  }\n  incrementInvoiceNumber() {\n    try {\n      const settings = this.getSettings();\n      settings.nextInvoiceNumber += 1;\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(settings));\n    } catch (error) {\n      console.error('خطأ في تحديث رقم الفاتورة:', error);\n    }\n  }\n\n  // === إدارة الإعدادات ===\n\n  getSettings() {\n    try {\n      const settings = localStorage.getItem('saudi_accounting_settings');\n      return settings ? JSON.parse(settings) : this.getDefaultSettings();\n    } catch (error) {\n      console.error('خطأ في جلب الإعدادات:', error);\n      return this.getDefaultSettings();\n    }\n  }\n  updateSettings(updates) {\n    try {\n      const settings = this.getSettings();\n      const newSettings = {\n        ...settings,\n        ...updates,\n        updatedAt: new Date().toISOString()\n      };\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(newSettings));\n      return newSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // === التقارير والإحصائيات ===\n\n  // إحصائيات اليوم\n  getTodayStats() {\n    const today = new Date().toDateString();\n    const todayInvoices = this.getInvoicesByDate(today);\n    const totalSales = todayInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = todayInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = todayInvoices.length;\n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // إحصائيات الشهر\n  getMonthStats() {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    const monthInvoices = this.getInvoices().filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt);\n      return invoiceDate.getMonth() === currentMonth && invoiceDate.getFullYear() === currentYear;\n    });\n    const totalSales = monthInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = monthInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = monthInvoices.length;\n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // منتجات منخفضة المخزون\n  getLowStockProducts(threshold = 5) {\n    const products = this.getProducts();\n    return products.filter(product => product.stock <= threshold);\n  }\n\n  // أفضل المنتجات مبيعاً\n  getTopSellingProducts(limit = 5) {\n    const invoices = this.getInvoices();\n    const productSales = {};\n    invoices.forEach(invoice => {\n      invoice.items.forEach(item => {\n        if (productSales[item.id]) {\n          productSales[item.id].quantity += item.quantity;\n          productSales[item.id].revenue += item.price * item.quantity;\n        } else {\n          productSales[item.id] = {\n            id: item.id,\n            name: item.name,\n            quantity: item.quantity,\n            revenue: item.price * item.quantity\n          };\n        }\n      });\n    });\n    return Object.values(productSales).sort((a, b) => b.quantity - a.quantity).slice(0, limit);\n  }\n\n  // تصدير البيانات\n  exportData() {\n    return {\n      invoices: this.getInvoices(),\n      products: this.getProducts(),\n      customers: this.getCustomers(),\n      suppliers: this.getSuppliers(),\n      accounts: this.getAccounts(),\n      journalEntries: this.getJournalEntries(),\n      settings: this.getSettings(),\n      exportDate: new Date().toISOString()\n    };\n  }\n\n  // استيراد البيانات\n  importData(data) {\n    try {\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // مسح جميع البيانات\n  clearAllData() {\n    try {\n      localStorage.removeItem('saudi_accounting_invoices');\n      localStorage.removeItem('saudi_accounting_products');\n      localStorage.removeItem('saudi_accounting_customers');\n      localStorage.removeItem('saudi_accounting_settings');\n      this.initializeDatabase();\n      return true;\n    } catch (error) {\n      console.error('خطأ في مسح البيانات:', error);\n      throw new Error('فشل في مسح البيانات');\n    }\n  }\n}\n\n// إنشاء مثيل واحد من قاعدة البيانات\nconst database = new LocalDatabase();\nexport default database;", "map": {"version": 3, "names": ["LocalDatabase", "constructor", "initializeDatabase", "localStorage", "getItem", "setItem", "JSON", "stringify", "getDefaultProducts", "getDefaultSettings", "id", "name", "price", "stock", "category", "barcode", "description", "vatRate", "createdAt", "Date", "toISOString", "companyName", "vatNumber", "address", "phone", "email", "currency", "invoicePrefix", "nextInvoiceNumber", "saveInvoice", "invoice", "invoices", "getInvoices", "newInvoice", "generateInvoiceId", "status", "push", "updateStockAfterSale", "items", "incrementInvoiceNumber", "error", "console", "Error", "parse", "getInvoiceById", "find", "getInvoicesByDate", "date", "filter", "invoiceDate", "toDateString", "searchDate", "getProducts", "products", "addProduct", "product", "newProduct", "generateProductId", "updateProduct", "updates", "index", "findIndex", "updatedAt", "deleteProduct", "filteredProducts", "for<PERSON>ach", "item", "productIndex", "quantity", "getCustomers", "customers", "addCustomer", "customer", "newCustomer", "generateCustomerId", "settings", "getSettings", "length", "Math", "max", "map", "p", "c", "getSuppliers", "suppliers", "addSupplier", "supplier", "newSupplier", "generateSupplierId", "updateSupplier", "deleteSupplier", "filteredSuppliers", "s", "getPayments", "payments", "addPayment", "payment", "newPayment", "generatePaymentId", "updateInvoicePaymentStatus", "invoiceId", "amount", "updatePayment", "paymentIndex", "deletePayment", "filteredPayments", "paidAmount", "invoiceIndex", "inv", "totalPaid", "paymentStatus", "total", "getCustomerPayments", "customerId", "getInvoicePayments", "getInventoryMovements", "movements", "addInventoryMovement", "movement", "newMovement", "generateMovementId", "updateProductStock", "productId", "type", "movementType", "currentStock", "newStock", "m", "getProductMovements", "getInventoryMovementsByDateRange", "startDate", "endDate", "movementDate", "calculateTotalInventoryValue", "reduce", "getLowStockProducts", "minStock", "getOutOfStockProducts", "getEmployees", "employees", "addEmployee", "employee", "newEmployee", "generateEmployeeId", "updateEmployee", "employeeIndex", "emp", "deleteEmployee", "filteredEmployees", "getActiveEmployees", "getEmployeesByDepartment", "department", "calculateTotalSalaries", "activeEmployees", "salary", "website", "defaultPaymentTerms", "autoSaveInvoices", "language", "timezone", "dateFormat", "enableNotifications", "enableDarkMode", "enableAutoBackup", "fiscalYearStart", "backupFrequency", "maxInvoiceItems", "lowStockThreshold", "updateSettings", "newSettings", "currentSettings", "updatedSettings", "resetSettings", "defaultSettings", "exportAllData", "data", "accounts", "getAccounts", "journalEntries", "getJournalEntries", "inventoryMovements", "exportDate", "version", "importAllData", "getDefaultAccounts", "code", "balance", "addAccount", "account", "newAccount", "generateAccountId", "a", "entries", "addJournalEntry", "entry", "newEntry", "generateJournalEntryId", "updateAccountBalances", "line", "accountIndex", "parseInt", "accountId", "debit", "credit", "e", "getTodayStats", "today", "todayInvoices", "totalSales", "sum", "totalVAT", "vat", "totalInvoices", "averageInvoice", "getMonthStats", "currentMonth", "getMonth", "currentYear", "getFullYear", "monthInvoices", "threshold", "getTopSellingProducts", "limit", "productSales", "revenue", "Object", "values", "sort", "b", "slice", "exportData", "importData", "clearAllData", "removeItem", "database"], "sources": ["D:/aronim/saudi-accounting-final/src/utils/database.js"], "sourcesContent": ["// نظام قاعدة البيانات المحلية للنظام المحاسبي السعودي\n// Local Database System for Saudi Accounting System\n\nclass LocalDatabase {\n  constructor() {\n    this.initializeDatabase();\n  }\n\n  // تهيئة قاعدة البيانات\n  initializeDatabase() {\n    // إنشاء الجداول الأساسية إذا لم تكن موجودة\n    if (!localStorage.getItem('saudi_accounting_invoices')) {\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify([]));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_products')) {\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(this.getDefaultProducts()));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_customers')) {\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify([]));\n    }\n    \n    if (!localStorage.getItem('saudi_accounting_settings')) {\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(this.getDefaultSettings()));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_payments')) {\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_inventory_movements')) {\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_employees')) {\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_returns')) {\n      localStorage.setItem('saudi_accounting_returns', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_quotations')) {\n      localStorage.setItem('saudi_accounting_quotations', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_purchase_invoices')) {\n      localStorage.setItem('saudi_accounting_purchase_invoices', JSON.stringify([]));\n    }\n\n    if (!localStorage.getItem('saudi_accounting_vouchers')) {\n      localStorage.setItem('saudi_accounting_vouchers', JSON.stringify([]));\n    }\n  }\n\n  // المنتجات الافتراضية\n  getDefaultProducts() {\n    return [\n      {\n        id: 1,\n        name: 'لابتوب Dell',\n        price: 2500,\n        stock: 10,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'لابتوب Dell Inspiron 15',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 2,\n        name: 'ماوس لاسلكي',\n        price: 85,\n        stock: 25,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'ماوس لاسلكي عالي الجودة',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 3,\n        name: 'كيبورد ميكانيكي',\n        price: 150,\n        stock: 15,\n        category: 'إلكترونيات',\n        barcode: '*************',\n        description: 'كيبورد ميكانيكي للألعاب',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 4,\n        name: 'شاشة 24 بوصة',\n        price: 800,\n        stock: 8,\n        category: 'إلكترونيات',\n        barcode: '1234567890126',\n        description: 'شاشة LED 24 بوصة Full HD',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 5,\n        name: 'طابعة HP',\n        price: 450,\n        stock: 5,\n        category: 'مكتبية',\n        barcode: '1234567890127',\n        description: 'طابعة HP LaserJet',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 6,\n        name: 'كاميرا ويب',\n        price: 120,\n        stock: 20,\n        category: 'إلكترونيات',\n        barcode: '1234567890128',\n        description: 'كاميرا ويب HD 1080p',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 7,\n        name: 'سماعات بلوتوث',\n        price: 200,\n        stock: 12,\n        category: 'إلكترونيات',\n        barcode: '1234567890129',\n        description: 'سماعات بلوتوث لاسلكية',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 8,\n        name: 'قلم رقمي',\n        price: 75,\n        stock: 30,\n        category: 'مكتبية',\n        barcode: '1234567890130',\n        description: 'قلم رقمي للرسم والكتابة',\n        vatRate: 0.15,\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // الإعدادات الافتراضية\n  getDefaultSettings() {\n    return {\n      companyName: 'شركة نظام المحاسبة السعودي المحدودة',\n      vatNumber: '310122393500003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      vatRate: 0.15,\n      currency: 'ريال سعودي',\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1001,\n      createdAt: new Date().toISOString()\n    };\n  }\n\n  // === إدارة الفواتير ===\n  \n  // حفظ فاتورة جديدة\n  saveInvoice(invoice) {\n    try {\n      const invoices = this.getInvoices();\n      const newInvoice = {\n        ...invoice,\n        id: this.generateInvoiceId(),\n        createdAt: new Date().toISOString(),\n        status: 'paid'\n      };\n      \n      invoices.push(newInvoice);\n      localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n      \n      // تحديث المخزون\n      this.updateStockAfterSale(invoice.items);\n      \n      // تحديث رقم الفاتورة التالي\n      this.incrementInvoiceNumber();\n      \n      return newInvoice;\n    } catch (error) {\n      console.error('خطأ في حفظ الفاتورة:', error);\n      throw new Error('فشل في حفظ الفاتورة');\n    }\n  }\n\n  // جلب جميع الفواتير\n  getInvoices() {\n    try {\n      const invoices = localStorage.getItem('saudi_accounting_invoices');\n      return invoices ? JSON.parse(invoices) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الفواتير:', error);\n      return [];\n    }\n  }\n\n  // جلب فاتورة بالرقم\n  getInvoiceById(id) {\n    const invoices = this.getInvoices();\n    return invoices.find(invoice => invoice.id === id);\n  }\n\n  // جلب فواتير بتاريخ محدد\n  getInvoicesByDate(date) {\n    const invoices = this.getInvoices();\n    return invoices.filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt).toDateString();\n      const searchDate = new Date(date).toDateString();\n      return invoiceDate === searchDate;\n    });\n  }\n\n  // === إدارة المنتجات ===\n  \n  // جلب جميع المنتجات\n  getProducts() {\n    try {\n      const products = localStorage.getItem('saudi_accounting_products');\n      return products ? JSON.parse(products) : this.getDefaultProducts();\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات:', error);\n      return this.getDefaultProducts();\n    }\n  }\n\n  // إضافة منتج جديد\n  addProduct(product) {\n    try {\n      const products = this.getProducts();\n      const newProduct = {\n        ...product,\n        id: this.generateProductId(),\n        createdAt: new Date().toISOString()\n      };\n      \n      products.push(newProduct);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      return newProduct;\n    } catch (error) {\n      console.error('خطأ في إضافة المنتج:', error);\n      throw new Error('فشل في إضافة المنتج');\n    }\n  }\n\n  // تحديث منتج\n  updateProduct(id, updates) {\n    try {\n      const products = this.getProducts();\n      const index = products.findIndex(product => product.id === id);\n      \n      if (index !== -1) {\n        products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n        return products[index];\n      }\n      \n      throw new Error('المنتج غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المنتج:', error);\n      throw new Error('فشل في تحديث المنتج');\n    }\n  }\n\n  // حذف منتج\n  deleteProduct(id) {\n    try {\n      const products = this.getProducts();\n      const filteredProducts = products.filter(product => product.id !== id);\n      localStorage.setItem('saudi_accounting_products', JSON.stringify(filteredProducts));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المنتج:', error);\n      throw new Error('فشل في حذف المنتج');\n    }\n  }\n\n  // تحديث المخزون بعد البيع\n  updateStockAfterSale(items) {\n    try {\n      const products = this.getProducts();\n      \n      items.forEach(item => {\n        const productIndex = products.findIndex(product => product.id === item.id);\n        if (productIndex !== -1) {\n          products[productIndex].stock -= item.quantity;\n          if (products[productIndex].stock < 0) {\n            products[productIndex].stock = 0;\n          }\n        }\n      });\n      \n      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n    } catch (error) {\n      console.error('خطأ في تحديث المخزون:', error);\n    }\n  }\n\n  // === إدارة العملاء ===\n  \n  // جلب جميع العملاء\n  getCustomers() {\n    try {\n      const customers = localStorage.getItem('saudi_accounting_customers');\n      return customers ? JSON.parse(customers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب العملاء:', error);\n      return [];\n    }\n  }\n\n  // إضافة عميل جديد\n  addCustomer(customer) {\n    try {\n      const customers = this.getCustomers();\n      const newCustomer = {\n        ...customer,\n        id: this.generateCustomerId(),\n        createdAt: new Date().toISOString()\n      };\n      \n      customers.push(newCustomer);\n      localStorage.setItem('saudi_accounting_customers', JSON.stringify(customers));\n      return newCustomer;\n    } catch (error) {\n      console.error('خطأ في إضافة العميل:', error);\n      throw new Error('فشل في إضافة العميل');\n    }\n  }\n\n  // === مولدات الأرقام ===\n  \n  generateInvoiceId() {\n    const settings = this.getSettings();\n    return `${settings.invoicePrefix}-${settings.nextInvoiceNumber}`;\n  }\n\n  generateProductId() {\n    const products = this.getProducts();\n    return products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;\n  }\n\n  generateCustomerId() {\n    const customers = this.getCustomers();\n    return customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;\n  }\n\n  // === إدارة الموردين ===\n\n  // جلب جميع الموردين\n  getSuppliers() {\n    try {\n      const suppliers = localStorage.getItem('saudi_accounting_suppliers');\n      return suppliers ? JSON.parse(suppliers) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموردين:', error);\n      return [];\n    }\n  }\n\n  // إضافة مورد جديد\n  addSupplier(supplier) {\n    try {\n      const suppliers = this.getSuppliers();\n      const newSupplier = {\n        ...supplier,\n        id: this.generateSupplierId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      suppliers.push(newSupplier);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n      return newSupplier;\n    } catch (error) {\n      console.error('خطأ في إضافة المورد:', error);\n      throw new Error('فشل في إضافة المورد');\n    }\n  }\n\n  // تحديث مورد\n  updateSupplier(id, updates) {\n    try {\n      const suppliers = this.getSuppliers();\n      const index = suppliers.findIndex(supplier => supplier.id === id);\n\n      if (index !== -1) {\n        suppliers[index] = {\n          ...suppliers[index],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));\n        return suppliers[index];\n      }\n\n      throw new Error('المورد غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث المورد:', error);\n      throw new Error('فشل في تحديث المورد');\n    }\n  }\n\n  // حذف مورد\n  deleteSupplier(id) {\n    try {\n      const suppliers = this.getSuppliers();\n      const filteredSuppliers = suppliers.filter(supplier => supplier.id !== id);\n      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(filteredSuppliers));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف المورد:', error);\n      throw new Error('فشل في حذف المورد');\n    }\n  }\n\n  // توليد معرف مورد\n  generateSupplierId() {\n    const suppliers = this.getSuppliers();\n    return suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 1;\n  }\n\n  // === إدارة المدفوعات ===\n\n  // جلب جميع المدفوعات\n  getPayments() {\n    try {\n      const payments = localStorage.getItem('saudi_accounting_payments');\n      return payments ? JSON.parse(payments) : [];\n    } catch (error) {\n      console.error('خطأ في جلب المدفوعات:', error);\n      return [];\n    }\n  }\n\n  // إضافة دفعة جديدة\n  addPayment(payment) {\n    try {\n      const payments = this.getPayments();\n      const newPayment = {\n        ...payment,\n        id: this.generatePaymentId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      payments.push(newPayment);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n\n      // تحديث حالة الفاتورة إذا كانت الدفعة مكتملة\n      if (payment.status === 'completed') {\n        this.updateInvoicePaymentStatus(payment.invoiceId, payment.amount);\n      }\n\n      return newPayment;\n    } catch (error) {\n      console.error('خطأ في إضافة الدفعة:', error);\n      throw new Error('فشل في إضافة الدفعة');\n    }\n  }\n\n  // تحديث دفعة\n  updatePayment(id, updates) {\n    try {\n      const payments = this.getPayments();\n      const paymentIndex = payments.findIndex(p => p.id === id);\n\n      if (paymentIndex !== -1) {\n        payments[paymentIndex] = {\n          ...payments[paymentIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));\n        return payments[paymentIndex];\n      }\n\n      throw new Error('الدفعة غير موجودة');\n    } catch (error) {\n      console.error('خطأ في تحديث الدفعة:', error);\n      throw new Error('فشل في تحديث الدفعة');\n    }\n  }\n\n  // حذف دفعة\n  deletePayment(id) {\n    try {\n      const payments = this.getPayments();\n      const filteredPayments = payments.filter(p => p.id !== id);\n      localStorage.setItem('saudi_accounting_payments', JSON.stringify(filteredPayments));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الدفعة:', error);\n      throw new Error('فشل في حذف الدفعة');\n    }\n  }\n\n  // توليد معرف دفعة\n  generatePaymentId() {\n    const payments = this.getPayments();\n    return payments.length > 0 ? Math.max(...payments.map(p => p.id)) + 1 : 1;\n  }\n\n  // تحديث حالة دفع الفاتورة\n  updateInvoicePaymentStatus(invoiceId, paidAmount) {\n    try {\n      const invoices = this.getInvoices();\n      const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);\n\n      if (invoiceIndex !== -1) {\n        const invoice = invoices[invoiceIndex];\n        const totalPaid = (invoice.paidAmount || 0) + paidAmount;\n\n        let paymentStatus = 'unpaid';\n        if (totalPaid >= invoice.total) {\n          paymentStatus = 'paid';\n        } else if (totalPaid > 0) {\n          paymentStatus = 'partial';\n        }\n\n        invoices[invoiceIndex] = {\n          ...invoice,\n          paidAmount: totalPaid,\n          paymentStatus: paymentStatus,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث حالة دفع الفاتورة:', error);\n    }\n  }\n\n  // جلب مدفوعات عميل معين\n  getCustomerPayments(customerId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.customerId === customerId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات العميل:', error);\n      return [];\n    }\n  }\n\n  // جلب مدفوعات فاتورة معينة\n  getInvoicePayments(invoiceId) {\n    try {\n      const payments = this.getPayments();\n      return payments.filter(payment => payment.invoiceId === invoiceId);\n    } catch (error) {\n      console.error('خطأ في جلب مدفوعات الفاتورة:', error);\n      return [];\n    }\n  }\n\n  // === إدارة حركات المخزون ===\n\n  // جلب جميع حركات المخزون\n  getInventoryMovements() {\n    try {\n      const movements = localStorage.getItem('saudi_accounting_inventory_movements');\n      return movements ? JSON.parse(movements) : [];\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون:', error);\n      return [];\n    }\n  }\n\n  // إضافة حركة مخزون جديدة\n  addInventoryMovement(movement) {\n    try {\n      const movements = this.getInventoryMovements();\n      const newMovement = {\n        ...movement,\n        id: this.generateMovementId(),\n        createdAt: new Date().toISOString()\n      };\n\n      movements.push(newMovement);\n      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(movements));\n\n      // تحديث مخزون المنتج\n      this.updateProductStock(movement.productId, movement.quantity, movement.type);\n\n      return newMovement;\n    } catch (error) {\n      console.error('خطأ في إضافة حركة المخزون:', error);\n      throw new Error('فشل في إضافة حركة المخزون');\n    }\n  }\n\n  // تحديث مخزون المنتج\n  updateProductStock(productId, quantity, movementType) {\n    try {\n      const products = this.getProducts();\n      const productIndex = products.findIndex(p => p.id === productId);\n\n      if (productIndex !== -1) {\n        const currentStock = products[productIndex].stock || 0;\n        let newStock = currentStock;\n\n        if (movementType === 'in') {\n          newStock = currentStock + quantity;\n        } else if (movementType === 'out' || movementType === 'damaged' || movementType === 'expired') {\n          newStock = Math.max(0, currentStock - quantity);\n        } else if (movementType === 'adjustment') {\n          newStock = quantity; // التعديل يحدد الكمية الجديدة مباشرة\n        }\n\n        products[productIndex] = {\n          ...products[productIndex],\n          stock: newStock,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));\n      }\n    } catch (error) {\n      console.error('خطأ في تحديث مخزون المنتج:', error);\n    }\n  }\n\n  // توليد معرف حركة مخزون\n  generateMovementId() {\n    const movements = this.getInventoryMovements();\n    return movements.length > 0 ? Math.max(...movements.map(m => m.id)) + 1 : 1;\n  }\n\n  // جلب حركات مخزون منتج معين\n  getProductMovements(productId) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => movement.productId === productId);\n    } catch (error) {\n      console.error('خطأ في جلب حركات المنتج:', error);\n      return [];\n    }\n  }\n\n  // جلب تقرير حركات المخزون لفترة معينة\n  getInventoryMovementsByDateRange(startDate, endDate) {\n    try {\n      const movements = this.getInventoryMovements();\n      return movements.filter(movement => {\n        const movementDate = new Date(movement.date);\n        return movementDate >= new Date(startDate) && movementDate <= new Date(endDate);\n      });\n    } catch (error) {\n      console.error('خطأ في جلب حركات المخزون للفترة:', error);\n      return [];\n    }\n  }\n\n  // حساب قيمة المخزون الإجمالية\n  calculateTotalInventoryValue() {\n    try {\n      const products = this.getProducts();\n      return products.reduce((total, product) => {\n        return total + (product.stock * product.price);\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب قيمة المخزون:', error);\n      return 0;\n    }\n  }\n\n  // جلب المنتجات منخفضة المخزون\n  getLowStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock <= (product.minStock || 5));\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);\n      return [];\n    }\n  }\n\n  // جلب المنتجات نافدة المخزون\n  getOutOfStockProducts() {\n    try {\n      const products = this.getProducts();\n      return products.filter(product => product.stock === 0);\n    } catch (error) {\n      console.error('خطأ في جلب المنتجات نافدة المخزون:', error);\n      return [];\n    }\n  }\n\n  // === إدارة الموظفين ===\n\n  // جلب جميع الموظفين\n  getEmployees() {\n    try {\n      const employees = localStorage.getItem('saudi_accounting_employees');\n      return employees ? JSON.parse(employees) : [];\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين:', error);\n      return [];\n    }\n  }\n\n  // إضافة موظف جديد\n  addEmployee(employee) {\n    try {\n      const employees = this.getEmployees();\n      const newEmployee = {\n        ...employee,\n        id: this.generateEmployeeId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      employees.push(newEmployee);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n\n      return newEmployee;\n    } catch (error) {\n      console.error('خطأ في إضافة الموظف:', error);\n      throw new Error('فشل في إضافة الموظف');\n    }\n  }\n\n  // تحديث موظف\n  updateEmployee(id, updates) {\n    try {\n      const employees = this.getEmployees();\n      const employeeIndex = employees.findIndex(emp => emp.id === id);\n\n      if (employeeIndex !== -1) {\n        employees[employeeIndex] = {\n          ...employees[employeeIndex],\n          ...updates,\n          updatedAt: new Date().toISOString()\n        };\n\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));\n        return employees[employeeIndex];\n      }\n\n      throw new Error('الموظف غير موجود');\n    } catch (error) {\n      console.error('خطأ في تحديث الموظف:', error);\n      throw new Error('فشل في تحديث الموظف');\n    }\n  }\n\n  // حذف موظف\n  deleteEmployee(id) {\n    try {\n      const employees = this.getEmployees();\n      const filteredEmployees = employees.filter(emp => emp.id !== id);\n      localStorage.setItem('saudi_accounting_employees', JSON.stringify(filteredEmployees));\n      return true;\n    } catch (error) {\n      console.error('خطأ في حذف الموظف:', error);\n      throw new Error('فشل في حذف الموظف');\n    }\n  }\n\n  // توليد معرف موظف\n  generateEmployeeId() {\n    const employees = this.getEmployees();\n    return employees.length > 0 ? Math.max(...employees.map(emp => emp.id)) + 1 : 1;\n  }\n\n  // جلب الموظفين النشطين\n  getActiveEmployees() {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.status === 'active');\n    } catch (error) {\n      console.error('خطأ في جلب الموظفين النشطين:', error);\n      return [];\n    }\n  }\n\n  // جلب موظفين حسب القسم\n  getEmployeesByDepartment(department) {\n    try {\n      const employees = this.getEmployees();\n      return employees.filter(employee => employee.department === department);\n    } catch (error) {\n      console.error('خطأ في جلب موظفين القسم:', error);\n      return [];\n    }\n  }\n\n  // حساب إجمالي الرواتب\n  calculateTotalSalaries() {\n    try {\n      const activeEmployees = this.getActiveEmployees();\n      return activeEmployees.reduce((total, employee) => {\n        return total + (employee.salary || 0);\n      }, 0);\n    } catch (error) {\n      console.error('خطأ في حساب إجمالي الرواتب:', error);\n      return 0;\n    }\n  }\n\n  // === إدارة الإعدادات المحسنة ===\n\n  // جلب الإعدادات الافتراضية المحسنة\n  getDefaultSettings() {\n    return {\n      // بيانات الشركة\n      companyName: 'شركة المحاسبة السعودية',\n      vatNumber: '300000000000003',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '+966 11 123 4567',\n      email: '<EMAIL>',\n      website: 'www.company.com',\n\n      // إعدادات الفواتير\n      invoicePrefix: 'INV',\n      nextInvoiceNumber: 1,\n      vatRate: 15,\n      currency: 'SAR',\n      defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',\n      autoSaveInvoices: true,\n\n      // إعدادات النظام\n      language: 'ar',\n      timezone: 'Asia/Riyadh',\n      dateFormat: 'DD/MM/YYYY',\n      enableNotifications: true,\n      enableDarkMode: false,\n      enableAutoBackup: false,\n\n      // إعدادات إضافية\n      fiscalYearStart: '01/01',\n      backupFrequency: 'weekly',\n      maxInvoiceItems: 50,\n      lowStockThreshold: 5,\n\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n  }\n\n  // تحديث الإعدادات\n  updateSettings(newSettings) {\n    try {\n      const currentSettings = this.getSettings();\n      const updatedSettings = {\n        ...currentSettings,\n        ...newSettings,\n        updatedAt: new Date().toISOString()\n      };\n\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(updatedSettings));\n      return updatedSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // إعادة تعيين الإعدادات للقيم الافتراضية\n  resetSettings() {\n    try {\n      const defaultSettings = this.getDefaultSettings();\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(defaultSettings));\n      return defaultSettings;\n    } catch (error) {\n      console.error('خطأ في إعادة تعيين الإعدادات:', error);\n      throw new Error('فشل في إعادة تعيين الإعدادات');\n    }\n  }\n\n  // تصدير جميع البيانات\n  exportAllData() {\n    try {\n      const data = {\n        settings: this.getSettings(),\n        invoices: this.getInvoices(),\n        products: this.getProducts(),\n        customers: this.getCustomers(),\n        suppliers: this.getSuppliers(),\n        accounts: this.getAccounts(),\n        journalEntries: this.getJournalEntries(),\n        payments: this.getPayments(),\n        inventoryMovements: this.getInventoryMovements(),\n        employees: this.getEmployees(),\n        exportDate: new Date().toISOString(),\n        version: '1.0.0'\n      };\n\n      return data;\n    } catch (error) {\n      console.error('خطأ في تصدير البيانات:', error);\n      throw new Error('فشل في تصدير البيانات');\n    }\n  }\n\n  // استيراد جميع البيانات\n  importAllData(data) {\n    try {\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.suppliers) {\n        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(data.suppliers));\n      }\n      if (data.accounts) {\n        localStorage.setItem('saudi_accounting_accounts', JSON.stringify(data.accounts));\n      }\n      if (data.journalEntries) {\n        localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(data.journalEntries));\n      }\n      if (data.payments) {\n        localStorage.setItem('saudi_accounting_payments', JSON.stringify(data.payments));\n      }\n      if (data.inventoryMovements) {\n        localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(data.inventoryMovements));\n      }\n      if (data.employees) {\n        localStorage.setItem('saudi_accounting_employees', JSON.stringify(data.employees));\n      }\n\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // === المحاسبة المالية ===\n\n  // جلب جميع الحسابات\n  getAccounts() {\n    try {\n      const accounts = localStorage.getItem('saudi_accounting_accounts');\n      return accounts ? JSON.parse(accounts) : this.getDefaultAccounts();\n    } catch (error) {\n      console.error('خطأ في جلب الحسابات:', error);\n      return this.getDefaultAccounts();\n    }\n  }\n\n  // الحسابات الافتراضية\n  getDefaultAccounts() {\n    return [\n      { id: 1, code: '1001', name: 'النقدية', type: 'asset', balance: 0, description: 'النقدية في الصندوق' },\n      { id: 2, code: '1002', name: 'البنك', type: 'asset', balance: 0, description: 'الحساب الجاري في البنك' },\n      { id: 3, code: '1101', name: 'العملاء', type: 'asset', balance: 0, description: 'مدينو العملاء' },\n      { id: 4, code: '1201', name: 'المخزون', type: 'asset', balance: 0, description: 'مخزون البضائع' },\n      { id: 5, code: '2001', name: 'الموردون', type: 'liability', balance: 0, description: 'دائنو الموردين' },\n      { id: 6, code: '2101', name: 'ضريبة القيمة المضافة', type: 'liability', balance: 0, description: 'ضريبة القيمة المضافة المستحقة' },\n      { id: 7, code: '3001', name: 'رأس المال', type: 'equity', balance: 0, description: 'رأس مال المالك' },\n      { id: 8, code: '4001', name: 'المبيعات', type: 'revenue', balance: 0, description: 'إيرادات المبيعات' },\n      { id: 9, code: '5001', name: 'تكلفة البضاعة المباعة', type: 'expense', balance: 0, description: 'تكلفة البضائع المباعة' },\n      { id: 10, code: '5101', name: 'مصاريف التشغيل', type: 'expense', balance: 0, description: 'المصاريف التشغيلية' }\n    ];\n  }\n\n  // إضافة حساب جديد\n  addAccount(account) {\n    try {\n      const accounts = this.getAccounts();\n      const newAccount = {\n        ...account,\n        id: this.generateAccountId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      accounts.push(newAccount);\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n      return newAccount;\n    } catch (error) {\n      console.error('خطأ في إضافة الحساب:', error);\n      throw new Error('فشل في إضافة الحساب');\n    }\n  }\n\n  // توليد معرف حساب\n  generateAccountId() {\n    const accounts = this.getAccounts();\n    return accounts.length > 0 ? Math.max(...accounts.map(a => a.id)) + 1 : 1;\n  }\n\n  // جلب جميع القيود المحاسبية\n  getJournalEntries() {\n    try {\n      const entries = localStorage.getItem('saudi_accounting_journal_entries');\n      return entries ? JSON.parse(entries) : [];\n    } catch (error) {\n      console.error('خطأ في جلب القيود:', error);\n      return [];\n    }\n  }\n\n  // إضافة قيد محاسبي\n  addJournalEntry(entry) {\n    try {\n      const entries = this.getJournalEntries();\n      const newEntry = {\n        ...entry,\n        id: this.generateJournalEntryId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      entries.push(newEntry);\n      localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(entries));\n\n      // تحديث أرصدة الحسابات\n      this.updateAccountBalances(newEntry);\n\n      return newEntry;\n    } catch (error) {\n      console.error('خطأ في إضافة القيد:', error);\n      throw new Error('فشل في إضافة القيد');\n    }\n  }\n\n  // تحديث أرصدة الحسابات\n  updateAccountBalances(entry) {\n    try {\n      const accounts = this.getAccounts();\n\n      entry.entries.forEach(line => {\n        const accountIndex = accounts.findIndex(a => a.id === parseInt(line.accountId));\n        if (accountIndex !== -1) {\n          // للأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص\n          // للخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص\n          if (accounts[accountIndex].type === 'asset' || accounts[accountIndex].type === 'expense') {\n            accounts[accountIndex].balance += (line.debit - line.credit);\n          } else {\n            accounts[accountIndex].balance += (line.credit - line.debit);\n          }\n          accounts[accountIndex].updatedAt = new Date().toISOString();\n        }\n      });\n\n      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));\n    } catch (error) {\n      console.error('خطأ في تحديث أرصدة الحسابات:', error);\n    }\n  }\n\n  // توليد معرف قيد محاسبي\n  generateJournalEntryId() {\n    const entries = this.getJournalEntries();\n    return entries.length > 0 ? Math.max(...entries.map(e => e.id)) + 1 : 1;\n  }\n\n  incrementInvoiceNumber() {\n    try {\n      const settings = this.getSettings();\n      settings.nextInvoiceNumber += 1;\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(settings));\n    } catch (error) {\n      console.error('خطأ في تحديث رقم الفاتورة:', error);\n    }\n  }\n\n  // === إدارة الإعدادات ===\n  \n  getSettings() {\n    try {\n      const settings = localStorage.getItem('saudi_accounting_settings');\n      return settings ? JSON.parse(settings) : this.getDefaultSettings();\n    } catch (error) {\n      console.error('خطأ في جلب الإعدادات:', error);\n      return this.getDefaultSettings();\n    }\n  }\n\n  updateSettings(updates) {\n    try {\n      const settings = this.getSettings();\n      const newSettings = { ...settings, ...updates, updatedAt: new Date().toISOString() };\n      localStorage.setItem('saudi_accounting_settings', JSON.stringify(newSettings));\n      return newSettings;\n    } catch (error) {\n      console.error('خطأ في تحديث الإعدادات:', error);\n      throw new Error('فشل في تحديث الإعدادات');\n    }\n  }\n\n  // === التقارير والإحصائيات ===\n  \n  // إحصائيات اليوم\n  getTodayStats() {\n    const today = new Date().toDateString();\n    const todayInvoices = this.getInvoicesByDate(today);\n    \n    const totalSales = todayInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = todayInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = todayInvoices.length;\n    \n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // إحصائيات الشهر\n  getMonthStats() {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    \n    const monthInvoices = this.getInvoices().filter(invoice => {\n      const invoiceDate = new Date(invoice.createdAt);\n      return invoiceDate.getMonth() === currentMonth && invoiceDate.getFullYear() === currentYear;\n    });\n    \n    const totalSales = monthInvoices.reduce((sum, invoice) => sum + invoice.total, 0);\n    const totalVAT = monthInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);\n    const totalInvoices = monthInvoices.length;\n    \n    return {\n      totalSales,\n      totalVAT,\n      totalInvoices,\n      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0\n    };\n  }\n\n  // منتجات منخفضة المخزون\n  getLowStockProducts(threshold = 5) {\n    const products = this.getProducts();\n    return products.filter(product => product.stock <= threshold);\n  }\n\n  // أفضل المنتجات مبيعاً\n  getTopSellingProducts(limit = 5) {\n    const invoices = this.getInvoices();\n    const productSales = {};\n    \n    invoices.forEach(invoice => {\n      invoice.items.forEach(item => {\n        if (productSales[item.id]) {\n          productSales[item.id].quantity += item.quantity;\n          productSales[item.id].revenue += item.price * item.quantity;\n        } else {\n          productSales[item.id] = {\n            id: item.id,\n            name: item.name,\n            quantity: item.quantity,\n            revenue: item.price * item.quantity\n          };\n        }\n      });\n    });\n    \n    return Object.values(productSales)\n      .sort((a, b) => b.quantity - a.quantity)\n      .slice(0, limit);\n  }\n\n  // تصدير البيانات\n  exportData() {\n    return {\n      invoices: this.getInvoices(),\n      products: this.getProducts(),\n      customers: this.getCustomers(),\n      suppliers: this.getSuppliers(),\n      accounts: this.getAccounts(),\n      journalEntries: this.getJournalEntries(),\n      settings: this.getSettings(),\n      exportDate: new Date().toISOString()\n    };\n  }\n\n  // استيراد البيانات\n  importData(data) {\n    try {\n      if (data.invoices) {\n        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));\n      }\n      if (data.products) {\n        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));\n      }\n      if (data.customers) {\n        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));\n      }\n      if (data.settings) {\n        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));\n      }\n      return true;\n    } catch (error) {\n      console.error('خطأ في استيراد البيانات:', error);\n      throw new Error('فشل في استيراد البيانات');\n    }\n  }\n\n  // مسح جميع البيانات\n  clearAllData() {\n    try {\n      localStorage.removeItem('saudi_accounting_invoices');\n      localStorage.removeItem('saudi_accounting_products');\n      localStorage.removeItem('saudi_accounting_customers');\n      localStorage.removeItem('saudi_accounting_settings');\n      this.initializeDatabase();\n      return true;\n    } catch (error) {\n      console.error('خطأ في مسح البيانات:', error);\n      throw new Error('فشل في مسح البيانات');\n    }\n  }\n}\n\n// إنشاء مثيل واحد من قاعدة البيانات\nconst database = new LocalDatabase();\n\nexport default database;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B;;EAEA;EACAA,kBAAkBA,CAAA,EAAG;IACnB;IACA,IAAI,CAACC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,EAAE;MACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACvE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,EAAE;MACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC9F;IAEA,IAAI,CAACL,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAAE;MACvDD,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACxE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,EAAE;MACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC9F;IAEA,IAAI,CAACN,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,EAAE;MACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACvE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,sCAAsC,CAAC,EAAE;MACjED,YAAY,CAACE,OAAO,CAAC,sCAAsC,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IAClF;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAAE;MACvDD,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACxE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,EAAE;MACrDD,YAAY,CAACE,OAAO,CAAC,0BAA0B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACtE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC,EAAE;MACxDD,YAAY,CAACE,OAAO,CAAC,6BAA6B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACzE;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,oCAAoC,CAAC,EAAE;MAC/DD,YAAY,CAACE,OAAO,CAAC,oCAAoC,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IAChF;IAEA,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,EAAE;MACtDD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;IACvE;EACF;;EAEA;EACAC,kBAAkBA,CAAA,EAAG;IACnB,OAAO,CACL;MACEE,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,yBAAyB;MACtCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,yBAAyB;MACtCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,yBAAyB;MACtCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,0BAA0B;MACvCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,mBAAmB;MAChCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,qBAAqB;MAClCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,uBAAuB;MACpCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,yBAAyB;MACtCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CACF;EACH;;EAEA;EACAX,kBAAkBA,CAAA,EAAG;IACnB,OAAO;MACLY,WAAW,EAAE,qCAAqC;MAClDC,SAAS,EAAE,iBAAiB;MAC5BC,OAAO,EAAE,kCAAkC;MAC3CC,KAAK,EAAE,kBAAkB;MACzBC,KAAK,EAAE,0BAA0B;MACjCR,OAAO,EAAE,IAAI;MACbS,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE,IAAI;MACvBV,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;EACH;;EAEA;;EAEA;EACAS,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAMC,UAAU,GAAG;QACjB,GAAGH,OAAO;QACVpB,EAAE,EAAE,IAAI,CAACwB,iBAAiB,CAAC,CAAC;QAC5BhB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCe,MAAM,EAAE;MACV,CAAC;MAEDJ,QAAQ,CAACK,IAAI,CAACH,UAAU,CAAC;MACzB9B,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwB,QAAQ,CAAC,CAAC;;MAE3E;MACA,IAAI,CAACM,oBAAoB,CAACP,OAAO,CAACQ,KAAK,CAAC;;MAExC;MACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAE7B,OAAON,UAAU;IACnB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAV,WAAWA,CAAA,EAAG;IACZ,IAAI;MACF,MAAMD,QAAQ,GAAG5B,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAClE,OAAO2B,QAAQ,GAAGzB,IAAI,CAACqC,KAAK,CAACZ,QAAQ,CAAC,GAAG,EAAE;IAC7C,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,EAAE;IACX;EACF;;EAEA;EACAI,cAAcA,CAAClC,EAAE,EAAE;IACjB,MAAMqB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,OAAOD,QAAQ,CAACc,IAAI,CAACf,OAAO,IAAIA,OAAO,CAACpB,EAAE,KAAKA,EAAE,CAAC;EACpD;;EAEA;EACAoC,iBAAiBA,CAACC,IAAI,EAAE;IACtB,MAAMhB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,OAAOD,QAAQ,CAACiB,MAAM,CAAClB,OAAO,IAAI;MAChC,MAAMmB,WAAW,GAAG,IAAI9B,IAAI,CAACW,OAAO,CAACZ,SAAS,CAAC,CAACgC,YAAY,CAAC,CAAC;MAC9D,MAAMC,UAAU,GAAG,IAAIhC,IAAI,CAAC4B,IAAI,CAAC,CAACG,YAAY,CAAC,CAAC;MAChD,OAAOD,WAAW,KAAKE,UAAU;IACnC,CAAC,CAAC;EACJ;;EAEA;;EAEA;EACAC,WAAWA,CAAA,EAAG;IACZ,IAAI;MACF,MAAMC,QAAQ,GAAGlD,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAClE,OAAOiD,QAAQ,GAAG/C,IAAI,CAACqC,KAAK,CAACU,QAAQ,CAAC,GAAG,IAAI,CAAC7C,kBAAkB,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,IAAI,CAAChC,kBAAkB,CAAC,CAAC;IAClC;EACF;;EAEA;EACA8C,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI;MACF,MAAMF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMI,UAAU,GAAG;QACjB,GAAGD,OAAO;QACV7C,EAAE,EAAE,IAAI,CAAC+C,iBAAiB,CAAC,CAAC;QAC5BvC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDiC,QAAQ,CAACjB,IAAI,CAACoB,UAAU,CAAC;MACzBrD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC;MAC3E,OAAOG,UAAU;IACnB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAgB,aAAaA,CAAChD,EAAE,EAAEiD,OAAO,EAAE;IACzB,IAAI;MACF,MAAMN,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMQ,KAAK,GAAGP,QAAQ,CAACQ,SAAS,CAACN,OAAO,IAAIA,OAAO,CAAC7C,EAAE,KAAKA,EAAE,CAAC;MAE9D,IAAIkD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBP,QAAQ,CAACO,KAAK,CAAC,GAAG;UAAE,GAAGP,QAAQ,CAACO,KAAK,CAAC;UAAE,GAAGD,OAAO;UAAEG,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QAAE,CAAC;QACzFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC;QAC3E,OAAOA,QAAQ,CAACO,KAAK,CAAC;MACxB;MAEA,MAAM,IAAIlB,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAqB,aAAaA,CAACrD,EAAE,EAAE;IAChB,IAAI;MACF,MAAM2C,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMY,gBAAgB,GAAGX,QAAQ,CAACL,MAAM,CAACO,OAAO,IAAIA,OAAO,CAAC7C,EAAE,KAAKA,EAAE,CAAC;MACtEP,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACyD,gBAAgB,CAAC,CAAC;MACnF,OAAO,IAAI;IACb,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACtC;EACF;;EAEA;EACAL,oBAAoBA,CAACC,KAAK,EAAE;IAC1B,IAAI;MACF,MAAMe,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MAEnCd,KAAK,CAAC2B,OAAO,CAACC,IAAI,IAAI;QACpB,MAAMC,YAAY,GAAGd,QAAQ,CAACQ,SAAS,CAACN,OAAO,IAAIA,OAAO,CAAC7C,EAAE,KAAKwD,IAAI,CAACxD,EAAE,CAAC;QAC1E,IAAIyD,YAAY,KAAK,CAAC,CAAC,EAAE;UACvBd,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,IAAIqD,IAAI,CAACE,QAAQ;UAC7C,IAAIf,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,GAAG,CAAC,EAAE;YACpCwC,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,GAAG,CAAC;UAClC;QACF;MACF,CAAC,CAAC;MAEFV,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF;;EAEA;;EAEA;EACA6B,YAAYA,CAAA,EAAG;IACb,IAAI;MACF,MAAMC,SAAS,GAAGnE,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;MACpE,OAAOkE,SAAS,GAAGhE,IAAI,CAACqC,KAAK,CAAC2B,SAAS,CAAC,GAAG,EAAE;IAC/C,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO,EAAE;IACX;EACF;;EAEA;EACA+B,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI;MACF,MAAMF,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMI,WAAW,GAAG;QAClB,GAAGD,QAAQ;QACX9D,EAAE,EAAE,IAAI,CAACgE,kBAAkB,CAAC,CAAC;QAC7BxD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDkD,SAAS,CAAClC,IAAI,CAACqC,WAAW,CAAC;MAC3BtE,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC+D,SAAS,CAAC,CAAC;MAC7E,OAAOG,WAAW;IACpB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;;EAEAR,iBAAiBA,CAAA,EAAG;IAClB,MAAMyC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,OAAO,GAAGD,QAAQ,CAAChD,aAAa,IAAIgD,QAAQ,CAAC/C,iBAAiB,EAAE;EAClE;EAEA6B,iBAAiBA,CAAA,EAAG;IAClB,MAAMJ,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;IACnC,OAAOC,QAAQ,CAACwB,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG1B,QAAQ,CAAC2B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC3E;EAEAgE,kBAAkBA,CAAA,EAAG;IACnB,MAAMJ,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACrC,OAAOC,SAAS,CAACO,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGT,SAAS,CAACU,GAAG,CAACE,CAAC,IAAIA,CAAC,CAACxE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC7E;;EAEA;;EAEA;EACAyE,YAAYA,CAAA,EAAG;IACb,IAAI;MACF,MAAMC,SAAS,GAAGjF,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;MACpE,OAAOgF,SAAS,GAAG9E,IAAI,CAACqC,KAAK,CAACyC,SAAS,CAAC,GAAG,EAAE;IAC/C,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,EAAE;IACX;EACF;;EAEA;EACA6C,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI;MACF,MAAMF,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMI,WAAW,GAAG;QAClB,GAAGD,QAAQ;QACX5E,EAAE,EAAE,IAAI,CAAC8E,kBAAkB,CAAC,CAAC;QAC7BtE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDgE,SAAS,CAAChD,IAAI,CAACmD,WAAW,CAAC;MAC3BpF,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC6E,SAAS,CAAC,CAAC;MAC7E,OAAOG,WAAW;IACpB,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACA+C,cAAcA,CAAC/E,EAAE,EAAEiD,OAAO,EAAE;IAC1B,IAAI;MACF,MAAMyB,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMvB,KAAK,GAAGwB,SAAS,CAACvB,SAAS,CAACyB,QAAQ,IAAIA,QAAQ,CAAC5E,EAAE,KAAKA,EAAE,CAAC;MAEjE,IAAIkD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBwB,SAAS,CAACxB,KAAK,CAAC,GAAG;UACjB,GAAGwB,SAAS,CAACxB,KAAK,CAAC;UACnB,GAAGD,OAAO;UACVG,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QACDjB,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC6E,SAAS,CAAC,CAAC;QAC7E,OAAOA,SAAS,CAACxB,KAAK,CAAC;MACzB;MAEA,MAAM,IAAIlB,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAgD,cAAcA,CAAChF,EAAE,EAAE;IACjB,IAAI;MACF,MAAM0E,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMQ,iBAAiB,GAAGP,SAAS,CAACpC,MAAM,CAACsC,QAAQ,IAAIA,QAAQ,CAAC5E,EAAE,KAAKA,EAAE,CAAC;MAC1EP,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACoF,iBAAiB,CAAC,CAAC;MACrF,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACtC;EACF;;EAEA;EACA8C,kBAAkBA,CAAA,EAAG;IACnB,MAAMJ,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACrC,OAAOC,SAAS,CAACP,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGK,SAAS,CAACJ,GAAG,CAACY,CAAC,IAAIA,CAAC,CAAClF,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC7E;;EAEA;;EAEA;EACAmF,WAAWA,CAAA,EAAG;IACZ,IAAI;MACF,MAAMC,QAAQ,GAAG3F,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAClE,OAAO0F,QAAQ,GAAGxF,IAAI,CAACqC,KAAK,CAACmD,QAAQ,CAAC,GAAG,EAAE;IAC7C,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO,EAAE;IACX;EACF;;EAEA;EACAuD,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI;MACF,MAAMF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMI,UAAU,GAAG;QACjB,GAAGD,OAAO;QACVtF,EAAE,EAAE,IAAI,CAACwF,iBAAiB,CAAC,CAAC;QAC5BhF,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAED0E,QAAQ,CAAC1D,IAAI,CAAC6D,UAAU,CAAC;MACzB9F,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACuF,QAAQ,CAAC,CAAC;;MAE3E;MACA,IAAIE,OAAO,CAAC7D,MAAM,KAAK,WAAW,EAAE;QAClC,IAAI,CAACgE,0BAA0B,CAACH,OAAO,CAACI,SAAS,EAAEJ,OAAO,CAACK,MAAM,CAAC;MACpE;MAEA,OAAOJ,UAAU;IACnB,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACA4D,aAAaA,CAAC5F,EAAE,EAAEiD,OAAO,EAAE;IACzB,IAAI;MACF,MAAMmC,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMU,YAAY,GAAGT,QAAQ,CAACjC,SAAS,CAACoB,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKA,EAAE,CAAC;MAEzD,IAAI6F,YAAY,KAAK,CAAC,CAAC,EAAE;QACvBT,QAAQ,CAACS,YAAY,CAAC,GAAG;UACvB,GAAGT,QAAQ,CAACS,YAAY,CAAC;UACzB,GAAG5C,OAAO;UACVG,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACuF,QAAQ,CAAC,CAAC;QAC3E,OAAOA,QAAQ,CAACS,YAAY,CAAC;MAC/B;MAEA,MAAM,IAAI7D,KAAK,CAAC,mBAAmB,CAAC;IACtC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACA8D,aAAaA,CAAC9F,EAAE,EAAE;IAChB,IAAI;MACF,MAAMoF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMY,gBAAgB,GAAGX,QAAQ,CAAC9C,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKA,EAAE,CAAC;MAC1DP,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACkG,gBAAgB,CAAC,CAAC;MACnF,OAAO,IAAI;IACb,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACtC;EACF;;EAEA;EACAwD,iBAAiBA,CAAA,EAAG;IAClB,MAAMJ,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;IACnC,OAAOC,QAAQ,CAACjB,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGe,QAAQ,CAACd,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC3E;;EAEA;EACAyF,0BAA0BA,CAACC,SAAS,EAAEM,UAAU,EAAE;IAChD,IAAI;MACF,MAAM3E,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAM2E,YAAY,GAAG5E,QAAQ,CAAC8B,SAAS,CAAC+C,GAAG,IAAIA,GAAG,CAAClG,EAAE,KAAK0F,SAAS,CAAC;MAEpE,IAAIO,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB,MAAM7E,OAAO,GAAGC,QAAQ,CAAC4E,YAAY,CAAC;QACtC,MAAME,SAAS,GAAG,CAAC/E,OAAO,CAAC4E,UAAU,IAAI,CAAC,IAAIA,UAAU;QAExD,IAAII,aAAa,GAAG,QAAQ;QAC5B,IAAID,SAAS,IAAI/E,OAAO,CAACiF,KAAK,EAAE;UAC9BD,aAAa,GAAG,MAAM;QACxB,CAAC,MAAM,IAAID,SAAS,GAAG,CAAC,EAAE;UACxBC,aAAa,GAAG,SAAS;QAC3B;QAEA/E,QAAQ,CAAC4E,YAAY,CAAC,GAAG;UACvB,GAAG7E,OAAO;UACV4E,UAAU,EAAEG,SAAS;UACrBC,aAAa,EAAEA,aAAa;UAC5BhD,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwB,QAAQ,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF;;EAEA;EACAwE,mBAAmBA,CAACC,UAAU,EAAE;IAC9B,IAAI;MACF,MAAMnB,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,OAAOC,QAAQ,CAAC9C,MAAM,CAACgD,OAAO,IAAIA,OAAO,CAACiB,UAAU,KAAKA,UAAU,CAAC;IACtE,CAAC,CAAC,OAAOzE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,EAAE;IACX;EACF;;EAEA;EACA0E,kBAAkBA,CAACd,SAAS,EAAE;IAC5B,IAAI;MACF,MAAMN,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,OAAOC,QAAQ,CAAC9C,MAAM,CAACgD,OAAO,IAAIA,OAAO,CAACI,SAAS,KAAKA,SAAS,CAAC;IACpE,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF;;EAEA;;EAEA;EACA2E,qBAAqBA,CAAA,EAAG;IACtB,IAAI;MACF,MAAMC,SAAS,GAAGjH,YAAY,CAACC,OAAO,CAAC,sCAAsC,CAAC;MAC9E,OAAOgH,SAAS,GAAG9G,IAAI,CAACqC,KAAK,CAACyE,SAAS,CAAC,GAAG,EAAE;IAC/C,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,EAAE;IACX;EACF;;EAEA;EACA6E,oBAAoBA,CAACC,QAAQ,EAAE;IAC7B,IAAI;MACF,MAAMF,SAAS,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;MAC9C,MAAMI,WAAW,GAAG;QAClB,GAAGD,QAAQ;QACX5G,EAAE,EAAE,IAAI,CAAC8G,kBAAkB,CAAC,CAAC;QAC7BtG,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDgG,SAAS,CAAChF,IAAI,CAACmF,WAAW,CAAC;MAC3BpH,YAAY,CAACE,OAAO,CAAC,sCAAsC,EAAEC,IAAI,CAACC,SAAS,CAAC6G,SAAS,CAAC,CAAC;;MAEvF;MACA,IAAI,CAACK,kBAAkB,CAACH,QAAQ,CAACI,SAAS,EAAEJ,QAAQ,CAAClD,QAAQ,EAAEkD,QAAQ,CAACK,IAAI,CAAC;MAE7E,OAAOJ,WAAW;IACpB,CAAC,CAAC,OAAO/E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAM,IAAIE,KAAK,CAAC,2BAA2B,CAAC;IAC9C;EACF;;EAEA;EACA+E,kBAAkBA,CAACC,SAAS,EAAEtD,QAAQ,EAAEwD,YAAY,EAAE;IACpD,IAAI;MACF,MAAMvE,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMe,YAAY,GAAGd,QAAQ,CAACQ,SAAS,CAACoB,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKgH,SAAS,CAAC;MAEhE,IAAIvD,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB,MAAM0D,YAAY,GAAGxE,QAAQ,CAACc,YAAY,CAAC,CAACtD,KAAK,IAAI,CAAC;QACtD,IAAIiH,QAAQ,GAAGD,YAAY;QAE3B,IAAID,YAAY,KAAK,IAAI,EAAE;UACzBE,QAAQ,GAAGD,YAAY,GAAGzD,QAAQ;QACpC,CAAC,MAAM,IAAIwD,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,SAAS,EAAE;UAC7FE,QAAQ,GAAGhD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE8C,YAAY,GAAGzD,QAAQ,CAAC;QACjD,CAAC,MAAM,IAAIwD,YAAY,KAAK,YAAY,EAAE;UACxCE,QAAQ,GAAG1D,QAAQ,CAAC,CAAC;QACvB;QAEAf,QAAQ,CAACc,YAAY,CAAC,GAAG;UACvB,GAAGd,QAAQ,CAACc,YAAY,CAAC;UACzBtD,KAAK,EAAEiH,QAAQ;UACfhE,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAAC8C,QAAQ,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF;;EAEA;EACAgF,kBAAkBA,CAAA,EAAG;IACnB,MAAMJ,SAAS,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;IAC9C,OAAOC,SAAS,CAACvC,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGqC,SAAS,CAACpC,GAAG,CAAC+C,CAAC,IAAIA,CAAC,CAACrH,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC7E;;EAEA;EACAsH,mBAAmBA,CAACN,SAAS,EAAE;IAC7B,IAAI;MACF,MAAMN,SAAS,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;MAC9C,OAAOC,SAAS,CAACpE,MAAM,CAACsE,QAAQ,IAAIA,QAAQ,CAACI,SAAS,KAAKA,SAAS,CAAC;IACvE,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX;EACF;;EAEA;EACAyF,gCAAgCA,CAACC,SAAS,EAAEC,OAAO,EAAE;IACnD,IAAI;MACF,MAAMf,SAAS,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;MAC9C,OAAOC,SAAS,CAACpE,MAAM,CAACsE,QAAQ,IAAI;QAClC,MAAMc,YAAY,GAAG,IAAIjH,IAAI,CAACmG,QAAQ,CAACvE,IAAI,CAAC;QAC5C,OAAOqF,YAAY,IAAI,IAAIjH,IAAI,CAAC+G,SAAS,CAAC,IAAIE,YAAY,IAAI,IAAIjH,IAAI,CAACgH,OAAO,CAAC;MACjF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO3F,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,EAAE;IACX;EACF;;EAEA;EACA6F,4BAA4BA,CAAA,EAAG;IAC7B,IAAI;MACF,MAAMhF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,OAAOC,QAAQ,CAACiF,MAAM,CAAC,CAACvB,KAAK,EAAExD,OAAO,KAAK;QACzC,OAAOwD,KAAK,GAAIxD,OAAO,CAAC1C,KAAK,GAAG0C,OAAO,CAAC3C,KAAM;MAChD,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,CAAC;IACV;EACF;;EAEA;EACA+F,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMlF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,OAAOC,QAAQ,CAACL,MAAM,CAACO,OAAO,IAAIA,OAAO,CAAC1C,KAAK,KAAK0C,OAAO,CAACiF,QAAQ,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOhG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,EAAE;IACX;EACF;;EAEA;EACAiG,qBAAqBA,CAAA,EAAG;IACtB,IAAI;MACF,MAAMpF,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;MACnC,OAAOC,QAAQ,CAACL,MAAM,CAACO,OAAO,IAAIA,OAAO,CAAC1C,KAAK,KAAK,CAAC,CAAC;IACxD,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,EAAE;IACX;EACF;;EAEA;;EAEA;EACAkG,YAAYA,CAAA,EAAG;IACb,IAAI;MACF,MAAMC,SAAS,GAAGxI,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;MACpE,OAAOuI,SAAS,GAAGrI,IAAI,CAACqC,KAAK,CAACgG,SAAS,CAAC,GAAG,EAAE;IAC/C,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,EAAE;IACX;EACF;;EAEA;EACAoG,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI;MACF,MAAMF,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMI,WAAW,GAAG;QAClB,GAAGD,QAAQ;QACXnI,EAAE,EAAE,IAAI,CAACqI,kBAAkB,CAAC,CAAC;QAC7B7H,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDuH,SAAS,CAACvG,IAAI,CAAC0G,WAAW,CAAC;MAC3B3I,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACoI,SAAS,CAAC,CAAC;MAE7E,OAAOG,WAAW;IACpB,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAsG,cAAcA,CAACtI,EAAE,EAAEiD,OAAO,EAAE;IAC1B,IAAI;MACF,MAAMgF,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMO,aAAa,GAAGN,SAAS,CAAC9E,SAAS,CAACqF,GAAG,IAAIA,GAAG,CAACxI,EAAE,KAAKA,EAAE,CAAC;MAE/D,IAAIuI,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBN,SAAS,CAACM,aAAa,CAAC,GAAG;UACzB,GAAGN,SAAS,CAACM,aAAa,CAAC;UAC3B,GAAGtF,OAAO;UACVG,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QAEDjB,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACoI,SAAS,CAAC,CAAC;QAC7E,OAAOA,SAAS,CAACM,aAAa,CAAC;MACjC;MAEA,MAAM,IAAIvG,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAyG,cAAcA,CAACzI,EAAE,EAAE;IACjB,IAAI;MACF,MAAMiI,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,MAAMU,iBAAiB,GAAGT,SAAS,CAAC3F,MAAM,CAACkG,GAAG,IAAIA,GAAG,CAACxI,EAAE,KAAKA,EAAE,CAAC;MAChEP,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAAC6I,iBAAiB,CAAC,CAAC;MACrF,OAAO,IAAI;IACb,CAAC,CAAC,OAAO5G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACtC;EACF;;EAEA;EACAqG,kBAAkBA,CAAA,EAAG;IACnB,MAAMJ,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACrC,OAAOC,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG4D,SAAS,CAAC3D,GAAG,CAACkE,GAAG,IAAIA,GAAG,CAACxI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACjF;;EAEA;EACA2I,kBAAkBA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMV,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,OAAOC,SAAS,CAAC3F,MAAM,CAAC6F,QAAQ,IAAIA,QAAQ,CAAC1G,MAAM,KAAK,QAAQ,CAAC;IACnE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF;;EAEA;EACA8G,wBAAwBA,CAACC,UAAU,EAAE;IACnC,IAAI;MACF,MAAMZ,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACrC,OAAOC,SAAS,CAAC3F,MAAM,CAAC6F,QAAQ,IAAIA,QAAQ,CAACU,UAAU,KAAKA,UAAU,CAAC;IACzE,CAAC,CAAC,OAAO/G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX;EACF;;EAEA;EACAgH,sBAAsBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMC,eAAe,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC;MACjD,OAAOI,eAAe,CAACnB,MAAM,CAAC,CAACvB,KAAK,EAAE8B,QAAQ,KAAK;QACjD,OAAO9B,KAAK,IAAI8B,QAAQ,CAACa,MAAM,IAAI,CAAC,CAAC;MACvC,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,CAAC;IACV;EACF;;EAEA;;EAEA;EACA/B,kBAAkBA,CAAA,EAAG;IACnB,OAAO;MACL;MACAY,WAAW,EAAE,wBAAwB;MACrCC,SAAS,EAAE,iBAAiB;MAC5BC,OAAO,EAAE,kCAAkC;MAC3CC,KAAK,EAAE,kBAAkB;MACzBC,KAAK,EAAE,kBAAkB;MACzBkI,OAAO,EAAE,iBAAiB;MAE1B;MACAhI,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE,CAAC;MACpBX,OAAO,EAAE,EAAE;MACXS,QAAQ,EAAE,KAAK;MACfkI,mBAAmB,EAAE,qCAAqC;MAC1DC,gBAAgB,EAAE,IAAI;MAEtB;MACAC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,aAAa;MACvBC,UAAU,EAAE,YAAY;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,KAAK;MAEvB;MACAC,eAAe,EAAE,OAAO;MACxBC,eAAe,EAAE,QAAQ;MACzBC,eAAe,EAAE,EAAE;MACnBC,iBAAiB,EAAE,CAAC;MAEpBrJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;EACH;;EAEA;EACAoJ,cAAcA,CAACC,WAAW,EAAE;IAC1B,IAAI;MACF,MAAMC,eAAe,GAAG,IAAI,CAAC9F,WAAW,CAAC,CAAC;MAC1C,MAAM+F,eAAe,GAAG;QACtB,GAAGD,eAAe;QAClB,GAAGD,WAAW;QACd3G,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAEDjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACoK,eAAe,CAAC,CAAC;MAClF,OAAOA,eAAe;IACxB,CAAC,CAAC,OAAOnI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACF;;EAEA;EACAkI,aAAaA,CAAA,EAAG;IACd,IAAI;MACF,MAAMC,eAAe,GAAG,IAAI,CAACpK,kBAAkB,CAAC,CAAC;MACjDN,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACsK,eAAe,CAAC,CAAC;MAClF,OAAOA,eAAe;IACxB,CAAC,CAAC,OAAOrI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAIE,KAAK,CAAC,8BAA8B,CAAC;IACjD;EACF;;EAEA;EACAoI,aAAaA,CAAA,EAAG;IACd,IAAI;MACF,MAAMC,IAAI,GAAG;QACXpG,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;QAC5B7C,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;QAC5BqB,QAAQ,EAAE,IAAI,CAACD,WAAW,CAAC,CAAC;QAC5BkB,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC;QAC9Be,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC;QAC9B6F,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;QAC5BC,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACxCrF,QAAQ,EAAE,IAAI,CAACD,WAAW,CAAC,CAAC;QAC5BuF,kBAAkB,EAAE,IAAI,CAACjE,qBAAqB,CAAC,CAAC;QAChDwB,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC;QAC9B2C,UAAU,EAAE,IAAIlK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCkK,OAAO,EAAE;MACX,CAAC;MAED,OAAOP,IAAI;IACb,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAIE,KAAK,CAAC,uBAAuB,CAAC;IAC1C;EACF;;EAEA;EACA6I,aAAaA,CAACR,IAAI,EAAE;IAClB,IAAI;MACF,IAAIA,IAAI,CAACpG,QAAQ,EAAE;QACjBxE,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpG,QAAQ,CAAC,CAAC;MAClF;MACA,IAAIoG,IAAI,CAAChJ,QAAQ,EAAE;QACjB5B,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAChJ,QAAQ,CAAC,CAAC;MAClF;MACA,IAAIgJ,IAAI,CAAC1H,QAAQ,EAAE;QACjBlD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC1H,QAAQ,CAAC,CAAC;MAClF;MACA,IAAI0H,IAAI,CAACzG,SAAS,EAAE;QAClBnE,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACzG,SAAS,CAAC,CAAC;MACpF;MACA,IAAIyG,IAAI,CAAC3F,SAAS,EAAE;QAClBjF,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC3F,SAAS,CAAC,CAAC;MACpF;MACA,IAAI2F,IAAI,CAACC,QAAQ,EAAE;QACjB7K,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACC,QAAQ,CAAC,CAAC;MAClF;MACA,IAAID,IAAI,CAACG,cAAc,EAAE;QACvB/K,YAAY,CAACE,OAAO,CAAC,kCAAkC,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACG,cAAc,CAAC,CAAC;MAC/F;MACA,IAAIH,IAAI,CAACjF,QAAQ,EAAE;QACjB3F,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACjF,QAAQ,CAAC,CAAC;MAClF;MACA,IAAIiF,IAAI,CAACK,kBAAkB,EAAE;QAC3BjL,YAAY,CAACE,OAAO,CAAC,sCAAsC,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACK,kBAAkB,CAAC,CAAC;MACvG;MACA,IAAIL,IAAI,CAACpC,SAAS,EAAE;QAClBxI,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpC,SAAS,CAAC,CAAC;MACpF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;IAC5C;EACF;;EAEA;;EAEA;EACAuI,WAAWA,CAAA,EAAG;IACZ,IAAI;MACF,MAAMD,QAAQ,GAAG7K,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAClE,OAAO4K,QAAQ,GAAG1K,IAAI,CAACqC,KAAK,CAACqI,QAAQ,CAAC,GAAG,IAAI,CAACQ,kBAAkB,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOhJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,IAAI,CAACgJ,kBAAkB,CAAC,CAAC;IAClC;EACF;;EAEA;EACAA,kBAAkBA,CAAA,EAAG;IACnB,OAAO,CACL;MAAE9K,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,SAAS;MAAEgH,IAAI,EAAE,OAAO;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAqB,CAAC,EACtG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,OAAO;MAAEgH,IAAI,EAAE,OAAO;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAyB,CAAC,EACxG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,SAAS;MAAEgH,IAAI,EAAE,OAAO;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAgB,CAAC,EACjG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,SAAS;MAAEgH,IAAI,EAAE,OAAO;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAgB,CAAC,EACjG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,UAAU;MAAEgH,IAAI,EAAE,WAAW;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAiB,CAAC,EACvG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,sBAAsB;MAAEgH,IAAI,EAAE,WAAW;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAgC,CAAC,EAClI;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,WAAW;MAAEgH,IAAI,EAAE,QAAQ;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAiB,CAAC,EACrG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,UAAU;MAAEgH,IAAI,EAAE,SAAS;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAmB,CAAC,EACvG;MAAEN,EAAE,EAAE,CAAC;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,uBAAuB;MAAEgH,IAAI,EAAE,SAAS;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAwB,CAAC,EACzH;MAAEN,EAAE,EAAE,EAAE;MAAE+K,IAAI,EAAE,MAAM;MAAE9K,IAAI,EAAE,gBAAgB;MAAEgH,IAAI,EAAE,SAAS;MAAE+D,OAAO,EAAE,CAAC;MAAE1K,WAAW,EAAE;IAAqB,CAAC,CACjH;EACH;;EAEA;EACA2K,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI;MACF,MAAMZ,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAMY,UAAU,GAAG;QACjB,GAAGD,OAAO;QACVlL,EAAE,EAAE,IAAI,CAACoL,iBAAiB,CAAC,CAAC;QAC5B5K,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAED4J,QAAQ,CAAC5I,IAAI,CAACyJ,UAAU,CAAC;MACzB1L,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACyK,QAAQ,CAAC,CAAC;MAC3E,OAAOa,UAAU;IACnB,CAAC,CAAC,OAAOrJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;;EAEA;EACAoJ,iBAAiBA,CAAA,EAAG;IAClB,MAAMd,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,OAAOD,QAAQ,CAACnG,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGiG,QAAQ,CAAChG,GAAG,CAAC+G,CAAC,IAAIA,CAAC,CAACrL,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAC3E;;EAEA;EACAyK,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMa,OAAO,GAAG7L,YAAY,CAACC,OAAO,CAAC,kCAAkC,CAAC;MACxE,OAAO4L,OAAO,GAAG1L,IAAI,CAACqC,KAAK,CAACqJ,OAAO,CAAC,GAAG,EAAE;IAC3C,CAAC,CAAC,OAAOxJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO,EAAE;IACX;EACF;;EAEA;EACAyJ,eAAeA,CAACC,KAAK,EAAE;IACrB,IAAI;MACF,MAAMF,OAAO,GAAG,IAAI,CAACb,iBAAiB,CAAC,CAAC;MACxC,MAAMgB,QAAQ,GAAG;QACf,GAAGD,KAAK;QACRxL,EAAE,EAAE,IAAI,CAAC0L,sBAAsB,CAAC,CAAC;QACjClL,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnC0C,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MAED4K,OAAO,CAAC5J,IAAI,CAAC+J,QAAQ,CAAC;MACtBhM,YAAY,CAACE,OAAO,CAAC,kCAAkC,EAAEC,IAAI,CAACC,SAAS,CAACyL,OAAO,CAAC,CAAC;;MAEjF;MACA,IAAI,CAACK,qBAAqB,CAACF,QAAQ,CAAC;MAEpC,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAO3J,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAM,IAAIE,KAAK,CAAC,oBAAoB,CAAC;IACvC;EACF;;EAEA;EACA2J,qBAAqBA,CAACH,KAAK,EAAE;IAC3B,IAAI;MACF,MAAMlB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MAEnCiB,KAAK,CAACF,OAAO,CAAC/H,OAAO,CAACqI,IAAI,IAAI;QAC5B,MAAMC,YAAY,GAAGvB,QAAQ,CAACnH,SAAS,CAACkI,CAAC,IAAIA,CAAC,CAACrL,EAAE,KAAK8L,QAAQ,CAACF,IAAI,CAACG,SAAS,CAAC,CAAC;QAC/E,IAAIF,YAAY,KAAK,CAAC,CAAC,EAAE;UACvB;UACA;UACA,IAAIvB,QAAQ,CAACuB,YAAY,CAAC,CAAC5E,IAAI,KAAK,OAAO,IAAIqD,QAAQ,CAACuB,YAAY,CAAC,CAAC5E,IAAI,KAAK,SAAS,EAAE;YACxFqD,QAAQ,CAACuB,YAAY,CAAC,CAACb,OAAO,IAAKY,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACK,MAAO;UAC9D,CAAC,MAAM;YACL3B,QAAQ,CAACuB,YAAY,CAAC,CAACb,OAAO,IAAKY,IAAI,CAACK,MAAM,GAAGL,IAAI,CAACI,KAAM;UAC9D;UACA1B,QAAQ,CAACuB,YAAY,CAAC,CAACzI,SAAS,GAAG,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7D;MACF,CAAC,CAAC;MAEFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACyK,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOxI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF;;EAEA;EACA4J,sBAAsBA,CAAA,EAAG;IACvB,MAAMJ,OAAO,GAAG,IAAI,CAACb,iBAAiB,CAAC,CAAC;IACxC,OAAOa,OAAO,CAACnH,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGiH,OAAO,CAAChH,GAAG,CAAC4H,CAAC,IAAIA,CAAC,CAAClM,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACzE;EAEA6B,sBAAsBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMoC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnCD,QAAQ,CAAC/C,iBAAiB,IAAI,CAAC;MAC/BzB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACoE,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF;;EAEA;;EAEAoC,WAAWA,CAAA,EAAG;IACZ,IAAI;MACF,MAAMD,QAAQ,GAAGxE,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MAClE,OAAOuE,QAAQ,GAAGrE,IAAI,CAACqC,KAAK,CAACgC,QAAQ,CAAC,GAAG,IAAI,CAAClE,kBAAkB,CAAC,CAAC;IACpE,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO,IAAI,CAAC/B,kBAAkB,CAAC,CAAC;IAClC;EACF;EAEA+J,cAAcA,CAAC7G,OAAO,EAAE;IACtB,IAAI;MACF,MAAMgB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAM6F,WAAW,GAAG;QAAE,GAAG9F,QAAQ;QAAE,GAAGhB,OAAO;QAAEG,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC;MACpFjB,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACkK,WAAW,CAAC,CAAC;MAC9E,OAAOA,WAAW;IACpB,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACF;;EAEA;;EAEA;EACAmK,aAAaA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG,IAAI3L,IAAI,CAAC,CAAC,CAAC+B,YAAY,CAAC,CAAC;IACvC,MAAM6J,aAAa,GAAG,IAAI,CAACjK,iBAAiB,CAACgK,KAAK,CAAC;IAEnD,MAAME,UAAU,GAAGD,aAAa,CAACzE,MAAM,CAAC,CAAC2E,GAAG,EAAEnL,OAAO,KAAKmL,GAAG,GAAGnL,OAAO,CAACiF,KAAK,EAAE,CAAC,CAAC;IACjF,MAAMmG,QAAQ,GAAGH,aAAa,CAACzE,MAAM,CAAC,CAAC2E,GAAG,EAAEnL,OAAO,KAAKmL,GAAG,GAAGnL,OAAO,CAACqL,GAAG,EAAE,CAAC,CAAC;IAC7E,MAAMC,aAAa,GAAGL,aAAa,CAAClI,MAAM;IAE1C,OAAO;MACLmI,UAAU;MACVE,QAAQ;MACRE,aAAa;MACbC,cAAc,EAAED,aAAa,GAAG,CAAC,GAAGJ,UAAU,GAAGI,aAAa,GAAG;IACnE,CAAC;EACH;;EAEA;EACAE,aAAaA,CAAA,EAAG;IACd,MAAMC,YAAY,GAAG,IAAIpM,IAAI,CAAC,CAAC,CAACqM,QAAQ,CAAC,CAAC;IAC1C,MAAMC,WAAW,GAAG,IAAItM,IAAI,CAAC,CAAC,CAACuM,WAAW,CAAC,CAAC;IAE5C,MAAMC,aAAa,GAAG,IAAI,CAAC3L,WAAW,CAAC,CAAC,CAACgB,MAAM,CAAClB,OAAO,IAAI;MACzD,MAAMmB,WAAW,GAAG,IAAI9B,IAAI,CAACW,OAAO,CAACZ,SAAS,CAAC;MAC/C,OAAO+B,WAAW,CAACuK,QAAQ,CAAC,CAAC,KAAKD,YAAY,IAAItK,WAAW,CAACyK,WAAW,CAAC,CAAC,KAAKD,WAAW;IAC7F,CAAC,CAAC;IAEF,MAAMT,UAAU,GAAGW,aAAa,CAACrF,MAAM,CAAC,CAAC2E,GAAG,EAAEnL,OAAO,KAAKmL,GAAG,GAAGnL,OAAO,CAACiF,KAAK,EAAE,CAAC,CAAC;IACjF,MAAMmG,QAAQ,GAAGS,aAAa,CAACrF,MAAM,CAAC,CAAC2E,GAAG,EAAEnL,OAAO,KAAKmL,GAAG,GAAGnL,OAAO,CAACqL,GAAG,EAAE,CAAC,CAAC;IAC7E,MAAMC,aAAa,GAAGO,aAAa,CAAC9I,MAAM;IAE1C,OAAO;MACLmI,UAAU;MACVE,QAAQ;MACRE,aAAa;MACbC,cAAc,EAAED,aAAa,GAAG,CAAC,GAAGJ,UAAU,GAAGI,aAAa,GAAG;IACnE,CAAC;EACH;;EAEA;EACA7E,mBAAmBA,CAACqF,SAAS,GAAG,CAAC,EAAE;IACjC,MAAMvK,QAAQ,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC;IACnC,OAAOC,QAAQ,CAACL,MAAM,CAACO,OAAO,IAAIA,OAAO,CAAC1C,KAAK,IAAI+M,SAAS,CAAC;EAC/D;;EAEA;EACAC,qBAAqBA,CAACC,KAAK,GAAG,CAAC,EAAE;IAC/B,MAAM/L,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,MAAM+L,YAAY,GAAG,CAAC,CAAC;IAEvBhM,QAAQ,CAACkC,OAAO,CAACnC,OAAO,IAAI;MAC1BA,OAAO,CAACQ,KAAK,CAAC2B,OAAO,CAACC,IAAI,IAAI;QAC5B,IAAI6J,YAAY,CAAC7J,IAAI,CAACxD,EAAE,CAAC,EAAE;UACzBqN,YAAY,CAAC7J,IAAI,CAACxD,EAAE,CAAC,CAAC0D,QAAQ,IAAIF,IAAI,CAACE,QAAQ;UAC/C2J,YAAY,CAAC7J,IAAI,CAACxD,EAAE,CAAC,CAACsN,OAAO,IAAI9J,IAAI,CAACtD,KAAK,GAAGsD,IAAI,CAACE,QAAQ;QAC7D,CAAC,MAAM;UACL2J,YAAY,CAAC7J,IAAI,CAACxD,EAAE,CAAC,GAAG;YACtBA,EAAE,EAAEwD,IAAI,CAACxD,EAAE;YACXC,IAAI,EAAEuD,IAAI,CAACvD,IAAI;YACfyD,QAAQ,EAAEF,IAAI,CAACE,QAAQ;YACvB4J,OAAO,EAAE9J,IAAI,CAACtD,KAAK,GAAGsD,IAAI,CAACE;UAC7B,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO6J,MAAM,CAACC,MAAM,CAACH,YAAY,CAAC,CAC/BI,IAAI,CAAC,CAACpC,CAAC,EAAEqC,CAAC,KAAKA,CAAC,CAAChK,QAAQ,GAAG2H,CAAC,CAAC3H,QAAQ,CAAC,CACvCiK,KAAK,CAAC,CAAC,EAAEP,KAAK,CAAC;EACpB;;EAEA;EACAQ,UAAUA,CAAA,EAAG;IACX,OAAO;MACLvM,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5BqB,QAAQ,EAAE,IAAI,CAACD,WAAW,CAAC,CAAC;MAC5BkB,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC;MAC9Be,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC,CAAC;MAC9B6F,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5BC,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACxCxG,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5ByG,UAAU,EAAE,IAAIlK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;EACH;;EAEA;EACAmN,UAAUA,CAACxD,IAAI,EAAE;IACf,IAAI;MACF,IAAIA,IAAI,CAAChJ,QAAQ,EAAE;QACjB5B,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAChJ,QAAQ,CAAC,CAAC;MAClF;MACA,IAAIgJ,IAAI,CAAC1H,QAAQ,EAAE;QACjBlD,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAAC1H,QAAQ,CAAC,CAAC;MAClF;MACA,IAAI0H,IAAI,CAACzG,SAAS,EAAE;QAClBnE,YAAY,CAACE,OAAO,CAAC,4BAA4B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACzG,SAAS,CAAC,CAAC;MACpF;MACA,IAAIyG,IAAI,CAACpG,QAAQ,EAAE;QACjBxE,YAAY,CAACE,OAAO,CAAC,2BAA2B,EAAEC,IAAI,CAACC,SAAS,CAACwK,IAAI,CAACpG,QAAQ,CAAC,CAAC;MAClF;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;IAC5C;EACF;;EAEA;EACA8L,YAAYA,CAAA,EAAG;IACb,IAAI;MACFrO,YAAY,CAACsO,UAAU,CAAC,2BAA2B,CAAC;MACpDtO,YAAY,CAACsO,UAAU,CAAC,2BAA2B,CAAC;MACpDtO,YAAY,CAACsO,UAAU,CAAC,4BAA4B,CAAC;MACrDtO,YAAY,CAACsO,UAAU,CAAC,2BAA2B,CAAC;MACpD,IAAI,CAACvO,kBAAkB,CAAC,CAAC;MACzB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF;AACF;;AAEA;AACA,MAAMgM,QAAQ,GAAG,IAAI1O,aAAa,CAAC,CAAC;AAEpC,eAAe0O,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}