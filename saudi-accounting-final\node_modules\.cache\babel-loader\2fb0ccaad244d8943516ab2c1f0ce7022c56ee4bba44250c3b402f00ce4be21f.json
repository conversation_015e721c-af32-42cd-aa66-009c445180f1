{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Returns\\\\Returns.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Returns.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Returns = () => {\n  _s();\n  const [returns, setReturns] = useState([]);\n  const [invoices, setInvoices] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingReturn, setEditingReturn] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [newReturn, setNewReturn] = useState({\n    type: 'sales_return',\n    originalInvoiceId: '',\n    customerId: '',\n    supplierId: '',\n    returnNumber: '',\n    returnDate: new Date().toISOString().split('T')[0],\n    reason: '',\n    status: 'pending',\n    items: [{\n      productId: '',\n      quantity: '',\n      price: '',\n      total: '',\n      reason: ''\n    }],\n    subtotal: 0,\n    vatAmount: 0,\n    total: 0,\n    notes: ''\n  });\n  const returnTypes = [{\n    value: 'sales_return',\n    label: '↩️ مرتجع مبيعات',\n    color: '#ef4444'\n  }, {\n    value: 'purchase_return',\n    label: '📤 مرتجع مشتريات',\n    color: '#f59e0b'\n  }, {\n    value: 'damaged_return',\n    label: '💔 مرتجع تالف',\n    color: '#8b5cf6'\n  }, {\n    value: 'expired_return',\n    label: '⏰ مرتجع منتهي الصلاحية',\n    color: '#6b7280'\n  }, {\n    value: 'quality_return',\n    label: '🔍 مرتجع جودة',\n    color: '#dc2626'\n  }];\n  const returnStatuses = [{\n    value: 'pending',\n    label: '⏳ معلق',\n    color: '#f59e0b'\n  }, {\n    value: 'approved',\n    label: '✅ معتمد',\n    color: '#10b981'\n  }, {\n    value: 'processed',\n    label: '🔄 تم المعالجة',\n    color: '#6366f1'\n  }, {\n    value: 'completed',\n    label: '✅ مكتمل',\n    color: '#059669'\n  }, {\n    value: 'cancelled',\n    label: '❌ ملغي',\n    color: '#ef4444'\n  }];\n  const returnReasons = ['عيب في المنتج', 'منتج خاطئ', 'تلف أثناء الشحن', 'انتهاء صلاحية', 'عدم مطابقة المواصفات', 'طلب العميل', 'خطأ في الكمية', 'خطأ في السعر', 'منتج معيب', 'أخرى'];\n  useEffect(() => {\n    loadData();\n    generateReturnNumber();\n  }, []);\n  const loadData = () => {\n    try {\n      const returnsData = database.getReturns();\n      const invoicesData = database.getInvoices();\n      const customersData = database.getCustomers();\n      const suppliersData = database.getSuppliers();\n      const productsData = database.getProducts();\n      setReturns(returnsData);\n      setInvoices(invoicesData);\n      setCustomers(customersData);\n      setSuppliers(suppliersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n  const generateReturnNumber = () => {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    const returnNumber = `RET-${year}${month}${day}-${random}`;\n    setNewReturn(prev => ({\n      ...prev,\n      returnNumber\n    }));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewReturn(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Auto-fill customer/supplier when original invoice is selected\n    if (name === 'originalInvoiceId' && value) {\n      const selectedInvoice = invoices.find(inv => inv.id === parseInt(value));\n      if (selectedInvoice) {\n        setNewReturn(prev => ({\n          ...prev,\n          customerId: selectedInvoice.customerId || '',\n          supplierId: selectedInvoice.supplierId || ''\n        }));\n      }\n    }\n  };\n  const handleItemChange = (index, field, value) => {\n    const updatedItems = [...newReturn.items];\n    updatedItems[index] = {\n      ...updatedItems[index],\n      [field]: value\n    };\n\n    // Calculate total for the item\n    if (field === 'quantity' || field === 'price') {\n      const quantity = parseFloat(updatedItems[index].quantity) || 0;\n      const price = parseFloat(updatedItems[index].price) || 0;\n      updatedItems[index].total = quantity * price;\n    }\n\n    // Auto-fill product price\n    if (field === 'productId' && value) {\n      const selectedProduct = products.find(p => p.id === parseInt(value));\n      if (selectedProduct) {\n        updatedItems[index].price = selectedProduct.price;\n        const quantity = parseFloat(updatedItems[index].quantity) || 0;\n        updatedItems[index].total = quantity * selectedProduct.price;\n      }\n    }\n    setNewReturn(prev => ({\n      ...prev,\n      items: updatedItems\n    }));\n    calculateTotals(updatedItems);\n  };\n  const calculateTotals = (items = newReturn.items) => {\n    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);\n    const vatRate = 0.15; // 15% VAT\n    const vatAmount = subtotal * vatRate;\n    const total = subtotal + vatAmount;\n    setNewReturn(prev => ({\n      ...prev,\n      subtotal,\n      vatAmount,\n      total\n    }));\n  };\n  const addItem = () => {\n    setNewReturn(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        productId: '',\n        quantity: '',\n        price: '',\n        total: '',\n        reason: ''\n      }]\n    }));\n  };\n  const removeItem = index => {\n    if (newReturn.items.length > 1) {\n      const updatedItems = newReturn.items.filter((_, i) => i !== index);\n      setNewReturn(prev => ({\n        ...prev,\n        items: updatedItems\n      }));\n      calculateTotals(updatedItems);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      const returnData = {\n        ...newReturn,\n        items: newReturn.items.filter(item => item.productId && item.quantity),\n        customerId: parseInt(newReturn.customerId) || null,\n        supplierId: parseInt(newReturn.supplierId) || null,\n        originalInvoiceId: parseInt(newReturn.originalInvoiceId) || null\n      };\n      if (editingReturn) {\n        await database.updateReturn(editingReturn.id, returnData);\n        setMessage('تم تحديث المرتجع بنجاح!');\n        setEditingReturn(null);\n      } else {\n        await database.addReturn(returnData);\n        setMessage('تم إضافة المرتجع بنجاح!');\n      }\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ المرتجع:', error);\n      setMessage('خطأ في حفظ المرتجع');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetForm = () => {\n    setNewReturn({\n      type: 'sales_return',\n      originalInvoiceId: '',\n      customerId: '',\n      supplierId: '',\n      returnNumber: '',\n      returnDate: new Date().toISOString().split('T')[0],\n      reason: '',\n      status: 'pending',\n      items: [{\n        productId: '',\n        quantity: '',\n        price: '',\n        total: '',\n        reason: ''\n      }],\n      subtotal: 0,\n      vatAmount: 0,\n      total: 0,\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingReturn(null);\n    generateReturnNumber();\n  };\n  const handleEdit = returnItem => {\n    setNewReturn({\n      ...returnItem,\n      returnDate: returnItem.returnDate.split('T')[0]\n    });\n    setEditingReturn(returnItem);\n    setShowAddForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا المرتجع؟')) {\n      try {\n        await database.deleteReturn(id);\n        setMessage('تم حذف المرتجع بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف المرتجع:', error);\n        setMessage('خطأ في حذف المرتجع');\n      }\n    }\n  };\n  const handleProcess = async id => {\n    if (window.confirm('هل أنت متأكد من معالجة هذا المرتجع؟')) {\n      try {\n        await database.processReturn(id);\n        setMessage('تم معالجة المرتجع بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في معالجة المرتجع:', error);\n        setMessage('خطأ في معالجة المرتجع');\n      }\n    }\n  };\n  const getCustomerName = customerId => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n  const getSupplierName = supplierId => {\n    const supplier = suppliers.find(s => s.id === supplierId);\n    return supplier ? supplier.name : 'غير محدد';\n  };\n  const getProductName = productId => {\n    const product = products.find(p => p.id === parseInt(productId));\n    return product ? product.name : 'غير محدد';\n  };\n  const getInvoiceNumber = invoiceId => {\n    const invoice = invoices.find(inv => inv.id === invoiceId);\n    return invoice ? invoice.invoiceNumber : 'غير محدد';\n  };\n  const getTypeInfo = type => {\n    const typeObj = returnTypes.find(t => t.value === type);\n    return typeObj || {\n      label: type,\n      color: '#64748b'\n    };\n  };\n  const getStatusInfo = status => {\n    const statusObj = returnStatuses.find(s => s.value === status);\n    return statusObj || {\n      label: status,\n      color: '#64748b'\n    };\n  };\n  const filteredReturns = returns.filter(returnItem => {\n    const matchesSearch = returnItem.returnNumber.toLowerCase().includes(searchTerm.toLowerCase()) || getCustomerName(returnItem.customerId).toLowerCase().includes(searchTerm.toLowerCase()) || getSupplierName(returnItem.supplierId).toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || returnItem.type === filterType;\n    const matchesStatus = filterStatus === 'all' || returnItem.status === filterStatus;\n    return matchesSearch && matchesType && matchesStatus;\n  });\n  const totalReturns = filteredReturns.reduce((sum, ret) => sum + ret.total, 0);\n  const salesReturns = filteredReturns.filter(r => r.type === 'sales_return');\n  const purchaseReturns = filteredReturns.filter(r => r.type === 'purchase_return');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"returns-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"returns-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u21A9\\uFE0F \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('خطأ') ? 'error' : 'success'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card total\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\u21A9\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [returns.length, \" \\u0645\\u0631\\u062A\\u062C\\u0639\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card sales\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCE4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [salesReturns.length, \" \\u0645\\u0631\\u062A\\u062C\\u0639\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card purchase\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [purchaseReturns.length, \" \\u0645\\u0631\\u062A\\u062C\\u0639\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card amount\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [totalReturns.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"returns-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-filters\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterType,\n          onChange: e => setFilterType(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), returnTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type.value,\n            children: type.label\n          }, type.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterStatus,\n          onChange: e => setFilterStatus(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0627\\u0644\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), returnStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: status.value,\n            children: status.label\n          }, status.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddForm(true),\n        className: \"add-return-btn\",\n        children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0631\\u062A\\u062C\\u0639 \\u062C\\u062F\\u064A\\u062F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"return-form-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: editingReturn ? '✏️ تعديل المرتجع' : '➕ إضافة مرتجع جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetForm,\n            className: \"close-btn\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"return-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-header\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"type\",\n                  value: newReturn.type,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: returnTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type.value,\n                    children: type.label\n                  }, type.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"returnNumber\",\n                  value: newReturn.returnNumber,\n                  onChange: handleInputChange,\n                  required: true,\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"returnDate\",\n                  value: newReturn.returnDate,\n                  onChange: handleInputChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"status\",\n                  value: newReturn.status,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: returnStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: status.value,\n                    children: status.label\n                  }, status.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0635\\u0644\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"originalInvoiceId\",\n                  value: newReturn.originalInvoiceId,\n                  onChange: handleInputChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 23\n                  }, this), invoices.map(invoice => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: invoice.id,\n                    children: [invoice.invoiceNumber, \" - \", getCustomerName(invoice.customerId)]\n                  }, invoice.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), newReturn.type === 'sales_return' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"customerId\",\n                  value: newReturn.customerId,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 25\n                  }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: customer.id,\n                    children: customer.name\n                  }, customer.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 21\n              }, this), newReturn.type === 'purchase_return' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"supplierId\",\n                  value: newReturn.supplierId,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: supplier.id,\n                    children: supplier.name\n                  }, supplier.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"\\u0633\\u0628\\u0628 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"reason\",\n                  value: newReturn.reason,\n                  onChange: handleInputChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0633\\u0628\\u0628\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 23\n                  }, this), returnReasons.map(reason => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: reason,\n                    children: reason\n                  }, reason, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDCE6 \\u0623\\u0635\\u0646\\u0627\\u0641 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addItem,\n                className: \"add-item-btn\",\n                children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0635\\u0646\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"items-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"items-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0635\\u0646\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0633\\u0639\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0627\\u0644\\u0633\\u0628\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this), newReturn.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: item.productId,\n                  onChange: e => handleItemChange(index, 'productId', e.target.value),\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: product.id,\n                    children: product.name\n                  }, product.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\",\n                  value: item.quantity,\n                  onChange: e => handleItemChange(index, 'quantity', e.target.value),\n                  min: \"1\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"\\u0627\\u0644\\u0633\\u0639\\u0631\",\n                  value: item.price,\n                  onChange: e => handleItemChange(index, 'price', e.target.value),\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: item.total,\n                  readOnly: true,\n                  className: \"total-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"\\u0633\\u0628\\u0628 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\",\n                  value: item.reason,\n                  onChange: e => handleItemChange(index, 'reason', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 23\n                }, this), newReturn.items.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeItem(index),\n                  className: \"remove-item-btn\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"totals-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newReturn.subtotal.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newReturn.vatAmount.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"total-row final-total\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newReturn.total.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"notes\",\n              value: newReturn.notes,\n              onChange: handleInputChange,\n              rows: \"3\",\n              placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: resetForm,\n              className: \"cancel-btn\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"save-btn\",\n              children: loading ? '⏳ جاري الحفظ...' : editingReturn ? '💾 تحديث' : '💾 حفظ'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"returns-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"returns-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062A\\u062C\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0646\\u0648\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644/\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0633\\u0628\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredReturns.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"8\",\n              className: \"no-data\",\n              children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A \\u0645\\u0637\\u0627\\u0628\\u0642\\u0629 \\u0644\\u0644\\u0628\\u062D\\u062B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this) : filteredReturns.map(returnItem => {\n            const typeInfo = getTypeInfo(returnItem.type);\n            const statusInfo = getStatusInfo(returnItem.status);\n            const clientName = returnItem.type === 'sales_return' ? getCustomerName(returnItem.customerId) : getSupplierName(returnItem.supplierId);\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"return-number\",\n                children: returnItem.returnNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"type-badge\",\n                  style: {\n                    backgroundColor: typeInfo.color\n                  },\n                  children: typeInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(returnItem.returnDate).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: clientName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: returnItem.reason\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"amount\",\n                children: [returnItem.total.toLocaleString(), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: statusInfo.color\n                  },\n                  children: statusInfo.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(returnItem),\n                  className: \"edit-btn\",\n                  title: \"\\u062A\\u0639\\u062F\\u064A\\u0644\",\n                  disabled: returnItem.status === 'completed',\n                  children: \"\\u270F\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 23\n                }, this), returnItem.status === 'approved' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleProcess(returnItem.id),\n                  className: \"process-btn\",\n                  title: \"\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629\",\n                  children: \"\\uD83D\\uDD04\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(returnItem.id),\n                  className: \"delete-btn\",\n                  title: \"\\u062D\\u0630\\u0641\",\n                  disabled: returnItem.status === 'completed',\n                  children: \"\\uD83D\\uDDD1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 21\n              }, this)]\n            }, returnItem.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n};\n_s(Returns, \"txaDc0ZhXMqbf6jh7QsXFJCFOAg=\");\n_c = Returns;\nexport default Returns;\nvar _c;\n$RefreshReg$(_c, \"Returns\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Returns", "_s", "returns", "setReturns", "invoices", "setInvoices", "customers", "setCustomers", "suppliers", "setSuppliers", "products", "setProducts", "showAddForm", "setShowAddForm", "editingReturn", "setEditingReturn", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "filterStatus", "setFilterStatus", "loading", "setLoading", "message", "setMessage", "newReturn", "setNewReturn", "type", "originalInvoiceId", "customerId", "supplierId", "returnNumber", "returnDate", "Date", "toISOString", "split", "reason", "status", "items", "productId", "quantity", "price", "total", "subtotal", "vatAmount", "notes", "returnTypes", "value", "label", "color", "returnStatuses", "returnReasons", "loadData", "generateReturnNumber", "returnsData", "getReturns", "invoicesData", "getInvoices", "customersData", "getCustomers", "suppliersData", "getSuppliers", "productsData", "getProducts", "error", "console", "today", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "random", "Math", "floor", "toString", "prev", "handleInputChange", "e", "name", "target", "selectedInvoice", "find", "inv", "id", "parseInt", "handleItemChange", "index", "field", "updatedItems", "parseFloat", "selectedProduct", "p", "calculateTotals", "reduce", "sum", "item", "vatRate", "addItem", "removeItem", "length", "filter", "_", "i", "handleSubmit", "preventDefault", "returnData", "updateReturn", "addReturn", "resetForm", "setTimeout", "handleEdit", "returnItem", "handleDelete", "window", "confirm", "deleteReturn", "handleProcess", "processReturn", "getCustomerName", "customer", "c", "getSupplierName", "supplier", "s", "getProductName", "product", "getInvoiceNumber", "invoiceId", "invoice", "invoiceNumber", "getTypeInfo", "typeObj", "t", "getStatusInfo", "statusObj", "filteredReturns", "matchesSearch", "toLowerCase", "includes", "matchesType", "matchesStatus", "totalReturns", "ret", "salesReturns", "r", "purchaseReturns", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "placeholder", "onChange", "map", "onClick", "onSubmit", "required", "readOnly", "min", "step", "rows", "disabled", "colSpan", "typeInfo", "statusInfo", "clientName", "style", "backgroundColor", "toLocaleDateString", "title", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Returns/Returns.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './Returns.css';\n\nconst Returns = () => {\n  const [returns, setReturns] = useState([]);\n  const [invoices, setInvoices] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingReturn, setEditingReturn] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const [newReturn, setNewReturn] = useState({\n    type: 'sales_return',\n    originalInvoiceId: '',\n    customerId: '',\n    supplierId: '',\n    returnNumber: '',\n    returnDate: new Date().toISOString().split('T')[0],\n    reason: '',\n    status: 'pending',\n    items: [{ productId: '', quantity: '', price: '', total: '', reason: '' }],\n    subtotal: 0,\n    vatAmount: 0,\n    total: 0,\n    notes: ''\n  });\n\n  const returnTypes = [\n    { value: 'sales_return', label: '↩️ مرتجع مبيعات', color: '#ef4444' },\n    { value: 'purchase_return', label: '📤 مرتجع مشتريات', color: '#f59e0b' },\n    { value: 'damaged_return', label: '💔 مرتجع تالف', color: '#8b5cf6' },\n    { value: 'expired_return', label: '⏰ مرتجع منتهي الصلاحية', color: '#6b7280' },\n    { value: 'quality_return', label: '🔍 مرتجع جودة', color: '#dc2626' }\n  ];\n\n  const returnStatuses = [\n    { value: 'pending', label: '⏳ معلق', color: '#f59e0b' },\n    { value: 'approved', label: '✅ معتمد', color: '#10b981' },\n    { value: 'processed', label: '🔄 تم المعالجة', color: '#6366f1' },\n    { value: 'completed', label: '✅ مكتمل', color: '#059669' },\n    { value: 'cancelled', label: '❌ ملغي', color: '#ef4444' }\n  ];\n\n  const returnReasons = [\n    'عيب في المنتج',\n    'منتج خاطئ',\n    'تلف أثناء الشحن',\n    'انتهاء صلاحية',\n    'عدم مطابقة المواصفات',\n    'طلب العميل',\n    'خطأ في الكمية',\n    'خطأ في السعر',\n    'منتج معيب',\n    'أخرى'\n  ];\n\n  useEffect(() => {\n    loadData();\n    generateReturnNumber();\n  }, []);\n\n  const loadData = () => {\n    try {\n      const returnsData = database.getReturns();\n      const invoicesData = database.getInvoices();\n      const customersData = database.getCustomers();\n      const suppliersData = database.getSuppliers();\n      const productsData = database.getProducts();\n      \n      setReturns(returnsData);\n      setInvoices(invoicesData);\n      setCustomers(customersData);\n      setSuppliers(suppliersData);\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل البيانات:', error);\n      setMessage('خطأ في تحميل البيانات');\n    }\n  };\n\n  const generateReturnNumber = () => {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    \n    const returnNumber = `RET-${year}${month}${day}-${random}`;\n    setNewReturn(prev => ({ ...prev, returnNumber }));\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewReturn(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Auto-fill customer/supplier when original invoice is selected\n    if (name === 'originalInvoiceId' && value) {\n      const selectedInvoice = invoices.find(inv => inv.id === parseInt(value));\n      if (selectedInvoice) {\n        setNewReturn(prev => ({\n          ...prev,\n          customerId: selectedInvoice.customerId || '',\n          supplierId: selectedInvoice.supplierId || ''\n        }));\n      }\n    }\n  };\n\n  const handleItemChange = (index, field, value) => {\n    const updatedItems = [...newReturn.items];\n    updatedItems[index] = { ...updatedItems[index], [field]: value };\n\n    // Calculate total for the item\n    if (field === 'quantity' || field === 'price') {\n      const quantity = parseFloat(updatedItems[index].quantity) || 0;\n      const price = parseFloat(updatedItems[index].price) || 0;\n      updatedItems[index].total = quantity * price;\n    }\n\n    // Auto-fill product price\n    if (field === 'productId' && value) {\n      const selectedProduct = products.find(p => p.id === parseInt(value));\n      if (selectedProduct) {\n        updatedItems[index].price = selectedProduct.price;\n        const quantity = parseFloat(updatedItems[index].quantity) || 0;\n        updatedItems[index].total = quantity * selectedProduct.price;\n      }\n    }\n\n    setNewReturn(prev => ({ ...prev, items: updatedItems }));\n    calculateTotals(updatedItems);\n  };\n\n  const calculateTotals = (items = newReturn.items) => {\n    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);\n    const vatRate = 0.15; // 15% VAT\n    const vatAmount = subtotal * vatRate;\n    const total = subtotal + vatAmount;\n\n    setNewReturn(prev => ({\n      ...prev,\n      subtotal,\n      vatAmount,\n      total\n    }));\n  };\n\n  const addItem = () => {\n    setNewReturn(prev => ({\n      ...prev,\n      items: [...prev.items, { productId: '', quantity: '', price: '', total: '', reason: '' }]\n    }));\n  };\n\n  const removeItem = (index) => {\n    if (newReturn.items.length > 1) {\n      const updatedItems = newReturn.items.filter((_, i) => i !== index);\n      setNewReturn(prev => ({ ...prev, items: updatedItems }));\n      calculateTotals(updatedItems);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      const returnData = {\n        ...newReturn,\n        items: newReturn.items.filter(item => item.productId && item.quantity),\n        customerId: parseInt(newReturn.customerId) || null,\n        supplierId: parseInt(newReturn.supplierId) || null,\n        originalInvoiceId: parseInt(newReturn.originalInvoiceId) || null\n      };\n\n      if (editingReturn) {\n        await database.updateReturn(editingReturn.id, returnData);\n        setMessage('تم تحديث المرتجع بنجاح!');\n        setEditingReturn(null);\n      } else {\n        await database.addReturn(returnData);\n        setMessage('تم إضافة المرتجع بنجاح!');\n      }\n\n      resetForm();\n      loadData();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      console.error('خطأ في حفظ المرتجع:', error);\n      setMessage('خطأ في حفظ المرتجع');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetForm = () => {\n    setNewReturn({\n      type: 'sales_return',\n      originalInvoiceId: '',\n      customerId: '',\n      supplierId: '',\n      returnNumber: '',\n      returnDate: new Date().toISOString().split('T')[0],\n      reason: '',\n      status: 'pending',\n      items: [{ productId: '', quantity: '', price: '', total: '', reason: '' }],\n      subtotal: 0,\n      vatAmount: 0,\n      total: 0,\n      notes: ''\n    });\n    setShowAddForm(false);\n    setEditingReturn(null);\n    generateReturnNumber();\n  };\n\n  const handleEdit = (returnItem) => {\n    setNewReturn({\n      ...returnItem,\n      returnDate: returnItem.returnDate.split('T')[0]\n    });\n    setEditingReturn(returnItem);\n    setShowAddForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا المرتجع؟')) {\n      try {\n        await database.deleteReturn(id);\n        setMessage('تم حذف المرتجع بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في حذف المرتجع:', error);\n        setMessage('خطأ في حذف المرتجع');\n      }\n    }\n  };\n\n  const handleProcess = async (id) => {\n    if (window.confirm('هل أنت متأكد من معالجة هذا المرتجع؟')) {\n      try {\n        await database.processReturn(id);\n        setMessage('تم معالجة المرتجع بنجاح!');\n        loadData();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        console.error('خطأ في معالجة المرتجع:', error);\n        setMessage('خطأ في معالجة المرتجع');\n      }\n    }\n  };\n\n  const getCustomerName = (customerId) => {\n    const customer = customers.find(c => c.id === customerId);\n    return customer ? customer.name : 'غير محدد';\n  };\n\n  const getSupplierName = (supplierId) => {\n    const supplier = suppliers.find(s => s.id === supplierId);\n    return supplier ? supplier.name : 'غير محدد';\n  };\n\n  const getProductName = (productId) => {\n    const product = products.find(p => p.id === parseInt(productId));\n    return product ? product.name : 'غير محدد';\n  };\n\n  const getInvoiceNumber = (invoiceId) => {\n    const invoice = invoices.find(inv => inv.id === invoiceId);\n    return invoice ? invoice.invoiceNumber : 'غير محدد';\n  };\n\n  const getTypeInfo = (type) => {\n    const typeObj = returnTypes.find(t => t.value === type);\n    return typeObj || { label: type, color: '#64748b' };\n  };\n\n  const getStatusInfo = (status) => {\n    const statusObj = returnStatuses.find(s => s.value === status);\n    return statusObj || { label: status, color: '#64748b' };\n  };\n\n  const filteredReturns = returns.filter(returnItem => {\n    const matchesSearch = \n      returnItem.returnNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      getCustomerName(returnItem.customerId).toLowerCase().includes(searchTerm.toLowerCase()) ||\n      getSupplierName(returnItem.supplierId).toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesType = filterType === 'all' || returnItem.type === filterType;\n    const matchesStatus = filterStatus === 'all' || returnItem.status === filterStatus;\n    \n    return matchesSearch && matchesType && matchesStatus;\n  });\n\n  const totalReturns = filteredReturns.reduce((sum, ret) => sum + ret.total, 0);\n  const salesReturns = filteredReturns.filter(r => r.type === 'sales_return');\n  const purchaseReturns = filteredReturns.filter(r => r.type === 'purchase_return');\n\n  return (\n    <div className=\"returns-container\">\n      <div className=\"returns-header\">\n        <h1>↩️ إدارة المرتجعات</h1>\n        <p>إدارة مرتجعات المبيعات والمشتريات</p>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>\n          {message}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card total\">\n          <div className=\"stat-icon\">↩️</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي المرتجعات</h3>\n            <p>{returns.length} مرتجع</p>\n          </div>\n        </div>\n        <div className=\"stat-card sales\">\n          <div className=\"stat-icon\">📤</div>\n          <div className=\"stat-info\">\n            <h3>مرتجعات المبيعات</h3>\n            <p>{salesReturns.length} مرتجع</p>\n          </div>\n        </div>\n        <div className=\"stat-card purchase\">\n          <div className=\"stat-icon\">📥</div>\n          <div className=\"stat-info\">\n            <h3>مرتجعات المشتريات</h3>\n            <p>{purchaseReturns.length} مرتجع</p>\n          </div>\n        </div>\n        <div className=\"stat-card amount\">\n          <div className=\"stat-icon\">💰</div>\n          <div className=\"stat-info\">\n            <h3>إجمالي القيمة</h3>\n            <p>{totalReturns.toLocaleString()} ريال</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"returns-controls\">\n        <div className=\"search-filters\">\n          <input\n            type=\"text\"\n            placeholder=\"🔍 البحث في المرتجعات...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">جميع الأنواع</option>\n            {returnTypes.map(type => (\n              <option key={type.value} value={type.value}>\n                {type.label}\n              </option>\n            ))}\n          </select>\n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">جميع الحالات</option>\n            {returnStatuses.map(status => (\n              <option key={status.value} value={status.value}>\n                {status.label}\n              </option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"add-return-btn\"\n        >\n          ➕ إضافة مرتجع جديد\n        </button>\n      </div>\n\n      {/* Add/Edit Form */}\n      {showAddForm && (\n        <div className=\"modal-overlay\">\n          <div className=\"return-form-modal\">\n            <div className=\"modal-header\">\n              <h3>{editingReturn ? '✏️ تعديل المرتجع' : '➕ إضافة مرتجع جديد'}</h3>\n              <button onClick={resetForm} className=\"close-btn\">✕</button>\n            </div>\n            \n            <form onSubmit={handleSubmit} className=\"return-form\">\n              <div className=\"form-header\">\n                <div className=\"form-grid\">\n                  <div className=\"form-group\">\n                    <label>نوع المرتجع</label>\n                    <select\n                      name=\"type\"\n                      value={newReturn.type}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {returnTypes.map(type => (\n                        <option key={type.value} value={type.value}>\n                          {type.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>رقم المرتجع</label>\n                    <input\n                      type=\"text\"\n                      name=\"returnNumber\"\n                      value={newReturn.returnNumber}\n                      onChange={handleInputChange}\n                      required\n                      readOnly\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>تاريخ المرتجع</label>\n                    <input\n                      type=\"date\"\n                      name=\"returnDate\"\n                      value={newReturn.returnDate}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>الحالة</label>\n                    <select\n                      name=\"status\"\n                      value={newReturn.status}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      {returnStatuses.map(status => (\n                        <option key={status.value} value={status.value}>\n                          {status.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>الفاتورة الأصلية</label>\n                    <select\n                      name=\"originalInvoiceId\"\n                      value={newReturn.originalInvoiceId}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"\">اختر الفاتورة (اختياري)</option>\n                      {invoices.map(invoice => (\n                        <option key={invoice.id} value={invoice.id}>\n                          {invoice.invoiceNumber} - {getCustomerName(invoice.customerId)}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {newReturn.type === 'sales_return' && (\n                    <div className=\"form-group\">\n                      <label>العميل</label>\n                      <select\n                        name=\"customerId\"\n                        value={newReturn.customerId}\n                        onChange={handleInputChange}\n                        required\n                      >\n                        <option value=\"\">اختر العميل</option>\n                        {customers.map(customer => (\n                          <option key={customer.id} value={customer.id}>\n                            {customer.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  )}\n\n                  {newReturn.type === 'purchase_return' && (\n                    <div className=\"form-group\">\n                      <label>المورد</label>\n                      <select\n                        name=\"supplierId\"\n                        value={newReturn.supplierId}\n                        onChange={handleInputChange}\n                        required\n                      >\n                        <option value=\"\">اختر المورد</option>\n                        {suppliers.map(supplier => (\n                          <option key={supplier.id} value={supplier.id}>\n                            {supplier.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  )}\n\n                  <div className=\"form-group\">\n                    <label>سبب المرتجع</label>\n                    <select\n                      name=\"reason\"\n                      value={newReturn.reason}\n                      onChange={handleInputChange}\n                      required\n                    >\n                      <option value=\"\">اختر السبب</option>\n                      {returnReasons.map(reason => (\n                        <option key={reason} value={reason}>\n                          {reason}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* Items Section */}\n              <div className=\"items-section\">\n                <div className=\"section-header\">\n                  <h4>📦 أصناف المرتجع</h4>\n                  <button type=\"button\" onClick={addItem} className=\"add-item-btn\">\n                    ➕ إضافة صنف\n                  </button>\n                </div>\n\n                <div className=\"items-table\">\n                  <div className=\"items-header\">\n                    <div>الصنف</div>\n                    <div>الكمية</div>\n                    <div>السعر</div>\n                    <div>الإجمالي</div>\n                    <div>السبب</div>\n                    <div>إجراءات</div>\n                  </div>\n\n                  {newReturn.items.map((item, index) => (\n                    <div key={index} className=\"item-row\">\n                      <select\n                        value={item.productId}\n                        onChange={(e) => handleItemChange(index, 'productId', e.target.value)}\n                        required\n                      >\n                        <option value=\"\">اختر الصنف</option>\n                        {products.map(product => (\n                          <option key={product.id} value={product.id}>\n                            {product.name}\n                          </option>\n                        ))}\n                      </select>\n                      <input\n                        type=\"number\"\n                        placeholder=\"الكمية\"\n                        value={item.quantity}\n                        onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}\n                        min=\"1\"\n                        required\n                      />\n                      <input\n                        type=\"number\"\n                        placeholder=\"السعر\"\n                        value={item.price}\n                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}\n                        step=\"0.01\"\n                        min=\"0\"\n                        required\n                      />\n                      <input\n                        type=\"number\"\n                        value={item.total}\n                        readOnly\n                        className=\"total-field\"\n                      />\n                      <input\n                        type=\"text\"\n                        placeholder=\"سبب المرتجع\"\n                        value={item.reason}\n                        onChange={(e) => handleItemChange(index, 'reason', e.target.value)}\n                      />\n                      {newReturn.items.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeItem(index)}\n                          className=\"remove-item-btn\"\n                        >\n                          🗑️\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"totals-section\">\n                  <div className=\"total-row\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{newReturn.subtotal.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"total-row\">\n                    <span>ضريبة القيمة المضافة (15%):</span>\n                    <span>{newReturn.vatAmount.toLocaleString()} ريال</span>\n                  </div>\n                  <div className=\"total-row final-total\">\n                    <span>الإجمالي:</span>\n                    <span>{newReturn.total.toLocaleString()} ريال</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label>ملاحظات</label>\n                <textarea\n                  name=\"notes\"\n                  value={newReturn.notes}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  placeholder=\"ملاحظات إضافية...\"\n                />\n              </div>\n\n              <div className=\"form-actions\">\n                <button type=\"button\" onClick={resetForm} className=\"cancel-btn\">\n                  إلغاء\n                </button>\n                <button type=\"submit\" disabled={loading} className=\"save-btn\">\n                  {loading ? '⏳ جاري الحفظ...' : (editingReturn ? '💾 تحديث' : '💾 حفظ')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Returns Table */}\n      <div className=\"returns-table-container\">\n        <table className=\"returns-table\">\n          <thead>\n            <tr>\n              <th>رقم المرتجع</th>\n              <th>النوع</th>\n              <th>التاريخ</th>\n              <th>العميل/المورد</th>\n              <th>السبب</th>\n              <th>المبلغ</th>\n              <th>الحالة</th>\n              <th>الإجراءات</th>\n            </tr>\n          </thead>\n          <tbody>\n            {filteredReturns.length === 0 ? (\n              <tr>\n                <td colSpan=\"8\" className=\"no-data\">\n                  لا توجد مرتجعات مطابقة للبحث\n                </td>\n              </tr>\n            ) : (\n              filteredReturns.map(returnItem => {\n                const typeInfo = getTypeInfo(returnItem.type);\n                const statusInfo = getStatusInfo(returnItem.status);\n                const clientName = returnItem.type === 'sales_return' ? \n                  getCustomerName(returnItem.customerId) : \n                  getSupplierName(returnItem.supplierId);\n                \n                return (\n                  <tr key={returnItem.id}>\n                    <td className=\"return-number\">{returnItem.returnNumber}</td>\n                    <td>\n                      <span \n                        className=\"type-badge\"\n                        style={{ backgroundColor: typeInfo.color }}\n                      >\n                        {typeInfo.label}\n                      </span>\n                    </td>\n                    <td>{new Date(returnItem.returnDate).toLocaleDateString('ar-SA')}</td>\n                    <td>{clientName}</td>\n                    <td>{returnItem.reason}</td>\n                    <td className=\"amount\">{returnItem.total.toLocaleString()} ريال</td>\n                    <td>\n                      <span \n                        className=\"status-badge\"\n                        style={{ backgroundColor: statusInfo.color }}\n                      >\n                        {statusInfo.label}\n                      </span>\n                    </td>\n                    <td className=\"actions\">\n                      <button\n                        onClick={() => handleEdit(returnItem)}\n                        className=\"edit-btn\"\n                        title=\"تعديل\"\n                        disabled={returnItem.status === 'completed'}\n                      >\n                        ✏️\n                      </button>\n                      {returnItem.status === 'approved' && (\n                        <button\n                          onClick={() => handleProcess(returnItem.id)}\n                          className=\"process-btn\"\n                          title=\"معالجة\"\n                        >\n                          🔄\n                        </button>\n                      )}\n                      <button\n                        onClick={() => handleDelete(returnItem.id)}\n                        className=\"delete-btn\"\n                        title=\"حذف\"\n                        disabled={returnItem.status === 'completed'}\n                      >\n                        🗑️\n                      </button>\n                    </td>\n                  </tr>\n                );\n              })\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default Returns;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC;IACzCiC,IAAI,EAAE,cAAc;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClDC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEN,MAAM,EAAE;IAAG,CAAC,CAAC;IAC1EO,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZF,KAAK,EAAE,CAAC;IACRG,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrE;IAAEF,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzE;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrE;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9E;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAU,CAAC,CACtE;EAED,MAAMC,cAAc,GAAG,CACrB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACvD;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjE;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC1D;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC1D;EAED,MAAME,aAAa,GAAG,CACpB,eAAe,EACf,WAAW,EACX,iBAAiB,EACjB,eAAe,EACf,sBAAsB,EACtB,YAAY,EACZ,eAAe,EACf,cAAc,EACd,WAAW,EACX,MAAM,CACP;EAEDxD,SAAS,CAAC,MAAM;IACdyD,QAAQ,CAAC,CAAC;IACVC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI;MACF,MAAME,WAAW,GAAG1D,QAAQ,CAAC2D,UAAU,CAAC,CAAC;MACzC,MAAMC,YAAY,GAAG5D,QAAQ,CAAC6D,WAAW,CAAC,CAAC;MAC3C,MAAMC,aAAa,GAAG9D,QAAQ,CAAC+D,YAAY,CAAC,CAAC;MAC7C,MAAMC,aAAa,GAAGhE,QAAQ,CAACiE,YAAY,CAAC,CAAC;MAC7C,MAAMC,YAAY,GAAGlE,QAAQ,CAACmE,WAAW,CAAC,CAAC;MAE3C7D,UAAU,CAACoD,WAAW,CAAC;MACvBlD,WAAW,CAACoD,YAAY,CAAC;MACzBlD,YAAY,CAACoD,aAAa,CAAC;MAC3BlD,YAAY,CAACoD,aAAa,CAAC;MAC3BlD,WAAW,CAACoD,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxC,UAAU,CAAC,uBAAuB,CAAC;IACrC;EACF,CAAC;EAED,MAAM6B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMa,KAAK,GAAG,IAAIjC,IAAI,CAAC,CAAC;IACxB,MAAMkC,IAAI,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IAChC,MAAMC,KAAK,GAAGC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAACG,QAAQ,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE3E,MAAMzC,YAAY,GAAG,OAAOoC,IAAI,GAAGE,KAAK,GAAGI,GAAG,IAAIE,MAAM,EAAE;IAC1DjD,YAAY,CAACqD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhD;IAAa,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMiD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEnC;IAAM,CAAC,GAAGkC,CAAC,CAACE,MAAM;IAChCzD,YAAY,CAACqD,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACG,IAAI,GAAGnC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAImC,IAAI,KAAK,mBAAmB,IAAInC,KAAK,EAAE;MACzC,MAAMqC,eAAe,GAAGjF,QAAQ,CAACkF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKC,QAAQ,CAACzC,KAAK,CAAC,CAAC;MACxE,IAAIqC,eAAe,EAAE;QACnB1D,YAAY,CAACqD,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPlD,UAAU,EAAEuD,eAAe,CAACvD,UAAU,IAAI,EAAE;UAC5CC,UAAU,EAAEsD,eAAe,CAACtD,UAAU,IAAI;QAC5C,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;EAED,MAAM2D,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAE5C,KAAK,KAAK;IAChD,MAAM6C,YAAY,GAAG,CAAC,GAAGnE,SAAS,CAACa,KAAK,CAAC;IACzCsD,YAAY,CAACF,KAAK,CAAC,GAAG;MAAE,GAAGE,YAAY,CAACF,KAAK,CAAC;MAAE,CAACC,KAAK,GAAG5C;IAAM,CAAC;;IAEhE;IACA,IAAI4C,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,OAAO,EAAE;MAC7C,MAAMnD,QAAQ,GAAGqD,UAAU,CAACD,YAAY,CAACF,KAAK,CAAC,CAAClD,QAAQ,CAAC,IAAI,CAAC;MAC9D,MAAMC,KAAK,GAAGoD,UAAU,CAACD,YAAY,CAACF,KAAK,CAAC,CAACjD,KAAK,CAAC,IAAI,CAAC;MACxDmD,YAAY,CAACF,KAAK,CAAC,CAAChD,KAAK,GAAGF,QAAQ,GAAGC,KAAK;IAC9C;;IAEA;IACA,IAAIkD,KAAK,KAAK,WAAW,IAAI5C,KAAK,EAAE;MAClC,MAAM+C,eAAe,GAAGrF,QAAQ,CAAC4E,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKC,QAAQ,CAACzC,KAAK,CAAC,CAAC;MACpE,IAAI+C,eAAe,EAAE;QACnBF,YAAY,CAACF,KAAK,CAAC,CAACjD,KAAK,GAAGqD,eAAe,CAACrD,KAAK;QACjD,MAAMD,QAAQ,GAAGqD,UAAU,CAACD,YAAY,CAACF,KAAK,CAAC,CAAClD,QAAQ,CAAC,IAAI,CAAC;QAC9DoD,YAAY,CAACF,KAAK,CAAC,CAAChD,KAAK,GAAGF,QAAQ,GAAGsD,eAAe,CAACrD,KAAK;MAC9D;IACF;IAEAf,YAAY,CAACqD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,KAAK,EAAEsD;IAAa,CAAC,CAAC,CAAC;IACxDI,eAAe,CAACJ,YAAY,CAAC;EAC/B,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAC1D,KAAK,GAAGb,SAAS,CAACa,KAAK,KAAK;IACnD,MAAMK,QAAQ,GAAGL,KAAK,CAAC2D,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIL,UAAU,CAACM,IAAI,CAACzD,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACpF,MAAM0D,OAAO,GAAG,IAAI,CAAC,CAAC;IACtB,MAAMxD,SAAS,GAAGD,QAAQ,GAAGyD,OAAO;IACpC,MAAM1D,KAAK,GAAGC,QAAQ,GAAGC,SAAS;IAElClB,YAAY,CAACqD,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPpC,QAAQ;MACRC,SAAS;MACTF;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2D,OAAO,GAAGA,CAAA,KAAM;IACpB3E,YAAY,CAACqD,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPzC,KAAK,EAAE,CAAC,GAAGyC,IAAI,CAACzC,KAAK,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEN,MAAM,EAAE;MAAG,CAAC;IAC1F,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkE,UAAU,GAAIZ,KAAK,IAAK;IAC5B,IAAIjE,SAAS,CAACa,KAAK,CAACiE,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMX,YAAY,GAAGnE,SAAS,CAACa,KAAK,CAACkE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKhB,KAAK,CAAC;MAClEhE,YAAY,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,KAAK,EAAEsD;MAAa,CAAC,CAAC,CAAC;MACxDI,eAAe,CAACJ,YAAY,CAAC;IAC/B;EACF,CAAC;EAED,MAAMe,YAAY,GAAG,MAAO1B,CAAC,IAAK;IAChCA,CAAC,CAAC2B,cAAc,CAAC,CAAC;IAClBtF,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMqF,UAAU,GAAG;QACjB,GAAGpF,SAAS;QACZa,KAAK,EAAEb,SAAS,CAACa,KAAK,CAACkE,MAAM,CAACL,IAAI,IAAIA,IAAI,CAAC5D,SAAS,IAAI4D,IAAI,CAAC3D,QAAQ,CAAC;QACtEX,UAAU,EAAE2D,QAAQ,CAAC/D,SAAS,CAACI,UAAU,CAAC,IAAI,IAAI;QAClDC,UAAU,EAAE0D,QAAQ,CAAC/D,SAAS,CAACK,UAAU,CAAC,IAAI,IAAI;QAClDF,iBAAiB,EAAE4D,QAAQ,CAAC/D,SAAS,CAACG,iBAAiB,CAAC,IAAI;MAC9D,CAAC;MAED,IAAIf,aAAa,EAAE;QACjB,MAAMjB,QAAQ,CAACkH,YAAY,CAACjG,aAAa,CAAC0E,EAAE,EAAEsB,UAAU,CAAC;QACzDrF,UAAU,CAAC,yBAAyB,CAAC;QACrCV,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,MAAM;QACL,MAAMlB,QAAQ,CAACmH,SAAS,CAACF,UAAU,CAAC;QACpCrF,UAAU,CAAC,yBAAyB,CAAC;MACvC;MAEAwF,SAAS,CAAC,CAAC;MACX5D,QAAQ,CAAC,CAAC;MACV6D,UAAU,CAAC,MAAMzF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CxC,UAAU,CAAC,oBAAoB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0F,SAAS,GAAGA,CAAA,KAAM;IACtBtF,YAAY,CAAC;MACXC,IAAI,EAAE,cAAc;MACpBC,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClDC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEN,MAAM,EAAE;MAAG,CAAC,CAAC;MAC1EO,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZF,KAAK,EAAE,CAAC;MACRG,KAAK,EAAE;IACT,CAAC,CAAC;IACFjC,cAAc,CAAC,KAAK,CAAC;IACrBE,gBAAgB,CAAC,IAAI,CAAC;IACtBuC,oBAAoB,CAAC,CAAC;EACxB,CAAC;EAED,MAAM6D,UAAU,GAAIC,UAAU,IAAK;IACjCzF,YAAY,CAAC;MACX,GAAGyF,UAAU;MACbnF,UAAU,EAAEmF,UAAU,CAACnF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACFrB,gBAAgB,CAACqG,UAAU,CAAC;IAC5BvG,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMwG,YAAY,GAAG,MAAO7B,EAAE,IAAK;IACjC,IAAI8B,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACtD,IAAI;QACF,MAAM1H,QAAQ,CAAC2H,YAAY,CAAChC,EAAE,CAAC;QAC/B/D,UAAU,CAAC,uBAAuB,CAAC;QACnC4B,QAAQ,CAAC,CAAC;QACV6D,UAAU,CAAC,MAAMzF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxC,UAAU,CAAC,oBAAoB,CAAC;MAClC;IACF;EACF,CAAC;EAED,MAAMgG,aAAa,GAAG,MAAOjC,EAAE,IAAK;IAClC,IAAI8B,MAAM,CAACC,OAAO,CAAC,qCAAqC,CAAC,EAAE;MACzD,IAAI;QACF,MAAM1H,QAAQ,CAAC6H,aAAa,CAAClC,EAAE,CAAC;QAChC/D,UAAU,CAAC,0BAA0B,CAAC;QACtC4B,QAAQ,CAAC,CAAC;QACV6D,UAAU,CAAC,MAAMzF,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CxC,UAAU,CAAC,uBAAuB,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAMkG,eAAe,GAAI7F,UAAU,IAAK;IACtC,MAAM8F,QAAQ,GAAGtH,SAAS,CAACgF,IAAI,CAACuC,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAK1D,UAAU,CAAC;IACzD,OAAO8F,QAAQ,GAAGA,QAAQ,CAACzC,IAAI,GAAG,UAAU;EAC9C,CAAC;EAED,MAAM2C,eAAe,GAAI/F,UAAU,IAAK;IACtC,MAAMgG,QAAQ,GAAGvH,SAAS,CAAC8E,IAAI,CAAC0C,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKzD,UAAU,CAAC;IACzD,OAAOgG,QAAQ,GAAGA,QAAQ,CAAC5C,IAAI,GAAG,UAAU;EAC9C,CAAC;EAED,MAAM8C,cAAc,GAAIzF,SAAS,IAAK;IACpC,MAAM0F,OAAO,GAAGxH,QAAQ,CAAC4E,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKC,QAAQ,CAACjD,SAAS,CAAC,CAAC;IAChE,OAAO0F,OAAO,GAAGA,OAAO,CAAC/C,IAAI,GAAG,UAAU;EAC5C,CAAC;EAED,MAAMgD,gBAAgB,GAAIC,SAAS,IAAK;IACtC,MAAMC,OAAO,GAAGjI,QAAQ,CAACkF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAK4C,SAAS,CAAC;IAC1D,OAAOC,OAAO,GAAGA,OAAO,CAACC,aAAa,GAAG,UAAU;EACrD,CAAC;EAED,MAAMC,WAAW,GAAI3G,IAAI,IAAK;IAC5B,MAAM4G,OAAO,GAAGzF,WAAW,CAACuC,IAAI,CAACmD,CAAC,IAAIA,CAAC,CAACzF,KAAK,KAAKpB,IAAI,CAAC;IACvD,OAAO4G,OAAO,IAAI;MAAEvF,KAAK,EAAErB,IAAI;MAAEsB,KAAK,EAAE;IAAU,CAAC;EACrD,CAAC;EAED,MAAMwF,aAAa,GAAIpG,MAAM,IAAK;IAChC,MAAMqG,SAAS,GAAGxF,cAAc,CAACmC,IAAI,CAAC0C,CAAC,IAAIA,CAAC,CAAChF,KAAK,KAAKV,MAAM,CAAC;IAC9D,OAAOqG,SAAS,IAAI;MAAE1F,KAAK,EAAEX,MAAM;MAAEY,KAAK,EAAE;IAAU,CAAC;EACzD,CAAC;EAED,MAAM0F,eAAe,GAAG1I,OAAO,CAACuG,MAAM,CAACW,UAAU,IAAI;IACnD,MAAMyB,aAAa,GACjBzB,UAAU,CAACpF,YAAY,CAAC8G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/H,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC,IACxEnB,eAAe,CAACP,UAAU,CAACtF,UAAU,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/H,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC,IACvFhB,eAAe,CAACV,UAAU,CAACrF,UAAU,CAAC,CAAC+G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/H,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC;IAEzF,MAAME,WAAW,GAAG9H,UAAU,KAAK,KAAK,IAAIkG,UAAU,CAACxF,IAAI,KAAKV,UAAU;IAC1E,MAAM+H,aAAa,GAAG7H,YAAY,KAAK,KAAK,IAAIgG,UAAU,CAAC9E,MAAM,KAAKlB,YAAY;IAElF,OAAOyH,aAAa,IAAIG,WAAW,IAAIC,aAAa;EACtD,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAGN,eAAe,CAAC1C,MAAM,CAAC,CAACC,GAAG,EAAEgD,GAAG,KAAKhD,GAAG,GAAGgD,GAAG,CAACxG,KAAK,EAAE,CAAC,CAAC;EAC7E,MAAMyG,YAAY,GAAGR,eAAe,CAACnC,MAAM,CAAC4C,CAAC,IAAIA,CAAC,CAACzH,IAAI,KAAK,cAAc,CAAC;EAC3E,MAAM0H,eAAe,GAAGV,eAAe,CAACnC,MAAM,CAAC4C,CAAC,IAAIA,CAAC,CAACzH,IAAI,KAAK,iBAAiB,CAAC;EAEjF,oBACE7B,OAAA;IAAKwJ,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCzJ,OAAA;MAAKwJ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzJ,OAAA;QAAAyJ,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B7J,OAAA;QAAAyJ,QAAA,EAAG;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,EAELpI,OAAO,iBACNzB,OAAA;MAAKwJ,SAAS,EAAE,WAAW/H,OAAO,CAACuH,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAS,QAAA,EACxEhI;IAAO;MAAAiI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD7J,OAAA;MAAKwJ,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzJ,OAAA;QAAKwJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzJ,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC7J,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzJ,OAAA;YAAAyJ,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB7J,OAAA;YAAAyJ,QAAA,GAAItJ,OAAO,CAACsG,MAAM,EAAC,iCAAM;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7J,OAAA;QAAKwJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzJ,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC7J,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzJ,OAAA;YAAAyJ,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB7J,OAAA;YAAAyJ,QAAA,GAAIJ,YAAY,CAAC5C,MAAM,EAAC,iCAAM;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7J,OAAA;QAAKwJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzJ,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC7J,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzJ,OAAA;YAAAyJ,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B7J,OAAA;YAAAyJ,QAAA,GAAIF,eAAe,CAAC9C,MAAM,EAAC,iCAAM;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7J,OAAA;QAAKwJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzJ,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnC7J,OAAA;UAAKwJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzJ,OAAA;YAAAyJ,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB7J,OAAA;YAAAyJ,QAAA,GAAIN,YAAY,CAACW,cAAc,CAAC,CAAC,EAAC,2BAAK;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7J,OAAA;MAAKwJ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzJ,OAAA;QAAKwJ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzJ,OAAA;UACE6B,IAAI,EAAC,MAAM;UACXkI,WAAW,EAAC,oHAA0B;UACtC9G,KAAK,EAAEhC,UAAW;UAClB+I,QAAQ,EAAG7E,CAAC,IAAKjE,aAAa,CAACiE,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;UAC/CuG,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACF7J,OAAA;UACEiD,KAAK,EAAE9B,UAAW;UAClB6I,QAAQ,EAAG7E,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;UAC/CuG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBzJ,OAAA;YAAQiD,KAAK,EAAC,KAAK;YAAAwG,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxC7G,WAAW,CAACiH,GAAG,CAACpI,IAAI,iBACnB7B,OAAA;YAAyBiD,KAAK,EAAEpB,IAAI,CAACoB,KAAM;YAAAwG,QAAA,EACxC5H,IAAI,CAACqB;UAAK,GADArB,IAAI,CAACoB,KAAK;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT7J,OAAA;UACEiD,KAAK,EAAE5B,YAAa;UACpB2I,QAAQ,EAAG7E,CAAC,IAAK7D,eAAe,CAAC6D,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;UACjDuG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBzJ,OAAA;YAAQiD,KAAK,EAAC,KAAK;YAAAwG,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCzG,cAAc,CAAC6G,GAAG,CAAC1H,MAAM,iBACxBvC,OAAA;YAA2BiD,KAAK,EAAEV,MAAM,CAACU,KAAM;YAAAwG,QAAA,EAC5ClH,MAAM,CAACW;UAAK,GADFX,MAAM,CAACU,KAAK;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN7J,OAAA;QACEkK,OAAO,EAAEA,CAAA,KAAMpJ,cAAc,CAAC,IAAI,CAAE;QACpC0I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLhJ,WAAW,iBACVb,OAAA;MAAKwJ,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BzJ,OAAA;QAAKwJ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzJ,OAAA;UAAKwJ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzJ,OAAA;YAAAyJ,QAAA,EAAK1I,aAAa,GAAG,kBAAkB,GAAG;UAAoB;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpE7J,OAAA;YAAQkK,OAAO,EAAEhD,SAAU;YAACsC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEN7J,OAAA;UAAMmK,QAAQ,EAAEtD,YAAa;UAAC2C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACnDzJ,OAAA;YAAKwJ,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BzJ,OAAA;cAAKwJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzJ,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B7J,OAAA;kBACEoF,IAAI,EAAC,MAAM;kBACXnC,KAAK,EAAEtB,SAAS,CAACE,IAAK;kBACtBmI,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBAAAX,QAAA,EAEPzG,WAAW,CAACiH,GAAG,CAACpI,IAAI,iBACnB7B,OAAA;oBAAyBiD,KAAK,EAAEpB,IAAI,CAACoB,KAAM;oBAAAwG,QAAA,EACxC5H,IAAI,CAACqB;kBAAK,GADArB,IAAI,CAACoB,KAAK;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B7J,OAAA;kBACE6B,IAAI,EAAC,MAAM;kBACXuD,IAAI,EAAC,cAAc;kBACnBnC,KAAK,EAAEtB,SAAS,CAACM,YAAa;kBAC9B+H,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBACRC,QAAQ;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5B7J,OAAA;kBACE6B,IAAI,EAAC,MAAM;kBACXuD,IAAI,EAAC,YAAY;kBACjBnC,KAAK,EAAEtB,SAAS,CAACO,UAAW;kBAC5B8H,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB7J,OAAA;kBACEoF,IAAI,EAAC,QAAQ;kBACbnC,KAAK,EAAEtB,SAAS,CAACY,MAAO;kBACxByH,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBAAAX,QAAA,EAEPrG,cAAc,CAAC6G,GAAG,CAAC1H,MAAM,iBACxBvC,OAAA;oBAA2BiD,KAAK,EAAEV,MAAM,CAACU,KAAM;oBAAAwG,QAAA,EAC5ClH,MAAM,CAACW;kBAAK,GADFX,MAAM,CAACU,KAAK;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7J,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/B7J,OAAA;kBACEoF,IAAI,EAAC,mBAAmB;kBACxBnC,KAAK,EAAEtB,SAAS,CAACG,iBAAkB;kBACnCkI,QAAQ,EAAE9E,iBAAkB;kBAAAuE,QAAA,gBAE5BzJ,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAwG,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChDxJ,QAAQ,CAAC4J,GAAG,CAAC3B,OAAO,iBACnBtI,OAAA;oBAAyBiD,KAAK,EAAEqF,OAAO,CAAC7C,EAAG;oBAAAgE,QAAA,GACxCnB,OAAO,CAACC,aAAa,EAAC,KAAG,EAACX,eAAe,CAACU,OAAO,CAACvG,UAAU,CAAC;kBAAA,GADnDuG,OAAO,CAAC7C,EAAE;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAELlI,SAAS,CAACE,IAAI,KAAK,cAAc,iBAChC7B,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB7J,OAAA;kBACEoF,IAAI,EAAC,YAAY;kBACjBnC,KAAK,EAAEtB,SAAS,CAACI,UAAW;kBAC5BiI,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBAAAX,QAAA,gBAERzJ,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAwG,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCtJ,SAAS,CAAC0J,GAAG,CAACpC,QAAQ,iBACrB7H,OAAA;oBAA0BiD,KAAK,EAAE4E,QAAQ,CAACpC,EAAG;oBAAAgE,QAAA,EAC1C5B,QAAQ,CAACzC;kBAAI,GADHyC,QAAQ,CAACpC,EAAE;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAEAlI,SAAS,CAACE,IAAI,KAAK,iBAAiB,iBACnC7B,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB7J,OAAA;kBACEoF,IAAI,EAAC,YAAY;kBACjBnC,KAAK,EAAEtB,SAAS,CAACK,UAAW;kBAC5BgI,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBAAAX,QAAA,gBAERzJ,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAwG,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCpJ,SAAS,CAACwJ,GAAG,CAACjC,QAAQ,iBACrBhI,OAAA;oBAA0BiD,KAAK,EAAE+E,QAAQ,CAACvC,EAAG;oBAAAgE,QAAA,EAC1CzB,QAAQ,CAAC5C;kBAAI,GADH4C,QAAQ,CAACvC,EAAE;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,eAED7J,OAAA;gBAAKwJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzJ,OAAA;kBAAAyJ,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B7J,OAAA;kBACEoF,IAAI,EAAC,QAAQ;kBACbnC,KAAK,EAAEtB,SAAS,CAACW,MAAO;kBACxB0H,QAAQ,EAAE9E,iBAAkB;kBAC5BkF,QAAQ;kBAAAX,QAAA,gBAERzJ,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAwG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnCxG,aAAa,CAAC4G,GAAG,CAAC3H,MAAM,iBACvBtC,OAAA;oBAAqBiD,KAAK,EAAEX,MAAO;oBAAAmH,QAAA,EAChCnH;kBAAM,GADIA,MAAM;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7J,OAAA;YAAKwJ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzJ,OAAA;cAAKwJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzJ,OAAA;gBAAAyJ,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB7J,OAAA;gBAAQ6B,IAAI,EAAC,QAAQ;gBAACqI,OAAO,EAAE3D,OAAQ;gBAACiD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7J,OAAA;cAAKwJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzJ,OAAA;gBAAKwJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BzJ,OAAA;kBAAAyJ,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB7J,OAAA;kBAAAyJ,QAAA,EAAK;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjB7J,OAAA;kBAAAyJ,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB7J,OAAA;kBAAAyJ,QAAA,EAAK;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnB7J,OAAA;kBAAAyJ,QAAA,EAAK;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChB7J,OAAA;kBAAAyJ,QAAA,EAAK;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EAELlI,SAAS,CAACa,KAAK,CAACyH,GAAG,CAAC,CAAC5D,IAAI,EAAET,KAAK,kBAC/B5F,OAAA;gBAAiBwJ,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACnCzJ,OAAA;kBACEiD,KAAK,EAAEoD,IAAI,CAAC5D,SAAU;kBACtBuH,QAAQ,EAAG7E,CAAC,IAAKQ,gBAAgB,CAACC,KAAK,EAAE,WAAW,EAAET,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;kBACtEmH,QAAQ;kBAAAX,QAAA,gBAERzJ,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAwG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnClJ,QAAQ,CAACsJ,GAAG,CAAC9B,OAAO,iBACnBnI,OAAA;oBAAyBiD,KAAK,EAAEkF,OAAO,CAAC1C,EAAG;oBAAAgE,QAAA,EACxCtB,OAAO,CAAC/C;kBAAI,GADF+C,OAAO,CAAC1C,EAAE;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACT7J,OAAA;kBACE6B,IAAI,EAAC,QAAQ;kBACbkI,WAAW,EAAC,sCAAQ;kBACpB9G,KAAK,EAAEoD,IAAI,CAAC3D,QAAS;kBACrBsH,QAAQ,EAAG7E,CAAC,IAAKQ,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAET,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;kBACrEqH,GAAG,EAAC,GAAG;kBACPF,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF7J,OAAA;kBACE6B,IAAI,EAAC,QAAQ;kBACbkI,WAAW,EAAC,gCAAO;kBACnB9G,KAAK,EAAEoD,IAAI,CAAC1D,KAAM;kBAClBqH,QAAQ,EAAG7E,CAAC,IAAKQ,gBAAgB,CAACC,KAAK,EAAE,OAAO,EAAET,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAE;kBAClEsH,IAAI,EAAC,MAAM;kBACXD,GAAG,EAAC,GAAG;kBACPF,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF7J,OAAA;kBACE6B,IAAI,EAAC,QAAQ;kBACboB,KAAK,EAAEoD,IAAI,CAACzD,KAAM;kBAClByH,QAAQ;kBACRb,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACF7J,OAAA;kBACE6B,IAAI,EAAC,MAAM;kBACXkI,WAAW,EAAC,+DAAa;kBACzB9G,KAAK,EAAEoD,IAAI,CAAC/D,MAAO;kBACnB0H,QAAQ,EAAG7E,CAAC,IAAKQ,gBAAgB,CAACC,KAAK,EAAE,QAAQ,EAAET,CAAC,CAACE,MAAM,CAACpC,KAAK;gBAAE;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,EACDlI,SAAS,CAACa,KAAK,CAACiE,MAAM,GAAG,CAAC,iBACzBzG,OAAA;kBACE6B,IAAI,EAAC,QAAQ;kBACbqI,OAAO,EAAEA,CAAA,KAAM1D,UAAU,CAACZ,KAAK,CAAE;kBACjC4D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GAlDOjE,KAAK;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7J,OAAA;cAAKwJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzJ,OAAA;gBAAKwJ,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzJ,OAAA;kBAAAyJ,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B7J,OAAA;kBAAAyJ,QAAA,GAAO9H,SAAS,CAACkB,QAAQ,CAACiH,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACN7J,OAAA;gBAAKwJ,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzJ,OAAA;kBAAAyJ,QAAA,EAAM;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7J,OAAA;kBAAAyJ,QAAA,GAAO9H,SAAS,CAACmB,SAAS,CAACgH,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN7J,OAAA;gBAAKwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCzJ,OAAA;kBAAAyJ,QAAA,EAAM;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtB7J,OAAA;kBAAAyJ,QAAA,GAAO9H,SAAS,CAACiB,KAAK,CAACkH,cAAc,CAAC,CAAC,EAAC,2BAAK;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7J,OAAA;YAAKwJ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzJ,OAAA;cAAAyJ,QAAA,EAAO;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtB7J,OAAA;cACEoF,IAAI,EAAC,OAAO;cACZnC,KAAK,EAAEtB,SAAS,CAACoB,KAAM;cACvBiH,QAAQ,EAAE9E,iBAAkB;cAC5BsF,IAAI,EAAC,GAAG;cACRT,WAAW,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7J,OAAA;YAAKwJ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzJ,OAAA;cAAQ6B,IAAI,EAAC,QAAQ;cAACqI,OAAO,EAAEhD,SAAU;cAACsC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7J,OAAA;cAAQ6B,IAAI,EAAC,QAAQ;cAAC4I,QAAQ,EAAElJ,OAAQ;cAACiI,SAAS,EAAC,UAAU;cAAAC,QAAA,EAC1DlI,OAAO,GAAG,iBAAiB,GAAIR,aAAa,GAAG,UAAU,GAAG;YAAS;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7J,OAAA;MAAKwJ,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCzJ,OAAA;QAAOwJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9BzJ,OAAA;UAAAyJ,QAAA,eACEzJ,OAAA;YAAAyJ,QAAA,gBACEzJ,OAAA;cAAAyJ,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB7J,OAAA;cAAAyJ,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd7J,OAAA;cAAAyJ,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB7J,OAAA;cAAAyJ,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB7J,OAAA;cAAAyJ,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd7J,OAAA;cAAAyJ,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf7J,OAAA;cAAAyJ,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACf7J,OAAA;cAAAyJ,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7J,OAAA;UAAAyJ,QAAA,EACGZ,eAAe,CAACpC,MAAM,KAAK,CAAC,gBAC3BzG,OAAA;YAAAyJ,QAAA,eACEzJ,OAAA;cAAI0K,OAAO,EAAC,GAAG;cAAClB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELhB,eAAe,CAACoB,GAAG,CAAC5C,UAAU,IAAI;YAChC,MAAMsD,QAAQ,GAAGnC,WAAW,CAACnB,UAAU,CAACxF,IAAI,CAAC;YAC7C,MAAM+I,UAAU,GAAGjC,aAAa,CAACtB,UAAU,CAAC9E,MAAM,CAAC;YACnD,MAAMsI,UAAU,GAAGxD,UAAU,CAACxF,IAAI,KAAK,cAAc,GACnD+F,eAAe,CAACP,UAAU,CAACtF,UAAU,CAAC,GACtCgG,eAAe,CAACV,UAAU,CAACrF,UAAU,CAAC;YAExC,oBACEhC,OAAA;cAAAyJ,QAAA,gBACEzJ,OAAA;gBAAIwJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEpC,UAAU,CAACpF;cAAY;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5D7J,OAAA;gBAAAyJ,QAAA,eACEzJ,OAAA;kBACEwJ,SAAS,EAAC,YAAY;kBACtBsB,KAAK,EAAE;oBAAEC,eAAe,EAAEJ,QAAQ,CAACxH;kBAAM,CAAE;kBAAAsG,QAAA,EAE1CkB,QAAQ,CAACzH;gBAAK;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7J,OAAA;gBAAAyJ,QAAA,EAAK,IAAItH,IAAI,CAACkF,UAAU,CAACnF,UAAU,CAAC,CAAC8I,kBAAkB,CAAC,OAAO;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtE7J,OAAA;gBAAAyJ,QAAA,EAAKoB;cAAU;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrB7J,OAAA;gBAAAyJ,QAAA,EAAKpC,UAAU,CAAC/E;cAAM;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5B7J,OAAA;gBAAIwJ,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAAEpC,UAAU,CAACzE,KAAK,CAACkH,cAAc,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE7J,OAAA;gBAAAyJ,QAAA,eACEzJ,OAAA;kBACEwJ,SAAS,EAAC,cAAc;kBACxBsB,KAAK,EAAE;oBAAEC,eAAe,EAAEH,UAAU,CAACzH;kBAAM,CAAE;kBAAAsG,QAAA,EAE5CmB,UAAU,CAAC1H;gBAAK;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7J,OAAA;gBAAIwJ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACrBzJ,OAAA;kBACEkK,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACC,UAAU,CAAE;kBACtCmC,SAAS,EAAC,UAAU;kBACpByB,KAAK,EAAC,gCAAO;kBACbR,QAAQ,EAAEpD,UAAU,CAAC9E,MAAM,KAAK,WAAY;kBAAAkH,QAAA,EAC7C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRxC,UAAU,CAAC9E,MAAM,KAAK,UAAU,iBAC/BvC,OAAA;kBACEkK,OAAO,EAAEA,CAAA,KAAMxC,aAAa,CAACL,UAAU,CAAC5B,EAAE,CAAE;kBAC5C+D,SAAS,EAAC,aAAa;kBACvByB,KAAK,EAAC,sCAAQ;kBAAAxB,QAAA,EACf;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACD7J,OAAA;kBACEkK,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAACD,UAAU,CAAC5B,EAAE,CAAE;kBAC3C+D,SAAS,EAAC,YAAY;kBACtByB,KAAK,EAAC,oBAAK;kBACXR,QAAQ,EAAEpD,UAAU,CAAC9E,MAAM,KAAK,WAAY;kBAAAkH,QAAA,EAC7C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAhDExC,UAAU,CAAC5B,EAAE;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDlB,CAAC;UAET,CAAC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3J,EAAA,CAluBID,OAAO;AAAAiL,EAAA,GAAPjL,OAAO;AAouBb,eAAeA,OAAO;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}