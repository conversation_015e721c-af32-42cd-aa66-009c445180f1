// Test database functionality
import database from './database';

// Test function to verify database works
export const testDatabase = () => {
  console.log('🧪 اختبار قاعدة البيانات...');
  
  try {
    // Initialize database
    database.initializeDatabase();
    console.log('✅ تم تهيئة قاعدة البيانات');
    
    // Test users
    const users = database.getUsers();
    console.log('👥 المستخدمين:', users);
    
    // Test authentication
    const user = database.authenticateUser('admin', 'admin123');
    console.log('🔐 اختبار المصادقة:', user);
    
    if (user) {
      console.log('✅ تسجيل الدخول نجح!');
      return true;
    } else {
      console.log('❌ فشل تسجيل الدخول');
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار قاعدة البيانات:', error);
    return false;
  }
};

// Run test immediately
testDatabase();
