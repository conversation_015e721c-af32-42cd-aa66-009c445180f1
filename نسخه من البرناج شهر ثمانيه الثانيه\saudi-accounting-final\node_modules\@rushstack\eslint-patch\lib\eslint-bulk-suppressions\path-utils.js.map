{"version": 3, "file": "path-utils.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/path-utils.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAe3D,sEAqBC;AAED,8CAMC;AAED,gEAKC;AAjDD,4CAAoB;AACpB,4CAAoB;AACpB,gDAAoE;AACpE,2CAA8D;AAC9D,sEAAoD;AAOpD,MAAM,uBAAuB,GAAW,sBAAkB,CAAC,OAAO,CAAC;AAEnE,SAAgB,6BAA6B;IAC3C,MAAM,2BAA2B,GAAuB,OAAO,CAAC,GAAG,CAAC,2CAA+B,CAAC,CAAC;IACrG,IAAI,2BAA2B,KAAK,MAAM,IAAI,2BAA2B,KAAK,GAAG,EAAE,CAAC;QAClF,OAAO;IACT,CAAC;IAED,MAAM,cAAc,GAAW,6BAA6B,CAAC;IAC7D,MAAM,YAAY,GAAW,2BAA2B,CAAC;IAEzD,MAAM,aAAa,GAAmB;QACpC;;WAEG;QACH,aAAa,EAAE,OAAO;QACtB;;WAEG;QACH,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC;KACzD,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7E,CAAC;AAED,SAAgB,iBAAiB;IAC/B,IAAI,CAAC,0BAAY,EAAE,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,GAAG,0BAAY,uBAAuB,CAAC;AAChD,CAAC;AAED,SAAgB,0BAA0B;IACxC,MAAM,iBAAiB,GAAW,GAAG,YAAE,CAAC,MAAM,EAAE,0BAA0B,uBAAuB,UAAU,CAAC;IAC5G,YAAE,CAAC,SAAS,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACrD,MAAM,oBAAoB,GAAW,GAAG,iBAAiB,kBAAkB,kCAAoB,KAAK,CAAC;IACrG,OAAO,oBAAoB,CAAC;AAC9B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport fs from 'fs';\nimport os from 'os';\nimport { eslintFolder, eslintPackageVersion } from '../_patch-base';\nimport { ESLINT_BULK_DETECT_ENV_VAR_NAME } from './constants';\nimport currentPackageJson from '../../package.json';\n\ninterface IConfiguration {\n  minCliVersion: string;\n  cliEntryPoint: string;\n}\n\nconst CURRENT_PACKAGE_VERSION: string = currentPackageJson.version;\n\nexport function findAndConsoleLogPatchPathCli(): void {\n  const eslintBulkDetectEnvVarValue: string | undefined = process.env[ESLINT_BULK_DETECT_ENV_VAR_NAME];\n  if (eslintBulkDetectEnvVarValue !== 'true' && eslintBulkDetectEnvVarValue !== '1') {\n    return;\n  }\n\n  const startDelimiter: string = 'RUSHSTACK_ESLINT_BULK_START';\n  const endDelimiter: string = 'RUSHSTACK_ESLINT_BULK_END';\n\n  const configuration: IConfiguration = {\n    /**\n     * `@rushstack/eslint-bulk` should report an error if its package.json is older than this number\n     */\n    minCliVersion: '0.0.0',\n    /**\n     * `@rushstack/eslint-bulk` will invoke this entry point\n     */\n    cliEntryPoint: require.resolve('../exports/eslint-bulk')\n  };\n\n  console.log(startDelimiter + JSON.stringify(configuration) + endDelimiter);\n}\n\nexport function getPathToLinterJS(): string {\n  if (!eslintFolder) {\n    throw new Error('Cannot find ESLint installation to patch.');\n  }\n\n  return `${eslintFolder}/lib/linter/linter.js`;\n}\n\nexport function ensurePathToGeneratedPatch(): string {\n  const patchesFolderPath: string = `${os.tmpdir()}/rushstack-eslint-bulk-${CURRENT_PACKAGE_VERSION}/patches`;\n  fs.mkdirSync(patchesFolderPath, { recursive: true });\n  const pathToGeneratedPatch: string = `${patchesFolderPath}/linter-patch-v${eslintPackageVersion}.js`;\n  return pathToGeneratedPatch;\n}\n"]}