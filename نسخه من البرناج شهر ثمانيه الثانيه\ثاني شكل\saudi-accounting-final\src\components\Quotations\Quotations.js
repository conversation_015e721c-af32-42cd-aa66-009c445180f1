import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Quotations.css';

const Quotations = () => {
  const [quotations, setQuotations] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingQuotation, setEditingQuotation] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newQuotation, setNewQuotation] = useState({
    quotationNumber: '',
    customerId: '',
    quotationDate: new Date().toISOString().split('T')[0],
    validUntil: '',
    status: 'draft',
    items: [{ productId: '', quantity: 1, price: 0, discount: 0, total: 0 }],
    subtotal: 0,
    discountAmount: 0,
    vatAmount: 0,
    total: 0,
    terms: '',
    notes: '',
    paymentTerms: 'نقداً عند التسليم'
  });

  const quotationStatuses = [
    { value: 'draft', label: '📝 مسودة', color: '#6b7280' },
    { value: 'sent', label: '📤 مرسل', color: '#3b82f6' },
    { value: 'accepted', label: '✅ مقبول', color: '#10b981' },
    { value: 'rejected', label: '❌ مرفوض', color: '#ef4444' },
    { value: 'expired', label: '⏰ منتهي الصلاحية', color: '#f59e0b' },
    { value: 'converted', label: '🔄 تم التحويل لفاتورة', color: '#8b5cf6' }
  ];

  const paymentTermsOptions = [
    'نقداً عند التسليم',
    'الدفع خلال 15 يوم',
    'الدفع خلال 30 يوم',
    'الدفع خلال 60 يوم',
    'دفع مقدم 50%',
    'دفع على دفعات',
    'تحويل بنكي',
    'شروط خاصة'
  ];

  useEffect(() => {
    loadData();
    generateQuotationNumber();
  }, []);

  const loadData = () => {
    try {
      const quotationsData = database.getQuotations();
      const customersData = database.getCustomers();
      const productsData = database.getProducts();
      
      setQuotations(quotationsData);
      setCustomers(customersData);
      setProducts(productsData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const generateQuotationNumber = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    const quotationNumber = `QUO-${year}${month}${day}-${random}`;
    setNewQuotation(prev => ({ ...prev, quotationNumber }));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewQuotation(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...newQuotation.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Auto-fill product price
    if (field === 'productId' && value) {
      const selectedProduct = products.find(p => p.id === parseInt(value));
      if (selectedProduct) {
        updatedItems[index].price = selectedProduct.price;
      }
    }

    // Calculate item total
    if (field === 'quantity' || field === 'price' || field === 'discount') {
      const quantity = parseFloat(updatedItems[index].quantity) || 0;
      const price = parseFloat(updatedItems[index].price) || 0;
      const discount = parseFloat(updatedItems[index].discount) || 0;
      const itemTotal = (quantity * price) - discount;
      updatedItems[index].total = Math.max(0, itemTotal);
    }

    setNewQuotation(prev => ({ ...prev, items: updatedItems }));
    calculateTotals(updatedItems);
  };

  const calculateTotals = (items = newQuotation.items) => {
    const subtotal = items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
    const discountAmount = parseFloat(newQuotation.discountAmount) || 0;
    const afterDiscount = subtotal - discountAmount;
    const vatRate = 0.15; // 15% VAT
    const vatAmount = afterDiscount * vatRate;
    const total = afterDiscount + vatAmount;

    setNewQuotation(prev => ({
      ...prev,
      subtotal,
      vatAmount,
      total: Math.max(0, total)
    }));
  };

  const addItem = () => {
    setNewQuotation(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', quantity: 1, price: 0, discount: 0, total: 0 }]
    }));
  };

  const removeItem = (index) => {
    if (newQuotation.items.length > 1) {
      const updatedItems = newQuotation.items.filter((_, i) => i !== index);
      setNewQuotation(prev => ({ ...prev, items: updatedItems }));
      calculateTotals(updatedItems);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      // Set valid until date if not set (30 days from quotation date)
      let validUntil = newQuotation.validUntil;
      if (!validUntil) {
        const quotationDate = new Date(newQuotation.quotationDate);
        quotationDate.setDate(quotationDate.getDate() + 30);
        validUntil = quotationDate.toISOString().split('T')[0];
      }

      const quotationData = {
        ...newQuotation,
        validUntil,
        items: newQuotation.items.filter(item => item.productId && item.quantity > 0),
        customerId: parseInt(newQuotation.customerId)
      };

      if (editingQuotation) {
        await database.updateQuotation(editingQuotation.id, quotationData);
        setMessage('تم تحديث عرض السعر بنجاح!');
        setEditingQuotation(null);
      } else {
        await database.addQuotation(quotationData);
        setMessage('تم إضافة عرض السعر بنجاح!');
      }

      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ عرض السعر:', error);
      setMessage('خطأ في حفظ عرض السعر');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewQuotation({
      quotationNumber: '',
      customerId: '',
      quotationDate: new Date().toISOString().split('T')[0],
      validUntil: '',
      status: 'draft',
      items: [{ productId: '', quantity: 1, price: 0, discount: 0, total: 0 }],
      subtotal: 0,
      discountAmount: 0,
      vatAmount: 0,
      total: 0,
      terms: '',
      notes: '',
      paymentTerms: 'نقداً عند التسليم'
    });
    setShowAddForm(false);
    setEditingQuotation(null);
    generateQuotationNumber();
  };

  const handleEdit = (quotation) => {
    setNewQuotation({
      ...quotation,
      quotationDate: quotation.quotationDate.split('T')[0],
      validUntil: quotation.validUntil.split('T')[0]
    });
    setEditingQuotation(quotation);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف عرض السعر؟')) {
      try {
        await database.deleteQuotation(id);
        setMessage('تم حذف عرض السعر بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف عرض السعر:', error);
        setMessage('خطأ في حذف عرض السعر');
      }
    }
  };

  const convertToInvoice = async (quotationId) => {
    if (window.confirm('هل تريد تحويل عرض السعر إلى فاتورة؟')) {
      try {
        const quotation = quotations.find(q => q.id === quotationId);
        if (quotation) {
          // إنشاء فاتورة جديدة من عرض السعر
          const invoiceData = {
            customerId: quotation.customerId,
            items: quotation.items,
            subtotal: quotation.subtotal,
            vatAmount: quotation.vatAmount,
            total: quotation.total,
            notes: `تم التحويل من عرض السعر رقم: ${quotation.quotationNumber}`,
            paymentTerms: quotation.paymentTerms
          };

          await database.addInvoice(invoiceData);
          await database.updateQuotation(quotationId, { status: 'converted' });
          
          setMessage('تم تحويل عرض السعر إلى فاتورة بنجاح!');
          loadData();
          setTimeout(() => setMessage(''), 3000);
        }
      } catch (error) {
        console.error('خطأ في تحويل عرض السعر:', error);
        setMessage('خطأ في تحويل عرض السعر');
      }
    }
  };

  const printQuotation = (quotation) => {
    const customer = customers.find(c => c.id === quotation.customerId);
    const settings = database.getSettings();
    
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>عرض سعر - ${quotation.quotationNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
            .company-info { text-align: center; margin-bottom: 20px; }
            .quotation-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
            .items-table th { background-color: #f5f5f5; }
            .totals { text-align: left; margin-top: 20px; }
            .total-row { margin: 5px 0; }
            .final-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #333; padding-top: 10px; }
            .terms { margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>عرض سعر</h1>
            <h2>${settings.companyName}</h2>
          </div>
          
          <div class="company-info">
            <p><strong>العنوان:</strong> ${settings.address}</p>
            <p><strong>الهاتف:</strong> ${settings.phone} | <strong>البريد الإلكتروني:</strong> ${settings.email}</p>
            <p><strong>الرقم الضريبي:</strong> ${settings.vatNumber}</p>
          </div>

          <div class="quotation-info">
            <div>
              <p><strong>رقم عرض السعر:</strong> ${quotation.quotationNumber}</p>
              <p><strong>التاريخ:</strong> ${new Date(quotation.quotationDate).toLocaleDateString('ar-SA')}</p>
              <p><strong>صالح حتى:</strong> ${new Date(quotation.validUntil).toLocaleDateString('ar-SA')}</p>
            </div>
            <div>
              <p><strong>العميل:</strong> ${customer?.name || 'غير محدد'}</p>
              <p><strong>الهاتف:</strong> ${customer?.phone || ''}</p>
              <p><strong>العنوان:</strong> ${customer?.address || ''}</p>
            </div>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th>م</th>
                <th>الصنف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الخصم</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              ${quotation.items.map((item, index) => {
                const product = products.find(p => p.id === item.productId);
                return `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${product?.name || 'غير محدد'}</td>
                    <td>${item.quantity}</td>
                    <td>${item.price.toLocaleString()} ريال</td>
                    <td>${item.discount.toLocaleString()} ريال</td>
                    <td>${item.total.toLocaleString()} ريال</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <div class="totals">
            <div class="total-row">المجموع الفرعي: ${quotation.subtotal.toLocaleString()} ريال</div>
            <div class="total-row">الخصم: ${quotation.discountAmount.toLocaleString()} ريال</div>
            <div class="total-row">ضريبة القيمة المضافة (15%): ${quotation.vatAmount.toLocaleString()} ريال</div>
            <div class="total-row final-total">الإجمالي: ${quotation.total.toLocaleString()} ريال</div>
          </div>

          ${quotation.terms ? `
            <div class="terms">
              <h4>الشروط والأحكام:</h4>
              <p>${quotation.terms}</p>
            </div>
          ` : ''}

          ${quotation.paymentTerms ? `
            <div class="terms">
              <h4>شروط الدفع:</h4>
              <p>${quotation.paymentTerms}</p>
            </div>
          ` : ''}

          ${quotation.notes ? `
            <div class="terms">
              <h4>ملاحظات:</h4>
              <p>${quotation.notes}</p>
            </div>
          ` : ''}

          <div style="margin-top: 50px; text-align: center; font-size: 0.9em; color: #666;">
            <p>شكراً لتعاملكم معنا</p>
            <p>هذا عرض سعر وليس فاتورة ضريبية</p>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    setTimeout(() => printWindow.print(), 500);
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
  };

  const getProductName = (productId) => {
    const product = products.find(p => p.id === parseInt(productId));
    return product ? product.name : 'غير محدد';
  };

  const getStatusInfo = (status) => {
    const statusObj = quotationStatuses.find(s => s.value === status);
    return statusObj || { label: status, color: '#64748b' };
  };

  const filteredQuotations = quotations.filter(quotation => {
    const matchesSearch = 
      quotation.quotationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getCustomerName(quotation.customerId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || quotation.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const totalQuotations = filteredQuotations.reduce((sum, q) => sum + q.total, 0);
  const acceptedQuotations = filteredQuotations.filter(q => q.status === 'accepted');
  const pendingQuotations = filteredQuotations.filter(q => q.status === 'sent');

  return (
    <div className="quotations-container">
      <div className="quotations-header">
        <h1>💼 عروض الأسعار</h1>
        <p>إدارة وتتبع عروض الأسعار للعملاء</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">💼</div>
          <div className="stat-info">
            <h3>إجمالي العروض</h3>
            <p>{quotations.length} عرض</p>
          </div>
        </div>
        <div className="stat-card accepted">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <h3>عروض مقبولة</h3>
            <p>{acceptedQuotations.length} عرض</p>
          </div>
        </div>
        <div className="stat-card pending">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <h3>عروض معلقة</h3>
            <p>{pendingQuotations.length} عرض</p>
          </div>
        </div>
        <div className="stat-card amount">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي القيمة</h3>
            <p>{totalQuotations.toLocaleString()} ريال</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="quotations-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في عروض الأسعار..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الحالات</option>
            {quotationStatuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="add-quotation-btn"
        >
          ➕ إضافة عرض سعر جديد
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="quotation-form-modal">
            <div className="modal-header">
              <h3>{editingQuotation ? '✏️ تعديل عرض السعر' : '➕ إضافة عرض سعر جديد'}</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="quotation-form">
              <div className="form-header">
                <div className="form-grid">
                  <div className="form-group">
                    <label>رقم عرض السعر</label>
                    <input
                      type="text"
                      name="quotationNumber"
                      value={newQuotation.quotationNumber}
                      onChange={handleInputChange}
                      required
                      readOnly
                    />
                  </div>

                  <div className="form-group">
                    <label>العميل</label>
                    <select
                      name="customerId"
                      value={newQuotation.customerId}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">اختر العميل</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>تاريخ العرض</label>
                    <input
                      type="date"
                      name="quotationDate"
                      value={newQuotation.quotationDate}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label>صالح حتى</label>
                    <input
                      type="date"
                      name="validUntil"
                      value={newQuotation.validUntil}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="form-group">
                    <label>الحالة</label>
                    <select
                      name="status"
                      value={newQuotation.status}
                      onChange={handleInputChange}
                      required
                    >
                      {quotationStatuses.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>شروط الدفع</label>
                    <select
                      name="paymentTerms"
                      value={newQuotation.paymentTerms}
                      onChange={handleInputChange}
                    >
                      {paymentTermsOptions.map(term => (
                        <option key={term} value={term}>
                          {term}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Items Section */}
              <div className="items-section">
                <div className="section-header">
                  <h4>📦 أصناف العرض</h4>
                  <button type="button" onClick={addItem} className="add-item-btn">
                    ➕ إضافة صنف
                  </button>
                </div>

                <div className="items-table">
                  <div className="items-header">
                    <div>الصنف</div>
                    <div>الكمية</div>
                    <div>السعر</div>
                    <div>الخصم</div>
                    <div>الإجمالي</div>
                    <div>إجراءات</div>
                  </div>

                  {newQuotation.items.map((item, index) => (
                    <div key={index} className="item-row">
                      <select
                        value={item.productId}
                        onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                        required
                      >
                        <option value="">اختر الصنف</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name} - {product.price} ريال
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        placeholder="الكمية"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                        min="1"
                        required
                      />
                      <input
                        type="number"
                        placeholder="السعر"
                        value={item.price}
                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                        step="0.01"
                        min="0"
                        required
                      />
                      <input
                        type="number"
                        placeholder="الخصم"
                        value={item.discount}
                        onChange={(e) => handleItemChange(index, 'discount', e.target.value)}
                        step="0.01"
                        min="0"
                      />
                      <input
                        type="number"
                        value={item.total}
                        readOnly
                        className="total-field"
                      />
                      {newQuotation.items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="remove-item-btn"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                <div className="discount-section">
                  <div className="form-group">
                    <label>خصم إضافي على الإجمالي</label>
                    <input
                      type="number"
                      name="discountAmount"
                      value={newQuotation.discountAmount}
                      onChange={(e) => {
                        handleInputChange(e);
                        calculateTotals();
                      }}
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="totals-section">
                  <div className="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{newQuotation.subtotal.toLocaleString()} ريال</span>
                  </div>
                  <div className="total-row">
                    <span>الخصم:</span>
                    <span>{newQuotation.discountAmount.toLocaleString()} ريال</span>
                  </div>
                  <div className="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{newQuotation.vatAmount.toLocaleString()} ريال</span>
                  </div>
                  <div className="total-row final-total">
                    <span>الإجمالي:</span>
                    <span>{newQuotation.total.toLocaleString()} ريال</span>
                  </div>
                </div>
              </div>

              <div className="form-group full-width">
                <label>الشروط والأحكام</label>
                <textarea
                  name="terms"
                  value={newQuotation.terms}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="الشروط والأحكام الخاصة بالعرض..."
                />
              </div>

              <div className="form-group full-width">
                <label>ملاحظات</label>
                <textarea
                  name="notes"
                  value={newQuotation.notes}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="ملاحظات إضافية..."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : (editingQuotation ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Quotations Table */}
      <div className="quotations-table-container">
        <table className="quotations-table">
          <thead>
            <tr>
              <th>رقم العرض</th>
              <th>العميل</th>
              <th>التاريخ</th>
              <th>صالح حتى</th>
              <th>المبلغ</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredQuotations.length === 0 ? (
              <tr>
                <td colSpan="7" className="no-data">
                  لا توجد عروض أسعار مطابقة للبحث
                </td>
              </tr>
            ) : (
              filteredQuotations.map(quotation => {
                const statusInfo = getStatusInfo(quotation.status);
                const isExpired = new Date(quotation.validUntil) < new Date();
                
                return (
                  <tr key={quotation.id} className={isExpired ? 'expired' : ''}>
                    <td className="quotation-number">{quotation.quotationNumber}</td>
                    <td>{getCustomerName(quotation.customerId)}</td>
                    <td>{new Date(quotation.quotationDate).toLocaleDateString('ar-SA')}</td>
                    <td className={isExpired ? 'expired-date' : ''}>
                      {new Date(quotation.validUntil).toLocaleDateString('ar-SA')}
                      {isExpired && ' ⚠️'}
                    </td>
                    <td className="amount">{quotation.total.toLocaleString()} ريال</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: statusInfo.color }}
                      >
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(quotation)}
                        className="edit-btn"
                        title="تعديل"
                        disabled={quotation.status === 'converted'}
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => printQuotation(quotation)}
                        className="print-btn"
                        title="طباعة"
                      >
                        🖨️
                      </button>
                      {quotation.status === 'accepted' && (
                        <button
                          onClick={() => convertToInvoice(quotation.id)}
                          className="convert-btn"
                          title="تحويل لفاتورة"
                        >
                          🔄
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(quotation.id)}
                        className="delete-btn"
                        title="حذف"
                        disabled={quotation.status === 'converted'}
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Quotations;
