/* تقارير النظام المحاسبي السعودي */
.reports-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* رأس التقارير */
.reports-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
}

.back-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.reports-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  text-align: center;
  flex: 1;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  font-weight: 500;
}

.print-btn {
  background: #17a2b8;
  color: white;
}

.print-btn:hover {
  background: #138496;
  transform: translateY(-2px);
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover {
  background: #218838;
  transform: translateY(-2px);
}

/* تبويبات التقارير */
.reports-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s;
  white-space: nowrap;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* محتوى التقارير */
.reports-content {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card.sales {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card.vat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.invoices {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

.stat-value {
  margin: 0;
  font-size: 1.8rem;
  font-weight: bold;
}

/* تقرير المنتجات */
.products-report {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.report-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  transition: all 0.3s;
}

.product-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.product-item.low-stock {
  border-left-color: #dc3545;
  background: #fff5f5;
}

.product-item.low-stock:hover {
  background: #ffe6e6;
}

.rank {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 50%;
  font-size: 0.8rem;
  font-weight: bold;
  min-width: 24px;
  text-align: center;
}

.name {
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  margin: 0 10px;
}

.quantity, .stock {
  color: #6c757d;
  font-size: 0.9rem;
}

.revenue, .price {
  color: #28a745;
  font-weight: 600;
}

/* سجل الفواتير */
.invoices-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.invoices-header h3 {
  margin: 0;
  color: #2c3e50;
}

.date-filter {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.date-filter:focus {
  outline: none;
  border-color: #007bff;
}

.invoices-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.invoice-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #007bff;
  transition: all 0.3s;
}

.invoice-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.invoice-id {
  font-weight: bold;
  color: #007bff;
  font-size: 1.1rem;
}

.invoice-date {
  color: #6c757d;
  font-size: 0.9rem;
}

.invoice-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.customer {
  color: #2c3e50;
  font-weight: 500;
}

.total {
  color: #28a745;
  font-weight: bold;
}

.payment {
  color: #6c757d;
  font-size: 0.9rem;
}

/* رسالة عدم وجود بيانات */
.no-data {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .reports-container {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .reports-tabs {
    flex-direction: column;
  }

  .tab-btn {
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .products-report {
    grid-template-columns: 1fr;
  }

  .invoice-details {
    flex-direction: column;
    align-items: flex-start;
  }

  .invoices-header {
    flex-direction: column;
    align-items: stretch;
  }

  .date-filter {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .reports-header {
    padding: 15px;
  }

  .reports-content {
    padding: 15px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    width: 100%;
  }
}
