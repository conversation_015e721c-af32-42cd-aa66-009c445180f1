{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Dashboard.css';\nimport database from '../../utils/database';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = ({\n  user,\n  onLogout,\n  onNavigate\n}) => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // تحديث الوقت كل ثانية\n  React.useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // إحصائيات من قاعدة البيانات\n  const [stats, setStats] = useState({\n    totalSales: 0,\n    todaySales: 0,\n    totalCustomers: 0,\n    pendingInvoices: 0\n  });\n\n  // تحميل الإحصائيات\n  useEffect(() => {\n    try {\n      const todayStats = database.getTodayStats();\n      const monthStats = database.getMonthStats();\n      const customers = database.getCustomers();\n      setStats({\n        totalSales: monthStats.totalSales || 0,\n        todaySales: todayStats.totalSales || 0,\n        totalCustomers: customers.length || 0,\n        pendingInvoices: 0 // يمكن إضافة هذه الميزة لاحقاً\n      });\n    } catch (error) {\n      console.error('خطأ في تحميل الإحصائيات:', error);\n    }\n  }, []);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const formatTime = date => {\n    return date.toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"dashboard-title\",\n            children: \"\\uD83E\\uDDEE \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"current-time\",\n            children: formatTime(currentTime)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-name\",\n              children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-role\",\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onLogout,\n            className: \"logout-btn\",\n            children: \"\\uD83D\\uDEAA \\u062A\\u0633\\u062C\\u064A\\u0644 \\u062E\\u0631\\u0648\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"stats-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card sales\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: formatCurrency(stats.totalSales)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card today\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: formatCurrency(stats.todaySales)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+8.2%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card customers\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: stats.totalCustomers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change positive\",\n                children: \"+5 \\u062C\\u062F\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card invoices\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0645\\u0639\\u0644\\u0642\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: stats.pendingInvoices\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-change neutral\",\n                children: \"\\u0628\\u062D\\u0627\\u062C\\u0629 \\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"actions-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"\\u26A1 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"actions-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            onClick: () => onNavigate('pos'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0639\\u0631\\u0636 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0648\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            onClick: () => onNavigate('reports'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629 \\u0648\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              children: \"\\uD83D\\uDCB3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"izotMVnKeuUqva/7Udv+K+GDBrw=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Dashboard", "user", "onLogout", "onNavigate", "_s", "currentTime", "setCurrentTime", "Date", "timer", "setInterval", "clearInterval", "stats", "setStats", "totalSales", "todaySales", "totalCustomers", "pendingInvoices", "todayStats", "getTodayStats", "monthStats", "getMonthStats", "customers", "getCustomers", "length", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatTime", "date", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "second", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Dashboard/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Dashboard.css';\nimport database from '../../utils/database';\n\nconst Dashboard = ({ user, onLogout, onNavigate }) => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // تحديث الوقت كل ثانية\n  React.useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  // إحصائيات من قاعدة البيانات\n  const [stats, setStats] = useState({\n    totalSales: 0,\n    todaySales: 0,\n    totalCustomers: 0,\n    pendingInvoices: 0\n  });\n\n  // تحميل الإحصائيات\n  useEffect(() => {\n    try {\n      const todayStats = database.getTodayStats();\n      const monthStats = database.getMonthStats();\n      const customers = database.getCustomers();\n\n      setStats({\n        totalSales: monthStats.totalSales || 0,\n        todaySales: todayStats.totalSales || 0,\n        totalCustomers: customers.length || 0,\n        pendingInvoices: 0 // يمكن إضافة هذه الميزة لاحقاً\n      });\n    } catch (error) {\n      console.error('خطأ في تحميل الإحصائيات:', error);\n    }\n  }, []);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      {/* شريط التنقل العلوي */}\n      <header className=\"dashboard-header\">\n        <div className=\"header-content\">\n          <div className=\"header-left\">\n            <h1 className=\"dashboard-title\">🧮 نظام المحاسبة السعودي</h1>\n            <p className=\"current-time\">{formatTime(currentTime)}</p>\n          </div>\n          <div className=\"header-right\">\n            <div className=\"user-info\">\n              <span className=\"user-name\">مرحباً، {user.name}</span>\n              <span className=\"user-role\">{user.role}</span>\n            </div>\n            <button onClick={onLogout} className=\"logout-btn\">\n              🚪 تسجيل خروج\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* المحتوى الرئيسي */}\n      <main className=\"dashboard-main\">\n        {/* بطاقات الإحصائيات */}\n        <section className=\"stats-section\">\n          <h2 className=\"section-title\">📊 الإحصائيات السريعة</h2>\n          <div className=\"stats-grid\">\n            <div className=\"stat-card sales\">\n              <div className=\"stat-icon\">💰</div>\n              <div className=\"stat-content\">\n                <h3>إجمالي المبيعات</h3>\n                <p className=\"stat-value\">{formatCurrency(stats.totalSales)}</p>\n                <span className=\"stat-change positive\">+12.5%</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card today\">\n              <div className=\"stat-icon\">📈</div>\n              <div className=\"stat-content\">\n                <h3>مبيعات اليوم</h3>\n                <p className=\"stat-value\">{formatCurrency(stats.todaySales)}</p>\n                <span className=\"stat-change positive\">+8.2%</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card customers\">\n              <div className=\"stat-icon\">👥</div>\n              <div className=\"stat-content\">\n                <h3>إجمالي العملاء</h3>\n                <p className=\"stat-value\">{stats.totalCustomers}</p>\n                <span className=\"stat-change positive\">+5 جديد</span>\n              </div>\n            </div>\n\n            <div className=\"stat-card invoices\">\n              <div className=\"stat-icon\">📋</div>\n              <div className=\"stat-content\">\n                <h3>فواتير معلقة</h3>\n                <p className=\"stat-value\">{stats.pendingInvoices}</p>\n                <span className=\"stat-change neutral\">بحاجة متابعة</span>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* الإجراءات السريعة */}\n        <section className=\"actions-section\">\n          <h2 className=\"section-title\">⚡ الإجراءات السريعة</h2>\n          <div className=\"actions-grid\">\n            <button\n              className=\"action-card\"\n              onClick={() => onNavigate('pos')}\n            >\n              <div className=\"action-icon\">🛒</div>\n              <h3>نقطة البيع</h3>\n              <p>إنشاء فاتورة جديدة</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">📦</div>\n              <h3>إدارة المخزون</h3>\n              <p>عرض وإدارة المنتجات</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">👤</div>\n              <h3>إدارة العملاء</h3>\n              <p>إضافة وتعديل العملاء</p>\n            </button>\n\n            <button\n              className=\"action-card\"\n              onClick={() => onNavigate('reports')}\n            >\n              <div className=\"action-icon\">📊</div>\n              <h3>التقارير</h3>\n              <p>عرض التقارير المالية والإحصائيات</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">⚙️</div>\n              <h3>الإعدادات</h3>\n              <p>إعدادات النظام</p>\n            </button>\n\n            <button className=\"action-card\">\n              <div className=\"action-icon\">💳</div>\n              <h3>المدفوعات</h3>\n              <p>إدارة المدفوعات</p>\n            </button>\n          </div>\n        </section>\n      </main>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,iBAAiB;AACxB,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,IAAIY,IAAI,CAAC,CAAC,CAAC;;EAE1D;EACAb,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMY,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BH,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC;IACjCkB,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACApB,SAAS,CAAC,MAAM;IACd,IAAI;MACF,MAAMqB,UAAU,GAAGpB,QAAQ,CAACqB,aAAa,CAAC,CAAC;MAC3C,MAAMC,UAAU,GAAGtB,QAAQ,CAACuB,aAAa,CAAC,CAAC;MAC3C,MAAMC,SAAS,GAAGxB,QAAQ,CAACyB,YAAY,CAAC,CAAC;MAEzCV,QAAQ,CAAC;QACPC,UAAU,EAAEM,UAAU,CAACN,UAAU,IAAI,CAAC;QACtCC,UAAU,EAAEG,UAAU,CAACJ,UAAU,IAAI,CAAC;QACtCE,cAAc,EAAEM,SAAS,CAACE,MAAM,IAAI,CAAC;QACrCP,eAAe,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAClCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE3C,OAAA;IAAK4C,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElC7C,OAAA;MAAQ4C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAClC7C,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7C,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7C,OAAA;YAAI4C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DjD,OAAA;YAAG4C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEX,UAAU,CAAC5B,WAAW;UAAC;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7C,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAM4C,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,6CAAQ,EAAC3C,IAAI,CAACgD,IAAI;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDjD,OAAA;cAAM4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAE3C,IAAI,CAACiD;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNjD,OAAA;YAAQoD,OAAO,EAAEjD,QAAS;YAACyC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTjD,OAAA;MAAM4C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE9B7C,OAAA;QAAS4C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAChC7C,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDjD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7C,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B7C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCjD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7C,OAAA;gBAAA6C,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBjD,OAAA;gBAAG4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElB,cAAc,CAACf,KAAK,CAACE,UAAU;cAAC;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEjD,OAAA;gBAAM4C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B7C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCjD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7C,OAAA;gBAAA6C,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBjD,OAAA;gBAAG4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElB,cAAc,CAACf,KAAK,CAACG,UAAU;cAAC;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEjD,OAAA;gBAAM4C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAK4C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCjD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7C,OAAA;gBAAA6C,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBjD,OAAA;gBAAG4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEjC,KAAK,CAACI;cAAc;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDjD,OAAA;gBAAM4C,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAK4C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC7C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCjD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7C,OAAA;gBAAA6C,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBjD,OAAA;gBAAG4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEjC,KAAK,CAACK;cAAe;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDjD,OAAA;gBAAM4C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjD,OAAA;QAAS4C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAClC7C,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDjD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7C,OAAA;YACE4C,SAAS,EAAC,aAAa;YACvBQ,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,KAAK,CAAE;YAAAyC,QAAA,gBAEjC7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBjD,OAAA;cAAA6C,QAAA,EAAG;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAETjD,OAAA;YAAQ4C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7B7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBjD,OAAA;cAAA6C,QAAA,EAAG;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAETjD,OAAA;YAAQ4C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7B7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBjD,OAAA;cAAA6C,QAAA,EAAG;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAETjD,OAAA;YACE4C,SAAS,EAAC,aAAa;YACvBQ,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,SAAS,CAAE;YAAAyC,QAAA,gBAErC7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBjD,OAAA;cAAA6C,QAAA,EAAG;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAETjD,OAAA;YAAQ4C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7B7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBjD,OAAA;cAAA6C,QAAA,EAAG;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAETjD,OAAA;YAAQ4C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC7B7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCjD,OAAA;cAAA6C,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBjD,OAAA;cAAA6C,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA5KIJ,SAAS;AAAAoD,EAAA,GAATpD,SAAS;AA8Kf,eAAeA,SAAS;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}