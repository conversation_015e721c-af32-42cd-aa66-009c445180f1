import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Settings.css';

const Settings = () => {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [activeTab, setActiveTab] = useState('company');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    try {
      const currentSettings = database.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
      setMessage('خطأ في تحميل الإعدادات');
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      await database.updateSettings(settings);
      setMessage('تم حفظ الإعدادات بنجاح!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      setMessage('خطأ في حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين الإعدادات للقيم الافتراضية؟')) {
      const defaultSettings = database.getDefaultSettings();
      setSettings(defaultSettings);
      setMessage('تم إعادة تعيين الإعدادات للقيم الافتراضية');
    }
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'settings-backup.json';
    link.click();
    URL.revokeObjectURL(url);
    setMessage('تم تصدير الإعدادات بنجاح!');
  };

  const importSettings = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedSettings = JSON.parse(event.target.result);
          setSettings(importedSettings);
          setMessage('تم استيراد الإعدادات بنجاح!');
        } catch (error) {
          setMessage('خطأ في استيراد الإعدادات - تأكد من صحة الملف');
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h1>⚙️ إعدادات النظام</h1>
        <p>إدارة إعدادات الشركة والنظام</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <div className="settings-tabs">
        <button 
          className={`tab-button ${activeTab === 'company' ? 'active' : ''}`}
          onClick={() => setActiveTab('company')}
        >
          🏢 بيانات الشركة
        </button>
        <button 
          className={`tab-button ${activeTab === 'invoice' ? 'active' : ''}`}
          onClick={() => setActiveTab('invoice')}
        >
          🧾 إعدادات الفواتير
        </button>
        <button 
          className={`tab-button ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => setActiveTab('system')}
        >
          💻 إعدادات النظام
        </button>
        <button 
          className={`tab-button ${activeTab === 'backup' ? 'active' : ''}`}
          onClick={() => setActiveTab('backup')}
        >
          💾 النسخ الاحتياطي
        </button>
      </div>

      <form onSubmit={handleSubmit} className="settings-form">
        {activeTab === 'company' && (
          <div className="tab-content">
            <h3>📋 معلومات الشركة</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>اسم الشركة</label>
                <input
                  type="text"
                  name="companyName"
                  value={settings.companyName || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label>الرقم الضريبي</label>
                <input
                  type="text"
                  name="vatNumber"
                  value={settings.vatNumber || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label>العنوان</label>
                <textarea
                  name="address"
                  value={settings.address || ''}
                  onChange={handleInputChange}
                  rows="3"
                  required
                />
              </div>
              <div className="form-group">
                <label>رقم الهاتف</label>
                <input
                  type="tel"
                  name="phone"
                  value={settings.phone || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  name="email"
                  value={settings.email || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div className="form-group">
                <label>الموقع الإلكتروني</label>
                <input
                  type="url"
                  name="website"
                  value={settings.website || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'invoice' && (
          <div className="tab-content">
            <h3>🧾 إعدادات الفواتير</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>بادئة رقم الفاتورة</label>
                <input
                  type="text"
                  name="invoicePrefix"
                  value={settings.invoicePrefix || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label>رقم الفاتورة التالي</label>
                <input
                  type="number"
                  name="nextInvoiceNumber"
                  value={settings.nextInvoiceNumber || 1}
                  onChange={handleInputChange}
                  min="1"
                  required
                />
              </div>
              <div className="form-group">
                <label>معدل ضريبة القيمة المضافة (%)</label>
                <input
                  type="number"
                  name="vatRate"
                  value={settings.vatRate || 15}
                  onChange={handleInputChange}
                  min="0"
                  max="100"
                  step="0.01"
                  required
                />
              </div>
              <div className="form-group">
                <label>العملة</label>
                <select
                  name="currency"
                  value={settings.currency || 'SAR'}
                  onChange={handleInputChange}
                >
                  <option value="SAR">ريال سعودي (SAR)</option>
                  <option value="USD">دولار أمريكي (USD)</option>
                  <option value="EUR">يورو (EUR)</option>
                  <option value="AED">درهم إماراتي (AED)</option>
                </select>
              </div>
              <div className="form-group">
                <label>شروط الدفع الافتراضية</label>
                <textarea
                  name="defaultPaymentTerms"
                  value={settings.defaultPaymentTerms || ''}
                  onChange={handleInputChange}
                  rows="2"
                  placeholder="مثال: الدفع خلال 30 يوم من تاريخ الفاتورة"
                />
              </div>
              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    name="autoSaveInvoices"
                    checked={settings.autoSaveInvoices || false}
                    onChange={handleInputChange}
                  />
                  حفظ الفواتير تلقائياً
                </label>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'system' && (
          <div className="tab-content">
            <h3>💻 إعدادات النظام</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>اللغة</label>
                <select
                  name="language"
                  value={settings.language || 'ar'}
                  onChange={handleInputChange}
                >
                  <option value="ar">العربية</option>
                  <option value="en">English</option>
                </select>
              </div>
              <div className="form-group">
                <label>المنطقة الزمنية</label>
                <select
                  name="timezone"
                  value={settings.timezone || 'Asia/Riyadh'}
                  onChange={handleInputChange}
                >
                  <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                  <option value="Asia/Dubai">دبي (GMT+4)</option>
                  <option value="UTC">UTC (GMT+0)</option>
                </select>
              </div>
              <div className="form-group">
                <label>تنسيق التاريخ</label>
                <select
                  name="dateFormat"
                  value={settings.dateFormat || 'DD/MM/YYYY'}
                  onChange={handleInputChange}
                >
                  <option value="DD/MM/YYYY">يوم/شهر/سنة</option>
                  <option value="MM/DD/YYYY">شهر/يوم/سنة</option>
                  <option value="YYYY-MM-DD">سنة-شهر-يوم</option>
                </select>
              </div>
              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    name="enableNotifications"
                    checked={settings.enableNotifications || false}
                    onChange={handleInputChange}
                  />
                  تفعيل الإشعارات
                </label>
              </div>
              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    name="enableDarkMode"
                    checked={settings.enableDarkMode || false}
                    onChange={handleInputChange}
                  />
                  الوضع الليلي
                </label>
              </div>
              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    name="enableAutoBackup"
                    checked={settings.enableAutoBackup || false}
                    onChange={handleInputChange}
                  />
                  النسخ الاحتياطي التلقائي
                </label>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'backup' && (
          <div className="tab-content">
            <h3>💾 النسخ الاحتياطي والاستعادة</h3>
            <div className="backup-section">
              <div className="backup-card">
                <h4>📤 تصدير الإعدادات</h4>
                <p>احفظ نسخة احتياطية من إعدادات النظام</p>
                <button type="button" onClick={exportSettings} className="backup-btn">
                  تصدير الإعدادات
                </button>
              </div>
              
              <div className="backup-card">
                <h4>📥 استيراد الإعدادات</h4>
                <p>استعادة الإعدادات من ملف احتياطي</p>
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  className="file-input"
                />
              </div>
              
              <div className="backup-card">
                <h4>🔄 إعادة تعيين</h4>
                <p>إعادة الإعدادات للقيم الافتراضية</p>
                <button type="button" onClick={resetToDefaults} className="reset-btn">
                  إعادة تعيين
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="form-actions">
          <button type="submit" disabled={loading} className="save-btn">
            {loading ? '⏳ جاري الحفظ...' : '💾 حفظ الإعدادات'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default Settings;
