{"version": 3, "file": "custom-config-package-names.js", "sourceRoot": "", "sources": ["../src/custom-config-package-names.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,gHAAgH;AAChH,wBAAwB;AACxB,EAAE;AACF,4FAA4F;AAC5F,EAAE;AACF,qEAAqE;AACrE,EAAE;AACF,+CAA2E;AAE3E,IAAI,CAAC,gCAAkB,CAAC,oCAAoC,EAAE,CAAC;IAC7D,gCAAkB,CAAC,oCAAoC,GAAG,IAAI,CAAC;IAC/D,sDAAsD;IACtD,MAAM,mCAAmC,GAAG,gCAAkB,CAAC,SAAS,CAAC,4BAA4B,CAAC;IAEtG,iCAAiC;IACjC,oHAAoH;IACpH,gCAAkB,CAAC,SAAS,CAAC,4BAA4B,GAAG,UAAU,UAAkB;QACtF,MAAM,eAAe,GAA2D,4BAAc,CAAC,OAAO,CAAC;QACvG,IAAI,CAAC;YACH,4BAAc,CAAC,OAAO,GAAG,UAAU,UAAkB,EAAE,cAAsB;gBAC3E,IAAI,CAAC;oBACH,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;gBAChE,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,uFAAuF;oBACvF,wFAAwF;oBACxF,yCAAyC;oBACzC,IACE,CAAC,CAA2B,aAA3B,CAAC,uBAAD,CAAC,CAA4B,IAAI,MAAK,kBAAkB;wBACzD,UAAU,KAAK,UAAU;wBACzB,UAAU,KAAK,oBAAM,CAAC,oBAAoB,CAAC,UAAU,EAAE,eAAe,CAAC,EACvE,CAAC;wBACD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;oBAChE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,CAAC;oBACV,CAAC;gBACH,CAAC;YACH,CAAC,CAAC;YACF,OAAO,mCAAmC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpE,CAAC;gBAAS,CAAC;YACT,4BAAc,CAAC,OAAO,GAAG,eAAe,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n// This is a workaround for ESLint's requirement to consume shareable configurations from package names prefixed\n// with \"eslint-config\".\n//\n// To remove this requirement, add this line to the top of your project's .eslintrc.js file:\n//\n//    require(\"@rushstack/eslint-patch/custom-config-package-names\");\n//\nimport { configArrayFactory, ModuleResolver, Naming } from './_patch-base';\n\nif (!configArrayFactory.__loadExtendedShareableConfigPatched) {\n  configArrayFactory.__loadExtendedShareableConfigPatched = true;\n  // eslint-disable-next-line @typescript-eslint/typedef\n  const originalLoadExtendedShareableConfig = configArrayFactory.prototype._loadExtendedShareableConfig;\n\n  // Common between ESLint versions\n  // https://github.com/eslint/eslintrc/blob/242d569020dfe4f561e4503787b99ec016337457/lib/config-array-factory.js#L910\n  configArrayFactory.prototype._loadExtendedShareableConfig = function (extendName: string): unknown {\n    const originalResolve: (moduleName: string, relativeToPath: string) => string = ModuleResolver.resolve;\n    try {\n      ModuleResolver.resolve = function (moduleName: string, relativeToPath: string): string {\n        try {\n          return originalResolve.call(this, moduleName, relativeToPath);\n        } catch (e) {\n          // Only change the name we resolve if we cannot find the normalized module, since it is\n          // valid to rely on the normalized package name. Use the originally provided module path\n          // instead of the normalized module path.\n          if (\n            (e as NodeJS.ErrnoException)?.code === 'MODULE_NOT_FOUND' &&\n            moduleName !== extendName &&\n            moduleName === Naming.normalizePackageName(extendName, 'eslint-config')\n          ) {\n            return originalResolve.call(this, extendName, relativeToPath);\n          } else {\n            throw e;\n          }\n        }\n      };\n      return originalLoadExtendedShareableConfig.apply(this, arguments);\n    } finally {\n      ModuleResolver.resolve = originalResolve;\n    }\n  };\n}\n"]}