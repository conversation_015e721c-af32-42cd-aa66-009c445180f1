.vouchers-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.vouchers-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.vouchers-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.vouchers-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.message {
  padding: 15px 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  font-weight: 600;
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 5px solid;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-card.total { border-left-color: #667eea; }
.stat-card.receipts { border-left-color: #10b981; }
.stat-card.payments { border-left-color: #ef4444; }
.stat-card.net-flow { border-left-color: #8b5cf6; }

.stat-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-info p {
  margin: 0;
  color: #1f2937;
  font-size: 1.8rem;
  font-weight: 800;
}

.stat-info p.positive {
  color: #10b981;
}

.stat-info p.negative {
  color: #ef4444;
}

.vouchers-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  flex: 1;
}

.search-input,
.filter-select,
.date-input {
  padding: 12px 18px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  min-width: 150px;
}

.search-input:focus,
.filter-select:focus,
.date-input:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.add-receipt-btn,
.add-payment-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.95rem;
}

.add-receipt-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.add-receipt-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.add-payment-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.add-payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.voucher-form-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 2px solid #e5e7eb;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.voucher-form {
  padding: 30px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #e5e7eb;
}

.cancel-btn,
.save-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.save-btn {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.vouchers-table-container {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.vouchers-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1000px;
}

.vouchers-table th {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  padding: 18px 15px;
  text-align: right;
  font-weight: 600;
  font-size: 0.95rem;
}

.vouchers-table td {
  padding: 15px;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
}

.vouchers-table tbody tr:hover {
  background: #f8fafc;
}

.voucher-number {
  font-weight: 700;
  color: #1f2937;
}

.amount {
  font-weight: 600;
  text-align: left;
}

.amount.positive {
  color: #10b981;
}

.amount.negative {
  color: #ef4444;
}

.type-badge {
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 100px;
}

.actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.print-btn,
.delete-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.edit-btn {
  background: #f3f4f6;
  color: #374151;
}

.edit-btn:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

.print-btn {
  background: #dbeafe;
  color: #1d4ed8;
}

.print-btn:hover {
  background: #bfdbfe;
  transform: scale(1.1);
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover {
  background: #fecaca;
  transform: scale(1.1);
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
  font-style: italic;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .vouchers-container {
    padding: 15px;
  }
  
  .vouchers-header h1 {
    font-size: 2.2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .vouchers-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
  }
  
  .search-input,
  .filter-select,
  .date-input {
    min-width: auto;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .voucher-form-modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .vouchers-table-container {
    overflow-x: auto;
  }
}
