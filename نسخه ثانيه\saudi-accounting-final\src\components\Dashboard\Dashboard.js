import React, { useState } from 'react';
import './Dashboard.css';

const Dashboard = ({ user, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // تحديث الوقت كل ثانية
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // بيانات وهمية للإحصائيات
  const stats = {
    totalSales: 125000,
    todaySales: 8500,
    totalCustomers: 342,
    pendingInvoices: 15
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatTime = (date) => {
    return date.toLocaleString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="dashboard-container">
      {/* شريط التنقل العلوي */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="dashboard-title">🧮 نظام المحاسبة السعودي</h1>
            <p className="current-time">{formatTime(currentTime)}</p>
          </div>
          <div className="header-right">
            <div className="user-info">
              <span className="user-name">مرحباً، {user.name}</span>
              <span className="user-role">{user.role}</span>
            </div>
            <button onClick={onLogout} className="logout-btn">
              🚪 تسجيل خروج
            </button>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="dashboard-main">
        {/* بطاقات الإحصائيات */}
        <section className="stats-section">
          <h2 className="section-title">📊 الإحصائيات السريعة</h2>
          <div className="stats-grid">
            <div className="stat-card sales">
              <div className="stat-icon">💰</div>
              <div className="stat-content">
                <h3>إجمالي المبيعات</h3>
                <p className="stat-value">{formatCurrency(stats.totalSales)}</p>
                <span className="stat-change positive">+12.5%</span>
              </div>
            </div>

            <div className="stat-card today">
              <div className="stat-icon">📈</div>
              <div className="stat-content">
                <h3>مبيعات اليوم</h3>
                <p className="stat-value">{formatCurrency(stats.todaySales)}</p>
                <span className="stat-change positive">+8.2%</span>
              </div>
            </div>

            <div className="stat-card customers">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <h3>إجمالي العملاء</h3>
                <p className="stat-value">{stats.totalCustomers}</p>
                <span className="stat-change positive">+5 جديد</span>
              </div>
            </div>

            <div className="stat-card invoices">
              <div className="stat-icon">📋</div>
              <div className="stat-content">
                <h3>فواتير معلقة</h3>
                <p className="stat-value">{stats.pendingInvoices}</p>
                <span className="stat-change neutral">بحاجة متابعة</span>
              </div>
            </div>
          </div>
        </section>

        {/* الإجراءات السريعة */}
        <section className="actions-section">
          <h2 className="section-title">⚡ الإجراءات السريعة</h2>
          <div className="actions-grid">
            <button className="action-card">
              <div className="action-icon">🛒</div>
              <h3>نقطة البيع</h3>
              <p>إنشاء فاتورة جديدة</p>
            </button>

            <button className="action-card">
              <div className="action-icon">📦</div>
              <h3>إدارة المخزون</h3>
              <p>عرض وإدارة المنتجات</p>
            </button>

            <button className="action-card">
              <div className="action-icon">👤</div>
              <h3>إدارة العملاء</h3>
              <p>إضافة وتعديل العملاء</p>
            </button>

            <button className="action-card">
              <div className="action-icon">📊</div>
              <h3>التقارير</h3>
              <p>عرض التقارير المالية</p>
            </button>

            <button className="action-card">
              <div className="action-icon">⚙️</div>
              <h3>الإعدادات</h3>
              <p>إعدادات النظام</p>
            </button>

            <button className="action-card">
              <div className="action-icon">💳</div>
              <h3>المدفوعات</h3>
              <p>إدارة المدفوعات</p>
            </button>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Dashboard;
