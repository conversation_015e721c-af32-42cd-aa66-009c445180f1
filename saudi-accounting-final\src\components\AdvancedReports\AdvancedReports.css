.advanced-reports-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.reports-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.reports-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.reports-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.report-selection {
  margin-bottom: 30px;
}

.report-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.report-type-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.report-type-card:hover {
  border-color: #1e40af;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.15);
}

.report-type-card.active {
  border-color: #1e40af;
  background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.2);
}

.report-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.report-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.3;
}

.report-controls {
  background: white;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.date-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.filter-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.action-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.generate-btn,
.print-btn,
.export-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.generate-btn {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
}

.generate-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.print-btn {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
}

.print-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
}

.export-btn {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(220, 38, 38, 0.3);
}

.report-display {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  animation: slideIn 0.3s ease-out;
}

.report-header-info {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e5e7eb;
}

.report-header-info h2 {
  margin: 0 0 10px 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
}

.report-period,
.report-date {
  margin: 5px 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.report-content {
  margin-top: 20px;
}

.error-message {
  text-align: center;
  padding: 40px;
  color: #ef4444;
  font-size: 1.2rem;
  font-weight: 600;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 10px;
}

.report-data {
  background: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
}

.report-data pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Report-specific styles */
.income-statement {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.revenue-section,
.costs-section,
.profit-section {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.revenue-section {
  border-left: 4px solid #10b981;
}

.costs-section {
  border-left: 4px solid #ef4444;
}

.profit-section {
  border-left: 4px solid #1e40af;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #1f2937;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: right;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.data-table .amount {
  text-align: left;
  font-weight: 600;
}

.data-table .positive {
  color: #10b981;
}

.data-table .negative {
  color: #ef4444;
}

.total-row {
  border-top: 2px solid #374151;
  font-weight: 700;
  background: #f8fafc;
}

.balance-sheet {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.assets-section,
.liabilities-equity-section {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.assets-section {
  border-left: 4px solid #1e40af;
}

.liabilities-equity-section {
  border-left: 4px solid #dc2626;
}

.subsection {
  margin: 15px 0;
  padding: 10px;
  background: #f8fafc;
  border-radius: 6px;
}

.subsection-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.trial-balance-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.trial-balance-table th,
.trial-balance-table td {
  padding: 10px;
  text-align: right;
  border: 1px solid #e5e7eb;
}

.trial-balance-table th {
  background: #1e40af;
  color: white;
  font-weight: 600;
}

.trial-balance-table .account-code {
  font-family: monospace;
  font-weight: 600;
}

.trial-balance-table .debit {
  color: #dc2626;
  text-align: left;
}

.trial-balance-table .credit {
  color: #10b981;
  text-align: left;
}

.balance-check {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
}

.balance-check.balanced {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.balance-check.unbalanced {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .balance-sheet {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .advanced-reports-container {
    padding: 15px;
  }
  
  .reports-header h1 {
    font-size: 2.2rem;
  }
  
  .report-types-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .report-type-card {
    padding: 15px;
  }
  
  .report-icon {
    font-size: 2rem;
  }
  
  .report-label {
    font-size: 0.8rem;
  }
  
  .date-controls,
  .filter-controls {
    flex-direction: column;
  }
  
  .form-group {
    min-width: auto;
  }
  
  .action-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .generate-btn,
  .print-btn,
  .export-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .report-display {
    padding: 20px;
  }
  
  .report-header-info h2 {
    font-size: 1.5rem;
  }
  
  .data-table {
    font-size: 0.9rem;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px;
  }
}
