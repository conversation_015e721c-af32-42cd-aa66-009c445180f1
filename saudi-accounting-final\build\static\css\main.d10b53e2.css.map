{"version": 3, "file": "static/css/main.d10b53e2.css", "mappings": "kGAKA,KASE,wBAAyB,CAPzB,mIAEY,CAHZ,QASF,CAeA,2CACE,aACF,CAMA,uBACE,gBACF,CAQA,yBAJE,uEAOF,CAHA,SAEE,eACF,CAGA,UACE,iBAAkB,CAClB,8BACF,CAEA,qBACE,uEAAgF,CAChF,eACF,CAEA,KACE,uEAEF,CAEA,KAEE,YAAa,CADb,iBAEF,CAEA,QACE,kDAA6D,CAG7D,kBAAmB,CAEnB,+BAAyC,CAJzC,UAAY,CAGZ,kBAAmB,CAFnB,iBAIF,CAEA,WAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,UAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAEA,UAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,cACE,eAAiB,CAEjB,kBAAmB,CACnB,8BAAwC,CAFxC,YAAa,CAGb,uBACF,CAEA,oBAEE,+BAA0C,CAD1C,0BAEF,CAEA,iBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,gBACE,UAAW,CACX,eACF,CAEA,QACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,eAAgB,CADhB,YAEF,CAEA,WACE,aAAc,CACd,kBACF,CAEA,UACE,aAAc,CACd,QACF,CC5IA,YAIE,+BACF,CAGA,SAaE,eACF,CAMA,gBAEE,iCAAiD,CADjD,yBAIF,CAkBA,WAEE,4CAAiD,CADjD,8BAEF,CA2BA,UAIE,WASF,CAkBA,gBAGE,0BACF,CAiBA,gBAEE,8BAA8C,CAD9C,yBAEF,CAEA,0BAKE,oBAAoC,CAHpC,8BAA+B,CAC/B,iBAAkB,CAClB,sBAAuB,CAHvB,yBAKF,CAEA,gCACE,gBAAoC,CACpC,cACF,CAGA,cAME,YAAa,CACb,qBACF,CAOA,gBACE,uBAAwB,CAExB,2BAKF,CAEA,YAIE,2CACF,CAEA,cAIE,6BAGF,CAEA,gBAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAKZ,8BAA+B,CAH/B,qBAAsB,CACtB,cAAe,CAIf,YAAa,CANb,6BAA8B,CAU9B,WAAY,CAFZ,sBAAuB,CALvB,yBAA0B,CAE1B,uBAAyB,CAIzB,UAEF,CAEA,sBACE,gCAAiC,CACjC,0BAA2B,CAC3B,oBACF,CAQA,eAGE,oBAAqB,CAErB,qBAAsB,CAJtB,YAAa,CACb,qBAAsB,CAEtB,6BAEF,CAEA,MAEE,qBAAsB,CADtB,eAEF,CAEA,MAGE,iCAAqC,CAFrC,eAGF,CAqBA,iBAvBE,0BA0BF,CAHA,WACE,6BAEF,CAEA,WACE,eAAgB,CAChB,kBACF,CAEA,mBAWE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAKZ,8BAA+B,CAH/B,qBAAsB,CACtB,cAAe,CAIf,YAAa,CANb,6BAA8B,CAU9B,WAAY,CAFZ,sBAAuB,CALvB,yBAA0B,CAN1B,iBAAkB,CAQlB,uBAAyB,CAIzB,UAEF,CAEA,yBACE,gCAAiC,CACjC,0BAA2B,CAC3B,oBACF,CAEA,oBAaE,kBAAmB,CAEnB,2BAA4B,CAX5B,8BAA+B,CAK/B,kBAAmB,CAJnB,kBAAmB,CAOnB,YAAa,CANb,6BAA8B,CAC9B,eAAgB,CAIhB,WAAY,CAGZ,sBAAuB,CAXvB,QAAS,CAOT,cAAe,CAFf,eAAgB,CAPhB,iBAAkB,CAClB,OAcF,CAeA,cACE,QAAO,CAEP,eACF,CAGA,0BACE,SACE,0BACF,CAEA,cACE,uBACF,CAMA,qCACE,cACF,CAEA,gBACE,qBACF,CAEA,eACE,YACF,CACF,CAEA,yBAKE,0BACE,yBACF,CAEA,cACE,6BACF,CAEA,WACE,YACF,CAMA,4BACE,UACF,CACF,CAGA,wBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAWA,gBACE,6BACF,CAGA,mCACE,gBACE,0BAA2B,CAC3B,mCACF,CAEA,cACE,kBACF,CAEA,gBACE,qBACF,CAEA,sBACE,gCAAiC,CACjC,0BACF,CACF,CC1aA,gBAGE,kBAAmB,CADnB,YAAa,CAKb,4BAAgC,CAHhC,sBAAuB,CAHvB,gBAAiB,CAKjB,eAAgB,CADhB,iBAGF,CAGA,kBAME,8FAAmG,CADnG,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,UACF,CAEA,oBAME,kIAIF,CAEA,qCAPE,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAKX,UAUF,CAEA,OAIE,uCAAwC,CADxC,oBAAoC,CADpC,iBAAkB,CADlB,iBAIF,CAEA,SAKE,kBAAmB,CAHnB,WAAY,CAEZ,QAAS,CADT,OAAQ,CAFR,UAKF,CAEA,SAKE,kBAAmB,CAHnB,YAAa,CAEb,QAAS,CADT,OAAQ,CAFR,WAKF,CAEA,SAKE,kBAAmB,CAHnB,WAAY,CAEZ,QAAS,CADT,OAAQ,CAFR,UAKF,CAEA,SAKE,kBAAmB,CAHnB,YAAa,CAEb,QAAS,CADT,OAAQ,CAFR,WAKF,CAcA,iBAOE,kBAAmB,CANnB,YAAa,CAKb,sBAAuB,CAEvB,sBAAuB,CAJvB,aAAc,CADd,gBAAiB,CAEjB,yBAA0B,CAH1B,UAOF,CAGA,YAOE,kCAA2B,CAA3B,0BAA2B,CAN3B,uBAAwB,CAOxB,sBAA0C,CAN1C,8BAA+B,CAC/B,2BAA4B,CAG5B,eAAgB,CAFhB,eAAgB,CAChB,UAIF,CAEA,cACE,qEAAyE,CAGzE,uCAAwC,CAFxC,yBAA0B,CAC1B,iBAEF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAGb,qBAAsB,CADtB,sBAAuB,CAEvB,+BACF,CAEA,WAIE,6BAAoC,CAFpC,kFAAsF,CACtF,4BAA6B,CAE7B,oBAAqB,CACrB,4CAAiD,CALjD,8BAMF,CAEA,cAGE,qBAAsB,CAFtB,8BAA+B,CAC/B,eAAgB,CAEhB,QAAS,CACT,+BACF,CAEA,aAEE,qBAAsB,CAEtB,eAAgB,CADhB,QAEF,CAEA,2BANE,6BAcF,CARA,cAIE,0BAA2B,CAE3B,8BAA+B,CAJ/B,qBAAsB,CAKtB,oBAAqB,CAFrB,2CAGF,CAGA,YACE,yBACF,CAEA,cAEE,+BAAgC,CADhC,iBAEF,CAEA,iBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,+BACF,CAEA,gBAEE,qBAAsB,CADtB,+BAAgC,CAEhC,QACF,CAGA,eAOE,kBAAmB,CANnB,kDAA6D,CAG7D,8BAA+B,CAF/B,yBAA0B,CAI1B,YAAa,CAIb,eAAgB,CAFhB,qBAAsB,CAHtB,+BAAgC,CAFhC,yBAQF,CAEA,YACE,6BACF,CAGA,YACE,+BACF,CAEA,YACE,+BACF,CAEA,kBAIE,qBAAsB,CAHtB,aAAc,CAId,6BAA8B,CAH9B,+BAIF,CAEA,iBACE,iBACF,CAEA,YAQE,uBAAwB,CAJxB,gCAAiC,CACjC,8BAA+B,CAI/B,mBAAoB,CAHpB,+BAAgC,CAJhC,2CAA4C,CAC5C,kBAAmB,CAInB,uBAAyB,CANzB,UASF,CAEA,kBAEE,iCAAkC,CAClC,8BAA4C,CAF5C,YAAa,CAGb,0BACF,CAEA,qBACE,gCAAiC,CACjC,kBAAmB,CACnB,UACF,CAEA,YAOE,mBAAoB,CALpB,uBAMF,CAEA,6BAJE,qBAAsB,CADtB,6BAA8B,CAJ9B,iBAAkB,CAElB,OAAQ,CACR,0BAmBF,CAbA,iBAKE,eAAgB,CAChB,WAAY,CAKZ,8BAA+B,CAF/B,cAAe,CAPf,sBAAuB,CAQvB,yBAA0B,CAE1B,uBACF,CAEA,uBAEE,0BAA2B,CAD3B,qBAEF,CAEA,0BACE,kBAAmB,CACnB,UACF,CAGA,cACE,+BACF,CAEA,oBAEE,kBAAmB,CAInB,qBAAsB,CAFtB,cAAe,CAHf,YAAa,CAIb,6BAA8B,CAE9B,eAAgB,CAJhB,qBAKF,CAEA,yCACE,YACF,CAEA,WAME,kBAAmB,CAGnB,uBAAwB,CANxB,gCAAiC,CACjC,8BAA+B,CAC/B,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CACvB,uBAAyB,CAPzB,UASF,CAEA,4DACE,+BAAgC,CAChC,iCACF,CAEA,kEAEE,kBAAmB,CADnB,WAAY,CAEZ,6BAA8B,CAC9B,eACF,CAGA,2BAWE,kBAAmB,CAPnB,WAAY,CACZ,8BAA+B,CAG/B,cAAe,CAEf,YAAa,CAIb,mBAAoB,CARpB,+BAAgC,CAChC,eAAgB,CAMhB,qBAAsB,CADtB,sBAAuB,CATvB,yBAA0B,CAM1B,uBAAyB,CAPzB,UAaF,CAEA,cACE,kFAAsF,CAEtF,2BAA4B,CAD5B,kBAAmB,CAEnB,+BACF,CAEA,mCACE,qEAAyE,CAEzE,2BAA4B,CAD5B,0BAEF,CAEA,uBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,aACE,yEAA6E,CAE7E,gCAAiC,CADjC,qBAEF,CAEA,kCACE,yEAA6E,CAE7E,2BAA4B,CAD5B,0BAEF,CAEA,aACE,6BACF,CAEA,iBAGE,0BAA0C,CAC1C,iCAAkC,CAFlC,WAAY,CADZ,UAMF,CAYA,uBAHE,+BAOF,CAJA,SAGE,iBAAkB,CAFlB,iBAGF,CAEA,gBAOE,0BAA2B,CAN3B,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,OAKF,CAEA,cACE,uBAAwB,CAExB,qBAAsB,CACtB,6BAA8B,CAC9B,eAAgB,CAHhB,2BAIF,CAGA,cACE,yBAA0B,CAE1B,oCAAqC,CADrC,yBAEF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,qBAAsB,CACtB,+BACF,CAEA,WAEE,kBAAmB,CAGnB,qBAAsB,CADtB,6BAA8B,CAD9B,qBAGF,CAEA,WACE,+BACF,CAEA,WAGE,qBAAsB,CADtB,6BAA8B,CAD9B,iBAGF,CAEA,aACE,QAAS,CACT,+BACF,CAGA,kBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAKrC,sBAA0C,CAH1C,8BAA+B,CAE/B,2BAA4B,CAE5B,eAAgB,CAHhB,yBAIF,CAEA,qBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,+BAAgC,CAChC,iBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,cAEE,sBAAuB,CAGvB,uBAAwB,CAGxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAN5B,YAAa,CAEb,qBAAsB,CACtB,yBAA0B,CAK1B,uBACF,CAEA,oBAGE,iCAAkC,CADlC,2BAA4B,CAD5B,0BAGF,CAEA,cAME,kBAAmB,CAEnB,kFAAsF,CACtF,8BAA+B,CAC/B,kBAAmB,CALnB,YAAa,CAHb,aAAc,CADd,8BAA+B,CAG/B,WAAY,CAGZ,sBAAuB,CAJvB,UAQF,CAEA,oBAGE,qBAAsB,CAFtB,+BAAgC,CAChC,eAAgB,CAEhB,+BACF,CAEA,mBAEE,qBAAsB,CADtB,6BAA8B,CAG9B,eAAgB,CADhB,QAEF,CAGA,0BACE,iBACE,qBAAsB,CACtB,qBACF,CAEA,kBACE,cAAe,CACf,QACF,CAEA,eAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CACF,CAEA,yBAKE,iCACE,yBACF,CAEA,YACE,cACF,CAEA,wCAGE,yBACF,CAEA,cACE,qBAAsB,CACtB,qBACF,CAEA,WACE,8BACF,CAEA,kBACE,yBACF,CAMA,4BAHE,yBAOF,CAJA,aAGE,0BAAsB,CAFtB,YAAa,CAEb,qBACF,CACF,CAEA,yBACE,YACE,2CAA4C,CAC5C,oBACF,CAOA,yCACE,yBACF,CAEA,cAGE,6BAA8B,CAD9B,WAAY,CADZ,UAGF,CACF,CCvmBA,oBAEE,yBAA0B,CAC1B,gBAAiB,CAFjB,yBAGF,CAGA,mBAGE,kBAAmB,CAGnB,qBAAsB,CALtB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAEF,CAEA,iBAME,iCAAkC,CAHlC,oCAAiC,CACjC,gCAA0C,CAC1C,iBAAkB,CADlB,qCAA0C,CAF1C,WAAY,CAKZ,+BAAgC,CANhC,UAOF,CAQA,iBAQE,kBAAmB,CAPnB,kFAAsF,CAGtF,8BAA+B,CAK/B,2BAA4B,CAP5B,kBAAmB,CAInB,YAAa,CACb,6BAA8B,CAF9B,+BAAgC,CAMhC,eAAgB,CARhB,0BAA2B,CAO3B,iBAEF,CAEA,wBAQE,uCAAwC,CADxC,wDAA8E,CAN9E,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,UAIF,CAEA,iBACE,MAAW,oCAAyC,CACpD,IAAM,0CAA6C,CACrD,CAEA,oBACE,8BAA+B,CAC/B,eAAgB,CAChB,+BAAgC,CAChC,+BACF,CAEA,mBACE,6BAA8B,CAE9B,+BAAgC,CADhC,UAEF,CAEA,cACE,+BAAgC,CAEhC,eAAgB,CADhB,UAEF,CAEA,iBACE,SACF,CAUA,WAQE,qBAAsB,CAGtB,eAAgB,CAThB,yBAA0B,CAQ1B,iBAEF,CAEA,kBAOE,+BAAgC,CANhC,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAKN,yBAA2B,CAH3B,SAIF,CAEA,iBAEE,2BAA4B,CAD5B,0BAEF,CAEA,wBACE,SACF,CAEA,0BAA6B,+BAAkC,CAC/D,0BAA6B,+BAAkC,CAC/D,uBAA0B,4BAA+B,CACzD,0BAA6B,+BAAkC,CAC/D,yBAA4B,8BAAiC,CAC7D,4BAA+B,iCAAoC,CAEnE,WASE,aAAc,CARd,8BAA+B,CAE/B,WAAY,CADZ,UAQF,CAEA,cACE,QACF,CAEA,YAIE,eAAgB,CAChB,+BACF,CAEA,YAGE,eACF,CAGA,uBACE,+BACF,CAEA,0BAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,+BACF,CAEA,oBAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CAEA,mBAOE,kBAAmB,CANnB,uBAAwB,CAIxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAK5B,cAAe,CAHf,YAAa,CAEb,qBAAsB,CAItB,eAAgB,CAVhB,yBAA0B,CAS1B,iBAAkB,CADlB,uBAGF,CAEA,0BAOE,+BAAgC,CANhC,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAKN,mBAAoB,CACpB,6BAA+B,CAJ/B,UAKF,CAEA,yBAEE,2BAA4B,CAD5B,0BAEF,CAEA,gCACE,mBACF,CAEA,wCAA2C,+BAAkC,CAC7E,wCAA2C,+BAAkC,CAC7E,qCAAwC,4BAA+B,CACvE,wCAA2C,+BAAkC,CAE7E,aAKE,kBAAmB,CAEnB,0BAA2B,CAC3B,8BAA+B,CAJ/B,YAAa,CAKb,aAAc,CARd,8BAA+B,CAE/B,WAAY,CAGZ,sBAAuB,CAJvB,UAQF,CAEA,mBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,+BACF,CAEA,kBAEE,qBAAsB,CADtB,6BAA8B,CAE9B,QACF,CAGA,mBAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CAEA,gBACE,uBAAwB,CAGxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAE5B,eAAgB,CAChB,uBACF,CAEA,sBACE,2BAA4B,CAC5B,0BACF,CAEA,aAME,kBAAmB,CAHnB,yBAA0B,CAC1B,YAAa,CACb,6BAEF,CAEA,gBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,QACF,CAOA,aAEE,eAAgB,CADhB,YAAa,CAEb,qBAAsB,CACtB,YAAa,CACb,2BACF,CAEA,WAIE,kBAAmB,CAFnB,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,qBACF,CAEA,KAEE,kFAAsF,CACtF,mDAAoD,CAEpD,cAAe,CACf,eAAgB,CAFhB,uBAAyB,CAHzB,UAMF,CAEA,WACE,mFAAuF,CACvF,sBACF,CAEA,WAEE,qBAAsB,CACtB,eACF,CAEA,sBALE,6BASF,CAJA,WAEE,qBAAsB,CACtB,eACF,CAGA,8BAEE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,4BAIE,kBAAmB,CAEnB,yBAA0B,CAE1B,gCAAiC,CADjC,8BAA+B,CAL/B,YAAa,CACb,6BAA8B,CAE9B,yBAA0B,CAI1B,uBACF,CAEA,wCAEE,0BAA2B,CAC3B,iCAAkC,CAClC,0BACF,CAEA,4BAEE,QACF,CAEA,8BAGE,qBAAsB,CADtB,eAAgB,CAEhB,+BACF,CAEA,gCAGE,qBAAsB,CADtB,6BAEF,CAEA,gBAEE,0BAA2B,CAC3B,6BAA8B,CAF9B,eAGF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,qBACF,CAEA,aAIE,8BAA+B,CAF/B,6BAA8B,CAD9B,eAAgB,CAIhB,cAAe,CAFf,2CAA4C,CAG5C,iBACF,CAEA,oBACE,oBAAkC,CAClC,yBACF,CAEA,aAEE,qBAAsB,CADtB,6BAEF,CAGA,aAEE,yBAEF,CAEA,eAEE,iBACF,CAGA,0BACE,mBACE,yBACF,CAEA,YACE,wDACF,CAEA,oBACE,wDACF,CACF,CAEA,yBACE,oBACE,yBACF,CAEA,iBACE,qBAAsB,CAEtB,qBAAsB,CACtB,yBAA0B,CAF1B,iBAGF,CAEA,oBACE,8BACF,CAEA,YAEE,qBAAsB,CADtB,yBAEF,CAEA,WACE,yBACF,CAEA,oBACE,yBACF,CAEA,mBACE,qBACF,CAEA,aAIE,mBAAoB,CAFpB,qBAAsB,CACtB,qBAEF,CAEA,wBANE,yBAQF,CAEA,aAEE,qBAAsB,CADtB,YAEF,CACF,CCreA,eAEE,wBAAyB,CACzB,aAAc,CACd,qDAA4D,CAH5D,gBAIF,CAGA,YACE,kDAAqD,CAGrD,8BAAqC,CAFrC,UAAY,CACZ,cAEF,CAEA,gBAEE,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,UACE,sBAAuC,CAEvC,0BAAuC,CAEvC,iBAAkB,CAGlB,eAAiB,CAJjB,gBAKF,CAEA,gBACE,0BAAuC,CACvC,0BACF,CAEA,eAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,WACE,eAAiB,CACjB,UACF,CAGA,UAME,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,+BAAgC,CAHhC,aAAc,CADd,gBAAiB,CAMjB,6BAA8B,CAJ9B,YAKF,CAGA,kBACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAqC,CADrC,YAEF,CAEA,YACE,kBACF,CAEA,cAGE,wBAAyB,CACzB,iBAAkB,CAFlB,iBAAkB,CAIlB,kBACF,CAEA,oBAEE,oBAAqB,CACrB,8BACF,CAEA,eAGE,aAAS,CAAT,QAAS,CADT,yDAA4D,CAE5D,8BAA+B,CAC/B,eACF,CAEA,cACE,wBAAyB,CACzB,iBAAkB,CAClB,YAAa,CACb,kBAEF,CAEA,oBACE,oBAAqB,CACrB,+BAA+C,CAC/C,0BACF,CAEA,iBAEE,UAAW,CACX,cAAe,CACf,eAAgB,CAHhB,cAIF,CAEA,kBAEE,UAAW,CACX,gBAAkB,CAFlB,cAGF,CAEA,eAEE,aAAc,CAEd,gBAAiB,CADjB,eAAgB,CAFhB,cAIF,CAEA,eAEE,UAAW,CACX,gBAAkB,CAFlB,eAGF,CAEA,iBAGE,kDAAqD,CAErD,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAEf,eAAiB,CACjB,eAAgB,CARhB,gBAAiB,CAMjB,kBAAoB,CAPpB,UAUF,CAEA,sCACE,kDAAqD,CACrD,0BACF,CAEA,0BACE,kBAAmB,CACnB,UAAW,CACX,kBACF,CAGA,cACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAqC,CACrC,YAAa,CACb,qBAAsB,CACtB,8BAA+B,CAJ/B,YAKF,CAEA,aAGE,kBAAmB,CAGnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,gBAEE,UAAW,CACX,gBAAiB,CAFjB,QAGF,CAEA,gBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,gBAAkB,CAHlB,gBAAiB,CAIjB,kBACF,CAEA,sBACE,kBAAmB,CACnB,0BACF,CAEA,YACE,QAAO,CAEP,kBAAmB,CACnB,gBAAiB,CACjB,gBAAiB,CAHjB,eAIF,CAEA,YAKE,kBAAmB,CAEnB,sBAAuB,CADvB,iBAAkB,CAJlB,UAAW,CACX,iBAAkB,CAClB,iBAAkB,CAHlB,iBAOF,CAEA,WAGE,aAAS,CACT,kBAAmB,CAGnB,eAAiB,CADjB,+BAAgC,CAEhC,iBAAkB,CAElB,8BAAqC,CATrC,YAAa,CAEb,QAAS,CADT,wCAAyC,CAOzC,iBAAkB,CAJlB,YAAa,CAMb,kBACF,CAEA,iBAEE,8BAAsC,CADtC,0BAEF,CAEA,sBACE,kBACF,CAEA,cAEE,UAAW,CACX,cAAe,CACf,eAAgB,CAHhB,cAIF,CAEA,aAEE,aAAc,CACd,eAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,mBAGE,OACF,CAEA,4BAJE,kBAAmB,CADnB,YAiBF,CAZA,SAIE,eAAiB,CADjB,wBAAyB,CAEzB,iBAAkB,CAClB,cAAe,CAIf,eAAgB,CARhB,WAAY,CAOZ,sBAAuB,CAEvB,kBAAoB,CAVpB,UAWF,CAEA,eACE,oBAAqB,CACrB,aACF,CAEA,UAGE,eAAgB,CAFhB,cAAe,CACf,iBAEF,CAEA,YAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,YACE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAHlB,cAAe,CACf,cAAe,CACf,WAAY,CAEZ,kBACF,CAEA,kBACE,kBACF,CAGA,aACE,4BAA6B,CAE7B,kBAAmB,CADnB,gBAEF,CAEA,YACE,YAAa,CAGb,gBAAkB,CAFlB,6BAA8B,CAC9B,iBAEF,CAEA,aAIE,4BAA6B,CAD7B,aAAc,CADd,gBAAiB,CADjB,eAAgB,CAKhB,cAAe,CADf,eAEF,CAGA,iBAGE,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CAHlB,kBAAmB,CACnB,YAIF,CAEA,kBAEE,UAAW,CACX,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,iBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,gBAEE,kBAAmB,CAGnB,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAPf,YAAa,CAEb,QAAS,CACT,iBAAkB,CAKlB,kBACF,CAEA,sBAEE,kBAAmB,CADnB,oBAEF,CAEA,yBAEE,kBAAmB,CADnB,oBAAqB,CAErB,8BACF,CAEA,kCAEE,oBAAqB,CADrB,QAEF,CAEA,cACE,gBACF,CAEA,gCAEE,UAAW,CADX,eAEF,CAEA,yCACE,aAAc,CACd,eACF,CAGA,kBACE,kBACF,CAEA,cAGE,kBAAmB,CAEnB,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CAEf,kBAAmB,CAPnB,YAAa,CAMb,kBAAoB,CAPpB,UASF,CAEA,oBACE,kBAAmB,CACnB,oBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,gBAEE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,gBAAiB,CAIjB,kBACF,CAEA,sBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,cAYE,kBAAmB,CATnB,kDAAqD,CAErD,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAIf,YAAa,CAHb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CADR,sBAAuB,CAXvB,YAAa,CAQb,kBAAoB,CATpB,UAcF,CAEA,mCACE,kDAAqD,CAErD,+BAA8C,CAD9C,0BAEF,CAEA,uBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,yBACE,mBACF,CAGA,SAME,iCAAkC,CADlC,0BAAuB,CADvB,iBAAkB,CAClB,qBAAuB,CAHvB,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,uBAA2B,CAClC,CAGA,0BACE,UACE,+BACF,CACF,CAEA,yBACE,UAEE,QAAS,CADT,yBAA0B,CAE1B,YACF,CAEA,cAEE,eAAgB,CADhB,QAEF,CAEA,eAEE,QAAS,CADT,yDAEF,CAEA,gBACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CACF,CAEA,yBACE,eACE,6BACF,CAEA,WAEE,OAAQ,CADR,yBAA0B,CAE1B,iBACF,CAEA,mBACE,sBACF,CACF,CCphBA,oBAIE,aAAc,CACd,qDAA4D,CAF5D,aAAc,CADd,gBAAiB,CADjB,YAKF,CAEA,iBACE,kDAA6D,CAC7D,kBAAmB,CAInB,+BAAyC,CADzC,UAAY,CADZ,kBAAmB,CADnB,YAIF,CA0CA,8BACE,kBAAmB,CAEnB,+BACF,CAOA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WASE,6BACF,CAsCA,kBAOE,kBAAmB,CANnB,eAAiB,CACjB,kBAAmB,CAOnB,+BAA0C,CAJ1C,YAAa,CAGb,cAAe,CAFf,QAAS,CAFT,kBAAmB,CADnB,YAOF,CAiBA,oBAIE,8BACF,CAaA,4DAhBE,oBAoBF,CAEA,0BACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAA0C,CAD1C,eAEF,CAEA,gBAEE,wBAAyB,CADzB,UAEF,CAEA,mBACE,kDAA6D,CAC7D,UAAY,CAIZ,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,mBAEE,+BAAgC,CADhC,YAAa,CAEb,qBACF,CAEA,yBACE,kBACF,CAEA,6BACE,kBACF,CAEA,mCACE,kBACF,CAEA,qBAGE,UAAW,CAFX,aAAc,CACd,eAAgB,CAEhB,iBACF,CAEA,oBACE,UAAW,CACX,cACF,CAEA,eACE,aAAc,CACd,eACF,CAEA,cACE,aAAc,CACd,eACF,CAEA,QAEE,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,iBACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,YACE,kBAAmB,CACnB,aACF,CAyCA,aAGE,UAAW,CACX,cACF,CAwHA,eACE,YAAa,CACb,QACF,CAEA,qBACE,QACF,CAEA,cAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAClB,cAAe,CACf,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CAQlB,uBACF,CAEA,oBACE,kBACF,CA+CA,yBACE,oBACE,YACF,CAEA,gBACE,qBAAsB,CACtB,iBACF,CAEA,gBACE,yBACF,CAEA,kBAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,cACF,CAEA,0BACE,eACF,CAEA,gBACE,eACF,CAEA,OAEE,WAAY,CADZ,SAEF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CACF,CClhBA,qBAIE,aAAc,CACd,qDAA4D,CAF5D,aAAc,CADd,gBAAiB,CADjB,YAKF,CAEA,kBACE,kDAA6D,CAC7D,kBAAmB,CAInB,+BAAyC,CADzC,UAAY,CADZ,kBAAmB,CADnB,YAIF,CAqCA,SACE,kBAEF,CAEA,8BACE,kBAAmB,CAEnB,+BACF,CAOA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WASE,6BACF,CAsCA,mBAOE,kBAAmB,CANnB,eAAiB,CACjB,kBAAmB,CAOnB,+BAA0C,CAJ1C,YAAa,CAGb,cAAe,CAFf,QAAS,CAFT,kBAAmB,CADnB,YAOF,CAiBA,oBAIE,8BACF,CAaA,4DAhBE,oBAoBF,CAEA,2BACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAA0C,CAD1C,eAEF,CAEA,iBAEE,wBAAyB,CADzB,UAEF,CAEA,oBACE,kDAA6D,CAC7D,UAAY,CAIZ,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,oBAEE,+BAAgC,CADhC,YAAa,CAEb,qBACF,CAEA,0BACE,kBACF,CAEA,sBAGE,UAAW,CAFX,aAAc,CACd,eAAgB,CAEhB,iBACF,CAEA,qBACE,UAAW,CACX,cACF,CAEA,eAEE,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,iBACF,CAEA,0BACE,kBAAmB,CACnB,aACF,CAEA,uBACE,kBAAmB,CACnB,aACF,CAEA,0BACE,kBAAmB,CACnB,aACF,CAEA,yBACE,kBAAmB,CACnB,aACF,CAyCA,cAGE,UAAW,CACX,cAAe,CAFf,iBAAkB,CADlB,iBAIF,CAsCA,cACE,kDAOF,CAiEA,4EAIE,oBAAqB,CAErB,8BACF,CA+BA,UACE,kBAEF,CAEA,+BACE,kBAAmB,CAEnB,+BACF,CAOA,yBACE,qBACE,YACF,CAEA,gBACE,qBAAsB,CACtB,iBACF,CAEA,iBACE,yBACF,CAEA,mBAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,cACF,CAEA,2BACE,eACF,CAEA,iBACE,eACF,CAEA,OAEE,WAAY,CADZ,SAEF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CACF,CCjfA,qBAIE,aAAc,CACd,qDAA4D,CAF5D,aAAc,CADd,gBAAiB,CADjB,YAKF,CAEA,kBACE,kDAA6D,CAC7D,kBAAmB,CAInB,+BAAyC,CADzC,UAAY,CADZ,kBAAmB,CADnB,YAIF,CAqCA,SACE,kBAEF,CAEA,8BACE,kBAAmB,CAEnB,+BACF,CAOA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WASE,6BACF,CAOA,mBACE,yBACF,CAEA,mBACE,yBACF,CAEA,gBACE,yBACF,CAqBA,mBAOE,kBAAmB,CANnB,eAAiB,CACjB,kBAAmB,CAOnB,+BAA0C,CAJ1C,YAAa,CAGb,cAAe,CAFf,QAAS,CAFT,kBAAmB,CADnB,YAOF,CAEA,YACE,QAAO,CACP,eACF,CAEA,cAOE,kBAAmB,CAJnB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAHf,iBAMF,CAEA,oBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BACF,CAEA,4BAIE,kBAAmB,CAFnB,wBAAyB,CACzB,kBAAmB,CAGnB,cAAe,CADf,cAAe,CAGf,eAAgB,CAPhB,iBAAkB,CAMlB,uBAEF,CAEA,wCAGE,eAAiB,CADjB,oBAAqB,CADrB,YAGF,CAEA,2BACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAA0C,CAD1C,eAEF,CAEA,iBAEE,wBAAyB,CADzB,UAEF,CAEA,oBACE,kDAA6D,CAC7D,UAAY,CAIZ,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,oBAEE,+BAAgC,CADhC,YAAa,CAEb,qBACF,CAEA,0BACE,kBACF,CAEA,sBAGE,UAAW,CAFX,aAAc,CACd,eAAgB,CAEhB,iBACF,CAEA,qBACE,UAAW,CACX,cACF,CAEA,eAEE,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,iBACF,CAEA,qBACE,kBAAmB,CACnB,aACF,CAEA,6BACE,kBAAmB,CACnB,aACF,CAEA,4BACE,kBAAmB,CACnB,aACF,CAEA,2BACE,kBAAmB,CACnB,aACF,CAEA,0BACE,kBAAmB,CACnB,aACF,CAEA,gBAEE,OACF,CAEA,sBAGE,iBAAkB,CAElB,cAEF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,+BACE,kBAAmB,CACnB,qBACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,iCACE,kBAAmB,CACnB,qBACF,CAEA,wCACE,UAEF,CAEA,cAGE,UAAW,CACX,cAAe,CAFf,iBAAkB,CADlB,iBAIF,CAgBA,OAIE,eAKF,CAaA,cACE,kDAOF,CAiEA,4EAIE,oBAAqB,CAErB,8BACF,CA+BA,UACE,kBAEF,CAEA,+BACE,kBAAmB,CAEnB,+BACF,CAOA,yBACE,qBACE,YACF,CAEA,gBACE,qBAAsB,CACtB,iBACF,CAEA,iBACE,yBACF,CAEA,mBAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,cACF,CAEA,2BACE,eACF,CAEA,iBACE,eACF,CAEA,OAEE,WAAY,CADZ,SAEF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CACF,CCpfA,oBAEE,yBAA0B,CAC1B,gBAAiB,CAFjB,yBAGF,CAEA,iBACE,+BACF,CAEA,YAIE,+BACF,CAEA,eAGE,qBAAsB,CAFtB,8BAA+B,CAC/B,eAAgB,CAEhB,QACF,CAGA,YAGE,0BAAsB,CAAtB,qBAAsB,CADtB,wDAA2D,CAE3D,+BACF,CAEA,WACE,uBAAwB,CAIxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAI5B,qBAAsB,CANtB,yBAQF,CAEA,iBAEE,2BAA4B,CAD5B,0BAEF,CAEA,mBACE,0CACF,CAEA,mBACE,0CACF,CAEA,kBACE,yCACF,CAEA,WAKE,kBAAmB,CAEnB,0BAA2B,CAC3B,8BAA+B,CAJ/B,YAAa,CAHb,8BAA+B,CAE/B,WAAY,CAGZ,sBAAuB,CAJvB,UAOF,CAEA,WACE,QACF,CAEA,YAGE,qBAAsB,CAFtB,8BAA+B,CAG/B,aACF,CAEA,YAEE,qBAAsB,CADtB,6BAA8B,CAE9B,4BACF,CAGA,iBACE,uBAAwB,CAIxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAE5B,+BAAgC,CAJhC,yBAKF,CAEA,YACE,+BACF,CAEA,cAGE,gCAAiC,CACjC,8BAA+B,CAC/B,+BAAgC,CAHhC,2CAKF,CAEA,oBAEE,iCAAkC,CAClC,8BACF,CAEA,aACE,YAAa,CAEb,cAAe,CADf,qBAEF,CAEA,eAKE,uBAAwB,CAHxB,gCAAiC,CACjC,8BAA+B,CAC/B,+BAAgC,CAEhC,eAAgB,CALhB,yBAMF,CAEA,qBAEE,iCACF,CAGA,kBACE,uBAAwB,CAGxB,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAE5B,eACF,CAEA,aAGE,qBAAsB,CADtB,0BAA2B,CAD3B,iBAGF,CAEA,YACE,cAAe,CACf,+BAAgC,CAChC,UACF,CAEA,gBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,+BAEF,CAEA,eACE,+BAAgC,CAChC,QACF,CAGA,kBAGE,yBAA0B,CAD1B,uCAAwC,CADxC,yBAGF,CAEA,cAGE,kBAAmB,CAEnB,qBAAsB,CAJtB,YAAa,CAGb,eAAgB,CAFhB,6BAIF,CAGA,0BACE,eACF,CAEA,gBAEE,wBAAyB,CACzB,6BAA8B,CAF9B,UAGF,CAEA,mBACE,yBAA0B,CAI1B,qBAAsB,CADtB,eAAgB,CADhB,gBAAiB,CAIjB,kBACF,CAEA,sCAJE,uCAAwC,CAJxC,2CAYF,CAJA,mBAGE,qBACF,CAEA,+BACE,yBACF,CAEA,gBAEE,0BAA2B,CAD3B,eAEF,CAEA,QAEE,0BACF,CAGA,cAEE,8BAA+B,CAC/B,6BAA8B,CAF9B,2CAA4C,CAK5C,kBACF,CAEA,sBACE,kBAAmB,CACnB,aACF,CAEA,sBACE,kBAAmB,CACnB,aACF,CAEA,qBACE,kBAAmB,CACnB,aACF,CAEA,wBACE,0BAA2B,CAC3B,qBACF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAEF,CAEA,sBAKE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAJvB,cAAe,CADf,yBAMF,CAGA,eAWE,yBACF,CAEA,uBAQE,mCAAqC,CAPrC,uBAAwB,CACxB,8BAA+B,CAK/B,2BAA4B,CAF5B,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,UAMF,CAEA,cAME,yBAA0B,CAD1B,uCAAwC,CAExC,mDAAoD,CAHpD,yBAIF,CAEA,iBAGE,qBAEF,CAEA,4BANE,6BAeF,CATA,WAOE,8BAA+B,CAH/B,qBAAsB,CAEtB,yBAGF,CAEA,iBACE,0BAA2B,CAC3B,qBACF,CAEA,YACE,yBACF,CAGA,mBAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAA2D,CAE3D,+BACF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,iBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAEF,CAEA,gBAEE,qBAAsB,CADtB,+BAEF,CAGA,eACE,+BACF,CAEA,kBAGE,qBAAsB,CAFtB,6BAA8B,CAC9B,eAAgB,CAEhB,+BACF,CAEA,aAIE,gCAAiC,CAFjC,wBAAyB,CAGzB,8BAA+B,CAF/B,6BAA8B,CAG9B,eAAgB,CALhB,UAMF,CAEA,gBACE,yBAA0B,CAI1B,qBAAsB,CADtB,eAAgB,CADhB,gBAIF,CAEA,gCAHE,uCAAwC,CAJxC,yBAWF,CAJA,gBAGE,qBACF,CAEA,oCACE,kBACF,CAGA,gBACE,yBAA0B,CAG1B,gCAAiC,CADjC,8BAA+B,CAD/B,yBAGF,CAEA,WAKE,6BAA8B,CAD9B,+BAEF,CAEA,sBACE,eACF,CAEA,uBAIE,oCAAqC,CADrC,0BAA2B,CAF3B,6BAA8B,CAC9B,eAAgB,CAIhB,4BAA6B,CAD7B,6BAEF,CAEA,cAME,yBAA0B,CAC1B,mDAAoD,CAFpD,oCAAqC,CAFrC,qBAAsB,CACtB,yBAIF,CAeA,yBACE,oBACE,yBACF,CAEA,YAGE,mBAAoB,CAFpB,qBAAsB,CACtB,qBAEF,CAEA,YACE,yBACF,CAEA,aACE,qBACF,CAEA,eACE,cACF,CAEA,gBACE,6BACF,CAEA,sCAEE,yBACF,CAEA,iBACE,qBAAsB,CACtB,qBACF,CAEA,mBACE,yBACF,CAEA,cACE,qBACF,CACF,CClfA,sBAIE,aAAc,CACd,qDAA4D,CAF5D,aAAc,CADd,gBAAiB,CADjB,YAKF,CAEA,mBACE,kDAA6D,CAC7D,kBAAmB,CAInB,+BAAyC,CADzC,UAAY,CADZ,kBAAmB,CADnB,YAIF,CAUA,mBAEE,cAAe,CACf,eAAgB,CAFhB,QAGF,CAOA,mBAEE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAEf,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,UAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,gBAGF,CAEA,gBACE,oBAEF,CAEA,SACE,kBAAmB,CACnB,UACF,CAEA,mBACE,kBACF,CAEA,8BAEE,2BAAyC,CADzC,0BAEF,CAEA,kBAEE,kBAAmB,CADnB,UAEF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WAOE,+BAEF,CAEA,iBAEE,+BACF,CAEA,iBACE,6BACF,CAEA,qBACE,6BACF,CAEA,kBACE,6BACF,CAEA,mBACE,6BACF,CAOA,cAGE,UAEF,CAEA,aAIE,UAAW,CADX,eAEF,CAEA,iBACE,eAAiB,CACjB,kBAAmB,CAKnB,+BAA0C,CAF1C,YAAa,CACb,OAAQ,CAFR,kBAAmB,CADnB,YAKF,CAEA,SAIE,kBAAmB,CAEnB,UAAW,CAIX,cAAe,CAHf,eAAgB,CALhB,iBAAkB,CAOlB,uBAEF,CAEA,eAEE,UACF,CAEA,gBACE,kDAA6D,CAE7D,+BACF,CAEA,aACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAA0C,CAC1C,gBAAiB,CAFjB,YAGF,CAEA,gBAME,+BAAgC,CAFhC,kBAAmB,CACnB,mBAEF,CAEA,mBAEE,UAAW,CACX,gBAAiB,CAFjB,QAGF,CAEA,2CACE,kBAAmB,CAGnB,kBAAmB,CAFnB,aAAc,CAGd,cAAe,CACf,eAAgB,CAHhB,gBAIF,CAEA,+CACE,eACF,CAEA,2CAEE,wBAAyB,CACzB,eAAgB,CAFhB,UAGF,CAEA,2CACE,kBAAmB,CAKnB,+BAAgC,CAJhC,UAAW,CAGX,eAAgB,CAFhB,YAAa,CACb,gBAGF,CAEA,2CAEE,+BAAgC,CADhC,iBAAkB,CAElB,qBACF,CAEA,uDACE,kBACF,CAEA,oBAEE,kBAAmB,CACnB,UAAY,CACZ,cAAe,CACf,eAAgB,CAJhB,eAKF,CAEA,UACE,aAAc,CACd,eACF,CAEA,UACE,aAAc,CACd,eACF,CAEA,iBACE,gBAAiB,CACjB,eACF,CAEA,eACE,wBAAyB,CACzB,kBAAmB,CACnB,kBAAmB,CACnB,eACF,CAEA,cAKE,kBAAmB,CAJnB,kBAAmB,CAKnB,+BAAgC,CAHhC,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,mBACE,UAAW,CACX,gBACF,CAOA,6BACE,UAAW,CACX,cACF,CAEA,mBAEE,eAAgB,CAChB,+BAAgC,CAEhC,UAAW,CADX,iBAAkB,CAHlB,iBAKF,CAEA,mBAEE,wBAAyB,CADzB,UAEF,CAEA,gBACE,kBAAmB,CAKnB,+BAAgC,CADhC,UAAW,CADX,eAAgB,CAFhB,iBAAkB,CAClB,gBAIF,CAEA,gBAEE,+BAAgC,CADhC,iBAEF,CAEA,OACE,aAGF,CAEA,eAJE,eAAgB,CAChB,iBAOF,CAJA,QACE,aAGF,CAEA,iBACE,gBAAiB,CACjB,eACF,CAEA,gBAEE,wBAAyB,CACzB,kBAAmB,CAFnB,kBAAmB,CAGnB,eACF,CAEA,gBAKE,kBAAmB,CAJnB,kDAA6D,CAK7D,+BAAgC,CAHhC,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,mBAEE,UAAW,CACX,gBAAiB,CAFjB,QAGF,CAEA,cACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,cAEE,wBAAyB,CADzB,UAEF,CAEA,iBACE,kBAAmB,CAKnB,+BAAgC,CADhC,UAAW,CADX,eAAgB,CAFhB,iBAAkB,CAClB,gBAIF,CAEA,iBAEE,+BAAgC,CADhC,iBAEF,CAEA,iBACE,kBAAmB,CACnB,eACF,CAEA,aAGE,UAAW,CADX,iBAAkB,CADlB,iBAGF,CAEA,gBAGE,UAAW,CADX,gBAAiB,CADjB,eAGF,CAEA,SAGE,UAAW,CACX,cAAe,CAFf,iBAGF,CAEA,eAWE,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAMF,CAEA,OAQE,+BAAiC,CAPjC,eAAiB,CACjB,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAEA,aACE,gBACF,CAEA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,cACE,kDAOF,CAEA,iBAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,WAWE,kBAAmB,CADnB,YAAa,CANb,cAAe,CAKf,WAAY,CAGZ,sBAAuB,CAJvB,UAMF,CAMA,YACE,YACF,CAiBA,kBAGE,UAAW,CACX,cACF,CAEA,0DAQE,kBAAmB,CAJnB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAMF,CAEA,4EAIE,oBAAqB,CAErB,8BACF,CAEA,qBACE,eACF,CAEA,wBAEE,UAAW,CACX,gBAAiB,CAFjB,eAGF,CAEA,mBAEE,kBAAmB,CADnB,eAEF,CAEA,yBAGE,wBAAyB,CADzB,wBAAyB,CAEzB,iBAAkB,CAClB,eAAgB,CAJhB,UAKF,CAEA,sBACE,kBAAmB,CAKnB,+BAAgC,CADhC,UAAW,CADX,eAAgB,CAFhB,YAAa,CACb,gBAIF,CAEA,sBAEE,+BAAgC,CADhC,WAEF,CAEA,mDAIE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CADZ,UAKF,CAEA,iBACE,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,eAGF,CAEA,0BAEE,kBAAmB,CADnB,UAEF,CAEA,cACE,kBAAmB,CAOnB,kBAAmB,CAHnB,iBAIF,CAEA,YACE,kBAAmB,CACnB,eACF,CAEA,2BAEE,eAAgB,CADhB,iBAEF,CAEA,iBACE,kBAAmB,CAInB,wBAAyB,CADzB,iBAAkB,CAFlB,aAAc,CAId,eAAgB,CAHhB,iBAIF,CAEA,cAME,kBAAmB,CACnB,2BAA4B,CAL5B,4BAA6B,CAC7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAHzB,iBAOF,CAEA,sBAGE,iBAAkB,CAIlB,cAAe,CANf,iBAOF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBACF,CAEA,UACE,kBAEF,CAEA,+BACE,kBAAmB,CAEnB,+BACF,CAEA,mBACE,UAEF,CAEA,yBACE,sBACE,YACF,CAEA,gBACE,qBAAsB,CACtB,iBACF,CAEA,gBACE,qBAAsB,CACtB,UACF,CAEA,kBACE,yBACF,CAEA,iBACE,qBACF,CAEA,aACE,YACF,CAEA,OAEE,WAAY,CADZ,SAEF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CAEA,mBACE,cACF,CACF,CC7rBA,mBAEE,kDAA6D,CAE7D,qDAA4D,CAH5D,gBAAiB,CAEjB,YAEF,CAGA,gBACE,eAAiB,CACjB,kBAAmB,CAGnB,+BAAyC,CADzC,kBAAmB,CADnB,YAGF,CAEA,gBAIE,cAAe,CAFf,6BAIF,CAEA,UACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,cAAe,CAHf,iBAAkB,CAIlB,kBACF,CAEA,gBACE,kBAAmB,CACnB,0BACF,CAEA,mBAEE,aAAc,CAGd,QAAO,CAFP,gBAAiB,CAFjB,QAAS,CAGT,iBAEF,CAEA,gBAEE,QACF,CAEA,YAGE,iBAAkB,CAElB,eAAiB,CAEjB,eAAgB,CANhB,iBAAkB,CAKlB,kBAEF,CAEA,WACE,kBAEF,CAEA,iBACE,kBAEF,CAEA,YACE,kBAEF,CAEA,kBACE,kBAEF,CAGA,cAEE,eAAiB,CACjB,kBAAmB,CAGnB,8BAAwC,CALxC,YAAa,CAIb,kBAAmB,CAEnB,eAAgB,CAHhB,WAIF,CAEA,SAIE,gBAAuB,CADvB,WAAY,CAGZ,iBAAkB,CAGlB,aAAc,CAJd,cAAe,CAJf,QAAO,CAMP,cAAe,CACf,eAAgB,CANhB,iBAAkB,CAQlB,kBAAoB,CACpB,kBACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kDAA6D,CAE7D,8BAA8C,CAD9C,UAEF,CAGA,iBACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAAyC,CACzC,gBAAiB,CAFjB,YAGF,CAUA,WACE,kDAA6D,CAG7D,kBAAmB,CAInB,+BAA+C,CAN/C,UAAY,CAKZ,QAAS,CAET,wBACF,CAMA,iBACE,kDACF,CAEA,eACE,kDACF,CAEA,oBACE,kDACF,CAEA,mBACE,kDACF,CAEA,WACE,gBAAiB,CACjB,UACF,CAEA,cAEE,cAAe,CAEf,eAAgB,CADhB,UAEF,CAEA,YAEE,gBAAiB,CADjB,QAGF,CAGA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,mBAIE,+BAAgC,CAHhC,aAAc,CACd,kBAAmB,CACnB,mBAEF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAGE,kBAAmB,CAEnB,kBAAmB,CAEnB,6BAA8B,CAD9B,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,YAAa,CAIb,kBACF,CAEA,oBACE,kBAAmB,CACnB,yBACF,CAEA,wBAEE,kBAAmB,CADnB,yBAEF,CAEA,8BACE,kBACF,CAEA,MACE,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CAGZ,eAAiB,CACjB,eAAiB,CACjB,cAAe,CAJf,eAAgB,CAKhB,iBACF,CAEA,MAEE,aAAc,CACd,QAAO,CAFP,eAAgB,CAGhB,aACF,CAEA,iBACE,aAAc,CACd,eACF,CAEA,gBACE,aAAc,CACd,eACF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CACf,QAAS,CAJT,6BAA8B,CAE9B,kBAGF,CAEA,oBAEE,aAAc,CADd,QAEF,CAEA,aAEE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,gBAAiB,CAIjB,2BACF,CAEA,mBAEE,oBAAqB,CADrB,YAEF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,gBAAiB,CACjB,eACF,CAEA,cACE,kBAAmB,CAGnB,6BAA8B,CAF9B,iBAAkB,CAClB,YAAa,CAEb,kBACF,CAEA,oBACE,kBAAmB,CACnB,yBACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,YAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,cACE,aAAc,CACd,eACF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CAGb,cAAe,CACf,QAAS,CAHT,6BAIF,CAEA,UACE,aAAc,CACd,eACF,CAEA,OACE,aAAc,CACd,eACF,CAEA,SACE,aAAc,CACd,eACF,CAGA,SAKE,kBAAmB,CAEnB,yBAA0B,CAD1B,iBAAkB,CAJlB,aAMF,CAGA,yBACE,mBACE,YACF,CAEA,gBAEE,iBACF,CAEA,8BAJE,qBAMF,CAEA,SACE,iBACF,CAMA,6BACE,yBACF,CAEA,iBAEE,sBAAuB,CADvB,qBAEF,CAEA,iBAEE,mBAAoB,CADpB,qBAEF,CAEA,aACE,UACF,CACF,CAEA,yBAKE,iCACE,YACF,CAEA,WACE,YACF,CAEA,WACE,cACF,CAEA,YACE,gBACF,CAEA,gBACE,qBAAsB,CACtB,UACF,CAEA,YACE,UACF,CACF,CChbA,oBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,iBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,oBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,mBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAEA,SAEE,kBAAmB,CAEnB,eAAgB,CADhB,kBAAmB,CAFnB,iBAMF,CAEA,iBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,eACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,eAEE,eAAiB,CACjB,kBAAmB,CAGnB,+BAAsC,CALtC,YAAa,CAIb,kBAAmB,CAEnB,eAAgB,CAHhB,WAIF,CAEA,YAIE,gBAAuB,CADvB,WAAY,CAIZ,kBAAmB,CAFnB,aAAc,CAGd,cAAe,CAPf,QAAO,CAKP,eAAgB,CAKhB,eAAgB,CAThB,iBAAkB,CAOlB,uBAAyB,CACzB,kBAEF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kDAA6D,CAE7D,+BAA+C,CAD/C,UAEF,CAEA,eACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,YAEF,CAEA,gBAME,+BAAgC,CAJhC,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAAkB,CAIlB,mBAEF,CAEA,WAGE,aAAS,CAAT,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAcA,0DAQE,kBACF,CAEA,4EAKE,eAEF,CAOA,gBAEE,kBAAmB,CADnB,kBAAmB,CAEnB,QACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,QAEF,CAEA,qCAEE,WAAY,CACZ,QAAS,CAFT,UAIF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aACE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CADnB,YAAa,CAGb,iBAAkB,CAClB,uBACF,CAEA,mBACE,oBAAqB,CACrB,+BACF,CAEA,gBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,eAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,uBAGE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAEf,gBAAkB,CAHlB,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,YACE,kDAA6D,CAC7D,UACF,CAEA,kBAEE,+BAA8C,CAD9C,0BAEF,CAEA,WACE,kDAA6D,CAC7D,UACF,CAEA,iBAEE,+BAA6C,CAD7C,0BAEF,CAEA,YAIE,eAAiB,CAFjB,yBAA0B,CAC1B,kBAAmB,CAEnB,cAAe,CAJf,YAAa,CAKb,uBACF,CAEA,kBAEE,kBAAmB,CADnB,oBAEF,CAEA,cAGE,4BAA6B,CAF7B,iBAGF,CAEA,UAIE,WAAY,CACZ,kBAAmB,CAGnB,cAAe,CAFf,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAThB,iBAAkB,CAQlB,uBAEF,CAEA,+BAEE,gCACF,CAmBA,yBACE,oBACE,YACF,CAEA,oBACE,cACF,CAEA,eACE,qBAAsB,CACtB,OACF,CAEA,YACE,cACF,CAEA,WAEE,QACF,CAEA,2BAJE,yBAMF,CAEA,eACE,YACF,CACF,CAGA,mCACE,oBACE,kBAAmB,CACnB,aACF,CAEA,eACE,kBACF,CAEA,0DAGE,kBAAmB,CACnB,oBAAqB,CACrB,aACF,CAEA,aACE,kBAAmB,CACnB,oBACF,CACF,CCrVA,kBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,eAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,kBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,iBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAuBA,oBACE,eAAiB,CAEjB,kBAAmB,CAEnB,+BAAsC,CADtC,kBAAmB,CAFnB,YAIF,CAEA,cAGE,kBAAmB,CACnB,kBAAmB,CAFnB,WAAY,CAIZ,kBAAmB,CADnB,eAAgB,CAJhB,UAMF,CAEA,eAEE,kDAA6D,CAE7D,kBAAmB,CAHnB,WAAY,CAEZ,yBAEF,CAEA,eAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,YACE,eAAiB,CAEjB,kBAAmB,CAEnB,gCAAuC,CADvC,kBAAmB,CAFnB,YAIF,CAEA,eAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,YAGE,aAAS,CAAT,QAAS,CADT,wDAEF,CAEA,WAGE,kBAAmB,CAEnB,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CALnB,YAAa,CACb,qBAAsB,CAEtB,YAAa,CAIb,uBACF,CAEA,iBAEE,+BAAsC,CADtC,0BAEF,CAEA,WACE,cAAe,CACf,iBACF,CAEA,YAEE,aAAc,CADd,eAAiB,CAEjB,iBACF,CAEA,YAGE,aAAc,CAFd,gBAAiB,CACjB,eAEF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aACE,eAAiB,CAEjB,kBAAmB,CACnB,gCAAuC,CAFvC,YAAa,CAGb,iBAAkB,CAClB,uBACF,CAEA,mBAEE,gCAAwC,CADxC,0BAEF,CAEA,gBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,eAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAEA,YAEE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAIf,oBAAqB,CAFrB,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAOlB,oBAAqB,CAFrB,uBAIF,CAEA,mBACE,kDAA6D,CAC7D,UACF,CAEA,wCAEE,+BAA8C,CAD9C,0BAEF,CAEA,oBACE,kDAA6D,CAC7D,UACF,CAEA,0BAEE,+BAA8C,CAD9C,0BAEF,CAEA,qBAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAEA,YACE,YACF,CAEA,gBACE,eAAiB,CAEjB,kBAAmB,CACnB,gCAAuC,CAFvC,YAGF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,YAGE,aAAc,CACd,iBAAkB,CAFlB,YAAa,CADb,iBAIF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAGE,sBAAuB,CAEvB,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CALnB,YAAa,CACb,6BAA8B,CAE9B,YAAa,CAIb,uBACF,CAEA,oBACE,kBAAmB,CACnB,oBACF,CAEA,aACE,QACF,CAEA,aAEE,aAAc,CAEd,gBAAiB,CAHjB,eAAgB,CAEhB,iBAEF,CAEA,gBAKE,aAAc,CAJd,YAAa,CAGb,eAAiB,CAFjB,QAAS,CACT,iBAGF,CAEA,oBACE,aAAc,CACd,kBACF,CAEA,cACE,YAAa,CACb,cAAe,CACf,OACF,CAEA,eACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,sBACE,YAAa,CACb,OACF,CAEA,mBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CAEf,cAAe,CAPf,gBAAiB,CAMjB,uBAEF,CAEA,yBACE,kBAAmB,CACnB,oBACF,CAgBA,eACE,eAAiB,CACjB,kBAAmB,CAGnB,gCAAuC,CADvC,eAAgB,CADhB,UAGF,CAEA,cAGE,kDAA6D,CAD7D,+BAAgC,CAGhC,2BAA4B,CAJ5B,iBAKF,CAEA,iBAEE,gBAEF,CAEA,eACE,YACF,CAEA,iBAGE,aAAc,CADd,eAAgB,CADhB,eAGF,CAEA,gBACE,kBAAmB,CAInB,wBAAyB,CAFzB,iBAAkB,CAClB,eAAgB,CAFhB,YAIF,CAEA,mBAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,kBAEE,eAAiB,CADjB,YAEF,CAEA,eAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,iBAEF,CAEA,yBAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAKlB,uBACF,CAWA,aACE,kDAA6D,CAC7D,UACF,CAEA,kCAEE,+BAA6C,CAD7C,0BAEF,CAEA,sBAEE,kBAAmB,CADnB,UAEF,CAcA,yBACE,kBACE,YACF,CAEA,kBACE,gBACF,CAEA,gBACE,yBACF,CAEA,YACE,mCACF,CAEA,cACE,qBAAsB,CACtB,QACF,CAEA,gBAEE,OACF,CAEA,+BAJE,qBAMF,CACF,CChdA,qBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,kBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,qBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,oBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAmDA,kBACE,yBACF,CAEA,uBACE,yBACF,CAEA,oBACE,yBACF,CAuBA,oBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CA2BA,kBAEE,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAOlB,uBAAyB,CACzB,kBACF,CAEA,wBAEE,+BAA8C,CAD9C,0BAEF,CAgBA,qBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,UAKF,CAkCA,eACE,YACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,iBAME,+BAAgC,CAJhC,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAAkB,CAIlB,mBAEF,CA2CA,qBAEE,eAAgB,CADhB,eAEF,CAEA,cAME,eAAgB,CAFhB,gBAGF,CAqCA,2BACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,eAEF,CAEA,iBAEE,wBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,oBACE,kDAA6D,CAC7D,UAAY,CAIZ,gBAAkB,CADlB,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,oBAEE,+BAAgC,CAChC,aAAc,CAFd,YAGF,CAEA,gCACE,kBACF,CAEA,eAEE,aAAc,CADd,eAEF,CAEA,QAEE,aAAc,CADd,eAEF,CAkBA,sBAGE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,cAAe,CALf,gBAAiB,CAIjB,uBAEF,CAyCA,yBACE,qBACE,YACF,CAEA,qBACE,gBACF,CAEA,YACE,yBACF,CAEA,oBAEE,mBACF,CAEA,oCAJE,qBAMF,CAEA,6BAEE,cACF,CAEA,qBACE,WAAY,CACZ,eACF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CAEA,2BACE,eACF,CACF,CCreA,yBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,sBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAKb,iBAAkB,CAPlB,iBAQF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CAGb,QAAS,CAFT,sBAAuB,CAGvB,kBACF,CAEA,yBAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,oBACE,iBACF,CAEA,cASE,2BAA4B,CAR5B,kBAAmB,CAEnB,iBAAkB,CADlB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAChB,cAAe,CAHf,eAAgB,CAIhB,iBAEF,CAEA,wBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAEA,wBAGE,kBAAmB,CAInB,eAAiB,CAEjB,kBAAmB,CACnB,+BAAsC,CATtC,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAAmB,CAInB,YAGF,CAEA,iBACE,QACF,CAEA,eAKE,eAAiB,CAHjB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAGf,eAAgB,CANhB,iBAAkB,CAKlB,uBAEF,CAEA,qBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,iBAEE,QAEF,CAEA,2CAIE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAEf,eAAiB,CAHjB,eAAgB,CAHhB,iBAAkB,CAKlB,uBAAyB,CAEzB,kBACF,CAEA,cACE,kDAA6D,CAC7D,UACF,CAEA,oBAEE,+BAA8C,CAD9C,0BAEF,CAEA,eACE,kDAA6D,CAC7D,UACF,CAEA,qBAEE,+BAA6C,CAD7C,0BAEF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,oBACE,kBACF,CAEA,uBAME,gCAAkC,CALlC,eAAiB,CAEjB,kBAAmB,CAEnB,+BAAsC,CADtC,kBAAmB,CAFnB,YAKF,CAEA,0BAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,eAGE,aAAS,CAAT,QAAS,CADT,wDAEF,CAEA,cAEE,kBAAmB,CAInB,iBAAkB,CAFlB,cAAe,CAHf,YAAa,CAEb,QAAS,CAET,YAAa,CAEb,uBACF,CAEA,oBACE,kBACF,CAEA,mCAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,kBAGE,eAAiB,CACjB,kBAAmB,CACnB,+BAAsC,CAHtC,iBAAkB,CADlB,iBAKF,CAEA,uBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,qBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,oBAEE,aAAc,CADd,QAEF,CAEA,mBACE,eAAiB,CAOjB,2BAAkC,CANlC,kBAAmB,CAEnB,+BAAsC,CAEtC,cAAe,CAHf,YAAa,CAIb,iBAAkB,CAFlB,uBAIF,CAEA,yBAEE,+BAAuC,CADvC,0BAEF,CAEA,0BAEE,+CAA6D,CAD7D,yBAEF,CAEA,sBACE,UACF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBAEE,eAAiB,CADjB,eAEF,CAEA,mBAEE,aAAc,CADd,eAEF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,cAIF,CAEA,sBAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAEA,mBACE,kBAAmB,CAInB,wBAAyB,CAFzB,iBAAkB,CAClB,kBAAmB,CAFnB,YAIF,CAEA,0BACE,aAAc,CACd,aAAc,CACd,iBACF,CAEA,sBAGE,aAAc,CAFd,QAAS,CACT,kBAEF,CAEA,sBACE,iBACF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,oBAGE,iBAAkB,CAClB,aAAc,CAFd,WAAY,CADZ,UAIF,CAEA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,YAEE,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CANhB,gBAAiB,CAQjB,uBACF,CAEA,kBAEE,+BAA+C,CAD/C,0BAEF,CAEA,YAIE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,eAAiB,CAPjB,eAAgB,CAMhB,uBAEF,CAOA,kBAQE,2BAA4B,CAF5B,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAClB,QAAS,CAET,SAKF,CAEA,iBACE,GAEE,SAAU,CADV,kBAEF,CACA,IAEE,UAAY,CADZ,oBAEF,CACA,GAEE,SAAU,CADV,kBAEF,CACF,CAcA,yBACE,yBACE,YACF,CAEA,yBACE,gBACF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAEA,wBAEE,mBAAoB,CADpB,qBAEF,CAEA,iBACE,sBACF,CAEA,eACE,cACF,CAEA,eACE,yBACF,CAEA,qBAGE,OACF,CAEA,0CAJE,sBAAuB,CADvB,qBASF,CAJA,qBAGE,QACF,CACF,CCpaA,2BAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,wBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,2BAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,0BAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAiDA,kBAAoB,yBAA4B,CAwBhD,kBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CAqBA,2DAIE,oBAAqB,CACrB,8BACF,CAEA,eAEE,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAOlB,uBAAyB,CACzB,kBACF,CAEA,qBAEE,+BAA8C,CAD9C,0BAEF,CAgBA,kBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,UAKF,CAEA,cAME,kDAGF,CAuBA,YACE,YACF,CAoDA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAEA,+BAEE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,eACE,yBACF,CAEA,gBACE,yBACF,CAkBA,cAEE,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CANhB,gBAAiB,CAQjB,uBACF,CAEA,oBAEE,+BAA8C,CAD9C,0BAEF,CAEA,YAGE,aAAS,CAET,kBAAmB,CAJnB,YAAa,CAEb,QAAS,CADT,sCAAuC,CAEvC,kBAEF,CAEA,qCAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,iBAIF,CAEA,iDAGE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,iBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CALf,WAAY,CAMZ,uBACF,CAEA,uBACE,kBAAmB,CACnB,oBACF,CAEA,eAME,eAAiB,CAEjB,wBAAyB,CADzB,iBAAkB,CAJlB,aAAc,CACd,gBAAiB,CAFjB,eAAgB,CAOhB,eAAgB,CAJhB,YAAa,CAJb,eASF,CAEA,eAEE,kBACF,CAEA,mBAGE,kBAAmB,CAFnB,oBAAqB,CAIrB,gBAAiB,CADjB,eAAgB,CAFhB,iBAIF,CAEA,4BACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,8BACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CA8BA,UACE,kDAEF,CAEA,+BAEE,+BACF,CAEA,mBACE,UAAY,CAEZ,wBACF,CAEA,yBACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,eAEF,CAEA,eAEE,wBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,kBACE,kDAA6D,CAC7D,UAAY,CAIZ,gBAAkB,CADlB,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,kBAEE,+BAAgC,CAChC,aAAc,CAFd,YAGF,CAEA,8BACE,kBACF,CAEA,WAEE,aAAc,CADd,eAEF,CAEA,QAEE,aAEF,CAEA,0BASE,cACF,CAOA,gCAIE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,cAAe,CALf,gBAAiB,CAIjB,uBAEF,CAYA,UACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kBAAmB,CACnB,oBACF,CAsCA,0BACE,iBACE,yBACF,CACF,CAEA,yBACE,2BACE,YACF,CAEA,2BACE,gBACF,CAEA,YACE,yBACF,CAEA,kBAEE,mBACF,CAEA,kCAJE,qBAMF,CAEA,yCAGE,cACF,CAEA,kBACE,WAAY,CACZ,eACF,CAMA,uBAHE,yBAMF,CAHA,YAEE,QACF,CAEA,cACE,qBACF,CAEA,yBACE,eACF,CACF,CC7mBA,mBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,gBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,mBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,kBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAgDA,iBAAmB,yBAA4B,CAC/C,oBAAsB,yBAA4B,CAClD,kBAAoB,yBAA4B,CAuBhD,kBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CAoBA,yCAGE,oBAAqB,CACrB,8BACF,CAEA,gBAEE,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAOlB,uBAAyB,CACzB,kBACF,CAEA,sBAEE,+BAA6C,CAD7C,0BAEF,CAgBA,mBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,UAKF,CAEA,cAME,kDAGF,CAuBA,aACE,YACF,CA4CA,4EAIE,oBAAqB,CACrB,8BACF,CA4DA,wBAEE,8CAOF,CAUA,6CAGE,oBAAqB,CACrB,8BACF,CAgFA,UACE,kDAEF,CAEA,+BAEE,+BACF,CAOA,yBACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,eAEF,CAEA,eAEE,wBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,kBACE,kDAA6D,CAC7D,UAAY,CAIZ,gBAAkB,CADlB,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,kBAEE,+BAAgC,CAChC,aAAc,CAFd,YAGF,CAEA,8BACE,kBACF,CAEA,eAEE,aAAc,CADd,eAEF,CAEA,QAEE,aAEF,CAEA,0BAGE,kBAAmB,CACnB,UAAY,CAIZ,oBAAqB,CAHrB,gBAAkB,CAClB,eAAgB,CAGhB,eAAgB,CAPhB,gBAAiB,CAKjB,iBAGF,CAOA,mCAIE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,cAAe,CALf,gBAAiB,CAIjB,uBAEF,CAYA,aACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kBAAmB,CACnB,oBACF,CAsCA,yBACE,mBACE,YACF,CAEA,mBACE,gBACF,CAEA,YACE,yBACF,CAEA,kBAEE,mBACF,CAEA,kCAJE,qBAMF,CAEA,6BAEE,cACF,CAEA,mBACE,WAAY,CACZ,eACF,CAMA,mCAHE,yBAOF,CAJA,wBAGE,OACF,CAEA,cACE,qBACF,CAEA,yBACE,eACF,CACF,CCvmBA,2BAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,wBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,2BAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,0BAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAuBA,iBACE,eAAiB,CAEjB,kBAAmB,CAEnB,+BAAsC,CADtC,kBAAmB,CAFnB,YAIF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAEA,UAEE,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAOlB,uBACF,CAEA,+BAEE,+BAA8C,CAD9C,0BAEF,CAEA,mBAEE,kBAAmB,CADnB,UAEF,CAEA,gBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CAEb,QAAS,CACT,iBAIF,CAEA,oBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAIZ,cAAe,CADf,eAAiB,CALjB,gBAAiB,CAOjB,uBACF,CAEA,0BACE,kBACF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CAEA,gBACE,QACF,CAEA,cAME,eAAiB,CAHjB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAHf,iBAAkB,CAKlB,uBAAyB,CANzB,UAOF,CAEA,oBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAEA,qCAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAEf,eAAiB,CAHjB,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,gBACE,kDAA6D,CAC7D,UACF,CAEA,sBAEE,+BAA8C,CAD9C,0BAEF,CAEA,qBACE,kBAAmB,CACnB,aACF,CAEA,2BACE,kBACF,CAEA,iBAIE,kBAAmB,CACnB,iBAAkB,CAHlB,aAAc,CADd,eAAgB,CAEhB,gBAGF,CAEA,gBACE,eAAiB,CAEjB,kBAAmB,CAEnB,+BAAsC,CADtC,kBAAmB,CAFnB,YAIF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,eACE,YAAa,CACb,qBACF,CAEA,8BAEE,kBAAmB,CADnB,kBAAmB,CAEnB,QACF,CAEA,qBAGE,aAAc,CACd,gBAAkB,CAFlB,eAAgB,CADhB,iBAIF,CAEA,sBAEE,cAAe,CADf,eAEF,CAEA,2CAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,uBACF,CAEA,uDAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,qCAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,gBAGE,QAAS,CADT,sBAAuB,CAEvB,kBAEF,CAEA,uBAGE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAEf,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,WACE,kDAEF,CAEA,gCAEE,+BAA8C,CAD9C,0BAEF,CAEA,YACE,kDAEF,CAEA,iCAEE,+BAA8C,CAD9C,0BAEF,CAEA,yCAGE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,aAIE,aAAc,CACd,iBAAkB,CAJlB,gBAAmB,CAEnB,iBAAkB,CADlB,iBAOF,CAEA,2BALE,eAAiB,CACjB,kBAAmB,CACnB,+BAWF,CARA,cAOE,sBAA6B,CAD7B,cAAe,CAHf,YAAa,CAEb,uBAGF,CAEA,oBAEE,gCAAwC,CADxC,0BAEF,CAEA,uBAEE,+CAA6D,CAD7D,oBAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,qBACE,gBAAiB,CACjB,uBACF,CAEA,cACE,kBACF,CAEA,gBAEE,aAAc,CACd,eAAiB,CAFjB,YAGF,CAEA,qBACE,aACF,CAEA,iBAIE,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CAHlB,kBAAmB,CACnB,YAAa,CAFb,iBAMF,CAEA,qBAEE,WAAY,CACZ,iBAAkB,CAFlB,cAGF,CAEA,cAGE,aAAc,CAFd,qBAAsB,CACtB,eAAiB,CAEjB,eACF,CAEA,iBACE,iBACF,CAEA,iBAEE,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CANhB,gBAAiB,CAQjB,uBACF,CAEA,uBAEE,+BAA8C,CAD9C,0BAEF,CAgBA,eACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CAFhB,YAAa,CACb,iBAAkB,CAElB,UAEF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,mBAKE,wBAAyB,CACzB,kBAAmB,CAHnB,YAAa,CACb,gBAAiB,CAGjB,eAAgB,CANhB,iBAAkB,CAClB,WAMF,CAEA,cAOE,0BAA2B,CAD3B,kBAAmB,CADnB,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,gBACE,GAAK,KAAQ,CACb,IAAM,oBAAuB,CAC7B,GAAO,KAAQ,CACjB,CAEA,qBAEE,aAAc,CADd,eAEF,CAEA,iBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,aAAc,CAId,cAAe,CADf,eAAgB,CAGhB,eAAgB,CARhB,iBAAkB,CAOlB,uBAEF,CAEA,uBACE,kBACF,CAcA,yBACE,2BACE,YACF,CAEA,2BACE,gBACF,CAEA,kBAEE,mBAAoB,CADpB,qBAEF,CAEA,oBACE,sBACF,CAEA,eACE,yBACF,CAEA,gBAEE,kBAAmB,CADnB,qBAEF,CAEA,eACE,yBACF,CAEA,kBAEE,mBAAoB,CADpB,qBAEF,CAEA,gBACE,qBAAsB,CACtB,iBACF,CACF,CC5hBA,sBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,mBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,sBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,qBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CA+CA,iBAAmB,yBAA4B,CAC/C,oBAAsB,yBAA4B,CAClD,mBAAqB,yBAA4B,CACjD,kBAAoB,yBAA4B,CAuBhD,qBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CASA,6BAME,eAAiB,CAHjB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAGf,eAAgB,CANhB,iBAAkB,CAKlB,uBAEF,CAEA,yCAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,mBAEE,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAIZ,cAAe,CADf,eAAgB,CALhB,iBAAkB,CAOlB,uBAAyB,CACzB,kBACF,CAEA,yBAEE,+BAA8C,CAD9C,0BAEF,CAgBA,sBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,UAKF,CAEA,cAME,kDAGF,CAuBA,gBACE,YACF,CAEA,aAGE,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CAHnB,kBAAmB,CACnB,YAIF,CAEA,WAEE,wDAGF,CA6BA,4EAIE,oBAAqB,CACrB,8BACF,CAEA,eAGE,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CAHnB,kBAAmB,CACnB,YAIF,CAEA,gBAGE,kBAAmB,CAGnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,cAEE,kDAA6D,CAE7D,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CANhB,gBAAiB,CAQjB,uBACF,CAEA,oBAEE,+BAA8C,CAD9C,0BAEF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAKE,kBAAmB,CAGnB,aAAc,CADd,eAEF,CAEA,wBARE,aAAS,CAGT,iBAAkB,CALlB,YAAa,CAEb,QAAS,CADT,8CAA+C,CAE/C,YAgBF,CATA,UAIE,kBAAmB,CAEnB,eAAiB,CAEjB,wBACF,CAEA,iCAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,gBAIF,CAEA,6CAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,aACE,4BAA8B,CAC9B,aAAc,CACd,eACF,CAEA,iBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CALf,WAAY,CAMZ,uBACF,CAEA,uBACE,kBAAmB,CACnB,oBACF,CAEA,kBACE,aAAc,CACd,YAIF,CAEA,kCALE,eAAiB,CAEjB,wBAAyB,CADzB,kBAUF,CANA,gBACE,eAAgB,CAChB,YAIF,CAEA,WAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,aAEF,CAEA,sBACE,kBACF,CAEA,aAIE,4BAA6B,CAD7B,aAAc,CADd,gBAAiB,CADjB,eAAgB,CAKhB,eAAgB,CADhB,gBAEF,CA8BA,UACE,kDAEF,CAEA,+BAEE,+BACF,CAOA,4BACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,eAEF,CAEA,kBAEE,wBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,qBACE,kDAA6D,CAC7D,UAAY,CAIZ,gBAAkB,CADlB,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,qBAEE,+BAAgC,CAChC,aAAc,CAFd,YAGF,CAEA,iCACE,kBACF,CAEA,mCACE,kBACF,CAEA,kBAEE,aAAc,CADd,eAEF,CAEA,QAEE,aAEF,CAEA,cACE,aAAc,CACd,eACF,CAEA,cAEE,kBAAmB,CACnB,UAAY,CAIZ,oBAAqB,CAHrB,gBAAkB,CAClB,eAAgB,CAGhB,eAAgB,CAPhB,gBAAiB,CAKjB,iBAGF,CAOA,8CAKE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,cAAe,CALf,gBAAiB,CAIjB,uBAEF,CAOA,+BACE,kBAAmB,CACnB,oBACF,CAYA,aACE,kBAAmB,CACnB,aACF,CAEA,mBACE,kBAAmB,CACnB,oBACF,CAOA,iCACE,kBAAmB,CACnB,oBACF,CAEA,wCAGE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAqBA,yBACE,sBACE,YACF,CAEA,sBACE,gBACF,CAEA,YACE,yBACF,CAEA,qBAEE,mBACF,CAEA,qCAJE,qBAMF,CAEA,6BAEE,cACF,CAEA,sBACE,WAAY,CACZ,eACF,CAMA,mCAHE,yBAOF,CAJA,wBAGE,OACF,CAEA,cACE,qBACF,CAEA,4BACE,eACF,CACF,CCloBA,oBAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,iBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,oBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,mBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAEA,SAME,8BAAgC,CAJhC,kBAAmB,CAEnB,eAAgB,CADhB,kBAAmB,CAFnB,iBAAkB,CAIlB,iBAEF,CAEA,iBACE,kDAA6D,CAE7D,+BAA8C,CAD9C,UAEF,CAEA,eACE,kDAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WAME,kBAAmB,CALnB,eAAiB,CAQjB,qBAAsB,CANtB,kBAAmB,CACnB,gCAAuC,CACvC,YAAa,CAEb,QAAS,CALT,YAAa,CAMb,uBAEF,CAEA,iBAEE,gCAAwC,CADxC,0BAEF,CAEA,iBAAmB,yBAA4B,CAC/C,oBAAsB,yBAA4B,CAClD,oBAAsB,yBAA4B,CAClD,oBAAsB,yBAA4B,CAElD,WACE,cAAe,CACf,UACF,CAEA,cAEE,aAAc,CACd,eAAiB,CACjB,eAAgB,CAEhB,mBAAqB,CALrB,cAAiB,CAIjB,wBAEF,CAEA,aAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,sBACE,aACF,CAEA,sBACE,aACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CAKb,cAAe,CADf,QAAS,CAHT,6BAA8B,CAE9B,kBAGF,CAEA,gBACE,YAAa,CAGb,QAAO,CADP,cAAe,CADf,QAGF,CAEA,yCAOE,eAAiB,CAHjB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAGf,eAAgB,CANhB,iBAAkB,CAKlB,uBAEF,CAEA,2DAIE,oBAAqB,CACrB,8BAA4C,CAF5C,YAGF,CAEA,gBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,kCAGE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAGf,gBAAkB,CAJlB,eAAgB,CAHhB,iBAAkB,CAKlB,uBAAyB,CACzB,kBAEF,CAEA,iBACE,kDAA6D,CAC7D,UACF,CAEA,uBAEE,+BAA8C,CAD9C,0BAEF,CAEA,iBACE,kDAA6D,CAC7D,UACF,CAEA,uBAEE,+BAA6C,CAD7C,0BAEF,CAEA,eASE,kBAAmB,CAHnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,oBACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAAuC,CAFvC,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,UAKF,CAEA,cAGE,kBAAmB,CAGnB,kDAA6D,CAD7D,+BAAgC,CAGhC,2BAA4B,CAD5B,UAAY,CANZ,YAAa,CACb,6BAA8B,CAE9B,iBAKF,CAEA,iBAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,WACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAY,CAEZ,cAAe,CADf,gBAAiB,CAEjB,WAAY,CAEZ,uBACF,CAEA,iBACE,gBACF,CAEA,cACE,YACF,CAEA,WAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAOA,uBACE,gBACF,CASA,0DAQE,eAAiB,CAJjB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAHf,iBAAkB,CAIlB,uBAEF,CAEA,4EAIE,oBAAqB,CACrB,8BAA4C,CAF5C,YAGF,CAEA,cAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,gBAEF,CAEA,sBAGE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAEf,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,YACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBACF,CAEA,UACE,kDAA6D,CAC7D,UACF,CAEA,+BAEE,+BAA6C,CAD7C,0BAEF,CAEA,mBAEE,kBAAmB,CADnB,UAEF,CAEA,0BACE,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,eAEF,CAEA,gBAEE,wBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,mBACE,kDAA6D,CAC7D,UAAY,CAIZ,gBAAkB,CADlB,eAAgB,CAFhB,iBAAkB,CAClB,gBAGF,CAEA,mBAEE,+BAAgC,CAChC,aAAc,CAFd,YAGF,CAEA,+BACE,kBACF,CAEA,gBAEE,aAAc,CADd,eAEF,CAEA,QACE,eAAgB,CAChB,eACF,CAEA,iBACE,aACF,CAEA,iBACE,aACF,CAEA,YAEE,kBAAmB,CACnB,UAAY,CAIZ,oBAAqB,CAHrB,gBAAkB,CAClB,eAAgB,CAGhB,eAAgB,CAPhB,gBAAiB,CAKjB,iBAGF,CAEA,SACE,YAAa,CACb,OACF,CAEA,iCAIE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,cAAe,CALf,gBAAiB,CAIjB,uBAEF,CAEA,UACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kBAAmB,CACnB,oBACF,CAEA,WACE,kBAAmB,CACnB,aACF,CAEA,iBACE,kBAAmB,CACnB,oBACF,CAEA,YACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBAAmB,CACnB,oBACF,CAEA,SAGE,aAAc,CACd,iBAAkB,CAFlB,YAAa,CADb,iBAIF,CAcA,yBACE,oBACE,YACF,CAEA,oBACE,gBACF,CAEA,YACE,yBACF,CAEA,mBAEE,mBACF,CAEA,mCAJE,qBAMF,CAEA,yCAGE,cACF,CAEA,gBACE,sBACF,CAEA,oBACE,WAAY,CACZ,eACF,CAEA,WACE,yBACF,CAEA,cACE,qBACF,CAEA,0BACE,eACF,CACF,CCtfA,4BAIE,kBAAmB,CAFnB,aAAc,CADd,gBAAiB,CAIjB,gBAAiB,CAFjB,YAGF,CAEA,gBAIE,kDAA6D,CAE7D,kBAAmB,CACnB,gCAAuC,CAFvC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,mBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,kBAEE,gBAAiB,CADjB,QAAS,CAET,UACF,CAEA,kBACE,kBACF,CAEA,mBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,kBACF,CAEA,kBAUE,kBAAmB,CATnB,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CAGnB,cAAe,CAEf,YAAa,CACb,qBAAsB,CAEtB,QAAS,CAPT,YAAa,CACb,iBAAkB,CAElB,uBAKF,CAEA,wBACE,oBAAqB,CAErB,+BAA8C,CAD9C,0BAEF,CAEA,yBAEE,+CAA6D,CAD7D,oBAAqB,CAErB,+BACF,CAEA,aACE,gBAAiB,CACjB,UACF,CAEA,cAEE,aAAc,CACd,eAAiB,CAFjB,eAAgB,CAIhB,eAAgB,CADhB,iBAEF,CAEA,iBACE,eAAiB,CAEjB,kBAAmB,CAEnB,+BAAsC,CADtC,kBAAmB,CAFnB,YAIF,CAEA,eACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,kBAEF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,eACF,CAEA,kBAGE,aAAc,CACd,gBAAkB,CAFlB,eAAgB,CADhB,iBAIF,CAEA,qCAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,uBACF,CAEA,iDAGE,oBAAqB,CACrB,8BAA4C,CAF5C,YAGF,CAEA,iBACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,kBAEF,CAEA,iBACE,YAAa,CAGb,cAAe,CADf,QAAS,CADT,sBAGF,CAEA,qCAIE,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CAEf,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,cACE,kDAA6D,CAC7D,UACF,CAEA,mCAEE,+BAA6C,CAD7C,0BAEF,CAEA,uBAEE,kBAAmB,CADnB,UAEF,CAEA,WACE,kDAA6D,CAC7D,UACF,CAEA,iBAEE,+BAA6C,CAD7C,0BAEF,CAEA,YACE,kDAA6D,CAC7D,UACF,CAEA,kBAEE,+BAA6C,CAD7C,0BAEF,CAEA,gBAKE,8BAAgC,CAJhC,eAAiB,CACjB,kBAAmB,CAEnB,gCAAuC,CADvC,YAGF,CAEA,oBAIE,+BAAgC,CAFhC,kBAAmB,CACnB,mBAAoB,CAFpB,iBAIF,CAEA,uBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,4BAGE,aAAc,CACd,gBAAiB,CAFjB,YAGF,CAEA,gBACE,eACF,CAEA,eAME,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CALnB,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,YAAa,CADb,iBAQF,CAEA,aACE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CAEnB,eAAgB,CAHhB,YAIF,CAEA,iBAOE,oBAAqB,CAFrB,aAAc,CAHd,iCAAqC,CACrC,eAAiB,CACjB,eAAgB,CAHhB,QAAS,CAKT,oBAEF,CAGA,kBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,gDAGE,eAAiB,CAGjB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,iBACE,6BACF,CAEA,eACE,6BACF,CAEA,gBACE,6BACF,CAEA,eAIE,aAAc,CAHd,gBAAiB,CACjB,eAAgB,CAChB,kBAEF,CAEA,YAEE,wBAAyB,CACzB,aAAc,CAFd,UAGF,CAEA,8BAIE,+BAAgC,CAFhC,YAAa,CACb,gBAEF,CAEA,eACE,kBAAmB,CAEnB,aAAc,CADd,eAEF,CAEA,oBAEE,eAAgB,CADhB,eAEF,CAEA,sBACE,aACF,CAEA,sBACE,aACF,CAEA,WAGE,kBAAmB,CAFnB,4BAA6B,CAC7B,eAEF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,4CAEE,eAAiB,CAGjB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,gBACE,6BACF,CAEA,4BACE,6BACF,CAEA,YAGE,kBAAmB,CACnB,iBAAkB,CAHlB,aAAc,CACd,YAGF,CAEA,kBAEE,aAAc,CADd,eAAgB,CAEhB,kBACF,CAEA,qBAEE,wBAAyB,CACzB,aAAc,CAFd,UAGF,CAEA,gDAIE,wBAAyB,CAFzB,YAAa,CACb,gBAEF,CAEA,wBACE,kBAAmB,CACnB,UAAY,CACZ,eACF,CAEA,mCACE,qBAAsB,CACtB,eACF,CAEA,4BACE,aAAc,CACd,eACF,CAEA,6BACE,aAAc,CACd,eACF,CAEA,eAGE,iBAAkB,CAElB,eAAgB,CAJhB,eAAgB,CAChB,YAAa,CAEb,iBAEF,CAEA,wBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,0BACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,0BACE,eACE,yBACF,CACF,CAEA,yBACE,4BACE,YACF,CAEA,mBACE,gBACF,CAEA,mBACE,yDACF,CAEA,kBACE,YACF,CAEA,aACE,cACF,CAEA,cACE,eACF,CAEA,gCAEE,qBACF,CAEA,YACE,cACF,CAEA,iBAEE,kBAAmB,CADnB,qBAEF,CAEA,qCAIE,eAAgB,CADhB,UAEF,CAEA,gBACE,YACF,CAEA,uBACE,gBACF,CAEA,YACE,eACF,CAEA,8BAEE,WACF,CACF,CCteA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,MAEE,uBAAwB,CACxB,sBAAuB,CACvB,uBAAwB,CACxB,yBAA0B,CAC1B,uBAAwB,CACxB,uBAAwB,CACxB,sBAAuB,CACvB,oBAAqB,CAGrB,YAAgB,CAChB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,oBAAqB,CACrB,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,kBAAmB,CAGnB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,gBAAiB,CAGjB,iCAA0C,CAC1C,6DAA6E,CAC7E,+DAA+E,CAC/E,gEAAgF,CAGhF,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CACzB,uBACF,CAEA,KAQE,kCAAmC,CACnC,iCAAkC,CANlC,aAAsB,CAAtB,qBAAsB,CACtB,aAAc,CAHd,2DAAqE,CAKrE,cAAgC,CAAhC,+BAAgC,CAChC,eAAgB,CAFhB,gBAKF,CAEA,UAVE,wBAAgC,CAAhC,+BAcF,CAGA,iBALE,YAAa,CADb,gBAWF,CALA,YAIE,kBAA0B,CAA1B,yBAA0B,CAF1B,UAGF,CAEA,SAEE,kDAAsF,CAAtF,kFAAsF,CAWtF,+BAA+C,CAF/C,+DAA4B,CAA5B,2BAA4B,CAR5B,UAAmB,CAAnB,kBAAmB,CACnB,YAAa,CACb,qBAAsB,CAEtB,YAAa,CADb,cAAe,CAEf,OAAQ,CACR,KAAM,CAGN,uBAAyB,CAXzB,WAAY,CASZ,YAIF,CAEA,mBACE,UACF,CAEA,gBAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAFrC,iCAAkD,CADlD,mBAA4C,CAA5C,2CAA4C,CAE5C,iBAGF,CAEA,cAME,kBAAmB,CAHnB,UAAmB,CAAnB,kBAAmB,CAEnB,YAAa,CAJb,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAMhB,SAAsB,CAAtB,qBAAsB,CADtB,sBAAuB,CAHvB,oBAAqB,CAKrB,uBACF,CAEA,oBACE,qBACF,CAEA,yBAEE,4CAAiD,CADjD,gBAA+B,CAA/B,8BAEF,CAEA,aACE,QAAO,CAEP,eAAgB,CADhB,gBAA4B,CAA5B,2BAA4B,CAG5B,+BAAqD,CADrD,oBAEF,CAEA,gCACE,SACF,CAEA,sCACE,gBACF,CAEA,sCACE,oBAAoC,CACpC,iBACF,CAEA,UACE,oBAAgC,CAAhC,+BACF,CAEA,UAEE,kBAAmB,CAKnB,4BAAmC,CAHnC,eAAgC,CAHhC,YAAa,CASb,iBAA8B,CAA9B,6BAA8B,CAD9B,eAAgB,CADhB,QAAsB,CAAtB,qBAAsB,CAItB,eAAgB,CAThB,iBAA4C,CAA5C,2CAA4C,CAQ5C,iBAAkB,CANlB,oBAAqB,CACrB,uBAOF,CAEA,iBAOE,oBAAoC,CANpC,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAKN,yBAA2B,CAH3B,OAAQ,CAIR,UACF,CAEA,uBACE,UACF,CAEA,gBAGE,0BACF,CAEA,iCAJE,uBAAgC,CAAhC,+BAAgC,CADhC,UAAmB,CAAnB,kBAWF,CANA,iBACE,0BAA2C,CAI3C,mCAAmD,CADnD,eAEF,CAEA,UAIE,aAAc,CAHd,kBAA8B,CAA9B,6BAA8B,CAE9B,iBAAkB,CADlB,UAGF,CAEA,cAGE,wBAAgC,CAAhC,+BAAgC,CAFhC,QAAO,CACP,kBAAmB,CAEnB,gBAAiB,CAEjB,iBAAkB,CADlB,gCAEF,CAEA,uBACE,iBACF,CAGA,gBAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,+CAAyE,CAAzE,qEAAyE,CAEzE,+BAAwC,CAAxC,uCAAwC,CACxC,4DAA4B,CAA5B,2BAA4B,CAF5B,mBAA4C,CAA5C,2CAA4C,CAG5C,eAAgB,CAChB,KAAM,CACN,WAEF,CAEA,YAEE,6BAA8B,CAE9B,kBAAgC,CAAhC,+BACF,CAEA,0BAJE,kBAAmB,CAFnB,YAcF,CARA,cAGE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAA+B,CAA/B,8BAA+B,CAC/B,eAAgB,CAIhB,QAAsB,CAAtB,qBAAsB,CACtB,+BACF,CAEA,gBAGE,UAAsB,CAAtB,qBACF,CAEA,2BAJE,kBAAmB,CADnB,YAiBF,CAZA,WAKE,kDAA4E,CAA5E,wEAA4E,CAI5E,wBAAiC,CAAjC,gCAAiC,CAHjC,oBAA+B,CAA/B,8BAA+B,CAK/B,gCAA4B,CAA5B,2BAA4B,CAH5B,aAAsB,CAAtB,qBAAsB,CADtB,eAAgB,CAJhB,SAAsB,CAAtB,qBAAsB,CACtB,kBAA4C,CAA5C,2CAA4C,CAM5C,uBAEF,CAEA,iBACE,kDAA6E,CAA7E,yEAA6E,CAE7E,4DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,cAEE,kBAA0B,CAA1B,yBAA0B,CAD1B,YAA0B,CAA1B,yBAEF,CAGA,MACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CAFjC,oBAA+B,CAA/B,8BAA+B,CAC/B,4DAA4B,CAA5B,2BAA4B,CAE5B,eAAgB,CAChB,uBACF,CAEA,YACE,8DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,aAGE,wBAAgC,CAAhC,+BAAgC,CADhC,+BAAwC,CAAxC,uCAAwC,CADxC,mBAA4C,CAA5C,2CAGF,CAEA,YAGE,aAAsB,CAAtB,qBAAsB,CAFtB,kBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,QACF,CAEA,WACE,YAA0B,CAA1B,yBACF,CAEA,aAGE,wBAAgC,CAAhC,+BAAgC,CADhC,4BAAqC,CAArC,oCAAqC,CADrC,mBAA4C,CAA5C,2CAGF,CAGA,KAEE,kBAAmB,CAInB,WAAY,CACZ,mBAA+B,CAA/B,8BAA+B,CAG/B,cAAe,CATf,mBAAoB,CAQpB,iBAA8B,CAA9B,6BAA8B,CAD9B,eAAgB,CAJhB,SAAsB,CAAtB,qBAAsB,CADtB,sBAAuB,CAavB,eAAgB,CAXhB,oBAA4C,CAA5C,2CAA4C,CAU5C,iBAAkB,CAHlB,oBAAqB,CADrB,uBAAyB,CAGzB,wBAAiB,CAAjB,gBAAiB,CADjB,kBAIF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,QAEE,gBAA8B,CAA9B,6BAA8B,CAD9B,mBAA4C,CAA5C,2CAEF,CAEA,QAEE,kBAA8B,CAA9B,6BAA8B,CAD9B,iBAA4C,CAA5C,2CAEF,CAEA,aACE,wBAAsC,CAAtC,qCAAsC,CAEtC,gCAA4B,CAA5B,2BAA4B,CAD5B,UAAmB,CAAnB,kBAEF,CAEA,kCACE,wBAAqC,CAArC,oCAAqC,CACrC,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,eACE,wBAAiC,CAAjC,gCAAiC,CACjC,UAAmB,CAAnB,kBACF,CAEA,oCACE,wBAAiC,CAAjC,gCACF,CAEA,aACE,wBAAsC,CAAtC,qCAAsC,CACtC,UAAmB,CAAnB,kBACF,CAEA,kCACE,wBAAyB,CACzB,0BACF,CAEA,aACE,wBAAsC,CAAtC,qCAAsC,CACtC,UAAmB,CAAnB,kBACF,CAEA,kCACE,wBACF,CAEA,YACE,wBAAqC,CAArC,oCAAqC,CACrC,UAAmB,CAAnB,kBACF,CAEA,iCACE,wBACF,CAEA,aACE,wBAA6B,CAC7B,wBAAsC,CAAtC,qCAAsC,CACtC,aAA2B,CAA3B,0BACF,CAEA,kCACE,wBAAsC,CAAtC,qCAAsC,CACtC,UAAmB,CAAnB,kBACF,CAGA,kBAME,wCAA2C,CAL3C,kBAAmB,CAEnB,iBAAkB,CAElB,kBAAmB,CAHnB,YAAa,CAEb,gBAGF,CAEA,gBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,kBAIF,CAEA,eACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,kBAKE,iCAAyC,CAFzC,UAAW,CADX,cAAe,CADf,iBAAkB,CAGlB,aAEF,CAEA,6BACE,kBACF,CAGA,WAME,gCAAkC,CAHlC,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CAHlB,eAAgB,CAChB,YAAa,CAKb,gBACF,CAEA,cACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,aAEE,UAAW,CACX,eAAgB,CAFhB,kBAGF,CAEA,kBACE,aACF,CAGA,gBAME,wCAA2C,CAH3C,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CAHlB,eAAgB,CAChB,YAKF,CAEA,cACE,aAAc,CAEd,gBAAiB,CACjB,eAAgB,CAFhB,kBAGF,CAEA,aAEE,UAAW,CACX,eAAgB,CAFhB,QAGF,CAGA,WASE,6BAA+B,CAF/B,kCAA2B,CAA3B,0BAA2B,CAH3B,eAAiB,CAIjB,sBAAuC,CALvC,kBAAmB,CAGnB,+BAAsC,CAJtC,aAAc,CADd,eAAgB,CAIhB,YAKF,CAGA,kBACE,GAAO,SAAU,CAAE,mBAAuB,CAC1C,GAAK,SAAU,CAAE,kBAAqB,CACxC,CAEA,sBACE,GAAO,SAAU,CAAE,2BAA8B,CACjD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,oBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,qBACE,GAAiD,YAAa,CAAvD,SAAU,CAAE,2BAA6C,CAChE,GAA2C,gBAAiB,CAAvD,SAAU,CAAE,uBAA6C,CAChE,CAGA,yBAKE,gBAHE,YAMF,CAHA,WAEE,aACF,CAEA,YACE,cACF,CAEA,mBAEE,kBAAmB,CADnB,qBAEF,CAEA,KAEE,eAAgB,CADhB,UAEF,CACF,CAEA,yBAKE,gBACE,YACF,CAEA,YACE,gBACF,CAEA,kBACE,cACF,CACF", "sources": ["index.css", "components/Layout/Layout.css", "components/Login/EnhancedLogin.css", "components/Dashboard/EnhancedDashboard.css", "components/POS/POS.css", "components/Products/Products.css", "components/Customers/Customers.css", "components/Suppliers/Suppliers.css", "components/Invoices/Invoices.css", "components/Accounting/Accounting.css", "components/Reports/Reports.css", "components/Settings/Settings.css", "components/Backup/Backup.css", "components/Employees/Employees.css", "components/Notifications/Notifications.css", "components/JournalEntries/JournalEntries.css", "components/Returns/Returns.css", "components/Barcode/BarcodeManager.css", "components/Quotations/Quotations.css", "components/Vouchers/Vouchers.css", "components/AdvancedReports/AdvancedReports.css", "App.css"], "sourcesContent": ["/* إعدادات عامة للتطبيق */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',\n    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  direction: rtl;\n  text-align: right;\n  background-color: #f5f5f5;\n}\n\n/* إعدادات RTL لـ Ant Design */\n.ant-layout {\n  direction: rtl;\n}\n\n.ant-menu {\n  direction: rtl;\n}\n\n.ant-table {\n  direction: rtl;\n}\n\n.ant-form {\n  direction: rtl;\n}\n\n.ant-input {\n  text-align: right;\n}\n\n.ant-select {\n  text-align: right;\n}\n\n/* تحسين الخطوط العربية */\n.ant-typography {\n  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> U<PERSON>', 'Roboto', sans-serif;\n}\n\n/* تحسين الأزرار */\n.ant-btn {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-weight: 500;\n}\n\n/* تحسين البطاقات */\n.ant-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.ant-card-head-title {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-weight: 600;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n.app {\n  text-align: center;\n  padding: 20px;\n}\n\n.header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px 20px;\n  border-radius: 12px;\n  margin-bottom: 30px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n}\n\n.header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.features {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 30px;\n}\n\n.feature-card {\n  background: white;\n  padding: 30px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.feature-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.feature-card h3 {\n  color: #1890ff;\n  margin-bottom: 15px;\n  font-size: 1.3rem;\n}\n\n.feature-card p {\n  color: #666;\n  line-height: 1.6;\n}\n\n.status {\n  background: #e6f7ff;\n  border: 1px solid #91d5ff;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 30px;\n}\n\n.status h3 {\n  color: #1890ff;\n  margin-bottom: 10px;\n}\n\n.status p {\n  color: #595959;\n  margin: 0;\n}\n", "/* Layout Styles - <PERSON><PERSON><PERSON> Design */\n\n.app-layout {\n  display: flex;\n  width: 100%;\n  min-height: 100vh;\n  background-color: var(--gray-50);\n}\n\n/* Sidebar Styles */\n.sidebar {\n  width: 280px;\n  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  display: flex;\n  flex-direction: column;\n  position: fixed;\n  height: 100vh;\n  right: 0;\n  top: 0;\n  z-index: 1000;\n  box-shadow: var(--shadow-xl);\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.sidebar.collapsed {\n  width: 80px;\n}\n\n.sidebar-header {\n  padding: var(--spacing-xl);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05);\n}\n\n.sidebar-logo {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--white);\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  transition: all 0.3s ease;\n}\n\n.sidebar-logo:hover {\n  transform: scale(1.05);\n}\n\n.logo-icon {\n  font-size: var(--font-size-2xl);\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: var(--spacing-lg) 0;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;\n}\n\n.sidebar-nav::-webkit-scrollbar {\n  width: 4px;\n}\n\n.sidebar-nav::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.sidebar-nav::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n}\n\n.nav-item {\n  margin-bottom: var(--spacing-xs);\n}\n\n.nav-link {\n  display: flex;\n  align-items: center;\n  padding: var(--spacing-md) var(--spacing-xl);\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border-right: 3px solid transparent;\n  gap: var(--spacing-md);\n  font-weight: 500;\n  font-size: var(--font-size-sm);\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 0;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  transition: width 0.3s ease;\n  z-index: -1;\n}\n\n.nav-link:hover::before {\n  width: 100%;\n}\n\n.nav-link:hover {\n  color: var(--white);\n  border-right-color: var(--white);\n  transform: translateX(-2px);\n}\n\n.nav-link.active {\n  background-color: rgba(255, 255, 255, 0.15);\n  color: var(--white);\n  border-right-color: var(--white);\n  font-weight: 600;\n  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.1);\n}\n\n.nav-icon {\n  font-size: var(--font-size-lg);\n  width: 24px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n.sidebar-footer {\n  padding: var(--spacing-lg);\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-footer .nav-link {\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  border-right: none;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-footer .nav-link:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: none;\n}\n\n/* Main Content Styles */\n.main-content {\n  flex: 1;\n  margin-right: 280px;\n  background-color: var(--gray-50);\n  min-height: 100vh;\n  transition: margin-right 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.main-content.expanded {\n  margin-right: 80px;\n}\n\n/* Header Styles */\n.content-header {\n  background: var(--white);\n  border-bottom: 1px solid var(--gray-200);\n  box-shadow: var(--shadow-sm);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  backdrop-filter: blur(10px);\n}\n\n.header-top {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--spacing-lg) var(--spacing-xl);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--gray-900);\n}\n\n.sidebar-toggle {\n  background: none;\n  border: none;\n  font-size: var(--font-size-lg);\n  color: var(--gray-600);\n  cursor: pointer;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n}\n\n.sidebar-toggle:hover {\n  background-color: var(--gray-100);\n  color: var(--primary-color);\n  transform: scale(1.1);\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-lg);\n}\n\n.datetime-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n}\n\n.date {\n  font-weight: 600;\n  color: var(--gray-800);\n}\n\n.time {\n  font-weight: 500;\n  color: var(--primary-color);\n  font-family: 'Courier New', monospace;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-sm) var(--spacing-md);\n  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  color: var(--gray-700);\n  border: 1px solid var(--gray-200);\n  transition: all 0.3s ease;\n}\n\n.user-info:hover {\n  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-100) 100%);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.user-icon {\n  font-size: var(--font-size-lg);\n  color: var(--primary-color);\n}\n\n.user-name {\n  font-weight: 600;\n  white-space: nowrap;\n}\n\n.notifications-btn {\n  position: relative;\n  background: none;\n  border: none;\n  font-size: var(--font-size-xl);\n  color: var(--gray-600);\n  cursor: pointer;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 44px;\n  height: 44px;\n}\n\n.notifications-btn:hover {\n  background-color: var(--gray-100);\n  color: var(--primary-color);\n  transform: scale(1.1);\n}\n\n.notification-badge {\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  background: var(--danger-color);\n  color: var(--white);\n  font-size: var(--font-size-xs);\n  font-weight: 600;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 18px;\n  height: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n/* Content Body */\n.content-body {\n  flex: 1;\n  padding: var(--spacing-xl);\n  overflow-y: auto;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .sidebar {\n    transform: translateX(100%);\n  }\n  \n  .sidebar.open {\n    transform: translateX(0);\n  }\n  \n  .main-content {\n    margin-right: 0;\n  }\n  \n  .main-content.expanded {\n    margin-right: 0;\n  }\n  \n  .header-actions {\n    gap: var(--spacing-md);\n  }\n  \n  .datetime-info {\n    display: none;\n  }\n}\n\n@media (max-width: 768px) {\n  .header-top {\n    padding: var(--spacing-md);\n  }\n  \n  .content-body {\n    padding: var(--spacing-md);\n  }\n  \n  .header-title {\n    font-size: var(--font-size-lg);\n  }\n  \n  .user-name {\n    display: none;\n  }\n  \n  .sidebar {\n    width: 100%;\n  }\n  \n  .sidebar.collapsed {\n    width: 100%;\n  }\n}\n\n/* Animation for smooth transitions */\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n.content-body > * {\n  animation: fadeIn 0.3s ease-out;\n}\n\n/* Dark mode support (future enhancement) */\n@media (prefers-color-scheme: dark) {\n  .content-header {\n    background: var(--gray-800);\n    border-bottom-color: var(--gray-700);\n  }\n  \n  .header-title {\n    color: var(--white);\n  }\n  \n  .sidebar-toggle {\n    color: var(--gray-300);\n  }\n  \n  .sidebar-toggle:hover {\n    background-color: var(--gray-700);\n    color: var(--primary-light);\n  }\n}\n", "/* Enhanced Login Styles - <PERSON><PERSON><PERSON>P Professional Design */\n\n.enhanced-login {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n  font-family: 'Cairo', sans-serif;\n}\n\n/* Background */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, #1e40af 100%);\n  z-index: -2;\n}\n\n.background-pattern {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: \n    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);\n  z-index: -1;\n}\n\n.floating-shapes {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -1;\n}\n\n.shape {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  animation: float 6s ease-in-out infinite;\n}\n\n.shape-1 {\n  width: 80px;\n  height: 80px;\n  top: 20%;\n  left: 10%;\n  animation-delay: 0s;\n}\n\n.shape-2 {\n  width: 120px;\n  height: 120px;\n  top: 60%;\n  left: 80%;\n  animation-delay: 2s;\n}\n\n.shape-3 {\n  width: 60px;\n  height: 60px;\n  top: 80%;\n  left: 20%;\n  animation-delay: 4s;\n}\n\n.shape-4 {\n  width: 100px;\n  height: 100px;\n  top: 10%;\n  left: 70%;\n  animation-delay: 1s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n    opacity: 0.7;\n  }\n  50% {\n    transform: translateY(-20px) rotate(180deg);\n    opacity: 1;\n  }\n}\n\n/* Main Container */\n.login-container {\n  display: flex;\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: var(--spacing-xl);\n  gap: var(--spacing-2xl);\n  align-items: center;\n  justify-content: center;\n}\n\n/* Login Card */\n.login-card {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n  overflow: hidden;\n  width: 100%;\n  max-width: 450px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.login-header {\n  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);\n  padding: var(--spacing-xl);\n  text-align: center;\n  border-bottom: 1px solid var(--gray-200);\n}\n\n.company-logo {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n}\n\n.logo-icon {\n  font-size: var(--font-size-4xl);\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\n}\n\n.logo-text h1 {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.logo-text p {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  margin: 0;\n  font-weight: 500;\n}\n\n.current-time {\n  font-size: var(--font-size-sm);\n  color: var(--gray-500);\n  font-weight: 500;\n  background: var(--gray-100);\n  padding: var(--spacing-sm) var(--spacing-md);\n  border-radius: var(--radius-lg);\n  display: inline-block;\n}\n\n/* Login Body */\n.login-body {\n  padding: var(--spacing-xl);\n}\n\n.welcome-text {\n  text-align: center;\n  margin-bottom: var(--spacing-xl);\n}\n\n.welcome-text h2 {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-sm);\n}\n\n.welcome-text p {\n  font-size: var(--font-size-base);\n  color: var(--gray-600);\n  margin: 0;\n}\n\n/* Error Message */\n.error-message {\n  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);\n  color: var(--danger-color);\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  margin-bottom: var(--spacing-lg);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  border: 1px solid #fecaca;\n  font-weight: 500;\n}\n\n.error-icon {\n  font-size: var(--font-size-lg);\n}\n\n/* Form Styles */\n.login-form {\n  margin-bottom: var(--spacing-xl);\n}\n\n.form-group {\n  margin-bottom: var(--spacing-lg);\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: var(--spacing-sm);\n  font-weight: 600;\n  color: var(--gray-700);\n  font-size: var(--font-size-sm);\n}\n\n.input-container {\n  position: relative;\n}\n\n.form-input {\n  width: 100%;\n  padding: var(--spacing-md) var(--spacing-lg);\n  padding-right: 3rem;\n  border: 2px solid var(--gray-300);\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-base);\n  transition: all 0.3s ease;\n  background: var(--white);\n  font-family: inherit;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);\n  transform: translateY(-1px);\n}\n\n.form-input:disabled {\n  background-color: var(--gray-100);\n  cursor: not-allowed;\n  opacity: 0.7;\n}\n\n.input-icon {\n  position: absolute;\n  right: var(--spacing-md);\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: var(--font-size-lg);\n  color: var(--gray-400);\n  pointer-events: none;\n}\n\n.password-toggle {\n  position: absolute;\n  left: var(--spacing-md);\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  font-size: var(--font-size-lg);\n  color: var(--gray-400);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n}\n\n.password-toggle:hover {\n  color: var(--gray-600);\n  background: var(--gray-100);\n}\n\n.password-toggle:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n\n/* Form Options */\n.form-options {\n  margin-bottom: var(--spacing-xl);\n}\n\n.checkbox-container {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  cursor: pointer;\n  font-size: var(--font-size-sm);\n  color: var(--gray-700);\n  font-weight: 500;\n}\n\n.checkbox-container input[type=\"checkbox\"] {\n  display: none;\n}\n\n.checkmark {\n  width: 20px;\n  height: 20px;\n  border: 2px solid var(--gray-300);\n  border-radius: var(--radius-sm);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  background: var(--white);\n}\n\n.checkbox-container input[type=\"checkbox\"]:checked + .checkmark {\n  background: var(--primary-color);\n  border-color: var(--primary-color);\n}\n\n.checkbox-container input[type=\"checkbox\"]:checked + .checkmark::after {\n  content: '✓';\n  color: var(--white);\n  font-size: var(--font-size-sm);\n  font-weight: 700;\n}\n\n/* Buttons */\n.login-button,\n.demo-button {\n  width: 100%;\n  padding: var(--spacing-lg);\n  border: none;\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-base);\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  font-family: inherit;\n}\n\n.login-button {\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  box-shadow: var(--shadow-md);\n  margin-bottom: var(--spacing-lg);\n}\n\n.login-button:hover:not(:disabled) {\n  background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-lg);\n}\n\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.demo-button {\n  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);\n  color: var(--gray-700);\n  border: 2px solid var(--gray-300);\n}\n\n.demo-button:hover:not(:disabled) {\n  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.button-icon {\n  font-size: var(--font-size-lg);\n}\n\n.loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid var(--white);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Demo Section */\n.demo-section {\n  margin-bottom: var(--spacing-lg);\n}\n\n.divider {\n  text-align: center;\n  margin-bottom: var(--spacing-lg);\n  position: relative;\n}\n\n.divider::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: var(--gray-300);\n}\n\n.divider span {\n  background: var(--white);\n  padding: 0 var(--spacing-md);\n  color: var(--gray-500);\n  font-size: var(--font-size-sm);\n  font-weight: 500;\n}\n\n/* Login Footer */\n.login-footer {\n  background: var(--gray-50);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--gray-200);\n}\n\n.system-info {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  margin-bottom: var(--spacing-lg);\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n}\n\n.info-icon {\n  font-size: var(--font-size-base);\n}\n\n.copyright {\n  text-align: center;\n  font-size: var(--font-size-xs);\n  color: var(--gray-500);\n}\n\n.copyright p {\n  margin: 0;\n  margin-bottom: var(--spacing-xs);\n}\n\n/* Features Sidebar */\n.features-sidebar {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: var(--radius-xl);\n  padding: var(--spacing-xl);\n  box-shadow: var(--shadow-xl);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  max-width: 400px;\n}\n\n.features-sidebar h3 {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-xl);\n  text-align: center;\n}\n\n.features-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n\n.feature-item {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--gray-200);\n  transition: all 0.3s ease;\n}\n\n.feature-item:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-md);\n  border-color: var(--primary-color);\n}\n\n.feature-icon {\n  font-size: var(--font-size-2xl);\n  flex-shrink: 0;\n  width: 50px;\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  border-radius: var(--radius-lg);\n  color: var(--white);\n}\n\n.feature-content h4 {\n  font-size: var(--font-size-base);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-xs);\n}\n\n.feature-content p {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  margin: 0;\n  line-height: 1.5;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .login-container {\n    flex-direction: column;\n    gap: var(--spacing-xl);\n  }\n  \n  .features-sidebar {\n    max-width: 100%;\n    order: -1;\n  }\n  \n  .features-list {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n    gap: var(--spacing-md);\n  }\n}\n\n@media (max-width: 768px) {\n  .enhanced-login {\n    padding: var(--spacing-md);\n  }\n  \n  .login-container {\n    padding: var(--spacing-md);\n  }\n  \n  .login-card {\n    max-width: 100%;\n  }\n  \n  .login-header,\n  .login-body,\n  .login-footer {\n    padding: var(--spacing-lg);\n  }\n  \n  .company-logo {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n  \n  .logo-icon {\n    font-size: var(--font-size-3xl);\n  }\n  \n  .features-sidebar {\n    padding: var(--spacing-lg);\n  }\n  \n  .features-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .system-info {\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: var(--spacing-sm);\n  }\n}\n\n@media (max-width: 480px) {\n  .form-input {\n    padding: var(--spacing-sm) var(--spacing-md);\n    padding-right: 2.5rem;\n  }\n  \n  .login-button,\n  .demo-button {\n    padding: var(--spacing-md);\n  }\n  \n  .feature-item {\n    padding: var(--spacing-md);\n  }\n  \n  .feature-icon {\n    width: 40px;\n    height: 40px;\n    font-size: var(--font-size-xl);\n  }\n}\n", "/* Enhanced Dashboard Styles - <PERSON><PERSON>im ERP Professional Design */\n\n.enhanced-dashboard {\n  padding: var(--spacing-xl);\n  background: var(--gray-50);\n  min-height: 100vh;\n}\n\n/* Loading State */\n.dashboard-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 60vh;\n  color: var(--gray-600);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid var(--gray-200);\n  border-top: 4px solid var(--primary-color);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: var(--spacing-lg);\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Welcome Section */\n.welcome-section {\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  padding: var(--spacing-2xl);\n  border-radius: var(--radius-xl);\n  margin-bottom: var(--spacing-xl);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: var(--shadow-xl);\n  position: relative;\n  overflow: hidden;\n}\n\n.welcome-section::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  right: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n  animation: float 6s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(180deg); }\n}\n\n.welcome-content h1 {\n  font-size: var(--font-size-3xl);\n  font-weight: 700;\n  margin-bottom: var(--spacing-sm);\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.welcome-content p {\n  font-size: var(--font-size-lg);\n  opacity: 0.9;\n  margin-bottom: var(--spacing-md);\n}\n\n.current-time {\n  font-size: var(--font-size-base);\n  opacity: 0.8;\n  font-weight: 500;\n}\n\n.welcome-actions {\n  z-index: 1;\n}\n\n/* Statistics Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-xl);\n}\n\n.stat-card {\n  background: var(--white);\n  padding: var(--spacing-xl);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-lg);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.stat-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: var(--primary-color);\n  transition: width 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-xl);\n}\n\n.stat-card:hover::before {\n  width: 8px;\n}\n\n.stat-card.primary::before { background: var(--primary-color); }\n.stat-card.success::before { background: var(--success-color); }\n.stat-card.info::before { background: var(--info-color); }\n.stat-card.warning::before { background: var(--warning-color); }\n.stat-card.danger::before { background: var(--danger-color); }\n.stat-card.secondary::before { background: var(--secondary-color); }\n\n.stat-icon {\n  font-size: var(--font-size-3xl);\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--gray-100);\n  border-radius: var(--radius-lg);\n  flex-shrink: 0;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  line-height: 1.2;\n  margin-bottom: var(--spacing-xs);\n}\n\n.stat-label {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  font-weight: 500;\n}\n\n/* Quick Actions Section */\n.quick-actions-section {\n  margin-bottom: var(--spacing-xl);\n}\n\n.quick-actions-section h2 {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-lg);\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--spacing-lg);\n}\n\n.quick-action-card {\n  background: var(--white);\n  padding: var(--spacing-xl);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-lg);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.quick-action-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: var(--primary-color);\n  transform: scaleX(0);\n  transition: transform 0.3s ease;\n}\n\n.quick-action-card:hover {\n  transform: translateY(-3px);\n  box-shadow: var(--shadow-lg);\n}\n\n.quick-action-card:hover::before {\n  transform: scaleX(1);\n}\n\n.quick-action-card.primary:hover::before { background: var(--primary-color); }\n.quick-action-card.success:hover::before { background: var(--success-color); }\n.quick-action-card.info:hover::before { background: var(--info-color); }\n.quick-action-card.warning:hover::before { background: var(--warning-color); }\n\n.action-icon {\n  font-size: var(--font-size-3xl);\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--gray-100);\n  border-radius: var(--radius-lg);\n  flex-shrink: 0;\n}\n\n.action-content h3 {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-xs);\n}\n\n.action-content p {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  margin: 0;\n}\n\n/* Dashboard Content Grid */\n.dashboard-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-xl);\n}\n\n.dashboard-card {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.dashboard-card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-2px);\n}\n\n.card-header {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-bottom: 1px solid var(--gray-200);\n  background: var(--gray-50);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin: 0;\n}\n\n.card-body {\n  padding: var(--spacing-xl);\n}\n\n/* Sales Chart */\n.sales-chart {\n  display: flex;\n  align-items: end;\n  gap: var(--spacing-md);\n  height: 200px;\n  padding: var(--spacing-lg) 0;\n}\n\n.chart-bar {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.bar {\n  width: 100%;\n  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  border-radius: var(--radius-sm) var(--radius-sm) 0 0;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  min-height: 10px;\n}\n\n.bar:hover {\n  background: linear-gradient(180deg, var(--primary-light) 0%, var(--primary-color) 100%);\n  transform: scaleY(1.05);\n}\n\n.bar-label {\n  font-size: var(--font-size-xs);\n  color: var(--gray-600);\n  font-weight: 500;\n}\n\n.bar-value {\n  font-size: var(--font-size-xs);\n  color: var(--gray-700);\n  font-weight: 600;\n}\n\n/* Lists */\n.invoices-list,\n.products-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n\n.invoice-item,\n.product-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--spacing-md);\n  background: var(--gray-50);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--gray-200);\n  transition: all 0.3s ease;\n}\n\n.invoice-item:hover,\n.product-item:hover {\n  background: var(--gray-100);\n  border-color: var(--primary-color);\n  transform: translateX(-2px);\n}\n\n.invoice-info,\n.product-info {\n  flex: 1;\n}\n\n.invoice-number,\n.product-name {\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-xs);\n}\n\n.invoice-date,\n.product-category {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n}\n\n.invoice-amount {\n  font-weight: 700;\n  color: var(--success-color);\n  font-size: var(--font-size-lg);\n}\n\n.product-stock {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n\n.stock-value {\n  font-weight: 700;\n  font-size: var(--font-size-lg);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-md);\n  min-width: 40px;\n  text-align: center;\n}\n\n.stock-value.danger {\n  background: rgba(239, 68, 68, 0.1);\n  color: var(--danger-color);\n}\n\n.stock-label {\n  font-size: var(--font-size-xs);\n  color: var(--gray-600);\n}\n\n/* Empty State */\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-xl);\n  color: var(--gray-500);\n}\n\n.empty-state p {\n  margin: 0;\n  font-style: italic;\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .dashboard-content {\n    grid-template-columns: 1fr;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  }\n  \n  .quick-actions-grid {\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  }\n}\n\n@media (max-width: 768px) {\n  .enhanced-dashboard {\n    padding: var(--spacing-md);\n  }\n  \n  .welcome-section {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-lg);\n    padding: var(--spacing-xl);\n  }\n  \n  .welcome-content h1 {\n    font-size: var(--font-size-2xl);\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  \n  .stat-card {\n    padding: var(--spacing-lg);\n  }\n  \n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .dashboard-content {\n    gap: var(--spacing-lg);\n  }\n  \n  .card-header {\n    padding: var(--spacing-md);\n    flex-direction: column;\n    gap: var(--spacing-sm);\n    align-items: stretch;\n  }\n  \n  .card-body {\n    padding: var(--spacing-md);\n  }\n  \n  .sales-chart {\n    height: 150px;\n    gap: var(--spacing-sm);\n  }\n}\n", "/* حاوية نقطة البيع */\n.pos-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  direction: rtl;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* شريط التنقل */\n.pos-header {\n  background: linear-gradient(135deg, #1890ff, #40a9ff);\n  color: white;\n  padding: 15px 0;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.header-content {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.back-btn {\n  background-color: rgba(255,255,255,0.2);\n  color: white;\n  border: 1px solid rgba(255,255,255,0.3);\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s;\n  font-size: 0.9rem;\n}\n\n.back-btn:hover {\n  background-color: rgba(255,255,255,0.3);\n  transform: translateY(-1px);\n}\n\n.pos-header h1 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.user-info {\n  font-size: 0.9rem;\n  opacity: 0.9;\n}\n\n/* المحتوى الرئيسي */\n.pos-main {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 20px;\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: 20px;\n  min-height: calc(100vh - 80px);\n}\n\n/* قسم المنتجات */\n.products-section {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.search-bar {\n  margin-bottom: 20px;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 15px;\n  border: 1px solid #d9d9d9;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #1890ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  max-height: calc(100vh - 200px);\n  overflow-y: auto;\n}\n\n.product-card {\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 15px;\n  transition: all 0.3s;\n  cursor: pointer;\n}\n\n.product-card:hover {\n  border-color: #1890ff;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.product-info h3 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.product-category {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 0.85rem;\n}\n\n.product-price {\n  margin: 0 0 5px 0;\n  color: #1890ff;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.product-stock {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 0.85rem;\n}\n\n.add-to-cart-btn {\n  width: 100%;\n  padding: 8px 12px;\n  background: linear-gradient(135deg, #52c41a, #73d13d);\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.add-to-cart-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #73d13d, #95de64);\n  transform: translateY(-1px);\n}\n\n.add-to-cart-btn:disabled {\n  background: #d9d9d9;\n  color: #999;\n  cursor: not-allowed;\n}\n\n/* قسم السلة */\n.cart-section {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  display: flex;\n  flex-direction: column;\n  max-height: calc(100vh - 120px);\n}\n\n.cart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.cart-header h2 {\n  margin: 0;\n  color: #333;\n  font-size: 1.3rem;\n}\n\n.clear-cart-btn {\n  background: #ff4d4f;\n  color: white;\n  border: none;\n  padding: 6px 12px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.85rem;\n  transition: all 0.3s;\n}\n\n.clear-cart-btn:hover {\n  background: #ff7875;\n  transform: translateY(-1px);\n}\n\n.cart-items {\n  flex: 1;\n  overflow-y: auto;\n  margin-bottom: 15px;\n  max-height: 300px;\n  min-height: 200px;\n}\n\n.empty-cart {\n  text-align: center;\n  color: #999;\n  font-style: italic;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 2px dashed #ddd;\n}\n\n.cart-item {\n  display: grid;\n  grid-template-columns: 1fr auto auto auto;\n  gap: 10px;\n  align-items: center;\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  border-radius: 6px;\n  margin-bottom: 8px;\n  box-shadow: 0 1px 3px rgba(0,0,0,0.1);\n  transition: all 0.3s;\n}\n\n.cart-item:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n}\n\n.cart-item:last-child {\n  border-bottom: none;\n}\n\n.item-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.item-info p {\n  margin: 0;\n  color: #1890ff;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.quantity-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.qty-btn {\n  width: 30px;\n  height: 30px;\n  border: 1px solid #d9d9d9;\n  background: white;\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  transition: all 0.3s;\n}\n\n.qty-btn:hover {\n  border-color: #1890ff;\n  color: #1890ff;\n}\n\n.quantity {\n  min-width: 30px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.item-total {\n  font-weight: 600;\n  color: #1890ff;\n  font-size: 0.9rem;\n}\n\n.remove-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s;\n}\n\n.remove-btn:hover {\n  background: #fff2f0;\n}\n\n/* إجماليات السلة */\n.cart-totals {\n  border-top: 1px solid #e8e8e8;\n  padding-top: 15px;\n  margin-bottom: 15px;\n}\n\n.total-line {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  font-size: 0.95rem;\n}\n\n.final-total {\n  font-weight: 600;\n  font-size: 1.1rem;\n  color: #1890ff;\n  border-top: 1px solid #e8e8e8;\n  padding-top: 8px;\n  margin-top: 8px;\n}\n\n/* قسم طرق الدفع */\n.payment-section {\n  margin-bottom: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e8e8e8;\n}\n\n.section-subtitle {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 1rem;\n  font-weight: 600;\n}\n\n.payment-methods {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.payment-option {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px 12px;\n  background: white;\n  border: 2px solid #e8e8e8;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.payment-option:hover {\n  border-color: #1890ff;\n  background: #f0f8ff;\n}\n\n.payment-option.selected {\n  border-color: #1890ff;\n  background: #e6f7ff;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\n}\n\n.payment-option input[type=\"radio\"] {\n  margin: 0;\n  accent-color: #1890ff;\n}\n\n.payment-icon {\n  font-size: 1.2rem;\n}\n\n.payment-option span:last-child {\n  font-weight: 500;\n  color: #333;\n}\n\n.payment-option.selected span:last-child {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n/* قسم العميل */\n.customer-section {\n  margin-bottom: 15px;\n}\n\n.customer-btn {\n  width: 100%;\n  padding: 10px;\n  background: #f0f8ff;\n  color: #1890ff;\n  border: 1px solid #d6e4ff;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s;\n  margin-bottom: 10px;\n}\n\n.customer-btn:hover {\n  background: #e6f7ff;\n  border-color: #91d5ff;\n}\n\n.customer-form {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.customer-input {\n  padding: 8px 12px;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  transition: all 0.3s;\n}\n\n.customer-input:focus {\n  outline: none;\n  border-color: #1890ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n/* زر الدفع */\n.checkout-btn {\n  width: 100%;\n  padding: 15px;\n  background: linear-gradient(135deg, #1890ff, #40a9ff);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.checkout-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #40a9ff, #69c0ff);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);\n}\n\n.checkout-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.checkout-btn.processing {\n  pointer-events: none;\n}\n\n/* دوار التحميل */\n.spinner {\n  width: 18px;\n  height: 18px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: white;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n/* تحسين الاستجابة */\n@media (max-width: 1200px) {\n  .pos-main {\n    grid-template-columns: 1fr 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .pos-main {\n    grid-template-columns: 1fr;\n    gap: 15px;\n    padding: 15px;\n  }\n\n  .cart-section {\n    order: -1;\n    max-height: 50vh;\n  }\n  \n  .products-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 10px;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    gap: 10px;\n    text-align: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .products-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n  \n  .cart-item {\n    grid-template-columns: 1fr;\n    gap: 8px;\n    text-align: center;\n  }\n  \n  .quantity-controls {\n    justify-content: center;\n  }\n}\n", ".products-container {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  direction: rtl;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.products-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  color: white;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.header-content h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 700;\n}\n\n.back-btn, .add-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.back-btn {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.back-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.add-btn {\n  background: #4CAF50;\n  color: white;\n}\n\n.add-btn:hover:not(:disabled) {\n  background: #45a049;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);\n}\n\n.add-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.products-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4px solid #667eea;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.stat-card.warning {\n  border-left-color: #ff9800;\n}\n\n.stat-card.success {\n  border-left-color: #4caf50;\n}\n\n.stat-card.info {\n  border-left-color: #2196f3;\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  font-size: 0.9rem;\n  color: #666;\n  font-weight: 500;\n}\n\n.stat-info p {\n  margin: 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #333;\n}\n\n.products-filters {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  flex-wrap: wrap;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.search-box {\n  flex: 1;\n  min-width: 300px;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 20px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.filter-select, .sort-select {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  background: #f8f9fa;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 150px;\n}\n\n.filter-select:focus, .sort-select:focus {\n  outline: none;\n  border-color: #667eea;\n  background: white;\n}\n\n.products-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.products-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.products-table th {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.products-table td {\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n  vertical-align: middle;\n}\n\n.products-table tr:hover {\n  background: #f8f9fa;\n}\n\n.products-table tr.low-stock {\n  background: #fff3e0;\n}\n\n.products-table tr.low-stock:hover {\n  background: #ffe0b2;\n}\n\n.product-name strong {\n  display: block;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.product-name small {\n  color: #666;\n  font-size: 12px;\n}\n\n.stock-warning {\n  color: #f44336;\n  font-weight: 600;\n}\n\n.stock-normal {\n  color: #4caf50;\n  font-weight: 600;\n}\n\n.status {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  text-align: center;\n}\n\n.status.available {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.status.low {\n  background: #fff3e0;\n  color: #ff9800;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn, .delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.edit-btn {\n  background: #2196f3;\n  color: white;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #1976d2;\n  transform: scale(1.05);\n}\n\n.delete-btn {\n  background: #f44336;\n  color: white;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #d32f2f;\n  transform: scale(1.05);\n}\n\n.edit-btn:disabled, .delete-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.no-products {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n  font-size: 18px;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.modal {\n  background: white;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 25px 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  width: 35px;\n  height: 35px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.modal-body {\n  padding: 30px;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.barcode-input {\n  display: flex;\n  gap: 10px;\n}\n\n.barcode-input input {\n  flex: 1;\n}\n\n.generate-btn {\n  padding: 12px 16px;\n  background: #667eea;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.generate-btn:hover {\n  background: #5a6fd8;\n}\n\n.modal-footer {\n  padding: 20px 30px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: #f8f9fa;\n  border-radius: 0 0 20px 20px;\n}\n\n.cancel-btn, .save-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.cancel-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.cancel-btn:hover {\n  background: #5a6268;\n}\n\n.save-btn {\n  background: #4CAF50;\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  background: #45a049;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);\n}\n\n.save-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .products-container {\n    padding: 15px;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .products-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .products-filters {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-box {\n    min-width: auto;\n  }\n  \n  .products-table-container {\n    overflow-x: auto;\n  }\n  \n  .products-table {\n    min-width: 800px;\n  }\n  \n  .modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", ".customers-container {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  direction: rtl;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.customers-header {\n  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  color: white;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.header-content h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 700;\n}\n\n.back-btn, .add-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.back-btn {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.back-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.add-btn {\n  background: #2196F3;\n  color: white;\n}\n\n.add-btn:hover:not(:disabled) {\n  background: #1976D2;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);\n}\n\n.add-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.customers-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4px solid #4CAF50;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.stat-card.warning {\n  border-left-color: #ff9800;\n}\n\n.stat-card.success {\n  border-left-color: #4caf50;\n}\n\n.stat-card.info {\n  border-left-color: #2196f3;\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  font-size: 0.9rem;\n  color: #666;\n  font-weight: 500;\n}\n\n.stat-info p {\n  margin: 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #333;\n}\n\n.customers-filters {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  flex-wrap: wrap;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.search-box {\n  flex: 1;\n  min-width: 300px;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 20px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #4CAF50;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);\n}\n\n.filter-select, .sort-select {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  background: #f8f9fa;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 150px;\n}\n\n.filter-select:focus, .sort-select:focus {\n  outline: none;\n  border-color: #4CAF50;\n  background: white;\n}\n\n.customers-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.customers-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.customers-table th {\n  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);\n  color: white;\n  padding: 20px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.customers-table td {\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n  vertical-align: middle;\n}\n\n.customers-table tr:hover {\n  background: #f8f9fa;\n}\n\n.customer-name strong {\n  display: block;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.customer-name small {\n  color: #666;\n  font-size: 12px;\n}\n\n.customer-type {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  text-align: center;\n}\n\n.customer-type.individual {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.customer-type.company {\n  background: #f3e5f5;\n  color: #7b1fa2;\n}\n\n.customer-type.government {\n  background: #e8f5e8;\n  color: #388e3c;\n}\n\n.customer-type.nonprofit {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn, .delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.edit-btn {\n  background: #2196f3;\n  color: white;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #1976d2;\n  transform: scale(1.05);\n}\n\n.delete-btn {\n  background: #f44336;\n  color: white;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #d32f2f;\n  transform: scale(1.05);\n}\n\n.edit-btn:disabled, .delete-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.no-customers {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n  font-size: 18px;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.modal {\n  background: white;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 900px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);\n  color: white;\n  padding: 25px 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  width: 35px;\n  height: 35px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.modal-body {\n  padding: 30px;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #4CAF50;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);\n}\n\n.modal-footer {\n  padding: 20px 30px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: #f8f9fa;\n  border-radius: 0 0 20px 20px;\n}\n\n.cancel-btn, .save-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.cancel-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.cancel-btn:hover {\n  background: #5a6268;\n}\n\n.save-btn {\n  background: #4CAF50;\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  background: #45a049;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);\n}\n\n.save-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .customers-container {\n    padding: 15px;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .customers-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .customers-filters {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-box {\n    min-width: auto;\n  }\n  \n  .customers-table-container {\n    overflow-x: auto;\n  }\n  \n  .customers-table {\n    min-width: 900px;\n  }\n  \n  .modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", ".suppliers-container {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  direction: rtl;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.suppliers-header {\n  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  color: white;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.header-content h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 700;\n}\n\n.back-btn, .add-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.back-btn {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.back-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.add-btn {\n  background: #9C27B0;\n  color: white;\n}\n\n.add-btn:hover:not(:disabled) {\n  background: #7B1FA2;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);\n}\n\n.add-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.suppliers-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4px solid #FF9800;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.stat-card.warning {\n  border-left-color: #ff9800;\n}\n\n.stat-card.success {\n  border-left-color: #4caf50;\n}\n\n.stat-card.info {\n  border-left-color: #2196f3;\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  font-size: 0.9rem;\n  color: #666;\n  font-weight: 500;\n}\n\n.stat-info p {\n  margin: 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #333;\n}\n\n.suppliers-filters {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  margin-bottom: 30px;\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  flex-wrap: wrap;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.search-box {\n  flex: 1;\n  min-width: 300px;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 20px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #FF9800;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.1);\n}\n\n.filter-select, .sort-select {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 10px;\n  background: #f8f9fa;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 150px;\n}\n\n.filter-select:focus, .sort-select:focus {\n  outline: none;\n  border-color: #FF9800;\n  background: white;\n}\n\n.suppliers-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.suppliers-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.suppliers-table th {\n  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);\n  color: white;\n  padding: 20px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.suppliers-table td {\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n  vertical-align: middle;\n}\n\n.suppliers-table tr:hover {\n  background: #f8f9fa;\n}\n\n.supplier-name strong {\n  display: block;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.supplier-name small {\n  color: #666;\n  font-size: 12px;\n}\n\n.supplier-type {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  text-align: center;\n}\n\n.supplier-type.local {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.supplier-type.international {\n  background: #e3f2fd;\n  color: #2196f3;\n}\n\n.supplier-type.manufacturer {\n  background: #f3e5f5;\n  color: #9c27b0;\n}\n\n.supplier-type.distributor {\n  background: #fff3e0;\n  color: #ff9800;\n}\n\n.supplier-type.wholesaler {\n  background: #fce4ec;\n  color: #e91e63;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn, .delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.edit-btn {\n  background: #2196f3;\n  color: white;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #1976d2;\n  transform: scale(1.05);\n}\n\n.delete-btn {\n  background: #f44336;\n  color: white;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #d32f2f;\n  transform: scale(1.05);\n}\n\n.edit-btn:disabled, .delete-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.no-suppliers {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n  font-size: 18px;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.modal {\n  background: white;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 900px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);\n  color: white;\n  padding: 25px 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  width: 35px;\n  height: 35px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.modal-body {\n  padding: 30px;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #FF9800;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.1);\n}\n\n.modal-footer {\n  padding: 20px 30px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: #f8f9fa;\n  border-radius: 0 0 20px 20px;\n}\n\n.cancel-btn, .save-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.cancel-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.cancel-btn:hover {\n  background: #5a6268;\n}\n\n.save-btn {\n  background: #FF9800;\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  background: #F57C00;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);\n}\n\n.save-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .suppliers-container {\n    padding: 15px;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .suppliers-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .suppliers-filters {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-box {\n    min-width: auto;\n  }\n  \n  .suppliers-table-container {\n    overflow-x: auto;\n  }\n  \n  .suppliers-table {\n    min-width: 900px;\n  }\n  \n  .modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", "/* Invoices Styles - <PERSON><PERSON><PERSON> ERP Design */\n\n.invoices-container {\n  padding: var(--spacing-xl);\n  background: var(--gray-50);\n  min-height: 100vh;\n}\n\n.invoices-header {\n  margin-bottom: var(--spacing-xl);\n}\n\n.header-top {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.header-top h1 {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin: 0;\n}\n\n/* Stats Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-xl);\n}\n\n.stat-card {\n  background: var(--white);\n  padding: var(--spacing-lg);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-lg);\n}\n\n.stat-card.success {\n  border-left: 4px solid var(--success-color);\n}\n\n.stat-card.warning {\n  border-left: 4px solid var(--warning-color);\n}\n\n.stat-card.danger {\n  border-left: 4px solid var(--danger-color);\n}\n\n.stat-icon {\n  font-size: var(--font-size-2xl);\n  width: 50px;\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--gray-100);\n  border-radius: var(--radius-lg);\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  margin-top: var(--spacing-xs);\n}\n\n/* Filters Section */\n.filters-section {\n  background: var(--white);\n  padding: var(--spacing-lg);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  margin-bottom: var(--spacing-xl);\n}\n\n.search-box {\n  margin-bottom: var(--spacing-lg);\n}\n\n.search-input {\n  width: 100%;\n  padding: var(--spacing-md) var(--spacing-lg);\n  border: 2px solid var(--gray-300);\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-base);\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);\n}\n\n.filters-row {\n  display: flex;\n  gap: var(--spacing-md);\n  flex-wrap: wrap;\n}\n\n.filter-select {\n  padding: var(--spacing-md);\n  border: 2px solid var(--gray-300);\n  border-radius: var(--radius-md);\n  font-size: var(--font-size-base);\n  background: var(--white);\n  min-width: 150px;\n}\n\n.filter-select:focus {\n  outline: none;\n  border-color: var(--primary-color);\n}\n\n/* Content */\n.invoices-content {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  overflow: hidden;\n}\n\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-2xl);\n  color: var(--gray-500);\n}\n\n.empty-icon {\n  font-size: 4rem;\n  margin-bottom: var(--spacing-lg);\n  opacity: 0.5;\n}\n\n.empty-state h3 {\n  font-size: var(--font-size-xl);\n  margin-bottom: var(--spacing-sm);\n  color: var(--gray-700);\n}\n\n.empty-state p {\n  font-size: var(--font-size-base);\n  margin: 0;\n}\n\n/* Summary */\n.invoices-summary {\n  padding: var(--spacing-lg);\n  border-bottom: 1px solid var(--gray-200);\n  background: var(--gray-50);\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: var(--gray-700);\n}\n\n/* Table */\n.invoices-table-container {\n  overflow-x: auto;\n}\n\n.invoices-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--font-size-sm);\n}\n\n.invoices-table th {\n  background: var(--gray-50);\n  padding: var(--spacing-md) var(--spacing-lg);\n  text-align: right;\n  font-weight: 600;\n  color: var(--gray-700);\n  border-bottom: 1px solid var(--gray-200);\n  white-space: nowrap;\n}\n\n.invoices-table td {\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-bottom: 1px solid var(--gray-200);\n  color: var(--gray-900);\n}\n\n.invoices-table tbody tr:hover {\n  background: var(--gray-50);\n}\n\n.invoice-number {\n  font-weight: 600;\n  color: var(--primary-color);\n}\n\n.amount {\n  font-weight: 600;\n  color: var(--success-color);\n}\n\n/* Status Badge */\n.status-badge {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-md);\n  font-size: var(--font-size-xs);\n  font-weight: 600;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.status-badge.success {\n  background: #dcfce7;\n  color: #166534;\n}\n\n.status-badge.warning {\n  background: #fef3c7;\n  color: #92400e;\n}\n\n.status-badge.danger {\n  background: #fee2e2;\n  color: #991b1b;\n}\n\n.status-badge.secondary {\n  background: var(--gray-200);\n  color: var(--gray-700);\n}\n\n/* Actions */\n.actions-buttons {\n  display: flex;\n  gap: var(--spacing-xs);\n  align-items: center;\n}\n\n.actions-buttons .btn {\n  padding: var(--spacing-xs);\n  min-width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Modal */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: var(--spacing-lg);\n}\n\n.invoice-details-modal {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: var(--shadow-xl);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--spacing-xl);\n  border-bottom: 1px solid var(--gray-200);\n  background: var(--gray-50);\n  border-radius: var(--radius-xl) var(--radius-xl) 0 0;\n}\n\n.modal-header h3 {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin: 0;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: var(--font-size-xl);\n  color: var(--gray-500);\n  cursor: pointer;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: var(--gray-100);\n  color: var(--gray-700);\n}\n\n.modal-body {\n  padding: var(--spacing-xl);\n}\n\n/* Invoice Info Grid */\n.invoice-info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-xl);\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.info-item label {\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n  color: var(--gray-600);\n}\n\n.info-item span {\n  font-size: var(--font-size-base);\n  color: var(--gray-900);\n}\n\n/* Items Section */\n.items-section {\n  margin-bottom: var(--spacing-xl);\n}\n\n.items-section h4 {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: var(--spacing-md);\n}\n\n.items-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--font-size-sm);\n  border: 1px solid var(--gray-200);\n  border-radius: var(--radius-md);\n  overflow: hidden;\n}\n\n.items-table th {\n  background: var(--gray-50);\n  padding: var(--spacing-md);\n  text-align: right;\n  font-weight: 600;\n  color: var(--gray-700);\n  border-bottom: 1px solid var(--gray-200);\n}\n\n.items-table td {\n  padding: var(--spacing-md);\n  border-bottom: 1px solid var(--gray-200);\n  color: var(--gray-900);\n}\n\n.items-table tbody tr:last-child td {\n  border-bottom: none;\n}\n\n/* Totals Section */\n.totals-section {\n  background: var(--gray-50);\n  padding: var(--spacing-lg);\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--gray-200);\n}\n\n.total-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-sm);\n  font-size: var(--font-size-sm);\n}\n\n.total-row:last-child {\n  margin-bottom: 0;\n}\n\n.total-row.final-total {\n  font-size: var(--font-size-lg);\n  font-weight: 700;\n  color: var(--primary-color);\n  border-top: 2px solid var(--gray-300);\n  padding-top: var(--spacing-md);\n  margin-top: var(--spacing-md);\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-md);\n  padding: var(--spacing-xl);\n  border-top: 1px solid var(--gray-200);\n  background: var(--gray-50);\n  border-radius: 0 0 var(--radius-xl) var(--radius-xl);\n}\n\n/* Animations */\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .invoices-container {\n    padding: var(--spacing-md);\n  }\n  \n  .header-top {\n    flex-direction: column;\n    gap: var(--spacing-md);\n    align-items: stretch;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .filters-row {\n    flex-direction: column;\n  }\n  \n  .filter-select {\n    min-width: auto;\n  }\n  \n  .invoices-table {\n    font-size: var(--font-size-xs);\n  }\n  \n  .invoices-table th,\n  .invoices-table td {\n    padding: var(--spacing-sm);\n  }\n  \n  .actions-buttons {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n  \n  .invoice-info-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", ".accounting-container {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  direction: rtl;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.accounting-header {\n  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  color: white;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.header-content h1 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 700;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.back-btn, .add-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.back-btn {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.back-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n}\n\n.add-btn {\n  background: #4CAF50;\n  color: white;\n}\n\n.add-btn.secondary {\n  background: #FF9800;\n}\n\n.add-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.add-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.accounting-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.stat-card.asset {\n  border-left: 4px solid #4CAF50;\n}\n\n.stat-card.liability {\n  border-left: 4px solid #f44336;\n}\n\n.stat-card.equity {\n  border-left: 4px solid #2196F3;\n}\n\n.stat-card.revenue {\n  border-left: 4px solid #FF9800;\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  font-size: 0.9rem;\n  color: #666;\n  font-weight: 500;\n}\n\n.stat-info p {\n  margin: 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #333;\n}\n\n.accounting-tabs {\n  background: white;\n  border-radius: 15px;\n  padding: 10px;\n  margin-bottom: 30px;\n  display: flex;\n  gap: 5px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 15px 20px;\n  border: none;\n  border-radius: 10px;\n  background: transparent;\n  color: #666;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.tab-btn:hover {\n  background: #f8f9fa;\n  color: #333;\n}\n\n.tab-btn.active {\n  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);\n  color: white;\n  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);\n}\n\n.tab-content {\n  background: white;\n  border-radius: 15px;\n  padding: 30px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  min-height: 500px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  padding-bottom: 15px;\n  border-bottom: 2px solid #f0f0f0;\n}\n\n.section-header h2 {\n  margin: 0;\n  color: #333;\n  font-size: 1.5rem;\n}\n\n.accounts-count, .entries-count, .trial-date {\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.accounts-table-container, .trial-balance-table {\n  overflow-x: auto;\n}\n\n.accounts-table, .trial-balance-table table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 20px;\n}\n\n.accounts-table th, .trial-balance-table th {\n  background: #f8f9fa;\n  color: #333;\n  padding: 15px;\n  text-align: right;\n  font-weight: 600;\n  border-bottom: 2px solid #e0e0e0;\n}\n\n.accounts-table td, .trial-balance-table td {\n  padding: 12px 15px;\n  border-bottom: 1px solid #f0f0f0;\n  vertical-align: middle;\n}\n\n.accounts-table tr:hover, .trial-balance-table tr:hover {\n  background: #f8f9fa;\n}\n\n.account-type-badge {\n  padding: 4px 8px;\n  border-radius: 12px;\n  color: white;\n  font-size: 11px;\n  font-weight: 600;\n}\n\n.positive {\n  color: #4CAF50;\n  font-weight: 600;\n}\n\n.negative {\n  color: #f44336;\n  font-weight: 600;\n}\n\n.journal-entries {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.journal-entry {\n  border: 1px solid #e0e0e0;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  overflow: hidden;\n}\n\n.entry-header {\n  background: #f8f9fa;\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.entry-info strong {\n  color: #333;\n  margin-left: 15px;\n}\n\n.entry-date {\n  color: #666;\n  font-size: 14px;\n}\n\n.entry-reference {\n  color: #666;\n  font-size: 14px;\n}\n\n.entry-description {\n  padding: 15px 20px;\n  background: #fff;\n  border-bottom: 1px solid #e0e0e0;\n  font-style: italic;\n  color: #555;\n}\n\n.entry-lines table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.entry-lines th {\n  background: #f8f9fa;\n  padding: 10px 15px;\n  text-align: right;\n  font-weight: 600;\n  color: #333;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.entry-lines td {\n  padding: 10px 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.debit {\n  color: #d32f2f;\n  font-weight: 600;\n  text-align: center;\n}\n\n.credit {\n  color: #388e3c;\n  font-weight: 600;\n  text-align: center;\n}\n\n.ledger-accounts {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.ledger-account {\n  margin-bottom: 30px;\n  border: 1px solid #e0e0e0;\n  border-radius: 10px;\n  overflow: hidden;\n}\n\n.account-header {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.account-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 1.1rem;\n}\n\n.account-type {\n  background: #2196F3;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: 600;\n}\n\n.ledger-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.ledger-table th {\n  background: #f8f9fa;\n  padding: 12px 15px;\n  text-align: right;\n  font-weight: 600;\n  color: #333;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.ledger-table td {\n  padding: 10px 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.opening-balance {\n  background: #fff3e0;\n  font-weight: 600;\n}\n\n.coming-soon {\n  text-align: center;\n  padding: 80px 20px;\n  color: #666;\n}\n\n.coming-soon h3 {\n  margin: 0 0 15px 0;\n  font-size: 1.5rem;\n  color: #999;\n}\n\n.no-data {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n  font-size: 18px;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.modal {\n  background: white;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease;\n}\n\n.modal.large {\n  max-width: 1200px;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);\n  color: white;\n  padding: 25px 30px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  width: 35px;\n  height: 35px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.modal-body {\n  padding: 30px;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #2196F3;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);\n}\n\n.entry-lines-section {\n  margin-top: 30px;\n}\n\n.entry-lines-section h3 {\n  margin: 0 0 20px 0;\n  color: #333;\n  font-size: 1.2rem;\n}\n\n.entry-lines-table {\n  overflow-x: auto;\n  margin-bottom: 20px;\n}\n\n.entry-lines-table table {\n  width: 100%;\n  border-collapse: collapse;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.entry-lines-table th {\n  background: #f8f9fa;\n  padding: 12px;\n  text-align: right;\n  font-weight: 600;\n  color: #333;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.entry-lines-table td {\n  padding: 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.entry-lines-table input,\n.entry-lines-table select {\n  width: 100%;\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.remove-line-btn {\n  background: #f44336;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  padding: 6px 8px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n.remove-line-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.add-line-btn {\n  background: #4CAF50;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  padding: 10px 20px;\n  cursor: pointer;\n  font-weight: 600;\n  margin-bottom: 20px;\n}\n\n.totals-row {\n  background: #f8f9fa;\n  font-weight: 600;\n}\n\n.total-debit, .total-credit {\n  text-align: center;\n  font-weight: 700;\n}\n\n.balance-warning {\n  background: #fff3cd;\n  color: #856404;\n  padding: 10px 15px;\n  border-radius: 8px;\n  border: 1px solid #ffeaa7;\n  margin-top: 10px;\n}\n\n.modal-footer {\n  padding: 20px 30px;\n  border-top: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: #f8f9fa;\n  border-radius: 0 0 20px 20px;\n}\n\n.cancel-btn, .save-btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.cancel-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.cancel-btn:hover {\n  background: #5a6268;\n}\n\n.save-btn {\n  background: #2196F3;\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  background: #1976D2;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);\n}\n\n.save-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .accounting-container {\n    padding: 15px;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .header-actions {\n    flex-direction: column;\n    width: 100%;\n  }\n  \n  .accounting-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .accounting-tabs {\n    flex-direction: column;\n  }\n  \n  .tab-content {\n    padding: 20px;\n  }\n  \n  .modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n  \n  .entry-lines-table {\n    font-size: 12px;\n  }\n}\n", "/* تقارير النظام المحاسبي السعودي */\n.reports-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* رأس التقارير */\n.reports-header {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.back-btn {\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s;\n}\n\n.back-btn:hover {\n  background: #5a6268;\n  transform: translateY(-2px);\n}\n\n.reports-header h1 {\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.8rem;\n  text-align: center;\n  flex: 1;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  padding: 10px 15px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: all 0.3s;\n  font-weight: 500;\n}\n\n.print-btn {\n  background: #17a2b8;\n  color: white;\n}\n\n.print-btn:hover {\n  background: #138496;\n  transform: translateY(-2px);\n}\n\n.export-btn {\n  background: #28a745;\n  color: white;\n}\n\n.export-btn:hover {\n  background: #218838;\n  transform: translateY(-2px);\n}\n\n/* تبويبات التقارير */\n.reports-tabs {\n  display: flex;\n  background: white;\n  border-radius: 12px;\n  padding: 5px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow-x: auto;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 20px;\n  border: none;\n  background: transparent;\n  cursor: pointer;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 500;\n  color: #6c757d;\n  transition: all 0.3s;\n  white-space: nowrap;\n}\n\n.tab-btn:hover {\n  background: #f8f9fa;\n  color: #495057;\n}\n\n.tab-btn.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n/* محتوى التقارير */\n.reports-content {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  min-height: 500px;\n}\n\n/* شبكة الإحصائيات */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 25px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transition: transform 0.3s;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n}\n\n.stat-card.sales {\n  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\n}\n\n.stat-card.vat {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.stat-card.invoices {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.stat-card.average {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.9;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  font-size: 1rem;\n  opacity: 0.9;\n  font-weight: 500;\n}\n\n.stat-value {\n  margin: 0;\n  font-size: 1.8rem;\n  font-weight: bold;\n}\n\n/* تقرير المنتجات */\n.products-report {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n}\n\n.report-section h3 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #e9ecef;\n}\n\n.products-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.product-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n  transition: all 0.3s;\n}\n\n.product-item:hover {\n  background: #e9ecef;\n  transform: translateX(5px);\n}\n\n.product-item.low-stock {\n  border-left-color: #dc3545;\n  background: #fff5f5;\n}\n\n.product-item.low-stock:hover {\n  background: #ffe6e6;\n}\n\n.rank {\n  background: #007bff;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 50%;\n  font-size: 0.8rem;\n  font-weight: bold;\n  min-width: 24px;\n  text-align: center;\n}\n\n.name {\n  font-weight: 600;\n  color: #2c3e50;\n  flex: 1;\n  margin: 0 10px;\n}\n\n.quantity, .stock {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.revenue, .price {\n  color: #28a745;\n  font-weight: 600;\n}\n\n/* سجل الفواتير */\n.invoices-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.invoices-header h3 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.date-filter {\n  padding: 8px 12px;\n  border: 2px solid #e9ecef;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.3s;\n}\n\n.date-filter:focus {\n  outline: none;\n  border-color: #007bff;\n}\n\n.invoices-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.invoice-item {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 15px;\n  border-left: 4px solid #007bff;\n  transition: all 0.3s;\n}\n\n.invoice-item:hover {\n  background: #e9ecef;\n  transform: translateX(5px);\n}\n\n.invoice-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.invoice-id {\n  font-weight: bold;\n  color: #007bff;\n  font-size: 1.1rem;\n}\n\n.invoice-date {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.invoice-details {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.customer {\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.total {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.payment {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n/* رسالة عدم وجود بيانات */\n.no-data {\n  text-align: center;\n  color: #6c757d;\n  font-style: italic;\n  padding: 40px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 2px dashed #dee2e6;\n}\n\n/* الاستجابة للشاشات الصغيرة */\n@media (max-width: 768px) {\n  .reports-container {\n    padding: 10px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .reports-tabs {\n    flex-direction: column;\n  }\n\n  .tab-btn {\n    text-align: center;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .products-report {\n    grid-template-columns: 1fr;\n  }\n\n  .invoice-details {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .invoices-header {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .date-filter {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .reports-header {\n    padding: 15px;\n  }\n\n  .reports-content {\n    padding: 15px;\n  }\n\n  .stat-card {\n    padding: 20px;\n  }\n\n  .stat-icon {\n    font-size: 2rem;\n  }\n\n  .stat-value {\n    font-size: 1.5rem;\n  }\n\n  .header-actions {\n    flex-direction: column;\n    width: 100%;\n  }\n\n  .action-btn {\n    width: 100%;\n  }\n}\n", ".settings-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.settings-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n}\n\n.settings-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n}\n\n.settings-header p {\n  margin: 0;\n  font-size: 1.1rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 20px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n  font-weight: 500;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.message.error {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n\n.settings-tabs {\n  display: flex;\n  background: white;\n  border-radius: 15px;\n  padding: 5px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 20px rgba(0,0,0,0.1);\n  overflow-x: auto;\n}\n\n.tab-button {\n  flex: 1;\n  padding: 15px 20px;\n  border: none;\n  background: transparent;\n  color: #64748b;\n  font-weight: 600;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n  min-width: 150px;\n}\n\n.tab-button:hover {\n  background: #f1f5f9;\n  color: #475569;\n}\n\n.tab-button.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);\n}\n\n.settings-form {\n  background: white;\n  border-radius: 15px;\n  padding: 30px;\n  box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n}\n\n.tab-content h3 {\n  margin: 0 0 25px 0;\n  color: #1e293b;\n  font-size: 1.5rem;\n  font-weight: 700;\n  padding-bottom: 10px;\n  border-bottom: 3px solid #e2e8f0;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 25px;\n  margin-bottom: 30px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: #fafafa;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n\n.checkbox-group {\n  flex-direction: row;\n  align-items: center;\n  gap: 10px;\n}\n\n.checkbox-group label {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.checkbox-group input[type=\"checkbox\"] {\n  width: 20px;\n  height: 20px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.backup-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.backup-card {\n  background: #f8fafc;\n  padding: 25px;\n  border-radius: 15px;\n  border: 2px solid #e2e8f0;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.backup-card:hover {\n  border-color: #667eea;\n  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.1);\n}\n\n.backup-card h4 {\n  margin: 0 0 10px 0;\n  color: #1e293b;\n  font-size: 1.2rem;\n}\n\n.backup-card p {\n  margin: 0 0 20px 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.backup-btn,\n.reset-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.95rem;\n}\n\n.backup-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.backup-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.reset-btn {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n.reset-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.file-input {\n  padding: 10px;\n  border: 2px dashed #d1d5db;\n  border-radius: 10px;\n  background: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.file-input:hover {\n  border-color: #667eea;\n  background: #f8fafc;\n}\n\n.form-actions {\n  text-align: center;\n  padding-top: 30px;\n  border-top: 2px solid #e2e8f0;\n}\n\n.save-btn {\n  padding: 15px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .settings-container {\n    padding: 15px;\n  }\n  \n  .settings-header h1 {\n    font-size: 2rem;\n  }\n  \n  .settings-tabs {\n    flex-direction: column;\n    gap: 5px;\n  }\n  \n  .tab-button {\n    min-width: auto;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n  \n  .backup-section {\n    grid-template-columns: 1fr;\n  }\n  \n  .settings-form {\n    padding: 20px;\n  }\n}\n\n/* Dark Mode Support */\n@media (prefers-color-scheme: dark) {\n  .settings-container {\n    background: #0f172a;\n    color: #e2e8f0;\n  }\n  \n  .settings-form {\n    background: #1e293b;\n  }\n  \n  .form-group input,\n  .form-group select,\n  .form-group textarea {\n    background: #334155;\n    border-color: #475569;\n    color: #e2e8f0;\n  }\n  \n  .backup-card {\n    background: #334155;\n    border-color: #475569;\n  }\n}\n", ".backup-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.backup-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.backup-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.backup-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.progress-container {\n  background: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 25px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.progress-bar {\n  width: 100%;\n  height: 20px;\n  background: #e5e7eb;\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 10px;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  transition: width 0.3s ease;\n  border-radius: 10px;\n}\n\n.progress-text {\n  display: block;\n  text-align: center;\n  font-weight: 600;\n  color: #374151;\n}\n\n.data-stats {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.data-stats h3 {\n  margin: 0 0 20px 0;\n  color: #1e293b;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 15px;\n  background: #f8fafc;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.stat-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.stat-icon {\n  font-size: 2rem;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 0.9rem;\n  color: #64748b;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.backup-actions {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 25px;\n  margin-bottom: 30px;\n}\n\n.action-card {\n  background: white;\n  padding: 30px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.action-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.action-card h3 {\n  margin: 0 0 15px 0;\n  color: #1e293b;\n  font-size: 1.3rem;\n  font-weight: 700;\n}\n\n.action-card p {\n  margin: 0 0 20px 0;\n  color: #64748b;\n  line-height: 1.6;\n}\n\n.backup-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.backup-btn.create {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.backup-btn.create:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);\n}\n\n.backup-btn.restore {\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n}\n\n.backup-btn.restore:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);\n}\n\n.backup-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.file-input {\n  display: none;\n}\n\n.backup-history {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.backup-history h3 {\n  margin: 0 0 20px 0;\n  color: #1e293b;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.no-backups {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n.history-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.history-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.history-item:hover {\n  background: #f1f5f9;\n  border-color: #cbd5e1;\n}\n\n.backup-info {\n  flex: 1;\n}\n\n.backup-date {\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 8px;\n  font-size: 1.1rem;\n}\n\n.backup-details {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 8px;\n  font-size: 0.9rem;\n  color: #64748b;\n}\n\n.backup-description {\n  color: #374151;\n  margin-bottom: 10px;\n}\n\n.backup-types {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 5px;\n}\n\n.data-type-tag {\n  background: #e0e7ff;\n  color: #3730a3;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.backup-actions-small {\n  display: flex;\n  gap: 8px;\n}\n\n.delete-backup-btn {\n  padding: 8px 12px;\n  background: #fee2e2;\n  color: #dc2626;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.delete-backup-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.confirm-modal {\n  background: white;\n  border-radius: 15px;\n  width: 100%;\n  max-width: 500px;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  padding: 20px 25px;\n  border-bottom: 1px solid #e5e7eb;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border-radius: 15px 15px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.3rem;\n  font-weight: 700;\n}\n\n.modal-content {\n  padding: 25px;\n}\n\n.modal-content p {\n  margin: 0 0 15px 0;\n  line-height: 1.6;\n  color: #374151;\n}\n\n.backup-preview {\n  background: #f8fafc;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.backup-preview h4 {\n  margin: 0 0 10px 0;\n  color: #1f2937;\n  font-size: 1rem;\n}\n\n.backup-preview p {\n  margin: 5px 0;\n  font-size: 0.9rem;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding: 20px 25px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.cancel-btn,\n.confirm-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.confirm-btn {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n.confirm-btn:hover:not(:disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.confirm-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .backup-container {\n    padding: 15px;\n  }\n  \n  .backup-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .backup-actions {\n    grid-template-columns: 1fr;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .history-item {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .backup-details {\n    flex-direction: column;\n    gap: 5px;\n  }\n  \n  .modal-actions {\n    flex-direction: column;\n  }\n}\n", ".employees-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.employees-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.employees-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.employees-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  border-left: 5px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.stat-card.total {\n  border-left-color: #667eea;\n}\n\n.stat-card.active {\n  border-left-color: #10b981;\n}\n\n.stat-card.departments {\n  border-left-color: #f59e0b;\n}\n\n.stat-card.salaries {\n  border-left-color: #8b5cf6;\n}\n\n.stat-icon {\n  font-size: 3rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  color: #374151;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.8rem;\n  font-weight: 800;\n}\n\n.employees-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-filters {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n  flex: 1;\n}\n\n.search-input,\n.filter-select {\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.search-input:focus,\n.filter-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.add-employee-btn {\n  padding: 12px 25px;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n}\n\n.add-employee-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.employee-form-modal {\n  background: white;\n  border-radius: 20px;\n  width: 100%;\n  max-width: 900px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 2px solid #e5e7eb;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.employee-form {\n  padding: 30px;\n}\n\n.form-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n.form-section {\n  background: #f8fafc;\n  padding: 25px;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.form-section h4 {\n  margin: 0 0 20px 0;\n  color: #1e293b;\n  font-size: 1.3rem;\n  font-weight: 700;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #e2e8f0;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding-top: 30px;\n  border-top: 2px solid #e5e7eb;\n  margin-top: 30px;\n}\n\n.cancel-btn,\n.save-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.employees-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.employees-table {\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 1000px;\n}\n\n.employees-table th {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 18px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.employees-table td {\n  padding: 15px;\n  border-bottom: 1px solid #e5e7eb;\n  color: #374151;\n}\n\n.employees-table tbody tr:hover {\n  background: #f8fafc;\n}\n\n.employee-name {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.salary {\n  font-weight: 600;\n  color: #059669;\n}\n\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 100px;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn,\n.delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.edit-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.edit-btn:hover {\n  background: #e5e7eb;\n  transform: scale(1.1);\n}\n\n.delete-btn {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.delete-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.no-data {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .employees-container {\n    padding: 15px;\n  }\n  \n  .employees-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .employees-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-filters {\n    flex-direction: column;\n  }\n  \n  .search-input,\n  .filter-select {\n    min-width: auto;\n  }\n  \n  .employee-form-modal {\n    margin: 10px;\n    max-height: 95vh;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .employees-table-container {\n    overflow-x: auto;\n  }\n}\n", ".notifications-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.notifications-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n  position: relative;\n}\n\n.header-content {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.notifications-header h1 {\n  margin: 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.notification-badge {\n  position: relative;\n}\n\n.unread-count {\n  background: #ef4444;\n  color: white;\n  border-radius: 50%;\n  padding: 4px 8px;\n  font-size: 0.8rem;\n  font-weight: 700;\n  min-width: 20px;\n  text-align: center;\n  animation: pulse 2s infinite;\n}\n\n.notifications-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.notifications-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  gap: 20px;\n  flex-wrap: wrap;\n  background: white;\n  padding: 20px;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.filter-controls {\n  flex: 1;\n}\n\n.filter-select {\n  padding: 10px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.filter-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.action-controls {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.mark-all-btn,\n.clear-all-btn,\n.settings-btn {\n  padding: 10px 15px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n  white-space: nowrap;\n}\n\n.mark-all-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.mark-all-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.clear-all-btn {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n.clear-all-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.settings-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.settings-btn:hover {\n  background: #e5e7eb;\n}\n\n.notification-settings {\n  background: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 25px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  animation: slideDown 0.3s ease-out;\n}\n\n.notification-settings h3 {\n  margin: 0 0 15px 0;\n  color: #1e293b;\n  font-size: 1.3rem;\n  font-weight: 700;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 15px;\n}\n\n.setting-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  cursor: pointer;\n  padding: 10px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.setting-item:hover {\n  background: #f8fafc;\n}\n\n.setting-item input[type=\"checkbox\"] {\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.notifications-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.no-notifications {\n  text-align: center;\n  padding: 60px 20px;\n  background: white;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.no-notifications-icon {\n  font-size: 4rem;\n  margin-bottom: 20px;\n  opacity: 0.5;\n}\n\n.no-notifications h3 {\n  margin: 0 0 10px 0;\n  color: #374151;\n  font-size: 1.5rem;\n}\n\n.no-notifications p {\n  margin: 0;\n  color: #9ca3af;\n}\n\n.notification-item {\n  background: white;\n  border-radius: 15px;\n  padding: 20px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n  border-left: 4px solid transparent;\n}\n\n.notification-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0,0,0,0.15);\n}\n\n.notification-item.unread {\n  border-left-color: #667eea;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n}\n\n.notification-content {\n  width: 100%;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.notification-type {\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.notification-time {\n  font-size: 0.8rem;\n  color: #9ca3af;\n}\n\n.notification-title {\n  margin: 0 0 8px 0;\n  color: #1f2937;\n  font-size: 1.2rem;\n  font-weight: 700;\n}\n\n.notification-message {\n  margin: 0 0 15px 0;\n  color: #4b5563;\n  line-height: 1.6;\n}\n\n.notification-data {\n  background: #f8fafc;\n  padding: 12px;\n  border-radius: 8px;\n  margin-bottom: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.notification-data strong {\n  color: #374151;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.notification-data ul {\n  margin: 0;\n  padding-right: 20px;\n  color: #6b7280;\n}\n\n.notification-data li {\n  margin-bottom: 4px;\n}\n\n.notification-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.priority-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  flex-shrink: 0;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.action-btn {\n  padding: 6px 12px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);\n}\n\n.delete-btn {\n  padding: 6px 8px;\n  background: #fee2e2;\n  color: #dc2626;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n}\n\n.delete-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.unread-indicator {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  width: 8px;\n  height: 8px;\n  background: #667eea;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.1);\n    opacity: 0.7;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .notifications-container {\n    padding: 15px;\n  }\n  \n  .notifications-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    gap: 10px;\n  }\n  \n  .notifications-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .action-controls {\n    justify-content: center;\n  }\n  \n  .filter-select {\n    min-width: auto;\n  }\n  \n  .settings-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .notification-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n  \n  .notification-footer {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n}\n", ".journal-entries-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.journal-entries-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.journal-entries-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.journal-entries-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  border-left: 5px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.stat-card.total { border-left-color: #667eea; }\n.stat-card.pending { border-left-color: #f59e0b; }\n.stat-card.posted { border-left-color: #10b981; }\n.stat-card.amount { border-left-color: #8b5cf6; }\n\n.stat-icon {\n  font-size: 3rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  color: #374151;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.8rem;\n  font-weight: 800;\n}\n\n.journal-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-filters {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n  flex: 1;\n}\n\n.search-input,\n.filter-select,\n.date-input {\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 150px;\n}\n\n.search-input:focus,\n.filter-select:focus,\n.date-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.add-entry-btn {\n  padding: 12px 25px;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n}\n\n.add-entry-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.entry-form-modal {\n  background: white;\n  border-radius: 20px;\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 2px solid #e5e7eb;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.entry-form {\n  padding: 30px;\n}\n\n.form-header {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.entries-section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin-bottom: 30px;\n}\n\n.debit-section,\n.credit-section {\n  background: #f8fafc;\n  padding: 20px;\n  border-radius: 15px;\n  border: 2px solid #e5e7eb;\n}\n\n.debit-section {\n  border-left-color: #ef4444;\n}\n\n.credit-section {\n  border-left-color: #10b981;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: #1e293b;\n  font-size: 1.2rem;\n  font-weight: 700;\n}\n\n.add-line-btn {\n  padding: 8px 15px;\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.add-line-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);\n}\n\n.entry-line {\n  display: grid;\n  grid-template-columns: 2fr 1fr 2fr auto;\n  gap: 10px;\n  margin-bottom: 15px;\n  align-items: center;\n}\n\n.entry-line select,\n.entry-line input {\n  padding: 10px 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.9rem;\n}\n\n.entry-line select:focus,\n.entry-line input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\n}\n\n.remove-line-btn {\n  padding: 8px;\n  background: #fee2e2;\n  color: #dc2626;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.remove-line-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.section-total {\n  text-align: left;\n  font-weight: 700;\n  color: #1f2937;\n  font-size: 1.1rem;\n  padding: 15px;\n  background: white;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n  margin-top: 15px;\n}\n\n.balance-check {\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n.balance-indicator {\n  display: inline-block;\n  padding: 12px 25px;\n  border-radius: 10px;\n  font-weight: 700;\n  font-size: 1.1rem;\n}\n\n.balance-indicator.balanced {\n  background: #d1fae5;\n  color: #065f46;\n  border: 2px solid #10b981;\n}\n\n.balance-indicator.unbalanced {\n  background: #fee2e2;\n  color: #991b1b;\n  border: 2px solid #ef4444;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding-top: 20px;\n  border-top: 2px solid #e5e7eb;\n}\n\n.cancel-btn,\n.save-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.entries-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.entries-table {\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 1000px;\n}\n\n.entries-table th {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 18px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.entries-table td {\n  padding: 15px;\n  border-bottom: 1px solid #e5e7eb;\n  color: #374151;\n}\n\n.entries-table tbody tr:hover {\n  background: #f8fafc;\n}\n\n.reference {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.amount {\n  font-weight: 600;\n  color: #059669;\n  text-align: left;\n}\n\n.type-badge,\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 80px;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn,\n.post-btn,\n.delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.edit-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #e5e7eb;\n  transform: scale(1.1);\n}\n\n.post-btn {\n  background: #dbeafe;\n  color: #1d4ed8;\n}\n\n.post-btn:hover {\n  background: #bfdbfe;\n  transform: scale(1.1);\n}\n\n.delete-btn {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.edit-btn:disabled,\n.delete-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.no-data {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .entries-section {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .journal-entries-container {\n    padding: 15px;\n  }\n  \n  .journal-entries-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .journal-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-filters {\n    flex-direction: column;\n  }\n  \n  .search-input,\n  .filter-select,\n  .date-input {\n    min-width: auto;\n  }\n  \n  .entry-form-modal {\n    margin: 10px;\n    max-height: 95vh;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .entry-line {\n    grid-template-columns: 1fr;\n    gap: 10px;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .entries-table-container {\n    overflow-x: auto;\n  }\n}\n", ".returns-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.returns-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.returns-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.returns-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  border-left: 5px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.stat-card.total { border-left-color: #667eea; }\n.stat-card.sales { border-left-color: #ef4444; }\n.stat-card.purchase { border-left-color: #f59e0b; }\n.stat-card.amount { border-left-color: #8b5cf6; }\n\n.stat-icon {\n  font-size: 3rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  color: #374151;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.8rem;\n  font-weight: 800;\n}\n\n.returns-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-filters {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n  flex: 1;\n}\n\n.search-input,\n.filter-select {\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.search-input:focus,\n.filter-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.add-return-btn {\n  padding: 12px 25px;\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n}\n\n.add-return-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.return-form-modal {\n  background: white;\n  border-radius: 20px;\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 2px solid #e5e7eb;\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.return-form {\n  padding: 30px;\n}\n\n.form-header {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.items-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: #1e293b;\n  font-size: 1.2rem;\n  font-weight: 700;\n}\n\n.add-item-btn {\n  padding: 8px 15px;\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.add-item-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);\n}\n\n.items-table {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.items-header {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr 2fr auto;\n  gap: 10px;\n  padding: 10px;\n  background: #e5e7eb;\n  border-radius: 8px;\n  font-weight: 600;\n  color: #374151;\n}\n\n.item-row {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr 2fr auto;\n  gap: 10px;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n}\n\n.item-row select,\n.item-row input {\n  padding: 8px 10px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 0.9rem;\n}\n\n.item-row select:focus,\n.item-row input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\n}\n\n.total-field {\n  background: #f3f4f6 !important;\n  color: #6b7280;\n  font-weight: 600;\n}\n\n.remove-item-btn {\n  padding: 8px;\n  background: #fee2e2;\n  color: #dc2626;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.remove-item-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.totals-section {\n  margin-top: 20px;\n  padding: 20px;\n  background: white;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n}\n\n.total-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.total-row:last-child {\n  border-bottom: none;\n}\n\n.final-total {\n  font-weight: 700;\n  font-size: 1.2rem;\n  color: #1f2937;\n  border-top: 2px solid #e5e7eb;\n  padding-top: 15px;\n  margin-top: 10px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding-top: 20px;\n  border-top: 2px solid #e5e7eb;\n}\n\n.cancel-btn,\n.save-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.returns-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.returns-table {\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 1000px;\n}\n\n.returns-table th {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  padding: 18px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.returns-table td {\n  padding: 15px;\n  border-bottom: 1px solid #e5e7eb;\n  color: #374151;\n}\n\n.returns-table tbody tr:hover {\n  background: #f8fafc;\n}\n\n.return-number {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.amount {\n  font-weight: 600;\n  color: #ef4444;\n  text-align: left;\n}\n\n.type-badge,\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 100px;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn,\n.process-btn,\n.delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.edit-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #e5e7eb;\n  transform: scale(1.1);\n}\n\n.process-btn {\n  background: #dbeafe;\n  color: #1d4ed8;\n}\n\n.process-btn:hover {\n  background: #bfdbfe;\n  transform: scale(1.1);\n}\n\n.delete-btn {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.edit-btn:disabled,\n.delete-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.no-data {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .returns-container {\n    padding: 15px;\n  }\n  \n  .returns-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .returns-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-filters {\n    flex-direction: column;\n  }\n  \n  .search-input,\n  .filter-select {\n    min-width: auto;\n  }\n  \n  .return-form-modal {\n    margin: 10px;\n    max-height: 95vh;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .items-header,\n  .item-row {\n    grid-template-columns: 1fr;\n    gap: 5px;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .returns-table-container {\n    overflow-x: auto;\n  }\n}\n", ".barcode-manager-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.barcode-manager-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.barcode-manager-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.barcode-manager-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.scanner-section {\n  background: white;\n  padding: 20px;\n  border-radius: 15px;\n  margin-bottom: 25px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.scanner-controls {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.scan-btn {\n  padding: 12px 25px;\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.scan-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);\n}\n\n.scan-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.scanned-result {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px 15px;\n  background: #f0fdf4;\n  border: 1px solid #10b981;\n  border-radius: 8px;\n}\n\n.search-scanned-btn {\n  padding: 6px 12px;\n  background: #10b981;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.search-scanned-btn:hover {\n  background: #059669;\n}\n\n.barcode-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #6366f1;\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);\n}\n\n.selection-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.select-all-btn,\n.clear-selection-btn {\n  padding: 10px 15px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n}\n\n.select-all-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.select-all-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.clear-selection-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.clear-selection-btn:hover {\n  background: #e5e7eb;\n}\n\n.selection-count {\n  font-weight: 600;\n  color: #6366f1;\n  padding: 8px 12px;\n  background: #e0e7ff;\n  border-radius: 6px;\n}\n\n.print-settings {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 25px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.print-settings h3 {\n  margin: 0 0 20px 0;\n  color: #1e293b;\n  font-size: 1.3rem;\n  font-weight: 700;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n}\n\n.setting-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-group.checkbox-group {\n  flex-direction: row;\n  align-items: center;\n  gap: 10px;\n}\n\n.setting-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.checkbox-group label {\n  margin-bottom: 0;\n  cursor: pointer;\n}\n\n.setting-group input,\n.setting-group select {\n  padding: 10px 12px;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n\n.setting-group input:focus,\n.setting-group select:focus {\n  outline: none;\n  border-color: #6366f1;\n  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);\n}\n\n.checkbox-group input[type=\"checkbox\"] {\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.action-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-bottom: 30px;\n  flex-wrap: wrap;\n}\n\n.print-btn,\n.export-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.print-btn {\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n}\n\n.print-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);\n}\n\n.export-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.export-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);\n}\n\n.print-btn:disabled,\n.export-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.no-products {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 60px 20px;\n  color: #9ca3af;\n  font-style: italic;\n  background: white;\n  border-radius: 15px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.product-card {\n  background: white;\n  border-radius: 15px;\n  padding: 20px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  border: 2px solid transparent;\n}\n\n.product-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.product-card.selected {\n  border-color: #6366f1;\n  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);\n}\n\n.product-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.product-header h4 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.1rem;\n  font-weight: 700;\n}\n\n.selection-indicator {\n  font-size: 1.5rem;\n  transition: all 0.3s ease;\n}\n\n.product-info {\n  margin-bottom: 15px;\n}\n\n.product-info p {\n  margin: 5px 0;\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n.product-info strong {\n  color: #374151;\n}\n\n.barcode-preview {\n  text-align: center;\n  margin-bottom: 15px;\n  padding: 10px;\n  background: #f8fafc;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n}\n\n.barcode-preview img {\n  max-width: 100%;\n  height: auto;\n  margin-bottom: 5px;\n}\n\n.barcode-text {\n  font-family: monospace;\n  font-size: 0.8rem;\n  color: #374151;\n  font-weight: 600;\n}\n\n.product-actions {\n  text-align: center;\n}\n\n.quick-print-btn {\n  padding: 8px 15px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.quick-print-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.scanner-modal {\n  background: white;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n  max-width: 400px;\n  width: 100%;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.scanner-content h3 {\n  margin: 0 0 20px 0;\n  color: #1f2937;\n  font-size: 1.5rem;\n}\n\n.scanner-animation {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  margin: 20px auto;\n  border: 2px solid #6366f1;\n  border-radius: 10px;\n  overflow: hidden;\n}\n\n.scanner-line {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: #ef4444;\n  animation: scan 2s infinite;\n}\n\n@keyframes scan {\n  0% { top: 0; }\n  50% { top: calc(100% - 2px); }\n  100% { top: 0; }\n}\n\n.scanner-animation p {\n  margin-top: 20px;\n  color: #6b7280;\n}\n\n.cancel-scan-btn {\n  padding: 10px 20px;\n  background: #f3f4f6;\n  color: #374151;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 20px;\n}\n\n.cancel-scan-btn:hover {\n  background: #e5e7eb;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .barcode-manager-container {\n    padding: 15px;\n  }\n  \n  .barcode-manager-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .barcode-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .selection-controls {\n    justify-content: center;\n  }\n  \n  .settings-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .products-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .scanner-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .scanned-result {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n", ".quotations-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.quotations-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.quotations-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.quotations-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  border-left: 5px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.stat-card.total { border-left-color: #8b5cf6; }\n.stat-card.accepted { border-left-color: #10b981; }\n.stat-card.pending { border-left-color: #f59e0b; }\n.stat-card.amount { border-left-color: #6366f1; }\n\n.stat-icon {\n  font-size: 3rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  color: #374151;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.8rem;\n  font-weight: 800;\n}\n\n.quotations-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-filters {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n  flex: 1;\n}\n\n.search-input,\n.filter-select {\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 200px;\n}\n\n.search-input:focus,\n.filter-select:focus {\n  outline: none;\n  border-color: #8b5cf6;\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\n}\n\n.add-quotation-btn {\n  padding: 12px 25px;\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n}\n\n.add-quotation-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.quotation-form-modal {\n  background: white;\n  border-radius: 20px;\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 2px solid #e5e7eb;\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.quotation-form {\n  padding: 30px;\n}\n\n.form-header {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #8b5cf6;\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\n}\n\n.items-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 15px;\n  border: 1px solid #e5e7eb;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: #1e293b;\n  font-size: 1.2rem;\n  font-weight: 700;\n}\n\n.add-item-btn {\n  padding: 8px 15px;\n  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.add-item-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);\n}\n\n.items-table {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.items-header {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;\n  gap: 10px;\n  padding: 10px;\n  background: #e5e7eb;\n  border-radius: 8px;\n  font-weight: 600;\n  color: #374151;\n}\n\n.item-row {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;\n  gap: 10px;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n}\n\n.item-row select,\n.item-row input {\n  padding: 8px 10px;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 0.9rem;\n}\n\n.item-row select:focus,\n.item-row input:focus {\n  outline: none;\n  border-color: #8b5cf6;\n  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);\n}\n\n.total-field {\n  background: #f3f4f6 !important;\n  color: #6b7280;\n  font-weight: 600;\n}\n\n.remove-item-btn {\n  padding: 8px;\n  background: #fee2e2;\n  color: #dc2626;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.remove-item-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.discount-section {\n  margin: 20px 0;\n  padding: 15px;\n  background: white;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n}\n\n.totals-section {\n  margin-top: 20px;\n  padding: 20px;\n  background: white;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n}\n\n.total-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.total-row:last-child {\n  border-bottom: none;\n}\n\n.final-total {\n  font-weight: 700;\n  font-size: 1.2rem;\n  color: #1f2937;\n  border-top: 2px solid #e5e7eb;\n  padding-top: 15px;\n  margin-top: 10px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding-top: 20px;\n  border-top: 2px solid #e5e7eb;\n}\n\n.cancel-btn,\n.save-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.quotations-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.quotations-table {\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 1000px;\n}\n\n.quotations-table th {\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  padding: 18px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.quotations-table td {\n  padding: 15px;\n  border-bottom: 1px solid #e5e7eb;\n  color: #374151;\n}\n\n.quotations-table tbody tr:hover {\n  background: #f8fafc;\n}\n\n.quotations-table tbody tr.expired {\n  background: #fef2f2;\n}\n\n.quotation-number {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.amount {\n  font-weight: 600;\n  color: #8b5cf6;\n  text-align: left;\n}\n\n.expired-date {\n  color: #ef4444;\n  font-weight: 600;\n}\n\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 100px;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn,\n.print-btn,\n.convert-btn,\n.delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.edit-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.edit-btn:hover:not(:disabled) {\n  background: #e5e7eb;\n  transform: scale(1.1);\n}\n\n.print-btn {\n  background: #dbeafe;\n  color: #1d4ed8;\n}\n\n.print-btn:hover {\n  background: #bfdbfe;\n  transform: scale(1.1);\n}\n\n.convert-btn {\n  background: #d1fae5;\n  color: #065f46;\n}\n\n.convert-btn:hover {\n  background: #a7f3d0;\n  transform: scale(1.1);\n}\n\n.delete-btn {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.delete-btn:hover:not(:disabled) {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.edit-btn:disabled,\n.delete-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.no-data {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .quotations-container {\n    padding: 15px;\n  }\n  \n  .quotations-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .quotations-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-filters {\n    flex-direction: column;\n  }\n  \n  .search-input,\n  .filter-select {\n    min-width: auto;\n  }\n  \n  .quotation-form-modal {\n    margin: 10px;\n    max-height: 95vh;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .items-header,\n  .item-row {\n    grid-template-columns: 1fr;\n    gap: 5px;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .quotations-table-container {\n    overflow-x: auto;\n  }\n}\n", ".vouchers-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.vouchers-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.vouchers-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.vouchers-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.message {\n  padding: 15px 25px;\n  border-radius: 12px;\n  margin-bottom: 25px;\n  font-weight: 600;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);\n}\n\n.message.error {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  transition: all 0.3s ease;\n  border-left: 5px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 35px rgba(0,0,0,0.15);\n}\n\n.stat-card.total { border-left-color: #667eea; }\n.stat-card.receipts { border-left-color: #10b981; }\n.stat-card.payments { border-left-color: #ef4444; }\n.stat-card.net-flow { border-left-color: #8b5cf6; }\n\n.stat-icon {\n  font-size: 3rem;\n  opacity: 0.8;\n}\n\n.stat-info h3 {\n  margin: 0 0 8px 0;\n  color: #374151;\n  font-size: 0.9rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.stat-info p {\n  margin: 0;\n  color: #1f2937;\n  font-size: 1.8rem;\n  font-weight: 800;\n}\n\n.stat-info p.positive {\n  color: #10b981;\n}\n\n.stat-info p.negative {\n  color: #ef4444;\n}\n\n.vouchers-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.search-filters {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n  flex: 1;\n}\n\n.search-input,\n.filter-select,\n.date-input {\n  padding: 12px 18px;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  font-size: 1rem;\n  background: white;\n  transition: all 0.3s ease;\n  min-width: 150px;\n}\n\n.search-input:focus,\n.filter-select:focus,\n.date-input:focus {\n  outline: none;\n  border-color: #059669;\n  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.add-receipt-btn,\n.add-payment-btn {\n  padding: 12px 20px;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n  font-size: 0.95rem;\n}\n\n.add-receipt-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.add-receipt-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);\n}\n\n.add-payment-btn {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n.add-payment-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 20px;\n}\n\n.voucher-form-modal {\n  background: white;\n  border-radius: 20px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 25px 50px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 25px 30px;\n  border-bottom: 2px solid #e5e7eb;\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.voucher-form {\n  padding: 30px;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group.full-width {\n  grid-column: 1 / -1;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 12px 15px;\n  border: 2px solid #e5e7eb;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #059669;\n  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  padding-top: 20px;\n  border-top: 2px solid #e5e7eb;\n}\n\n.cancel-btn,\n.save-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.cancel-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.cancel-btn:hover {\n  background: #e5e7eb;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.vouchers-table-container {\n  background: white;\n  border-radius: 15px;\n  overflow: hidden;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n}\n\n.vouchers-table {\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 1000px;\n}\n\n.vouchers-table th {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n  padding: 18px 15px;\n  text-align: right;\n  font-weight: 600;\n  font-size: 0.95rem;\n}\n\n.vouchers-table td {\n  padding: 15px;\n  border-bottom: 1px solid #e5e7eb;\n  color: #374151;\n}\n\n.vouchers-table tbody tr:hover {\n  background: #f8fafc;\n}\n\n.voucher-number {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.amount {\n  font-weight: 600;\n  text-align: left;\n}\n\n.amount.positive {\n  color: #10b981;\n}\n\n.amount.negative {\n  color: #ef4444;\n}\n\n.type-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 100px;\n}\n\n.actions {\n  display: flex;\n  gap: 8px;\n}\n\n.edit-btn,\n.print-btn,\n.delete-btn {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.edit-btn {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.edit-btn:hover {\n  background: #e5e7eb;\n  transform: scale(1.1);\n}\n\n.print-btn {\n  background: #dbeafe;\n  color: #1d4ed8;\n}\n\n.print-btn:hover {\n  background: #bfdbfe;\n  transform: scale(1.1);\n}\n\n.delete-btn {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.delete-btn:hover {\n  background: #fecaca;\n  transform: scale(1.1);\n}\n\n.no-data {\n  text-align: center;\n  padding: 40px;\n  color: #9ca3af;\n  font-style: italic;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .vouchers-container {\n    padding: 15px;\n  }\n  \n  .vouchers-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .vouchers-controls {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .search-filters {\n    flex-direction: column;\n  }\n  \n  .search-input,\n  .filter-select,\n  .date-input {\n    min-width: auto;\n  }\n  \n  .action-buttons {\n    justify-content: center;\n  }\n  \n  .voucher-form-modal {\n    margin: 10px;\n    max-height: 95vh;\n  }\n  \n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .vouchers-table-container {\n    overflow-x: auto;\n  }\n}\n", ".advanced-reports-container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n\n.reports-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 25px;\n  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\n  color: white;\n  border-radius: 20px;\n  box-shadow: 0 15px 35px rgba(0,0,0,0.1);\n}\n\n.reports-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.8rem;\n  font-weight: 800;\n}\n\n.reports-header p {\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n}\n\n.report-selection {\n  margin-bottom: 30px;\n}\n\n.report-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.report-type-card {\n  background: white;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 20px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n}\n\n.report-type-card:hover {\n  border-color: #1e40af;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.15);\n}\n\n.report-type-card.active {\n  border-color: #1e40af;\n  background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);\n  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.2);\n}\n\n.report-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.report-label {\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.9rem;\n  text-align: center;\n  line-height: 1.3;\n}\n\n.report-controls {\n  background: white;\n  padding: 25px;\n  border-radius: 15px;\n  margin-bottom: 30px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n}\n\n.date-controls {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  min-width: 150px;\n}\n\n.form-group label {\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.95rem;\n}\n\n.form-group input,\n.form-group select {\n  padding: 10px 12px;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #1e40af;\n  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);\n}\n\n.filter-controls {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n}\n\n.action-controls {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.generate-btn,\n.print-btn,\n.export-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 1rem;\n}\n\n.generate-btn {\n  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\n  color: white;\n}\n\n.generate-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);\n}\n\n.generate-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.print-btn {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n}\n\n.print-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);\n}\n\n.export-btn {\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\n  color: white;\n}\n\n.export-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(220, 38, 38, 0.3);\n}\n\n.report-display {\n  background: white;\n  border-radius: 15px;\n  padding: 30px;\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n  animation: slideIn 0.3s ease-out;\n}\n\n.report-header-info {\n  text-align: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 2px solid #e5e7eb;\n}\n\n.report-header-info h2 {\n  margin: 0 0 10px 0;\n  color: #1f2937;\n  font-size: 2rem;\n  font-weight: 700;\n}\n\n.report-period,\n.report-date {\n  margin: 5px 0;\n  color: #6b7280;\n  font-size: 1.1rem;\n}\n\n.report-content {\n  margin-top: 20px;\n}\n\n.error-message {\n  text-align: center;\n  padding: 40px;\n  color: #ef4444;\n  font-size: 1.2rem;\n  font-weight: 600;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 10px;\n}\n\n.report-data {\n  background: #f8fafc;\n  padding: 20px;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n  overflow-x: auto;\n}\n\n.report-data pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  color: #374151;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n\n/* Report-specific styles */\n.income-statement {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.revenue-section,\n.costs-section,\n.profit-section {\n  background: white;\n  padding: 20px;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n}\n\n.revenue-section {\n  border-left: 4px solid #10b981;\n}\n\n.costs-section {\n  border-left: 4px solid #ef4444;\n}\n\n.profit-section {\n  border-left: 4px solid #1e40af;\n}\n\n.section-title {\n  font-size: 1.3rem;\n  font-weight: 700;\n  margin-bottom: 15px;\n  color: #1f2937;\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 10px 0;\n}\n\n.data-table th,\n.data-table td {\n  padding: 12px;\n  text-align: right;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.data-table th {\n  background: #f8fafc;\n  font-weight: 600;\n  color: #374151;\n}\n\n.data-table .amount {\n  text-align: left;\n  font-weight: 600;\n}\n\n.data-table .positive {\n  color: #10b981;\n}\n\n.data-table .negative {\n  color: #ef4444;\n}\n\n.total-row {\n  border-top: 2px solid #374151;\n  font-weight: 700;\n  background: #f8fafc;\n}\n\n.balance-sheet {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n}\n\n.assets-section,\n.liabilities-equity-section {\n  background: white;\n  padding: 20px;\n  border-radius: 10px;\n  border: 1px solid #e5e7eb;\n}\n\n.assets-section {\n  border-left: 4px solid #1e40af;\n}\n\n.liabilities-equity-section {\n  border-left: 4px solid #dc2626;\n}\n\n.subsection {\n  margin: 15px 0;\n  padding: 10px;\n  background: #f8fafc;\n  border-radius: 6px;\n}\n\n.subsection-title {\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 10px;\n}\n\n.trial-balance-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 20px 0;\n}\n\n.trial-balance-table th,\n.trial-balance-table td {\n  padding: 10px;\n  text-align: right;\n  border: 1px solid #e5e7eb;\n}\n\n.trial-balance-table th {\n  background: #1e40af;\n  color: white;\n  font-weight: 600;\n}\n\n.trial-balance-table .account-code {\n  font-family: monospace;\n  font-weight: 600;\n}\n\n.trial-balance-table .debit {\n  color: #dc2626;\n  text-align: left;\n}\n\n.trial-balance-table .credit {\n  color: #10b981;\n  text-align: left;\n}\n\n.balance-check {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 8px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.balance-check.balanced {\n  background: #d1fae5;\n  color: #065f46;\n  border: 1px solid #10b981;\n}\n\n.balance-check.unbalanced {\n  background: #fee2e2;\n  color: #991b1b;\n  border: 1px solid #ef4444;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .balance-sheet {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .advanced-reports-container {\n    padding: 15px;\n  }\n  \n  .reports-header h1 {\n    font-size: 2.2rem;\n  }\n  \n  .report-types-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n  }\n  \n  .report-type-card {\n    padding: 15px;\n  }\n  \n  .report-icon {\n    font-size: 2rem;\n  }\n  \n  .report-label {\n    font-size: 0.8rem;\n  }\n  \n  .date-controls,\n  .filter-controls {\n    flex-direction: column;\n  }\n  \n  .form-group {\n    min-width: auto;\n  }\n  \n  .action-controls {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .generate-btn,\n  .print-btn,\n  .export-btn {\n    width: 100%;\n    max-width: 300px;\n  }\n  \n  .report-display {\n    padding: 20px;\n  }\n  \n  .report-header-info h2 {\n    font-size: 1.5rem;\n  }\n  \n  .data-table {\n    font-size: 0.9rem;\n  }\n  \n  .data-table th,\n  .data-table td {\n    padding: 8px;\n  }\n}\n", "/* تنسيقات عامة للتطبيق - <PERSON><PERSON><PERSON> ERP Style */\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n:root {\n  /* Aronim ERP Color Palette */\n  --primary-color: #2563eb;\n  --primary-dark: #1d4ed8;\n  --primary-light: #3b82f6;\n  --secondary-color: #64748b;\n  --success-color: #10b981;\n  --warning-color: #f59e0b;\n  --danger-color: #ef4444;\n  --info-color: #06b6d4;\n\n  /* Neutral Colors */\n  --white: #ffffff;\n  --gray-50: #f8fafc;\n  --gray-100: #f1f5f9;\n  --gray-200: #e2e8f0;\n  --gray-300: #cbd5e1;\n  --gray-400: #94a3b8;\n  --gray-500: #64748b;\n  --gray-600: #475569;\n  --gray-700: #334155;\n  --gray-800: #1e293b;\n  --gray-900: #0f172a;\n\n  /* Spacing */\n  --spacing-xs: 0.25rem;\n  --spacing-sm: 0.5rem;\n  --spacing-md: 1rem;\n  --spacing-lg: 1.5rem;\n  --spacing-xl: 2rem;\n  --spacing-2xl: 3rem;\n\n  /* Border Radius */\n  --radius-sm: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n\n  /* Typography */\n  --font-size-xs: 0.75rem;\n  --font-size-sm: 0.875rem;\n  --font-size-base: 1rem;\n  --font-size-lg: 1.125rem;\n  --font-size-xl: 1.25rem;\n  --font-size-2xl: 1.5rem;\n  --font-size-3xl: 1.875rem;\n  --font-size-4xl: 2.25rem;\n}\n\nbody {\n  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  background-color: var(--gray-50);\n  color: var(--gray-900);\n  direction: rtl;\n  text-align: right;\n  font-size: var(--font-size-base);\n  line-height: 1.6;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.App {\n  min-height: 100vh;\n  display: flex;\n  background-color: var(--gray-50);\n}\n\n/* Enhanced Layout Styles */\n.app-layout {\n  display: flex;\n  width: 100%;\n  min-height: 100vh;\n  background: var(--gray-50);\n}\n\n.sidebar {\n  width: 280px;\n  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  display: flex;\n  flex-direction: column;\n  position: fixed;\n  height: 100vh;\n  right: 0;\n  top: 0;\n  z-index: 1000;\n  box-shadow: var(--shadow-xl);\n  transition: all 0.3s ease;\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.sidebar.collapsed {\n  width: 80px;\n}\n\n.sidebar-header {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.15);\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(10px);\n}\n\n.sidebar-logo {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--white);\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  transition: all 0.3s ease;\n}\n\n.sidebar-logo:hover {\n  transform: scale(1.05);\n}\n\n.sidebar-logo .logo-icon {\n  font-size: var(--font-size-2xl);\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: var(--spacing-lg) 0;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;\n}\n\n.sidebar-nav::-webkit-scrollbar {\n  width: 4px;\n}\n\n.sidebar-nav::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.sidebar-nav::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n}\n\n.nav-item {\n  margin-bottom: var(--spacing-xs);\n}\n\n.nav-link {\n  display: flex;\n  align-items: center;\n  padding: var(--spacing-md) var(--spacing-xl);\n  color: rgba(255, 255, 255, 0.85);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border-right: 3px solid transparent;\n  gap: var(--spacing-md);\n  font-weight: 500;\n  font-size: var(--font-size-sm);\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 0;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  transition: width 0.3s ease;\n  z-index: -1;\n}\n\n.nav-link:hover::before {\n  width: 100%;\n}\n\n.nav-link:hover {\n  color: var(--white);\n  border-right-color: var(--white);\n  transform: translateX(-3px);\n}\n\n.nav-link.active {\n  background-color: rgba(255, 255, 255, 0.15);\n  color: var(--white);\n  border-right-color: var(--white);\n  font-weight: 600;\n  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.1);\n}\n\n.nav-icon {\n  font-size: var(--font-size-lg);\n  width: 24px;\n  text-align: center;\n  flex-shrink: 0;\n}\n\n.main-content {\n  flex: 1;\n  margin-right: 280px;\n  background-color: var(--gray-50);\n  min-height: 100vh;\n  transition: margin-right 0.3s ease;\n  position: relative;\n}\n\n.main-content.expanded {\n  margin-right: 80px;\n}\n\n/* Content Styles */\n.content-header {\n  background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-bottom: 1px solid var(--gray-200);\n  box-shadow: var(--shadow-md);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  backdrop-filter: blur(10px);\n}\n\n.header-top {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-md);\n}\n\n.header-title {\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-lg);\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-sm) var(--spacing-md);\n  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  color: var(--gray-700);\n  border: 1px solid var(--gray-200);\n  transition: all 0.3s ease;\n  box-shadow: var(--shadow-sm);\n}\n\n.user-info:hover {\n  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-100) 100%);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.content-body {\n  padding: var(--spacing-xl);\n  background: var(--gray-50);\n}\n\n/* Card Styles */\n.card {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-2px);\n}\n\n.card-header {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-bottom: 1px solid var(--gray-200);\n  background-color: var(--gray-50);\n}\n\n.card-title {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin: 0;\n}\n\n.card-body {\n  padding: var(--spacing-xl);\n}\n\n.card-footer {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-top: 1px solid var(--gray-200);\n  background-color: var(--gray-50);\n}\n\n/* Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-sm) var(--spacing-lg);\n  border: none;\n  border-radius: var(--radius-md);\n  font-weight: 500;\n  font-size: var(--font-size-sm);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  white-space: nowrap;\n  user-select: none;\n  position: relative;\n  overflow: hidden;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-sm {\n  padding: var(--spacing-xs) var(--spacing-md);\n  font-size: var(--font-size-xs);\n}\n\n.btn-lg {\n  padding: var(--spacing-md) var(--spacing-xl);\n  font-size: var(--font-size-lg);\n}\n\n.btn-primary {\n  background-color: var(--primary-color);\n  color: var(--white);\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: var(--primary-dark);\n  box-shadow: var(--shadow-md);\n  transform: translateY(-1px);\n}\n\n.btn-secondary {\n  background-color: var(--gray-500);\n  color: var(--white);\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: var(--gray-600);\n}\n\n.btn-success {\n  background-color: var(--success-color);\n  color: var(--white);\n}\n\n.btn-success:hover:not(:disabled) {\n  background-color: #059669;\n  transform: translateY(-1px);\n}\n\n.btn-warning {\n  background-color: var(--warning-color);\n  color: var(--white);\n}\n\n.btn-warning:hover:not(:disabled) {\n  background-color: #d97706;\n}\n\n.btn-danger {\n  background-color: var(--danger-color);\n  color: var(--white);\n}\n\n.btn-danger:hover:not(:disabled) {\n  background-color: #dc2626;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border: 2px solid var(--primary-color);\n  color: var(--primary-color);\n}\n\n.btn-outline:hover:not(:disabled) {\n  background-color: var(--primary-color);\n  color: var(--white);\n}\n\n/* قسم المميزات */\n.features-section {\n  background: #f0f2f5;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: right;\n  margin-bottom: 30px;\n  animation: fadeInUp 0.8s ease-out 0.6s both;\n}\n\n.features-title {\n  margin-bottom: 15px;\n  color: #262626;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.features-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.features-list li {\n  margin-bottom: 8px;\n  font-size: 16px;\n  color: #555;\n  padding: 5px 0;\n  border-bottom: 1px solid rgba(0,0,0,0.05);\n}\n\n.features-list li:last-child {\n  border-bottom: none;\n}\n\n/* قسم المعلومات الإضافية */\n.more-info {\n  margin-top: 20px;\n  padding: 20px;\n  background: #f6ffed;\n  border-radius: 8px;\n  border: 1px solid #b7eb8f;\n  animation: slideDown 0.5s ease-out;\n  text-align: right;\n}\n\n.more-info h4 {\n  color: #52c41a;\n  margin-bottom: 15px;\n  font-size: 1.1rem;\n}\n\n.more-info p {\n  margin-bottom: 10px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.more-info strong {\n  color: #262626;\n}\n\n/* قسم حالة التطوير */\n.status-section {\n  margin-top: 30px;\n  padding: 20px;\n  background: #e6f7ff;\n  border-radius: 8px;\n  border: 1px solid #91d5ff;\n  animation: fadeInUp 0.8s ease-out 0.8s both;\n}\n\n.status-title {\n  color: #1890ff;\n  margin-bottom: 10px;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n\n.status-text {\n  margin: 0;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* الحاوية الرئيسية للبطاقة */\n.main-card {\n  max-width: 600px;\n  margin: 0 auto;\n  border-radius: 12px;\n  background: white;\n  padding: 40px;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255,255,255,0.2);\n  animation: fadeIn 0.8s ease-out;\n}\n\n/* الرسوم المتحركة */\n@keyframes fadeIn {\n  from { opacity: 0; transform: scale(0.9); }\n  to { opacity: 1; transform: scale(1); }\n}\n\n@keyframes fadeInDown {\n  from { opacity: 0; transform: translateY(-30px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes fadeInUp {\n  from { opacity: 0; transform: translateY(30px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes slideDown {\n  from { opacity: 0; transform: translateY(-20px); max-height: 0; }\n  to { opacity: 1; transform: translateY(0); max-height: 500px; }\n}\n\n/* تحسين الاستجابة */\n@media (max-width: 768px) {\n  .app {\n    padding: 20px;\n  }\n\n  .main-card {\n    padding: 20px;\n    margin: 0 10px;\n  }\n\n  .main-title {\n    font-size: 2rem;\n  }\n\n  .buttons-container {\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .btn {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n@media (max-width: 480px) {\n  .app {\n    padding: 15px;\n  }\n\n  .main-card {\n    padding: 15px;\n  }\n\n  .main-title {\n    font-size: 1.8rem;\n  }\n\n  .main-description {\n    font-size: 16px;\n  }\n}\n"], "names": [], "sourceRoot": ""}