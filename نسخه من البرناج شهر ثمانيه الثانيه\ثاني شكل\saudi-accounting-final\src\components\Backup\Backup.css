.backup-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.backup-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.backup-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.backup-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.message {
  padding: 15px 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  font-weight: 600;
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

.message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.progress-container {
  background: white;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-text {
  display: block;
  text-align: center;
  font-weight: 600;
  color: #374151;
}

.data-stats {
  background: white;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.data-stats h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.backup-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.action-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.action-card h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 1.3rem;
  font-weight: 700;
}

.action-card p {
  margin: 0 0 20px 0;
  color: #64748b;
  line-height: 1.6;
}

.backup-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  text-decoration: none;
  display: inline-block;
}

.backup-btn.create {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.backup-btn.create:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.backup-btn.restore {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.backup-btn.restore:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.backup-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.file-input {
  display: none;
}

.backup-history {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.backup-history h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
}

.no-backups {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
  font-style: italic;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: #f8fafc;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.backup-info {
  flex: 1;
}

.backup-date {
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.backup-details {
  display: flex;
  gap: 20px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #64748b;
}

.backup-description {
  color: #374151;
  margin-bottom: 10px;
}

.backup-types {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.data-type-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.backup-actions-small {
  display: flex;
  gap: 8px;
}

.delete-backup-btn {
  padding: 8px 12px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.delete-backup-btn:hover {
  background: #fecaca;
  transform: scale(1.1);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.confirm-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.modal-content {
  padding: 25px;
}

.modal-content p {
  margin: 0 0 15px 0;
  line-height: 1.6;
  color: #374151;
}

.backup-preview {
  background: #f8fafc;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
  border: 1px solid #e5e7eb;
}

.backup-preview h4 {
  margin: 0 0 10px 0;
  color: #1f2937;
  font-size: 1rem;
}

.backup-preview p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px 25px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .backup-container {
    padding: 15px;
  }
  
  .backup-header h1 {
    font-size: 2.2rem;
  }
  
  .backup-actions {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .history-item {
    flex-direction: column;
    gap: 15px;
  }
  
  .backup-details {
    flex-direction: column;
    gap: 5px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
