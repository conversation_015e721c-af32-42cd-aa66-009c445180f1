{"version": 3, "file": "workbox-background-sync.dev.js", "sources": ["../node_modules/idb/build/wrap-idb-value.js", "../node_modules/idb/build/index.js", "../_version.js", "../lib/QueueDb.js", "../lib/QueueStore.js", "../lib/StorableRequest.js", "../Queue.js", "../BackgroundSyncPlugin.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction));\n        });\n    }\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking)\n            db.addEventListener('versionchange', () => blocking());\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:background-sync:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2021 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB } from 'idb';\nimport '../_version.js';\nconst DB_VERSION = 3;\nconst DB_NAME = 'workbox-background-sync';\nconst REQUEST_OBJECT_STORE_NAME = 'requests';\nconst QUEUE_NAME_INDEX = 'queueName';\n/**\n * A class to interact directly an IndexedDB created specifically to save and\n * retrieve QueueStoreEntries. This class encapsulates all the schema details\n * to store the representation of a Queue.\n *\n * @private\n */\nexport class QueueDb {\n    constructor() {\n        this._db = null;\n    }\n    /**\n     * Add QueueStoreEntry to underlying db.\n     *\n     * @param {UnidentifiedQueueStoreEntry} entry\n     */\n    async addEntry(entry) {\n        const db = await this.getDb();\n        const tx = db.transaction(REQUEST_OBJECT_STORE_NAME, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.add(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the first entry id in the ObjectStore.\n     *\n     * @return {number | undefined}\n     */\n    async getFirstEntryId() {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.openCursor();\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value.id;\n    }\n    /**\n     * Get all the entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<QueueStoreEntry[]>}\n     */\n    async getAllEntriesByQueueName(queueName) {\n        const db = await this.getDb();\n        const results = await db.getAllFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n        return results ? results : new Array();\n    }\n    /**\n     * Returns the number of entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<number>}\n     */\n    async getEntryCountByQueueName(queueName) {\n        const db = await this.getDb();\n        return db.countFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n    }\n    /**\n     * Deletes a single entry by id.\n     *\n     * @param {number} id the id of the entry to be deleted\n     */\n    async deleteEntry(id) {\n        const db = await this.getDb();\n        await db.delete(REQUEST_OBJECT_STORE_NAME, id);\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getFirstEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'next');\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getLastEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'prev');\n    }\n    /**\n     * Returns either the first or the last entries, depending on direction.\n     * Filtered by index.\n     *\n     * @param {IDBCursorDirection} direction\n     * @param {IDBKeyRange} query\n     * @return {Promise<QueueStoreEntry | undefined>}\n     * @private\n     */\n    async getEndEntryFromIndex(query, direction) {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.index(QUEUE_NAME_INDEX)\n            .openCursor(query, direction);\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value;\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, DB_VERSION, {\n                upgrade: this._upgradeDb,\n            });\n        }\n        return this._db;\n    }\n    /**\n     * Upgrades QueueDB\n     *\n     * @param {IDBPDatabase<QueueDBSchema>} db\n     * @param {number} oldVersion\n     * @private\n     */\n    _upgradeDb(db, oldVersion) {\n        if (oldVersion > 0 && oldVersion < DB_VERSION) {\n            if (db.objectStoreNames.contains(REQUEST_OBJECT_STORE_NAME)) {\n                db.deleteObjectStore(REQUEST_OBJECT_STORE_NAME);\n            }\n        }\n        const objStore = db.createObjectStore(REQUEST_OBJECT_STORE_NAME, {\n            autoIncrement: true,\n            keyPath: 'id',\n        });\n        objStore.createIndex(QUEUE_NAME_INDEX, QUEUE_NAME_INDEX, { unique: false });\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { QueueDb, } from './QueueDb.js';\nimport '../_version.js';\n/**\n * A class to manage storing requests from a Queue in IndexedDB,\n * indexed by their queue name for easier access.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nexport class QueueStore {\n    /**\n     * Associates this instance with a Queue instance, so entries added can be\n     * identified by their queue name.\n     *\n     * @param {string} queueName\n     */\n    constructor(queueName) {\n        this._queueName = queueName;\n        this._queueDb = new QueueDb();\n    }\n    /**\n     * Append an entry last in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async pushEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        // Don't specify an ID since one is automatically generated.\n        delete entry.id;\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Prepend an entry first in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async unshiftEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        const firstId = await this._queueDb.getFirstEntryId();\n        if (firstId) {\n            // Pick an ID one less than the lowest ID in the object store.\n            entry.id = firstId - 1;\n        }\n        else {\n            // Otherwise let the auto-incrementor assign the ID.\n            delete entry.id;\n        }\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Removes and returns the last entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async popEntry() {\n        return this._removeEntry(await this._queueDb.getLastEntryByQueueName(this._queueName));\n    }\n    /**\n     * Removes and returns the first entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async shiftEntry() {\n        return this._removeEntry(await this._queueDb.getFirstEntryByQueueName(this._queueName));\n    }\n    /**\n     * Returns all entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~getAll}\n     * @return {Promise<Array<Object>>}\n     */\n    async getAll() {\n        return await this._queueDb.getAllEntriesByQueueName(this._queueName);\n    }\n    /**\n     * Returns the number of entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~size}\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueDb.getEntryCountByQueueName(this._queueName);\n    }\n    /**\n     * Deletes the entry for the given ID.\n     *\n     * WARNING: this method does not ensure the deleted entry belongs to this\n     * queue (i.e. matches the `queueName`). But this limitation is acceptable\n     * as this class is not publicly exposed. An additional check would make\n     * this method slower than it needs to be.\n     *\n     * @param {number} id\n     */\n    async deleteEntry(id) {\n        await this._queueDb.deleteEntry(id);\n    }\n    /**\n     * Removes and returns the first or last entry in the queue (based on the\n     * `direction` argument) matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     * @private\n     */\n    async _removeEntry(entry) {\n        if (entry) {\n            await this.deleteEntry(entry.id);\n        }\n        return entry;\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\nconst serializableProperties = [\n    'method',\n    'referrer',\n    'referrerPolicy',\n    'mode',\n    'credentials',\n    'cache',\n    'redirect',\n    'integrity',\n    'keepalive',\n];\n/**\n * A class to make it easier to serialize and de-serialize requests so they\n * can be stored in IndexedDB.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nclass StorableRequest {\n    /**\n     * Converts a Request object to a plain object that can be structured\n     * cloned or JSON-stringified.\n     *\n     * @param {Request} request\n     * @return {Promise<StorableRequest>}\n     */\n    static async fromRequest(request) {\n        const requestData = {\n            url: request.url,\n            headers: {},\n        };\n        // Set the body if present.\n        if (request.method !== 'GET') {\n            // Use ArrayBuffer to support non-text request bodies.\n            // NOTE: we can't use Blobs becuse Safari doesn't support storing\n            // Blobs in IndexedDB in some cases:\n            // https://github.com/dfahlander/Dexie.js/issues/618#issuecomment-398348457\n            requestData.body = await request.clone().arrayBuffer();\n        }\n        // Convert the headers from an iterable to an object.\n        for (const [key, value] of request.headers.entries()) {\n            requestData.headers[key] = value;\n        }\n        // Add all other serializable request properties\n        for (const prop of serializableProperties) {\n            if (request[prop] !== undefined) {\n                requestData[prop] = request[prop];\n            }\n        }\n        return new StorableRequest(requestData);\n    }\n    /**\n     * Accepts an object of request data that can be used to construct a\n     * `Request` but can also be stored in IndexedDB.\n     *\n     * @param {Object} requestData An object of request data that includes the\n     *     `url` plus any relevant properties of\n     *     [requestInit]{@link https://fetch.spec.whatwg.org/#requestinit}.\n     */\n    constructor(requestData) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData',\n            });\n            assert.isType(requestData.url, 'string', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData.url',\n            });\n        }\n        // If the request's mode is `navigate`, convert it to `same-origin` since\n        // navigation requests can't be constructed via script.\n        if (requestData['mode'] === 'navigate') {\n            requestData['mode'] = 'same-origin';\n        }\n        this._requestData = requestData;\n    }\n    /**\n     * Returns a deep clone of the instances `_requestData` object.\n     *\n     * @return {Object}\n     */\n    toObject() {\n        const requestData = Object.assign({}, this._requestData);\n        requestData.headers = Object.assign({}, this._requestData.headers);\n        if (requestData.body) {\n            requestData.body = requestData.body.slice(0);\n        }\n        return requestData;\n    }\n    /**\n     * Converts this instance to a Request.\n     *\n     * @return {Request}\n     */\n    toRequest() {\n        return new Request(this._requestData.url, this._requestData);\n    }\n    /**\n     * Creates and returns a deep clone of the instance.\n     *\n     * @return {StorableRequest}\n     */\n    clone() {\n        return new StorableRequest(this.toObject());\n    }\n}\nexport { StorableRequest };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { QueueStore } from './lib/QueueStore.js';\nimport { StorableRequest } from './lib/StorableRequest.js';\nimport './_version.js';\nconst TAG_PREFIX = 'workbox-background-sync';\nconst MAX_RETENTION_TIME = 60 * 24 * 7; // 7 days in minutes\nconst queueNames = new Set();\n/**\n * Converts a QueueStore entry into the format exposed by Queue. This entails\n * converting the request data into a real request and omitting the `id` and\n * `queueName` properties.\n *\n * @param {UnidentifiedQueueStoreEntry} queueStoreEntry\n * @return {Queue}\n * @private\n */\nconst convertEntry = (queueStoreEntry) => {\n    const queueEntry = {\n        request: new StorableRequest(queueStoreEntry.requestData).toRequest(),\n        timestamp: queueStoreEntry.timestamp,\n    };\n    if (queueStoreEntry.metadata) {\n        queueEntry.metadata = queueStoreEntry.metadata;\n    }\n    return queueEntry;\n};\n/**\n * A class to manage storing failed requests in IndexedDB and retrying them\n * later. All parts of the storing and replaying process are observable via\n * callbacks.\n *\n * @memberof workbox-background-sync\n */\nclass Queue {\n    /**\n     * Creates an instance of Queue with the given options\n     *\n     * @param {string} name The unique name for this queue. This name must be\n     *     unique as it's used to register sync events and store requests\n     *     in IndexedDB specific to this instance. An error will be thrown if\n     *     a duplicate name is detected.\n     * @param {Object} [options]\n     * @param {Function} [options.onSync] A function that gets invoked whenever\n     *     the 'sync' event fires. The function is invoked with an object\n     *     containing the `queue` property (referencing this instance), and you\n     *     can use the callback to customize the replay behavior of the queue.\n     *     When not set the `replayRequests()` method is called.\n     *     Note: if the replay fails after a sync event, make sure you throw an\n     *     error, so the browser knows to retry the sync event later.\n     * @param {number} [options.maxRetentionTime=7 days] The amount of time (in\n     *     minutes) a request may be retried. After this amount of time has\n     *     passed, the request will be deleted from the queue.\n     * @param {boolean} [options.forceSyncFallback=false] If `true`, instead\n     *     of attempting to use background sync events, always attempt to replay\n     *     queued request at service worker startup. Most folks will not need\n     *     this, unless you explicitly target a runtime like Electron that\n     *     exposes the interfaces for background sync, but does not have a working\n     *     implementation.\n     */\n    constructor(name, { forceSyncFallback, onSync, maxRetentionTime } = {}) {\n        this._syncInProgress = false;\n        this._requestsAddedDuringSync = false;\n        // Ensure the store name is not already being used\n        if (queueNames.has(name)) {\n            throw new WorkboxError('duplicate-queue-name', { name });\n        }\n        else {\n            queueNames.add(name);\n        }\n        this._name = name;\n        this._onSync = onSync || this.replayRequests;\n        this._maxRetentionTime = maxRetentionTime || MAX_RETENTION_TIME;\n        this._forceSyncFallback = Boolean(forceSyncFallback);\n        this._queueStore = new QueueStore(this._name);\n        this._addSyncListener();\n    }\n    /**\n     * @return {string}\n     */\n    get name() {\n        return this._name;\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the end of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async pushRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'push');\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the beginning of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async unshiftRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'unshift');\n    }\n    /**\n     * Removes and returns the last request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async popRequest() {\n        return this._removeRequest('pop');\n    }\n    /**\n     * Removes and returns the first request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async shiftRequest() {\n        return this._removeRequest('shift');\n    }\n    /**\n     * Returns all the entries that have not expired (per `maxRetentionTime`).\n     * Any expired entries are removed from the queue.\n     *\n     * @return {Promise<Array<QueueEntry>>}\n     */\n    async getAll() {\n        const allEntries = await this._queueStore.getAll();\n        const now = Date.now();\n        const unexpiredEntries = [];\n        for (const entry of allEntries) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                await this._queueStore.deleteEntry(entry.id);\n            }\n            else {\n                unexpiredEntries.push(convertEntry(entry));\n            }\n        }\n        return unexpiredEntries;\n    }\n    /**\n     * Returns the number of entries present in the queue.\n     * Note that expired entries (per `maxRetentionTime`) are also included in this count.\n     *\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueStore.size();\n    }\n    /**\n     * Adds the entry to the QueueStore and registers for a sync event.\n     *\n     * @param {Object} entry\n     * @param {Request} entry.request\n     * @param {Object} [entry.metadata]\n     * @param {number} [entry.timestamp=Date.now()]\n     * @param {string} operation ('push' or 'unshift')\n     * @private\n     */\n    async _addRequest({ request, metadata, timestamp = Date.now() }, operation) {\n        const storableRequest = await StorableRequest.fromRequest(request.clone());\n        const entry = {\n            requestData: storableRequest.toObject(),\n            timestamp,\n        };\n        // Only include metadata if it's present.\n        if (metadata) {\n            entry.metadata = metadata;\n        }\n        switch (operation) {\n            case 'push':\n                await this._queueStore.pushEntry(entry);\n                break;\n            case 'unshift':\n                await this._queueStore.unshiftEntry(entry);\n                break;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Request for '${getFriendlyURL(request.url)}' has ` +\n                `been added to background sync queue '${this._name}'.`);\n        }\n        // Don't register for a sync if we're in the middle of a sync. Instead,\n        // we wait until the sync is complete and call register if\n        // `this._requestsAddedDuringSync` is true.\n        if (this._syncInProgress) {\n            this._requestsAddedDuringSync = true;\n        }\n        else {\n            await this.registerSync();\n        }\n    }\n    /**\n     * Removes and returns the first or last (depending on `operation`) entry\n     * from the QueueStore that's not older than the `maxRetentionTime`.\n     *\n     * @param {string} operation ('pop' or 'shift')\n     * @return {Object|undefined}\n     * @private\n     */\n    async _removeRequest(operation) {\n        const now = Date.now();\n        let entry;\n        switch (operation) {\n            case 'pop':\n                entry = await this._queueStore.popEntry();\n                break;\n            case 'shift':\n                entry = await this._queueStore.shiftEntry();\n                break;\n        }\n        if (entry) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                return this._removeRequest(operation);\n            }\n            return convertEntry(entry);\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Loops through each request in the queue and attempts to re-fetch it.\n     * If any request fails to re-fetch, it's put back in the same position in\n     * the queue (which registers a retry for the next sync event).\n     */\n    async replayRequests() {\n        let entry;\n        while ((entry = await this.shiftRequest())) {\n            try {\n                await fetch(entry.request.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `has been replayed in queue '${this._name}'`);\n                }\n            }\n            catch (error) {\n                await this.unshiftRequest(entry);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `failed to replay, putting it back in queue '${this._name}'`);\n                }\n                throw new WorkboxError('queue-replay-failed', { name: this._name });\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`All requests in queue '${this.name}' have successfully ` +\n                `replayed; the queue is now empty!`);\n        }\n    }\n    /**\n     * Registers a sync event with a tag unique to this instance.\n     */\n    async registerSync() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            try {\n                await self.registration.sync.register(`${TAG_PREFIX}:${this._name}`);\n            }\n            catch (err) {\n                // This means the registration failed for some reason, possibly due to\n                // the user disabling it.\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn(`Unable to register sync event for '${this._name}'.`, err);\n                }\n            }\n        }\n    }\n    /**\n     * In sync-supporting browsers, this adds a listener for the sync event.\n     * In non-sync-supporting browsers, or if _forceSyncFallback is true, this\n     * will retry the queue on service worker startup.\n     *\n     * @private\n     */\n    _addSyncListener() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            self.addEventListener('sync', (event) => {\n                if (event.tag === `${TAG_PREFIX}:${this._name}`) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logger.log(`Background sync for tag '${event.tag}' ` + `has been received`);\n                    }\n                    const syncComplete = async () => {\n                        this._syncInProgress = true;\n                        let syncError;\n                        try {\n                            await this._onSync({ queue: this });\n                        }\n                        catch (error) {\n                            if (error instanceof Error) {\n                                syncError = error;\n                                // Rethrow the error. Note: the logic in the finally clause\n                                // will run before this gets rethrown.\n                                throw syncError;\n                            }\n                        }\n                        finally {\n                            // New items may have been added to the queue during the sync,\n                            // so we need to register for a new sync if that's happened...\n                            // Unless there was an error during the sync, in which\n                            // case the browser will automatically retry later, as long\n                            // as `event.lastChance` is not true.\n                            if (this._requestsAddedDuringSync &&\n                                !(syncError && !event.lastChance)) {\n                                await this.registerSync();\n                            }\n                            this._syncInProgress = false;\n                            this._requestsAddedDuringSync = false;\n                        }\n                    };\n                    event.waitUntil(syncComplete());\n                }\n            });\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Background sync replaying without background sync event`);\n            }\n            // If the browser doesn't support background sync, or the developer has\n            // opted-in to not using it, retry every time the service worker starts up\n            // as a fallback.\n            void this._onSync({ queue: this });\n        }\n    }\n    /**\n     * Returns the set of queue names. This is primarily used to reset the list\n     * of queue names in tests.\n     *\n     * @return {Set<string>}\n     *\n     * @private\n     */\n    static get _queueNames() {\n        return queueNames;\n    }\n}\nexport { Queue };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Queue } from './Queue.js';\nimport './_version.js';\n/**\n * A class implementing the `fetchDidFail` lifecycle callback. This makes it\n * easier to add failed requests to a background sync Queue.\n *\n * @memberof workbox-background-sync\n */\nclass BackgroundSyncPlugin {\n    /**\n     * @param {string} name See the {@link workbox-background-sync.Queue}\n     *     documentation for parameter details.\n     * @param {Object} [options] See the\n     *     {@link workbox-background-sync.Queue} documentation for\n     *     parameter details.\n     */\n    constructor(name, options) {\n        /**\n         * @param {Object} options\n         * @param {Request} options.request\n         * @private\n         */\n        this.fetchDidFail = async ({ request }) => {\n            await this._queue.pushRequest({ request });\n        };\n        this._queue = new Queue(name, options);\n    }\n}\nexport { BackgroundSyncPlugin };\n"], "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "request", "promise", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "success", "error", "wrap", "result", "addEventListener", "then", "value", "set", "catch", "cacheDonePromiseForTransaction", "tx", "has", "done", "complete", "DOMException", "idbProxyTraps", "get", "target", "prop", "receiver", "objectStoreNames", "undefined", "objectStore", "replaceTraps", "callback", "wrapFunction", "func", "transaction", "storeNames", "args", "call", "unwrap", "sort", "includes", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "replace", "useIndex", "isWrite", "method", "storeName", "store", "index", "shift", "all", "oldTraps", "self", "_", "e", "DB_VERSION", "DB_NAME", "REQUEST_OBJECT_STORE_NAME", "QUEUE_NAME_INDEX", "QueueDb", "constructor", "_db", "addEntry", "entry", "getDb", "durability", "add", "getFirstEntryId", "cursor", "openCursor", "id", "getAllEntriesByQueueName", "queueName", "results", "getAllFromIndex", "IDBKeyRange", "only", "Array", "getEntryCountByQueueName", "countFromIndex", "deleteEntry", "delete", "getFirstEntryByQueueName", "getEndEntryFromIndex", "getLastEntryByQueueName", "query", "direction", "_upgradeDb", "contains", "deleteObjectStore", "objStore", "createObjectStore", "autoIncrement", "keyP<PERSON>", "createIndex", "unique", "QueueStore", "_queueName", "_queueDb", "pushEntry", "assert", "isType", "moduleName", "className", "funcName", "paramName", "requestData", "unshiftEntry", "firstId", "popEntry", "_removeEntry", "shiftEntry", "getAll", "size", "serializableProperties", "StorableRequest", "fromRequest", "url", "headers", "body", "clone", "arrayBuffer", "key", "entries", "_requestData", "toObject", "Object", "assign", "slice", "toRequest", "Request", "TAG_PREFIX", "MAX_RETENTION_TIME", "queueNames", "Set", "convertEntry", "queueStoreEntry", "queueEntry", "timestamp", "metadata", "Queue", "forceSyncFallback", "onSync", "maxRetentionTime", "_syncInProgress", "_requestsAddedDuringSync", "WorkboxError", "_name", "_onSync", "replayRequests", "_maxRetentionTime", "_forceSyncFallback", "Boolean", "_queueStore", "_addSyncListener", "pushRequest", "isInstance", "_addRequest", "unshiftRequest", "popRequest", "_removeRequest", "shiftRequest", "allEntries", "now", "Date", "unexpiredEntries", "maxRetentionTimeInMs", "push", "operation", "storableRequest", "logger", "log", "getFriendlyURL", "registerSync", "fetch", "process", "registration", "sync", "register", "err", "warn", "tag", "syncComplete", "syncError", "queue", "Error", "lastC<PERSON>ce", "waitUntil", "_queueNames", "BackgroundSyncPlugin", "options", "fetchDidFail", "_queue"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;EAAA,MAAMA,aAAa,GAAG,CAACC,MAAD,EAASC,YAAT,KAA0BA,YAAY,CAACC,IAAb,CAAmBC,CAAD,IAAOH,MAAM,YAAYG,CAA3C,CAAhD;;EAEA,IAAIC,iBAAJ;EACA,IAAIC,oBAAJ;;EAEA,SAASC,oBAAT,GAAgC;EAC5B,SAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WADiB,EAEjBC,cAFiB,EAGjBC,QAHiB,EAIjBC,SAJiB,EAKjBC,cALiB,CADA,CAAzB;EAQH;;;EAED,SAASC,uBAAT,GAAmC;EAC/B,SAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAV,CAAoBC,OADA,EAEpBJ,SAAS,CAACG,SAAV,CAAoBE,QAFA,EAGpBL,SAAS,CAACG,SAAV,CAAoBG,kBAHA,CADA,CAA5B;EAMH;;EACD,MAAMC,gBAAgB,GAAG,IAAIC,OAAJ,EAAzB;EACA,MAAMC,kBAAkB,GAAG,IAAID,OAAJ,EAA3B;EACA,MAAME,wBAAwB,GAAG,IAAIF,OAAJ,EAAjC;EACA,MAAMG,cAAc,GAAG,IAAIH,OAAJ,EAAvB;EACA,MAAMI,qBAAqB,GAAG,IAAIJ,OAAJ,EAA9B;;EACA,SAASK,gBAAT,CAA0BC,OAA1B,EAAmC;EAC/B,QAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;EAC7C,UAAMC,QAAQ,GAAG,MAAM;EACnBL,MAAAA,OAAO,CAACM,mBAAR,CAA4B,SAA5B,EAAuCC,OAAvC;EACAP,MAAAA,OAAO,CAACM,mBAAR,CAA4B,OAA5B,EAAqCE,KAArC;EACH,KAHD;;EAIA,UAAMD,OAAO,GAAG,MAAM;EAClBJ,MAAAA,OAAO,CAACM,IAAI,CAACT,OAAO,CAACU,MAAT,CAAL,CAAP;EACAL,MAAAA,QAAQ;EACX,KAHD;;EAIA,UAAMG,KAAK,GAAG,MAAM;EAChBJ,MAAAA,MAAM,CAACJ,OAAO,CAACQ,KAAT,CAAN;EACAH,MAAAA,QAAQ;EACX,KAHD;;EAIAL,IAAAA,OAAO,CAACW,gBAAR,CAAyB,SAAzB,EAAoCJ,OAApC;EACAP,IAAAA,OAAO,CAACW,gBAAR,CAAyB,OAAzB,EAAkCH,KAAlC;EACH,GAfe,CAAhB;EAgBAP,EAAAA,OAAO,CACFW,IADL,CACWC,KAAD,IAAW;EACjB;EACA;EACA,QAAIA,KAAK,YAAY3B,SAArB,EAAgC;EAC5BO,MAAAA,gBAAgB,CAACqB,GAAjB,CAAqBD,KAArB,EAA4Bb,OAA5B;EACH,KALgB;;EAOpB,GARD,EASKe,KATL,CASW,MAAM,EATjB,EAjB+B;EA4B/B;;EACAjB,EAAAA,qBAAqB,CAACgB,GAAtB,CAA0Bb,OAA1B,EAAmCD,OAAnC;EACA,SAAOC,OAAP;EACH;;EACD,SAASe,8BAAT,CAAwCC,EAAxC,EAA4C;EACxC;EACA,MAAItB,kBAAkB,CAACuB,GAAnB,CAAuBD,EAAvB,CAAJ,EACI;EACJ,QAAME,IAAI,GAAG,IAAIjB,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;EAC1C,UAAMC,QAAQ,GAAG,MAAM;EACnBY,MAAAA,EAAE,CAACX,mBAAH,CAAuB,UAAvB,EAAmCc,QAAnC;EACAH,MAAAA,EAAE,CAACX,mBAAH,CAAuB,OAAvB,EAAgCE,KAAhC;EACAS,MAAAA,EAAE,CAACX,mBAAH,CAAuB,OAAvB,EAAgCE,KAAhC;EACH,KAJD;;EAKA,UAAMY,QAAQ,GAAG,MAAM;EACnBjB,MAAAA,OAAO;EACPE,MAAAA,QAAQ;EACX,KAHD;;EAIA,UAAMG,KAAK,GAAG,MAAM;EAChBJ,MAAAA,MAAM,CAACa,EAAE,CAACT,KAAH,IAAY,IAAIa,YAAJ,CAAiB,YAAjB,EAA+B,YAA/B,CAAb,CAAN;EACAhB,MAAAA,QAAQ;EACX,KAHD;;EAIAY,IAAAA,EAAE,CAACN,gBAAH,CAAoB,UAApB,EAAgCS,QAAhC;EACAH,IAAAA,EAAE,CAACN,gBAAH,CAAoB,OAApB,EAA6BH,KAA7B;EACAS,IAAAA,EAAE,CAACN,gBAAH,CAAoB,OAApB,EAA6BH,KAA7B;EACH,GAjBY,CAAb,CAJwC;;EAuBxCb,EAAAA,kBAAkB,CAACmB,GAAnB,CAAuBG,EAAvB,EAA2BE,IAA3B;EACH;;EACD,IAAIG,aAAa,GAAG;EAChBC,EAAAA,GAAG,CAACC,MAAD,EAASC,IAAT,EAAeC,QAAf,EAAyB;EACxB,QAAIF,MAAM,YAAYrC,cAAtB,EAAsC;EAClC;EACA,UAAIsC,IAAI,KAAK,MAAb,EACI,OAAO9B,kBAAkB,CAAC4B,GAAnB,CAAuBC,MAAvB,CAAP,CAH8B;;EAKlC,UAAIC,IAAI,KAAK,kBAAb,EAAiC;EAC7B,eAAOD,MAAM,CAACG,gBAAP,IAA2B/B,wBAAwB,CAAC2B,GAAzB,CAA6BC,MAA7B,CAAlC;EACH,OAPiC;;;EASlC,UAAIC,IAAI,KAAK,OAAb,EAAsB;EAClB,eAAOC,QAAQ,CAACC,gBAAT,CAA0B,CAA1B,IACDC,SADC,GAEDF,QAAQ,CAACG,WAAT,CAAqBH,QAAQ,CAACC,gBAAT,CAA0B,CAA1B,CAArB,CAFN;EAGH;EACJ,KAfuB;;;EAiBxB,WAAOlB,IAAI,CAACe,MAAM,CAACC,IAAD,CAAP,CAAX;EACH,GAnBe;;EAoBhBX,EAAAA,GAAG,CAACU,MAAD,EAASC,IAAT,EAAeZ,KAAf,EAAsB;EACrBW,IAAAA,MAAM,CAACC,IAAD,CAAN,GAAeZ,KAAf;EACA,WAAO,IAAP;EACH,GAvBe;;EAwBhBK,EAAAA,GAAG,CAACM,MAAD,EAASC,IAAT,EAAe;EACd,QAAID,MAAM,YAAYrC,cAAlB,KACCsC,IAAI,KAAK,MAAT,IAAmBA,IAAI,KAAK,OAD7B,CAAJ,EAC2C;EACvC,aAAO,IAAP;EACH;;EACD,WAAOA,IAAI,IAAID,MAAf;EACH;;EA9Be,CAApB;;EAgCA,SAASM,YAAT,CAAsBC,QAAtB,EAAgC;EAC5BT,EAAAA,aAAa,GAAGS,QAAQ,CAACT,aAAD,CAAxB;EACH;;EACD,SAASU,YAAT,CAAsBC,IAAtB,EAA4B;EACxB;EACA;EACA;EACA,MAAIA,IAAI,KAAKlD,WAAW,CAACM,SAAZ,CAAsB6C,WAA/B,IACA,EAAE,sBAAsB/C,cAAc,CAACE,SAAvC,CADJ,EACuD;EACnD,WAAO,UAAU8C,UAAV,EAAsB,GAAGC,IAAzB,EAA+B;EAClC,YAAMnB,EAAE,GAAGgB,IAAI,CAACI,IAAL,CAAUC,MAAM,CAAC,IAAD,CAAhB,EAAwBH,UAAxB,EAAoC,GAAGC,IAAvC,CAAX;EACAxC,MAAAA,wBAAwB,CAACkB,GAAzB,CAA6BG,EAA7B,EAAiCkB,UAAU,CAACI,IAAX,GAAkBJ,UAAU,CAACI,IAAX,EAAlB,GAAsC,CAACJ,UAAD,CAAvE;EACA,aAAO1B,IAAI,CAACQ,EAAD,CAAX;EACH,KAJD;EAKH,GAXuB;EAaxB;EACA;EACA;EACA;;;EACA,MAAI7B,uBAAuB,GAAGoD,QAA1B,CAAmCP,IAAnC,CAAJ,EAA8C;EAC1C,WAAO,UAAU,GAAGG,IAAb,EAAmB;EACtB;EACA;EACAH,MAAAA,IAAI,CAACQ,KAAL,CAAWH,MAAM,CAAC,IAAD,CAAjB,EAAyBF,IAAzB;EACA,aAAO3B,IAAI,CAAChB,gBAAgB,CAAC8B,GAAjB,CAAqB,IAArB,CAAD,CAAX;EACH,KALD;EAMH;;EACD,SAAO,UAAU,GAAGa,IAAb,EAAmB;EACtB;EACA;EACA,WAAO3B,IAAI,CAACwB,IAAI,CAACQ,KAAL,CAAWH,MAAM,CAAC,IAAD,CAAjB,EAAyBF,IAAzB,CAAD,CAAX;EACH,GAJD;EAKH;;EACD,SAASM,sBAAT,CAAgC7B,KAAhC,EAAuC;EACnC,MAAI,OAAOA,KAAP,KAAiB,UAArB,EACI,OAAOmB,YAAY,CAACnB,KAAD,CAAnB,CAF+B;EAInC;;EACA,MAAIA,KAAK,YAAY1B,cAArB,EACI6B,8BAA8B,CAACH,KAAD,CAA9B;EACJ,MAAItC,aAAa,CAACsC,KAAD,EAAQ/B,oBAAoB,EAA5B,CAAjB,EACI,OAAO,IAAI6D,KAAJ,CAAU9B,KAAV,EAAiBS,aAAjB,CAAP,CAR+B;;EAUnC,SAAOT,KAAP;EACH;;EACD,SAASJ,IAAT,CAAcI,KAAd,EAAqB;EACjB;EACA;EACA,MAAIA,KAAK,YAAY+B,UAArB,EACI,OAAO7C,gBAAgB,CAACc,KAAD,CAAvB,CAJa;EAMjB;;EACA,MAAIhB,cAAc,CAACqB,GAAf,CAAmBL,KAAnB,CAAJ,EACI,OAAOhB,cAAc,CAAC0B,GAAf,CAAmBV,KAAnB,CAAP;EACJ,QAAMgC,QAAQ,GAAGH,sBAAsB,CAAC7B,KAAD,CAAvC,CATiB;EAWjB;;EACA,MAAIgC,QAAQ,KAAKhC,KAAjB,EAAwB;EACpBhB,IAAAA,cAAc,CAACiB,GAAf,CAAmBD,KAAnB,EAA0BgC,QAA1B;EACA/C,IAAAA,qBAAqB,CAACgB,GAAtB,CAA0B+B,QAA1B,EAAoChC,KAApC;EACH;;EACD,SAAOgC,QAAP;EACH;;EACD,MAAMP,MAAM,GAAIzB,KAAD,IAAWf,qBAAqB,CAACyB,GAAtB,CAA0BV,KAA1B,CAA1B;;ECnLA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,SAASiC,MAAT,CAAgBC,IAAhB,EAAsBC,OAAtB,EAA+B;EAAEC,EAAAA,OAAF;EAAWC,EAAAA,OAAX;EAAoBC,EAAAA,QAApB;EAA8BC,EAAAA;EAA9B,IAA6C,EAA5E,EAAgF;EAC5E,QAAMpD,OAAO,GAAGqD,SAAS,CAACC,IAAV,CAAeP,IAAf,EAAqBC,OAArB,CAAhB;EACA,QAAMO,WAAW,GAAG9C,IAAI,CAACT,OAAD,CAAxB;;EACA,MAAIkD,OAAJ,EAAa;EACTlD,IAAAA,OAAO,CAACW,gBAAR,CAAyB,eAAzB,EAA2C6C,KAAD,IAAW;EACjDN,MAAAA,OAAO,CAACzC,IAAI,CAACT,OAAO,CAACU,MAAT,CAAL,EAAuB8C,KAAK,CAACC,UAA7B,EAAyCD,KAAK,CAACE,UAA/C,EAA2DjD,IAAI,CAACT,OAAO,CAACkC,WAAT,CAA/D,CAAP;EACH,KAFD;EAGH;;EACD,MAAIe,OAAJ,EACIjD,OAAO,CAACW,gBAAR,CAAyB,SAAzB,EAAoC,MAAMsC,OAAO,EAAjD;EACJM,EAAAA,WAAW,CACN3C,IADL,CACW+C,EAAD,IAAQ;EACd,QAAIP,UAAJ,EACIO,EAAE,CAAChD,gBAAH,CAAoB,OAApB,EAA6B,MAAMyC,UAAU,EAA7C;EACJ,QAAID,QAAJ,EACIQ,EAAE,CAAChD,gBAAH,CAAoB,eAApB,EAAqC,MAAMwC,QAAQ,EAAnD;EACP,GAND,EAOKpC,KAPL,CAOW,MAAM,EAPjB;EAQA,SAAOwC,WAAP;EACH;;EAaD,MAAMK,WAAW,GAAG,CAAC,KAAD,EAAQ,QAAR,EAAkB,QAAlB,EAA4B,YAA5B,EAA0C,OAA1C,CAApB;EACA,MAAMC,YAAY,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,QAAf,EAAyB,OAAzB,CAArB;EACA,MAAMC,aAAa,GAAG,IAAIC,GAAJ,EAAtB;;EACA,SAASC,SAAT,CAAmBxC,MAAnB,EAA2BC,IAA3B,EAAiC;EAC7B,MAAI,EAAED,MAAM,YAAYzC,WAAlB,IACF,EAAE0C,IAAI,IAAID,MAAV,CADE,IAEF,OAAOC,IAAP,KAAgB,QAFhB,CAAJ,EAE+B;EAC3B;EACH;;EACD,MAAIqC,aAAa,CAACvC,GAAd,CAAkBE,IAAlB,CAAJ,EACI,OAAOqC,aAAa,CAACvC,GAAd,CAAkBE,IAAlB,CAAP;EACJ,QAAMwC,cAAc,GAAGxC,IAAI,CAACyC,OAAL,CAAa,YAAb,EAA2B,EAA3B,CAAvB;EACA,QAAMC,QAAQ,GAAG1C,IAAI,KAAKwC,cAA1B;EACA,QAAMG,OAAO,GAAGP,YAAY,CAACrB,QAAb,CAAsByB,cAAtB,CAAhB;;EACA;EAEA,IAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGlF,QAAH,GAAcD,cAAvB,EAAuCK,SAA3D,KACI,EAAE+E,OAAO,IAAIR,WAAW,CAACpB,QAAZ,CAAqByB,cAArB,CAAb,CAHJ,EAGwD;EACpD;EACH;;EACD,QAAMI,MAAM,GAAG,gBAAgBC,SAAhB,EAA2B,GAAGlC,IAA9B,EAAoC;EAC/C;EACA,UAAMnB,EAAE,GAAG,KAAKiB,WAAL,CAAiBoC,SAAjB,EAA4BF,OAAO,GAAG,WAAH,GAAiB,UAApD,CAAX;EACA,QAAI5C,MAAM,GAAGP,EAAE,CAACsD,KAAhB;EACA,QAAIJ,QAAJ,EACI3C,MAAM,GAAGA,MAAM,CAACgD,KAAP,CAAapC,IAAI,CAACqC,KAAL,EAAb,CAAT,CAL2C;EAO/C;EACA;EACA;EACA;;EACA,WAAO,CAAC,MAAMvE,OAAO,CAACwE,GAAR,CAAY,CACtBlD,MAAM,CAACyC,cAAD,CAAN,CAAuB,GAAG7B,IAA1B,CADsB,EAEtBgC,OAAO,IAAInD,EAAE,CAACE,IAFQ,CAAZ,CAAP,EAGH,CAHG,CAAP;EAIH,GAfD;;EAgBA2C,EAAAA,aAAa,CAAChD,GAAd,CAAkBW,IAAlB,EAAwB4C,MAAxB;EACA,SAAOA,MAAP;EACH;;EACDvC,YAAY,CAAE6C,QAAD,iBACNA,QADM;EAETpD,EAAAA,GAAG,EAAE,CAACC,MAAD,EAASC,IAAT,EAAeC,QAAf,KAA4BsC,SAAS,CAACxC,MAAD,EAASC,IAAT,CAAT,IAA2BkD,QAAQ,CAACpD,GAAT,CAAaC,MAAb,EAAqBC,IAArB,EAA2BC,QAA3B,CAFnD;EAGTR,EAAAA,GAAG,EAAE,CAACM,MAAD,EAASC,IAAT,KAAkB,CAAC,CAACuC,SAAS,CAACxC,MAAD,EAASC,IAAT,CAAX,IAA6BkD,QAAQ,CAACzD,GAAT,CAAaM,MAAb,EAAqBC,IAArB;EAH3C,EAAD,CAAZ;;EC/EA,IAAI;EACAmD,EAAAA,IAAI,CAAC,+BAAD,CAAJ,IAAyCC,CAAC,EAA1C;EACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ECLV;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMC,UAAU,GAAG,CAAnB;EACA,MAAMC,OAAO,GAAG,yBAAhB;EACA,MAAMC,yBAAyB,GAAG,UAAlC;EACA,MAAMC,gBAAgB,GAAG,WAAzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,MAAMC,OAAN,CAAc;EACjBC,EAAAA,WAAW,GAAG;EACV,SAAKC,GAAL,GAAW,IAAX;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMC,QAAN,CAAeC,KAAf,EAAsB;EAClB,UAAM5B,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,UAAMvE,EAAE,GAAG0C,EAAE,CAACzB,WAAH,CAAe+C,yBAAf,EAA0C,WAA1C,EAAuD;EAC9DQ,MAAAA,UAAU,EAAE;EADkD,KAAvD,CAAX;EAGA,UAAMxE,EAAE,CAACsD,KAAH,CAASmB,GAAT,CAAaH,KAAb,CAAN;EACA,UAAMtE,EAAE,CAACE,IAAT;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMwE,eAAN,GAAwB;EACpB,UAAMhC,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,UAAMI,MAAM,GAAG,MAAMjC,EAAE,CAClBzB,WADgB,CACJ+C,yBADI,EAEhBV,KAFgB,CAEVsB,UAFU,EAArB;EAGA,WAAOD,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAC/E,KAAP,CAAaiF,EAApE;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMC,wBAAN,CAA+BC,SAA/B,EAA0C;EACtC,UAAMrC,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,UAAMS,OAAO,GAAG,MAAMtC,EAAE,CAACuC,eAAH,CAAmBjB,yBAAnB,EAA8CC,gBAA9C,EAAgEiB,WAAW,CAACC,IAAZ,CAAiBJ,SAAjB,CAAhE,CAAtB;EACA,WAAOC,OAAO,GAAGA,OAAH,GAAa,IAAII,KAAJ,EAA3B;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMC,wBAAN,CAA+BN,SAA/B,EAA0C;EACtC,UAAMrC,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,WAAO7B,EAAE,CAAC4C,cAAH,CAAkBtB,yBAAlB,EAA6CC,gBAA7C,EAA+DiB,WAAW,CAACC,IAAZ,CAAiBJ,SAAjB,CAA/D,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMQ,WAAN,CAAkBV,EAAlB,EAAsB;EAClB,UAAMnC,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,UAAM7B,EAAE,CAAC8C,MAAH,CAAUxB,yBAAV,EAAqCa,EAArC,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMY,wBAAN,CAA+BV,SAA/B,EAA0C;EACtC,WAAO,MAAM,KAAKW,oBAAL,CAA0BR,WAAW,CAACC,IAAZ,CAAiBJ,SAAjB,CAA1B,EAAuD,MAAvD,CAAb;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMY,uBAAN,CAA8BZ,SAA9B,EAAyC;EACrC,WAAO,MAAM,KAAKW,oBAAL,CAA0BR,WAAW,CAACC,IAAZ,CAAiBJ,SAAjB,CAA1B,EAAuD,MAAvD,CAAb;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMW,oBAAN,CAA2BE,KAA3B,EAAkCC,SAAlC,EAA6C;EACzC,UAAMnD,EAAE,GAAG,MAAM,KAAK6B,KAAL,EAAjB;EACA,UAAMI,MAAM,GAAG,MAAMjC,EAAE,CAClBzB,WADgB,CACJ+C,yBADI,EAEhBV,KAFgB,CAEVC,KAFU,CAEJU,gBAFI,EAGhBW,UAHgB,CAGLgB,KAHK,EAGEC,SAHF,CAArB;EAIA,WAAOlB,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAC/E,KAA9D;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAM2E,KAAN,GAAc;EACV,QAAI,CAAC,KAAKH,GAAV,EAAe;EACX,WAAKA,GAAL,GAAW,MAAMvC,MAAM,CAACkC,OAAD,EAAUD,UAAV,EAAsB;EACzC7B,QAAAA,OAAO,EAAE,KAAK6D;EAD2B,OAAtB,CAAvB;EAGH;;EACD,WAAO,KAAK1B,GAAZ;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACI0B,EAAAA,UAAU,CAACpD,EAAD,EAAKF,UAAL,EAAiB;EACvB,QAAIA,UAAU,GAAG,CAAb,IAAkBA,UAAU,GAAGsB,UAAnC,EAA+C;EAC3C,UAAIpB,EAAE,CAAChC,gBAAH,CAAoBqF,QAApB,CAA6B/B,yBAA7B,CAAJ,EAA6D;EACzDtB,QAAAA,EAAE,CAACsD,iBAAH,CAAqBhC,yBAArB;EACH;EACJ;;EACD,UAAMiC,QAAQ,GAAGvD,EAAE,CAACwD,iBAAH,CAAqBlC,yBAArB,EAAgD;EAC7DmC,MAAAA,aAAa,EAAE,IAD8C;EAE7DC,MAAAA,OAAO,EAAE;EAFoD,KAAhD,CAAjB;EAIAH,IAAAA,QAAQ,CAACI,WAAT,CAAqBpC,gBAArB,EAAuCA,gBAAvC,EAAyD;EAAEqC,MAAAA,MAAM,EAAE;EAAV,KAAzD;EACH;;EA3HgB;;ECpBrB;EACA;AACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,MAAMC,UAAN,CAAiB;EACpB;EACJ;EACA;EACA;EACA;EACA;EACIpC,EAAAA,WAAW,CAACY,SAAD,EAAY;EACnB,SAAKyB,UAAL,GAAkBzB,SAAlB;EACA,SAAK0B,QAAL,GAAgB,IAAIvC,OAAJ,EAAhB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMwC,SAAN,CAAgBpC,KAAhB,EAAuB;EACnB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAd,EAAqB,QAArB,EAA+B;EAC3BuC,QAAAA,UAAU,EAAE,yBADe;EAE3BC,QAAAA,SAAS,EAAE,YAFgB;EAG3BC,QAAAA,QAAQ,EAAE,WAHiB;EAI3BC,QAAAA,SAAS,EAAE;EAJgB,OAA/B;EAMAL,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAK,CAAC2C,WAApB,EAAiC,QAAjC,EAA2C;EACvCJ,QAAAA,UAAU,EAAE,yBAD2B;EAEvCC,QAAAA,SAAS,EAAE,YAF4B;EAGvCC,QAAAA,QAAQ,EAAE,WAH6B;EAIvCC,QAAAA,SAAS,EAAE;EAJ4B,OAA3C;EAMH,KAdkB;;;EAgBnB,WAAO1C,KAAK,CAACO,EAAb;EACAP,IAAAA,KAAK,CAACS,SAAN,GAAkB,KAAKyB,UAAvB;EACA,UAAM,KAAKC,QAAL,CAAcpC,QAAd,CAAuBC,KAAvB,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAM4C,YAAN,CAAmB5C,KAAnB,EAA0B;EACtB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAd,EAAqB,QAArB,EAA+B;EAC3BuC,QAAAA,UAAU,EAAE,yBADe;EAE3BC,QAAAA,SAAS,EAAE,YAFgB;EAG3BC,QAAAA,QAAQ,EAAE,cAHiB;EAI3BC,QAAAA,SAAS,EAAE;EAJgB,OAA/B;EAMAL,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAK,CAAC2C,WAApB,EAAiC,QAAjC,EAA2C;EACvCJ,QAAAA,UAAU,EAAE,yBAD2B;EAEvCC,QAAAA,SAAS,EAAE,YAF4B;EAGvCC,QAAAA,QAAQ,EAAE,cAH6B;EAIvCC,QAAAA,SAAS,EAAE;EAJ4B,OAA3C;EAMH;;EACD,UAAMG,OAAO,GAAG,MAAM,KAAKV,QAAL,CAAc/B,eAAd,EAAtB;;EACA,QAAIyC,OAAJ,EAAa;EACT;EACA7C,MAAAA,KAAK,CAACO,EAAN,GAAWsC,OAAO,GAAG,CAArB;EACH,KAHD,MAIK;EACD;EACA,aAAO7C,KAAK,CAACO,EAAb;EACH;;EACDP,IAAAA,KAAK,CAACS,SAAN,GAAkB,KAAKyB,UAAvB;EACA,UAAM,KAAKC,QAAL,CAAcpC,QAAd,CAAuBC,KAAvB,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAM8C,QAAN,GAAiB;EACb,WAAO,KAAKC,YAAL,CAAkB,MAAM,KAAKZ,QAAL,CAAcd,uBAAd,CAAsC,KAAKa,UAA3C,CAAxB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMc,UAAN,GAAmB;EACf,WAAO,KAAKD,YAAL,CAAkB,MAAM,KAAKZ,QAAL,CAAchB,wBAAd,CAAuC,KAAKe,UAA5C,CAAxB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMe,MAAN,GAAe;EACX,WAAO,MAAM,KAAKd,QAAL,CAAc3B,wBAAd,CAAuC,KAAK0B,UAA5C,CAAb;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMgB,IAAN,GAAa;EACT,WAAO,MAAM,KAAKf,QAAL,CAAcpB,wBAAd,CAAuC,KAAKmB,UAA5C,CAAb;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMjB,WAAN,CAAkBV,EAAlB,EAAsB;EAClB,UAAM,KAAK4B,QAAL,CAAclB,WAAd,CAA0BV,EAA1B,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMwC,YAAN,CAAmB/C,KAAnB,EAA0B;EACtB,QAAIA,KAAJ,EAAW;EACP,YAAM,KAAKiB,WAAL,CAAiBjB,KAAK,CAACO,EAAvB,CAAN;EACH;;EACD,WAAOP,KAAP;EACH;;EArImB;;ECjBxB;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMmD,sBAAsB,GAAG,CAC3B,QAD2B,EAE3B,UAF2B,EAG3B,gBAH2B,EAI3B,MAJ2B,EAK3B,aAL2B,EAM3B,OAN2B,EAO3B,UAP2B,EAQ3B,WAR2B,EAS3B,WAT2B,CAA/B;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAMC,eAAN,CAAsB;EAClB;EACJ;EACA;EACA;EACA;EACA;EACA;EACI,eAAaC,WAAb,CAAyB5I,OAAzB,EAAkC;EAC9B,UAAMkI,WAAW,GAAG;EAChBW,MAAAA,GAAG,EAAE7I,OAAO,CAAC6I,GADG;EAEhBC,MAAAA,OAAO,EAAE;EAFO,KAApB,CAD8B;;EAM9B,QAAI9I,OAAO,CAACqE,MAAR,KAAmB,KAAvB,EAA8B;EAC1B;EACA;EACA;EACA;EACA6D,MAAAA,WAAW,CAACa,IAAZ,GAAmB,MAAM/I,OAAO,CAACgJ,KAAR,GAAgBC,WAAhB,EAAzB;EACH,KAZ6B;;;EAc9B,SAAK,MAAM,CAACC,GAAD,EAAMrI,KAAN,CAAX,IAA2Bb,OAAO,CAAC8I,OAAR,CAAgBK,OAAhB,EAA3B,EAAsD;EAClDjB,MAAAA,WAAW,CAACY,OAAZ,CAAoBI,GAApB,IAA2BrI,KAA3B;EACH,KAhB6B;;;EAkB9B,SAAK,MAAMY,IAAX,IAAmBiH,sBAAnB,EAA2C;EACvC,UAAI1I,OAAO,CAACyB,IAAD,CAAP,KAAkBG,SAAtB,EAAiC;EAC7BsG,QAAAA,WAAW,CAACzG,IAAD,CAAX,GAAoBzB,OAAO,CAACyB,IAAD,CAA3B;EACH;EACJ;;EACD,WAAO,IAAIkH,eAAJ,CAAoBT,WAApB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI9C,EAAAA,WAAW,CAAC8C,WAAD,EAAc;EACrB,IAA2C;EACvCN,MAAAA,gBAAM,CAACC,MAAP,CAAcK,WAAd,EAA2B,QAA3B,EAAqC;EACjCJ,QAAAA,UAAU,EAAE,yBADqB;EAEjCC,QAAAA,SAAS,EAAE,iBAFsB;EAGjCC,QAAAA,QAAQ,EAAE,aAHuB;EAIjCC,QAAAA,SAAS,EAAE;EAJsB,OAArC;EAMAL,MAAAA,gBAAM,CAACC,MAAP,CAAcK,WAAW,CAACW,GAA1B,EAA+B,QAA/B,EAAyC;EACrCf,QAAAA,UAAU,EAAE,yBADyB;EAErCC,QAAAA,SAAS,EAAE,iBAF0B;EAGrCC,QAAAA,QAAQ,EAAE,aAH2B;EAIrCC,QAAAA,SAAS,EAAE;EAJ0B,OAAzC;EAMH,KAdoB;EAgBrB;;;EACA,QAAIC,WAAW,CAAC,MAAD,CAAX,KAAwB,UAA5B,EAAwC;EACpCA,MAAAA,WAAW,CAAC,MAAD,CAAX,GAAsB,aAAtB;EACH;;EACD,SAAKkB,YAAL,GAAoBlB,WAApB;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACImB,EAAAA,QAAQ,GAAG;EACP,UAAMnB,WAAW,GAAGoB,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,YAAvB,CAApB;EACAlB,IAAAA,WAAW,CAACY,OAAZ,GAAsBQ,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,YAAL,CAAkBN,OAApC,CAAtB;;EACA,QAAIZ,WAAW,CAACa,IAAhB,EAAsB;EAClBb,MAAAA,WAAW,CAACa,IAAZ,GAAmBb,WAAW,CAACa,IAAZ,CAAiBS,KAAjB,CAAuB,CAAvB,CAAnB;EACH;;EACD,WAAOtB,WAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACIuB,EAAAA,SAAS,GAAG;EACR,WAAO,IAAIC,OAAJ,CAAY,KAAKN,YAAL,CAAkBP,GAA9B,EAAmC,KAAKO,YAAxC,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;;EACIJ,EAAAA,KAAK,GAAG;EACJ,WAAO,IAAIL,eAAJ,CAAoB,KAAKU,QAAL,EAApB,CAAP;EACH;;EA3FiB;;EC3BtB;EACA;AACA;EACA;EACA;EACA;EACA;EAQA,MAAMM,UAAU,GAAG,yBAAnB;EACA,MAAMC,kBAAkB,GAAG,KAAK,EAAL,GAAU,CAArC;;EACA,MAAMC,UAAU,GAAG,IAAIC,GAAJ,EAAnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAMC,YAAY,GAAIC,eAAD,IAAqB;EACtC,QAAMC,UAAU,GAAG;EACfjK,IAAAA,OAAO,EAAE,IAAI2I,eAAJ,CAAoBqB,eAAe,CAAC9B,WAApC,EAAiDuB,SAAjD,EADM;EAEfS,IAAAA,SAAS,EAAEF,eAAe,CAACE;EAFZ,GAAnB;;EAIA,MAAIF,eAAe,CAACG,QAApB,EAA8B;EAC1BF,IAAAA,UAAU,CAACE,QAAX,GAAsBH,eAAe,CAACG,QAAtC;EACH;;EACD,SAAOF,UAAP;EACH,CATD;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMG,KAAN,CAAY;EACR;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACIhF,EAAAA,WAAW,CAACrC,IAAD,EAAO;EAAEsH,IAAAA,iBAAF;EAAqBC,IAAAA,MAArB;EAA6BC,IAAAA;EAA7B,MAAkD,EAAzD,EAA6D;EACpE,SAAKC,eAAL,GAAuB,KAAvB;EACA,SAAKC,wBAAL,GAAgC,KAAhC,CAFoE;;EAIpE,QAAIZ,UAAU,CAAC3I,GAAX,CAAe6B,IAAf,CAAJ,EAA0B;EACtB,YAAM,IAAI2H,4BAAJ,CAAiB,sBAAjB,EAAyC;EAAE3H,QAAAA;EAAF,OAAzC,CAAN;EACH,KAFD,MAGK;EACD8G,MAAAA,UAAU,CAACnE,GAAX,CAAe3C,IAAf;EACH;;EACD,SAAK4H,KAAL,GAAa5H,IAAb;EACA,SAAK6H,OAAL,GAAeN,MAAM,IAAI,KAAKO,cAA9B;EACA,SAAKC,iBAAL,GAAyBP,gBAAgB,IAAIX,kBAA7C;EACA,SAAKmB,kBAAL,GAA0BC,OAAO,CAACX,iBAAD,CAAjC;EACA,SAAKY,WAAL,GAAmB,IAAIzD,UAAJ,CAAe,KAAKmD,KAApB,CAAnB;;EACA,SAAKO,gBAAL;EACH;EACD;EACJ;EACA;;;EACI,MAAInI,IAAJ,GAAW;EACP,WAAO,KAAK4H,KAAZ;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMQ,WAAN,CAAkB5F,KAAlB,EAAyB;EACrB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAd,EAAqB,QAArB,EAA+B;EAC3BuC,QAAAA,UAAU,EAAE,yBADe;EAE3BC,QAAAA,SAAS,EAAE,OAFgB;EAG3BC,QAAAA,QAAQ,EAAE,aAHiB;EAI3BC,QAAAA,SAAS,EAAE;EAJgB,OAA/B;EAMAL,MAAAA,gBAAM,CAACwD,UAAP,CAAkB7F,KAAK,CAACvF,OAAxB,EAAiC0J,OAAjC,EAA0C;EACtC5B,QAAAA,UAAU,EAAE,yBAD0B;EAEtCC,QAAAA,SAAS,EAAE,OAF2B;EAGtCC,QAAAA,QAAQ,EAAE,aAH4B;EAItCC,QAAAA,SAAS,EAAE;EAJ2B,OAA1C;EAMH;;EACD,UAAM,KAAKoD,WAAL,CAAiB9F,KAAjB,EAAwB,MAAxB,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAM+F,cAAN,CAAqB/F,KAArB,EAA4B;EACxB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAP,CAActC,KAAd,EAAqB,QAArB,EAA+B;EAC3BuC,QAAAA,UAAU,EAAE,yBADe;EAE3BC,QAAAA,SAAS,EAAE,OAFgB;EAG3BC,QAAAA,QAAQ,EAAE,gBAHiB;EAI3BC,QAAAA,SAAS,EAAE;EAJgB,OAA/B;EAMAL,MAAAA,gBAAM,CAACwD,UAAP,CAAkB7F,KAAK,CAACvF,OAAxB,EAAiC0J,OAAjC,EAA0C;EACtC5B,QAAAA,UAAU,EAAE,yBAD0B;EAEtCC,QAAAA,SAAS,EAAE,OAF2B;EAGtCC,QAAAA,QAAQ,EAAE,gBAH4B;EAItCC,QAAAA,SAAS,EAAE;EAJ2B,OAA1C;EAMH;;EACD,UAAM,KAAKoD,WAAL,CAAiB9F,KAAjB,EAAwB,SAAxB,CAAN;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMgG,UAAN,GAAmB;EACf,WAAO,KAAKC,cAAL,CAAoB,KAApB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMC,YAAN,GAAqB;EACjB,WAAO,KAAKD,cAAL,CAAoB,OAApB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMhD,MAAN,GAAe;EACX,UAAMkD,UAAU,GAAG,MAAM,KAAKT,WAAL,CAAiBzC,MAAjB,EAAzB;EACA,UAAMmD,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;EACA,UAAME,gBAAgB,GAAG,EAAzB;;EACA,SAAK,MAAMtG,KAAX,IAAoBmG,UAApB,EAAgC;EAC5B;EACA;EACA,YAAMI,oBAAoB,GAAG,KAAKhB,iBAAL,GAAyB,EAAzB,GAA8B,IAA3D;;EACA,UAAIa,GAAG,GAAGpG,KAAK,CAAC2E,SAAZ,GAAwB4B,oBAA5B,EAAkD;EAC9C,cAAM,KAAKb,WAAL,CAAiBzE,WAAjB,CAA6BjB,KAAK,CAACO,EAAnC,CAAN;EACH,OAFD,MAGK;EACD+F,QAAAA,gBAAgB,CAACE,IAAjB,CAAsBhC,YAAY,CAACxE,KAAD,CAAlC;EACH;EACJ;;EACD,WAAOsG,gBAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;;EACI,QAAMpD,IAAN,GAAa;EACT,WAAO,MAAM,KAAKwC,WAAL,CAAiBxC,IAAjB,EAAb;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAM4C,WAAN,CAAkB;EAAErL,IAAAA,OAAF;EAAWmK,IAAAA,QAAX;EAAqBD,IAAAA,SAAS,GAAG0B,IAAI,CAACD,GAAL;EAAjC,GAAlB,EAAiEK,SAAjE,EAA4E;EACxE,UAAMC,eAAe,GAAG,MAAMtD,eAAe,CAACC,WAAhB,CAA4B5I,OAAO,CAACgJ,KAAR,EAA5B,CAA9B;EACA,UAAMzD,KAAK,GAAG;EACV2C,MAAAA,WAAW,EAAE+D,eAAe,CAAC5C,QAAhB,EADH;EAEVa,MAAAA;EAFU,KAAd,CAFwE;;EAOxE,QAAIC,QAAJ,EAAc;EACV5E,MAAAA,KAAK,CAAC4E,QAAN,GAAiBA,QAAjB;EACH;;EACD,YAAQ6B,SAAR;EACI,WAAK,MAAL;EACI,cAAM,KAAKf,WAAL,CAAiBtD,SAAjB,CAA2BpC,KAA3B,CAAN;EACA;;EACJ,WAAK,SAAL;EACI,cAAM,KAAK0F,WAAL,CAAiB9C,YAAjB,CAA8B5C,KAA9B,CAAN;EACA;EANR;;EAQA,IAA2C;EACvC2G,MAAAA,gBAAM,CAACC,GAAP,CAAY,gBAAeC,gCAAc,CAACpM,OAAO,CAAC6I,GAAT,CAAc,QAA5C,GACN,wCAAuC,KAAK8B,KAAM,IADvD;EAEH,KArBuE;EAuBxE;EACA;;;EACA,QAAI,KAAKH,eAAT,EAA0B;EACtB,WAAKC,wBAAL,GAAgC,IAAhC;EACH,KAFD,MAGK;EACD,YAAM,KAAK4B,YAAL,EAAN;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,QAAMb,cAAN,CAAqBQ,SAArB,EAAgC;EAC5B,UAAML,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;EACA,QAAIpG,KAAJ;;EACA,YAAQyG,SAAR;EACI,WAAK,KAAL;EACIzG,QAAAA,KAAK,GAAG,MAAM,KAAK0F,WAAL,CAAiB5C,QAAjB,EAAd;EACA;;EACJ,WAAK,OAAL;EACI9C,QAAAA,KAAK,GAAG,MAAM,KAAK0F,WAAL,CAAiB1C,UAAjB,EAAd;EACA;EANR;;EAQA,QAAIhD,KAAJ,EAAW;EACP;EACA;EACA,YAAMuG,oBAAoB,GAAG,KAAKhB,iBAAL,GAAyB,EAAzB,GAA8B,IAA3D;;EACA,UAAIa,GAAG,GAAGpG,KAAK,CAAC2E,SAAZ,GAAwB4B,oBAA5B,EAAkD;EAC9C,eAAO,KAAKN,cAAL,CAAoBQ,SAApB,CAAP;EACH;;EACD,aAAOjC,YAAY,CAACxE,KAAD,CAAnB;EACH,KARD,MASK;EACD,aAAO3D,SAAP;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;;EACI,QAAMiJ,cAAN,GAAuB;EACnB,QAAItF,KAAJ;;EACA,WAAQA,KAAK,GAAG,MAAM,KAAKkG,YAAL,EAAtB,EAA4C;EACxC,UAAI;EACA,cAAMa,KAAK,CAAC/G,KAAK,CAACvF,OAAN,CAAcgJ,KAAd,EAAD,CAAX;;EACA,YAAIuD,KAAA,KAAyB,YAA7B,EAA2C;EACvCL,UAAAA,gBAAM,CAACC,GAAP,CAAY,gBAAeC,gCAAc,CAAC7G,KAAK,CAACvF,OAAN,CAAc6I,GAAf,CAAoB,IAAlD,GACN,+BAA8B,KAAK8B,KAAM,GAD9C;EAEH;EACJ,OAND,CAOA,OAAOnK,KAAP,EAAc;EACV,cAAM,KAAK8K,cAAL,CAAoB/F,KAApB,CAAN;;EACA,QAA2C;EACvC2G,UAAAA,gBAAM,CAACC,GAAP,CAAY,gBAAeC,gCAAc,CAAC7G,KAAK,CAACvF,OAAN,CAAc6I,GAAf,CAAoB,IAAlD,GACN,+CAA8C,KAAK8B,KAAM,GAD9D;EAEH;;EACD,cAAM,IAAID,4BAAJ,CAAiB,qBAAjB,EAAwC;EAAE3H,UAAAA,IAAI,EAAE,KAAK4H;EAAb,SAAxC,CAAN;EACH;EACJ;;EACD,IAA2C;EACvCuB,MAAAA,gBAAM,CAACC,GAAP,CAAY,0BAAyB,KAAKpJ,IAAK,sBAApC,GACN,mCADL;EAEH;EACJ;EACD;EACJ;EACA;;;EACI,QAAMsJ,YAAN,GAAqB;EACjB;EACA,QAAI,UAAUzH,IAAI,CAAC4H,YAAf,IAA+B,CAAC,KAAKzB,kBAAzC,EAA6D;EACzD,UAAI;EACA,cAAMnG,IAAI,CAAC4H,YAAL,CAAkBC,IAAlB,CAAuBC,QAAvB,CAAiC,GAAE/C,UAAW,IAAG,KAAKgB,KAAM,EAA5D,CAAN;EACH,OAFD,CAGA,OAAOgC,GAAP,EAAY;EACR;EACA;EACA,QAA2C;EACvCT,UAAAA,gBAAM,CAACU,IAAP,CAAa,sCAAqC,KAAKjC,KAAM,IAA7D,EAAkEgC,GAAlE;EACH;EACJ;EACJ;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EACIzB,EAAAA,gBAAgB,GAAG;EACf;EACA,QAAI,UAAUtG,IAAI,CAAC4H,YAAf,IAA+B,CAAC,KAAKzB,kBAAzC,EAA6D;EACzDnG,MAAAA,IAAI,CAACjE,gBAAL,CAAsB,MAAtB,EAA+B6C,KAAD,IAAW;EACrC,YAAIA,KAAK,CAACqJ,GAAN,KAAe,GAAElD,UAAW,IAAG,KAAKgB,KAAM,EAA9C,EAAiD;EAC7C,UAA2C;EACvCuB,YAAAA,gBAAM,CAACC,GAAP,CAAY,4BAA2B3I,KAAK,CAACqJ,GAAI,IAAtC,GAA6C,mBAAxD;EACH;;EACD,gBAAMC,YAAY,GAAG,YAAY;EAC7B,iBAAKtC,eAAL,GAAuB,IAAvB;EACA,gBAAIuC,SAAJ;;EACA,gBAAI;EACA,oBAAM,KAAKnC,OAAL,CAAa;EAAEoC,gBAAAA,KAAK,EAAE;EAAT,eAAb,CAAN;EACH,aAFD,CAGA,OAAOxM,KAAP,EAAc;EACV,kBAAIA,KAAK,YAAYyM,KAArB,EAA4B;EACxBF,gBAAAA,SAAS,GAAGvM,KAAZ,CADwB;EAGxB;;EACA,sBAAMuM,SAAN;EACH;EACJ,aAVD,SAWQ;EACJ;EACA;EACA;EACA;EACA;EACA,kBAAI,KAAKtC,wBAAL,IACA,EAAEsC,SAAS,IAAI,CAACvJ,KAAK,CAAC0J,UAAtB,CADJ,EACuC;EACnC,sBAAM,KAAKb,YAAL,EAAN;EACH;;EACD,mBAAK7B,eAAL,GAAuB,KAAvB;EACA,mBAAKC,wBAAL,GAAgC,KAAhC;EACH;EACJ,WA3BD;;EA4BAjH,UAAAA,KAAK,CAAC2J,SAAN,CAAgBL,YAAY,EAA5B;EACH;EACJ,OAnCD;EAoCH,KArCD,MAsCK;EACD,MAA2C;EACvCZ,QAAAA,gBAAM,CAACC,GAAP,CAAY,yDAAZ;EACH,OAHA;EAKD;EACA;;;EACA,WAAK,KAAKvB,OAAL,CAAa;EAAEoC,QAAAA,KAAK,EAAE;EAAT,OAAb,CAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;EACI,aAAWI,WAAX,GAAyB;EACrB,WAAOvD,UAAP;EACH;;EAnWO;;EC3CZ;EACA;AACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAMwD,oBAAN,CAA2B;EACvB;EACJ;EACA;EACA;EACA;EACA;EACA;EACIjI,EAAAA,WAAW,CAACrC,IAAD,EAAOuK,OAAP,EAAgB;EACvB;EACR;EACA;EACA;EACA;EACQ,SAAKC,YAAL,GAAoB,OAAO;EAAEvN,MAAAA;EAAF,KAAP,KAAuB;EACvC,YAAM,KAAKwN,MAAL,CAAYrC,WAAZ,CAAwB;EAAEnL,QAAAA;EAAF,OAAxB,CAAN;EACH,KAFD;;EAGA,SAAKwN,MAAL,GAAc,IAAIpD,KAAJ,CAAUrH,IAAV,EAAgBuK,OAAhB,CAAd;EACH;;EAlBsB;;;;;;;;;;;;;"}