{"ast": null, "code": "import React,{useState,useEffect}from'react';import Layout from'./components/Layout/Layout';import EnhancedLogin from'./components/Login/EnhancedLogin';import EnhancedDashboard from'./components/Dashboard/EnhancedDashboard';import POS from'./components/POS/POS';import Reports from'./components/Reports/Reports';import Products from'./components/Products/Products';import Customers from'./components/Customers/Customers';import Suppliers from'./components/Suppliers/Suppliers';import Accounting from'./components/Accounting/Accounting';import Settings from'./components/Settings/Settings';import Backup from'./components/Backup/Backup';import Employees from'./components/Employees/Employees';import Notifications from'./components/Notifications/Notifications';import JournalEntries from'./components/JournalEntries/JournalEntries';import Returns from'./components/Returns/Returns';import BarcodeManager from'./components/Barcode/BarcodeManager';import Quotations from'./components/Quotations/Quotations';import Vouchers from'./components/Vouchers/Vouchers';import AdvancedReports from'./components/AdvancedReports/AdvancedReports';import'./App.css';import{jsx as _jsx}from\"react/jsx-runtime\";function App(){const[currentView,setCurrentView]=useState('login');const[user,setUser]=useState(null);useEffect(()=>{// التحقق من وجود جلسة مستخدم محفوظة\nconst savedUser=localStorage.getItem('currentUser');if(savedUser){setUser(JSON.parse(savedUser));setCurrentView('pos');// Start with POS as main screen\n}},[]);const handleLogin=userData=>{setUser(userData);localStorage.setItem('currentUser',JSON.stringify(userData));setCurrentView('pos');// Go directly to POS after login\n};const handleLogout=()=>{setUser(null);localStorage.removeItem('currentUser');setCurrentView('login');};const handleNavigate=view=>{setCurrentView(view);};const handleBackToDashboard=()=>{setCurrentView('dashboard');};// عرض صفحة تسجيل الدخول\nif(currentView==='login'){return/*#__PURE__*/_jsx(EnhancedLogin,{onLogin:handleLogin});}// إذا كان المستخدم مسجل دخول، عرض التطبيق مع Layout\nif(user){let currentComponent;switch(currentView){case'dashboard':currentComponent=/*#__PURE__*/_jsx(EnhancedDashboard,{user:user,onLogout:handleLogout,onNavigate:handleNavigate});break;case'pos':currentComponent=/*#__PURE__*/_jsx(POS,{user:user,onBack:handleBackToDashboard});break;case'reports':currentComponent=/*#__PURE__*/_jsx(Reports,{user:user,onBack:handleBackToDashboard});break;case'products':currentComponent=/*#__PURE__*/_jsx(Products,{user:user,onBack:handleBackToDashboard});break;case'customers':currentComponent=/*#__PURE__*/_jsx(Customers,{user:user,onBack:handleBackToDashboard});break;case'suppliers':currentComponent=/*#__PURE__*/_jsx(Suppliers,{user:user,onBack:handleBackToDashboard});break;case'accounting':currentComponent=/*#__PURE__*/_jsx(Accounting,{user:user,onBack:handleBackToDashboard});break;case'settings':currentComponent=/*#__PURE__*/_jsx(Settings,{user:user,onBack:handleBackToDashboard});break;case'employees':currentComponent=/*#__PURE__*/_jsx(Employees,{user:user,onBack:handleBackToDashboard});break;case'backup':currentComponent=/*#__PURE__*/_jsx(Backup,{user:user,onBack:handleBackToDashboard});break;case'notifications':currentComponent=/*#__PURE__*/_jsx(Notifications,{user:user,onBack:handleBackToDashboard});break;case'journal-entries':currentComponent=/*#__PURE__*/_jsx(JournalEntries,{user:user,onBack:handleBackToDashboard});break;case'returns':currentComponent=/*#__PURE__*/_jsx(Returns,{user:user,onBack:handleBackToDashboard});break;case'barcode':currentComponent=/*#__PURE__*/_jsx(BarcodeManager,{user:user,onBack:handleBackToDashboard});break;case'quotations':currentComponent=/*#__PURE__*/_jsx(Quotations,{user:user,onBack:handleBackToDashboard});break;case'vouchers':currentComponent=/*#__PURE__*/_jsx(Vouchers,{user:user,onBack:handleBackToDashboard});break;case'advanced-reports':currentComponent=/*#__PURE__*/_jsx(AdvancedReports,{user:user,onBack:handleBackToDashboard});break;case'invoices':currentComponent=/*#__PURE__*/_jsx(Invoices,{user:user,onBack:handleBackToDashboard});break;default:currentComponent=/*#__PURE__*/_jsx(EnhancedDashboard,{user:user,onLogout:handleLogout,onNavigate:handleNavigate});}return/*#__PURE__*/_jsx(Layout,{currentView:currentView,onNavigate:handleNavigate,user:user,onLogout:handleLogout,children:currentComponent});}// إذا لم يكن هناك مستخدم مسجل دخول، عرض صفحة تسجيل الدخول\nreturn/*#__PURE__*/_jsx(EnhancedLogin,{onLogin:handleLogin});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "EnhancedDashboard", "POS", "Reports", "Products", "Customers", "Suppliers", "Accounting", "Settings", "Backup", "Employees", "Notifications", "JournalEntries", "Returns", "BarcodeManager", "Quotations", "Vouchers", "AdvancedReports", "jsx", "_jsx", "App", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "user", "setUser", "savedUser", "localStorage", "getItem", "JSON", "parse", "handleLogin", "userData", "setItem", "stringify", "handleLogout", "removeItem", "handleNavigate", "view", "handleBackToDashboard", "onLogin", "currentComponent", "onLogout", "onNavigate", "onBack", "Invoices", "children"], "sources": ["D:/aronim/saudi-accounting-final/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Layout from './components/Layout/Layout';\n\nimport EnhancedLogin from './components/Login/EnhancedLogin';\n\nimport EnhancedDashboard from './components/Dashboard/EnhancedDashboard';\nimport POS from './components/POS/POS';\nimport Reports from './components/Reports/Reports';\nimport Products from './components/Products/Products';\nimport Customers from './components/Customers/Customers';\nimport Suppliers from './components/Suppliers/Suppliers';\nimport Accounting from './components/Accounting/Accounting';\nimport Settings from './components/Settings/Settings';\nimport Backup from './components/Backup/Backup';\nimport Employees from './components/Employees/Employees';\nimport Notifications from './components/Notifications/Notifications';\nimport JournalEntries from './components/JournalEntries/JournalEntries';\nimport Returns from './components/Returns/Returns';\nimport BarcodeManager from './components/Barcode/BarcodeManager';\nimport Quotations from './components/Quotations/Quotations';\nimport Vouchers from './components/Vouchers/Vouchers';\nimport AdvancedReports from './components/AdvancedReports/AdvancedReports';\nimport './App.css';\n\nfunction App() {\n  const [currentView, setCurrentView] = useState('login');\n  const [user, setUser] = useState(null);\n\n  useEffect(() => {\n    // التحقق من وجود جلسة مستخدم محفوظة\n    const savedUser = localStorage.getItem('currentUser');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n      setCurrentView('pos'); // Start with POS as main screen\n    }\n  }, []);\n\n  const handleLogin = (userData) => {\n    setUser(userData);\n    localStorage.setItem('currentUser', JSON.stringify(userData));\n    setCurrentView('pos'); // Go directly to POS after login\n  };\n\n  const handleLogout = () => {\n    setUser(null);\n    localStorage.removeItem('currentUser');\n    setCurrentView('login');\n  };\n\n  const handleNavigate = (view) => {\n    setCurrentView(view);\n  };\n\n  const handleBackToDashboard = () => {\n    setCurrentView('dashboard');\n  };\n\n  // عرض صفحة تسجيل الدخول\n  if (currentView === 'login') {\n    return <EnhancedLogin onLogin={handleLogin} />;\n  }\n\n  // إذا كان المستخدم مسجل دخول، عرض التطبيق مع Layout\n  if (user) {\n    let currentComponent;\n\n    switch (currentView) {\n      case 'dashboard':\n        currentComponent = <EnhancedDashboard user={user} onLogout={handleLogout} onNavigate={handleNavigate} />;\n        break;\n      case 'pos':\n        currentComponent = <POS user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'reports':\n        currentComponent = <Reports user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'products':\n        currentComponent = <Products user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'customers':\n        currentComponent = <Customers user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'suppliers':\n        currentComponent = <Suppliers user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'accounting':\n        currentComponent = <Accounting user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'settings':\n        currentComponent = <Settings user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'employees':\n        currentComponent = <Employees user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'backup':\n        currentComponent = <Backup user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'notifications':\n        currentComponent = <Notifications user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'journal-entries':\n        currentComponent = <JournalEntries user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'returns':\n        currentComponent = <Returns user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'barcode':\n        currentComponent = <BarcodeManager user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'quotations':\n        currentComponent = <Quotations user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'vouchers':\n        currentComponent = <Vouchers user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'advanced-reports':\n        currentComponent = <AdvancedReports user={user} onBack={handleBackToDashboard} />;\n        break;\n      case 'invoices':\n        currentComponent = <Invoices user={user} onBack={handleBackToDashboard} />;\n        break;\n      default:\n        currentComponent = <EnhancedDashboard user={user} onLogout={handleLogout} onNavigate={handleNavigate} />;\n    }\n\n    return (\n      <Layout\n        currentView={currentView}\n        onNavigate={handleNavigate}\n        user={user}\n        onLogout={handleLogout}\n      >\n        {currentComponent}\n      </Layout>\n    );\n  }\n\n  // إذا لم يكن هناك مستخدم مسجل دخول، عرض صفحة تسجيل الدخول\n  return <EnhancedLogin onLogin={handleLogin} />;\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAE/C,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAE5D,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,MAAO,CAAAC,GAAG,KAAM,sBAAsB,CACtC,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,UAAU,KAAM,oCAAoC,CAC3D,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,MAAO,CAAAC,aAAa,KAAM,0CAA0C,CACpE,MAAO,CAAAC,cAAc,KAAM,4CAA4C,CACvE,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,cAAc,KAAM,qCAAqC,CAChE,MAAO,CAAAC,UAAU,KAAM,oCAAoC,CAC3D,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,eAAe,KAAM,8CAA8C,CAC1E,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGzB,QAAQ,CAAC,OAAO,CAAC,CACvD,KAAM,CAAC0B,IAAI,CAAEC,OAAO,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAEtCC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2B,SAAS,CAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CACrD,GAAIF,SAAS,CAAE,CACbD,OAAO,CAACI,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC,CAC9BH,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAQ,WAAW,CAAIC,QAAQ,EAAK,CAChCP,OAAO,CAACO,QAAQ,CAAC,CACjBL,YAAY,CAACM,OAAO,CAAC,aAAa,CAAEJ,IAAI,CAACK,SAAS,CAACF,QAAQ,CAAC,CAAC,CAC7DT,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGA,CAAA,GAAM,CACzBV,OAAO,CAAC,IAAI,CAAC,CACbE,YAAY,CAACS,UAAU,CAAC,aAAa,CAAC,CACtCb,cAAc,CAAC,OAAO,CAAC,CACzB,CAAC,CAED,KAAM,CAAAc,cAAc,CAAIC,IAAI,EAAK,CAC/Bf,cAAc,CAACe,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClChB,cAAc,CAAC,WAAW,CAAC,CAC7B,CAAC,CAED;AACA,GAAID,WAAW,GAAK,OAAO,CAAE,CAC3B,mBAAOF,IAAA,CAACnB,aAAa,EAACuC,OAAO,CAAET,WAAY,CAAE,CAAC,CAChD,CAEA;AACA,GAAIP,IAAI,CAAE,CACR,GAAI,CAAAiB,gBAAgB,CAEpB,OAAQnB,WAAW,EACjB,IAAK,WAAW,CACdmB,gBAAgB,cAAGrB,IAAA,CAAClB,iBAAiB,EAACsB,IAAI,CAAEA,IAAK,CAACkB,QAAQ,CAAEP,YAAa,CAACQ,UAAU,CAAEN,cAAe,CAAE,CAAC,CACxG,MACF,IAAK,KAAK,CACRI,gBAAgB,cAAGrB,IAAA,CAACjB,GAAG,EAACqB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CACrE,MACF,IAAK,SAAS,CACZE,gBAAgB,cAAGrB,IAAA,CAAChB,OAAO,EAACoB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CACzE,MACF,IAAK,UAAU,CACbE,gBAAgB,cAAGrB,IAAA,CAACf,QAAQ,EAACmB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC1E,MACF,IAAK,WAAW,CACdE,gBAAgB,cAAGrB,IAAA,CAACd,SAAS,EAACkB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC3E,MACF,IAAK,WAAW,CACdE,gBAAgB,cAAGrB,IAAA,CAACb,SAAS,EAACiB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC3E,MACF,IAAK,YAAY,CACfE,gBAAgB,cAAGrB,IAAA,CAACZ,UAAU,EAACgB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC5E,MACF,IAAK,UAAU,CACbE,gBAAgB,cAAGrB,IAAA,CAACX,QAAQ,EAACe,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC1E,MACF,IAAK,WAAW,CACdE,gBAAgB,cAAGrB,IAAA,CAACT,SAAS,EAACa,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC3E,MACF,IAAK,QAAQ,CACXE,gBAAgB,cAAGrB,IAAA,CAACV,MAAM,EAACc,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CACxE,MACF,IAAK,eAAe,CAClBE,gBAAgB,cAAGrB,IAAA,CAACR,aAAa,EAACY,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC/E,MACF,IAAK,iBAAiB,CACpBE,gBAAgB,cAAGrB,IAAA,CAACP,cAAc,EAACW,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAChF,MACF,IAAK,SAAS,CACZE,gBAAgB,cAAGrB,IAAA,CAACN,OAAO,EAACU,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CACzE,MACF,IAAK,SAAS,CACZE,gBAAgB,cAAGrB,IAAA,CAACL,cAAc,EAACS,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAChF,MACF,IAAK,YAAY,CACfE,gBAAgB,cAAGrB,IAAA,CAACJ,UAAU,EAACQ,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC5E,MACF,IAAK,UAAU,CACbE,gBAAgB,cAAGrB,IAAA,CAACH,QAAQ,EAACO,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC1E,MACF,IAAK,kBAAkB,CACrBE,gBAAgB,cAAGrB,IAAA,CAACF,eAAe,EAACM,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CACjF,MACF,IAAK,UAAU,CACbE,gBAAgB,cAAGrB,IAAA,CAACyB,QAAQ,EAACrB,IAAI,CAAEA,IAAK,CAACoB,MAAM,CAAEL,qBAAsB,CAAE,CAAC,CAC1E,MACF,QACEE,gBAAgB,cAAGrB,IAAA,CAAClB,iBAAiB,EAACsB,IAAI,CAAEA,IAAK,CAACkB,QAAQ,CAAEP,YAAa,CAACQ,UAAU,CAAEN,cAAe,CAAE,CAAC,CAC5G,CAEA,mBACEjB,IAAA,CAACpB,MAAM,EACLsB,WAAW,CAAEA,WAAY,CACzBqB,UAAU,CAAEN,cAAe,CAC3Bb,IAAI,CAAEA,IAAK,CACXkB,QAAQ,CAAEP,YAAa,CAAAW,QAAA,CAEtBL,gBAAgB,CACX,CAAC,CAEb,CAEA;AACA,mBAAOrB,IAAA,CAACnB,aAAa,EAACuC,OAAO,CAAET,WAAY,CAAE,CAAC,CAChD,CAEA,cAAe,CAAAV,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}