/* Layout Styles - <PERSON><PERSON><PERSON> Design */

.app-layout {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background-color: var(--gray-50);
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  right: 0;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
}

.sidebar-logo {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--white);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all 0.3s ease;
}

.sidebar-logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: var(--font-size-2xl);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg) 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
  gap: var(--spacing-md);
  font-weight: 500;
  font-size: var(--font-size-sm);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transition: width 0.3s ease;
  z-index: -1;
}

.nav-link:hover::before {
  width: 100%;
}

.nav-link:hover {
  color: var(--white);
  border-right-color: var(--white);
  transform: translateX(-2px);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-right-color: var(--white);
  font-weight: 600;
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.1);
}

.nav-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .nav-link {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border-right: none;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-footer .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: none;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  margin-right: 280px;
  background-color: var(--gray-50);
  min-height: 100vh;
  transition: margin-right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.main-content.expanded {
  margin-right: 80px;
}

/* Header Styles */
.content-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
  transform: scale(1.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.datetime-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.date {
  font-weight: 600;
  color: var(--gray-800);
}

.time {
  font-weight: 500;
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
  border-radius: var(--radius-lg);
  font-weight: 500;
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.user-info:hover {
  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-100) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-icon {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.user-name {
  font-weight: 600;
  white-space: nowrap;
}

.notifications-btn {
  position: relative;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-600);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.notifications-btn:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
  transform: scale(1.1);
}

.notification-badge {
  position: absolute;
  top: 2px;
  left: 2px;
  background: var(--danger-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Content Body */
.content-body {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-right: 0;
  }
  
  .main-content.expanded {
    margin-right: 0;
  }
  
  .header-actions {
    gap: var(--spacing-md);
  }
  
  .datetime-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-top {
    padding: var(--spacing-md);
  }
  
  .content-body {
    padding: var(--spacing-md);
  }
  
  .header-title {
    font-size: var(--font-size-lg);
  }
  
  .user-name {
    display: none;
  }
  
  .sidebar {
    width: 100%;
  }
  
  .sidebar.collapsed {
    width: 100%;
  }
}

/* Animation for smooth transitions */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.content-body > * {
  animation: fadeIn 0.3s ease-out;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .content-header {
    background: var(--gray-800);
    border-bottom-color: var(--gray-700);
  }
  
  .header-title {
    color: var(--white);
  }
  
  .sidebar-toggle {
    color: var(--gray-300);
  }
  
  .sidebar-toggle:hover {
    background-color: var(--gray-700);
    color: var(--primary-light);
  }
}
