/* تنسيقات عامة للتطبيق - <PERSON><PERSON><PERSON> ERP Style */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Aronim ERP Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--gray-50);
  color: var(--gray-900);
  direction: rtl;
  text-align: right;
  font-size: var(--font-size-base);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  min-height: 100vh;
  display: flex;
  background-color: var(--gray-50);
}

/* Layout Styles */
.app-layout {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  right: 0;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-xl);
  transition: transform 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-logo {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--white);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.sidebar-logo .logo-icon {
  font-size: var(--font-size-3xl);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg) 0;
  overflow-y: auto;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
  gap: var(--spacing-md);
  font-weight: 500;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-right-color: var(--white);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-right-color: var(--white);
  font-weight: 600;
}

.nav-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

.main-content {
  flex: 1;
  margin-right: 280px;
  background-color: var(--gray-50);
  min-height: 100vh;
  transition: margin-right 0.3s ease;
}

.main-content.expanded {
  margin-right: 80px;
}

/* Content Styles */
.content-header {
  background: var(--white);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.header-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--gray-100);
  border-radius: var(--radius-lg);
  font-weight: 500;
  color: var(--gray-700);
}

.content-body {
  padding: var(--spacing-xl);
}

/* Card Styles */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.card-body {
  padding: var(--spacing-xl);
}

.card-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--gray-500);
  color: var(--white);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-600);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--white);
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--white);
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--white);
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

/* قسم المميزات */
.features-section {
  background: #f0f2f5;
  padding: 20px;
  border-radius: 8px;
  text-align: right;
  margin-bottom: 30px;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.features-title {
  margin-bottom: 15px;
  color: #262626;
  font-size: 1.2rem;
  font-weight: 600;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  margin-bottom: 8px;
  font-size: 16px;
  color: #555;
  padding: 5px 0;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.features-list li:last-child {
  border-bottom: none;
}

/* قسم المعلومات الإضافية */
.more-info {
  margin-top: 20px;
  padding: 20px;
  background: #f6ffed;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
  animation: slideDown 0.5s ease-out;
  text-align: right;
}

.more-info h4 {
  color: #52c41a;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.more-info p {
  margin-bottom: 10px;
  color: #666;
  line-height: 1.5;
}

.more-info strong {
  color: #262626;
}

/* قسم حالة التطوير */
.status-section {
  margin-top: 30px;
  padding: 20px;
  background: #e6f7ff;
  border-radius: 8px;
  border: 1px solid #91d5ff;
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.status-title {
  color: #1890ff;
  margin-bottom: 10px;
  font-size: 1.1rem;
  font-weight: 600;
}

.status-text {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* الحاوية الرئيسية للبطاقة */
.main-card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  background: white;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  animation: fadeIn 0.8s ease-out;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); max-height: 0; }
  to { opacity: 1; transform: translateY(0); max-height: 500px; }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .app {
    padding: 20px;
  }

  .main-card {
    padding: 20px;
    margin: 0 10px;
  }

  .main-title {
    font-size: 2rem;
  }

  .buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 15px;
  }

  .main-card {
    padding: 15px;
  }

  .main-title {
    font-size: 1.8rem;
  }

  .main-description {
    font-size: 16px;
  }
}
