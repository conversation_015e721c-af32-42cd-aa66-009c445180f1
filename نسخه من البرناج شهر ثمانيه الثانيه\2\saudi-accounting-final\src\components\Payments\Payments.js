import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Payments.css';

const Payments = () => {
  const [payments, setPayments] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPayment, setEditingPayment] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMethod, setFilterMethod] = useState('all');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [newPayment, setNewPayment] = useState({
    invoiceId: '',
    customerId: '',
    amount: '',
    paymentMethod: 'cash',
    paymentDate: new Date().toISOString().split('T')[0],
    reference: '',
    notes: '',
    status: 'completed'
  });

  const paymentMethods = [
    { value: 'cash', label: '💵 نقداً', icon: '💵' },
    { value: 'card', label: '💳 بطاقة ائتمان', icon: '💳' },
    { value: 'bank_transfer', label: '🏦 تحويل بنكي', icon: '🏦' },
    { value: 'check', label: '📝 شيك', icon: '📝' },
    { value: 'digital_wallet', label: '📱 محفظة رقمية', icon: '📱' },
    { value: 'installment', label: '📅 تقسيط', icon: '📅' }
  ];

  const paymentStatuses = [
    { value: 'pending', label: '⏳ معلق', color: '#f59e0b' },
    { value: 'completed', label: '✅ مكتمل', color: '#10b981' },
    { value: 'failed', label: '❌ فاشل', color: '#ef4444' },
    { value: 'refunded', label: '↩️ مسترد', color: '#6366f1' },
    { value: 'partial', label: '📊 جزئي', color: '#8b5cf6' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const paymentsData = database.getPayments();
      const invoicesData = database.getInvoices();
      const customersData = database.getCustomers();
      
      setPayments(paymentsData);
      setInvoices(invoicesData);
      setCustomers(customersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewPayment(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-fill customer when invoice is selected
    if (name === 'invoiceId' && value) {
      const selectedInvoice = invoices.find(inv => inv.id === parseInt(value));
      if (selectedInvoice) {
        setNewPayment(prev => ({
          ...prev,
          customerId: selectedInvoice.customerId,
          amount: selectedInvoice.total
        }));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const paymentData = {
        ...newPayment,
        amount: parseFloat(newPayment.amount),
        invoiceId: parseInt(newPayment.invoiceId),
        customerId: parseInt(newPayment.customerId)
      };

      if (editingPayment) {
        await database.updatePayment(editingPayment.id, paymentData);
        setMessage('تم تحديث الدفعة بنجاح!');
        setEditingPayment(null);
      } else {
        await database.addPayment(paymentData);
        setMessage('تم إضافة الدفعة بنجاح!');
      }

      resetForm();
      loadData();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الدفعة:', error);
      setMessage('خطأ في حفظ الدفعة');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewPayment({
      invoiceId: '',
      customerId: '',
      amount: '',
      paymentMethod: 'cash',
      paymentDate: new Date().toISOString().split('T')[0],
      reference: '',
      notes: '',
      status: 'completed'
    });
    setShowAddForm(false);
    setEditingPayment(null);
  };

  const handleEdit = (payment) => {
    setNewPayment({
      ...payment,
      paymentDate: payment.paymentDate.split('T')[0]
    });
    setEditingPayment(payment);
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
      try {
        await database.deletePayment(id);
        setMessage('تم حذف الدفعة بنجاح!');
        loadData();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        console.error('خطأ في حذف الدفعة:', error);
        setMessage('خطأ في حذف الدفعة');
      }
    }
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
  };

  const getInvoiceNumber = (invoiceId) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    return invoice ? invoice.invoiceNumber : 'غير محدد';
  };

  const getPaymentMethodLabel = (method) => {
    const methodObj = paymentMethods.find(m => m.value === method);
    return methodObj ? methodObj.label : method;
  };

  const getStatusInfo = (status) => {
    const statusObj = paymentStatuses.find(s => s.value === status);
    return statusObj || { label: status, color: '#64748b' };
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      getCustomerName(payment.customerId).toLowerCase().includes(searchTerm.toLowerCase()) ||
      getInvoiceNumber(payment.invoiceId).toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.reference.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;
    const matchesMethod = filterMethod === 'all' || payment.paymentMethod === filterMethod;
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const totalPayments = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0);
  const completedPayments = filteredPayments.filter(p => p.status === 'completed');
  const pendingPayments = filteredPayments.filter(p => p.status === 'pending');

  return (
    <div className="payments-container">
      <div className="payments-header">
        <h1>💳 إدارة المدفوعات</h1>
        <p>تتبع وإدارة جميع المدفوعات والتحصيلات</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>إجمالي المدفوعات</h3>
            <p>{totalPayments.toLocaleString()} ريال</p>
          </div>
        </div>
        <div className="stat-card completed">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <h3>المدفوعات المكتملة</h3>
            <p>{completedPayments.length} دفعة</p>
          </div>
        </div>
        <div className="stat-card pending">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <h3>المدفوعات المعلقة</h3>
            <p>{pendingPayments.length} دفعة</p>
          </div>
        </div>
        <div className="stat-card methods">
          <div className="stat-icon">💳</div>
          <div className="stat-info">
            <h3>طرق الدفع</h3>
            <p>{paymentMethods.length} طريقة</p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="payments-controls">
        <div className="search-filters">
          <input
            type="text"
            placeholder="🔍 البحث في المدفوعات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع الحالات</option>
            {paymentStatuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
          <select
            value={filterMethod}
            onChange={(e) => setFilterMethod(e.target.value)}
            className="filter-select"
          >
            <option value="all">جميع طرق الدفع</option>
            {paymentMethods.map(method => (
              <option key={method.value} value={method.value}>
                {method.label}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="add-payment-btn"
        >
          ➕ إضافة دفعة جديدة
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="modal-overlay">
          <div className="payment-form-modal">
            <div className="modal-header">
              <h3>{editingPayment ? '✏️ تعديل الدفعة' : '➕ إضافة دفعة جديدة'}</h3>
              <button onClick={resetForm} className="close-btn">✕</button>
            </div>
            
            <form onSubmit={handleSubmit} className="payment-form">
              <div className="form-grid">
                <div className="form-group">
                  <label>الفاتورة</label>
                  <select
                    name="invoiceId"
                    value={newPayment.invoiceId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر الفاتورة</option>
                    {invoices.map(invoice => (
                      <option key={invoice.id} value={invoice.id}>
                        {invoice.invoiceNumber} - {getCustomerName(invoice.customerId)} - {invoice.total} ريال
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>العميل</label>
                  <select
                    name="customerId"
                    value={newPayment.customerId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>المبلغ (ريال)</label>
                  <input
                    type="number"
                    name="amount"
                    value={newPayment.amount}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع</label>
                  <select
                    name="paymentMethod"
                    value={newPayment.paymentMethod}
                    onChange={handleInputChange}
                    required
                  >
                    {paymentMethods.map(method => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>تاريخ الدفع</label>
                  <input
                    type="date"
                    name="paymentDate"
                    value={newPayment.paymentDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label>الحالة</label>
                  <select
                    name="status"
                    value={newPayment.status}
                    onChange={handleInputChange}
                    required
                  >
                    {paymentStatuses.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم المرجع</label>
                  <input
                    type="text"
                    name="reference"
                    value={newPayment.reference}
                    onChange={handleInputChange}
                    placeholder="رقم الشيك، رقم التحويل، إلخ"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    name="notes"
                    value={newPayment.notes}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>

              <div className="form-actions">
                <button type="button" onClick={resetForm} className="cancel-btn">
                  إلغاء
                </button>
                <button type="submit" disabled={loading} className="save-btn">
                  {loading ? '⏳ جاري الحفظ...' : (editingPayment ? '💾 تحديث' : '💾 حفظ')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Payments Table */}
      <div className="payments-table-container">
        <table className="payments-table">
          <thead>
            <tr>
              <th>رقم الفاتورة</th>
              <th>العميل</th>
              <th>المبلغ</th>
              <th>طريقة الدفع</th>
              <th>التاريخ</th>
              <th>الحالة</th>
              <th>المرجع</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredPayments.length === 0 ? (
              <tr>
                <td colSpan="8" className="no-data">
                  لا توجد مدفوعات مطابقة للبحث
                </td>
              </tr>
            ) : (
              filteredPayments.map(payment => {
                const statusInfo = getStatusInfo(payment.status);
                return (
                  <tr key={payment.id}>
                    <td>{getInvoiceNumber(payment.invoiceId)}</td>
                    <td>{getCustomerName(payment.customerId)}</td>
                    <td className="amount">{payment.amount.toLocaleString()} ريال</td>
                    <td>{getPaymentMethodLabel(payment.paymentMethod)}</td>
                    <td>{new Date(payment.paymentDate).toLocaleDateString('ar-SA')}</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: statusInfo.color }}
                      >
                        {statusInfo.label}
                      </span>
                    </td>
                    <td>{payment.reference || '-'}</td>
                    <td className="actions">
                      <button
                        onClick={() => handleEdit(payment)}
                        className="edit-btn"
                        title="تعديل"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDelete(payment.id)}
                        className="delete-btn"
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Payments;
