.notifications-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.notifications-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  position: relative;
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.notifications-header h1 {
  margin: 0;
  font-size: 2.8rem;
  font-weight: 800;
}

.notification-badge {
  position: relative;
}

.unread-count {
  background: #ef4444;
  color: white;
  border-radius: 50%;
  padding: 4px 8px;
  font-size: 0.8rem;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
  animation: pulse 2s infinite;
}

.notifications-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.notifications-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
  flex-wrap: wrap;
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.filter-controls {
  flex: 1;
}

.filter-select {
  padding: 10px 15px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  min-width: 200px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.action-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.mark-all-btn,
.clear-all-btn,
.settings-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  white-space: nowrap;
}

.mark-all-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.mark-all-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.clear-all-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.clear-all-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.settings-btn {
  background: #f3f4f6;
  color: #374151;
}

.settings-btn:hover {
  background: #e5e7eb;
}

.notification-settings {
  background: white;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  animation: slideDown 0.3s ease-out;
}

.notification-settings h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 1.3rem;
  font-weight: 700;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.setting-item:hover {
  background: #f8fafc;
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.no-notifications {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.no-notifications-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-notifications h3 {
  margin: 0 0 10px 0;
  color: #374151;
  font-size: 1.5rem;
}

.no-notifications p {
  margin: 0;
  color: #9ca3af;
}

.notification-item {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border-left: 4px solid transparent;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.notification-item.unread {
  border-left-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.notification-content {
  width: 100%;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.notification-type {
  font-weight: 600;
  font-size: 0.9rem;
}

.notification-time {
  font-size: 0.8rem;
  color: #9ca3af;
}

.notification-title {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 700;
}

.notification-message {
  margin: 0 0 15px 0;
  color: #4b5563;
  line-height: 1.6;
}

.notification-data {
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #e5e7eb;
}

.notification-data strong {
  color: #374151;
  display: block;
  margin-bottom: 8px;
}

.notification-data ul {
  margin: 0;
  padding-right: 20px;
  color: #6b7280;
}

.notification-data li {
  margin-bottom: 4px;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.priority-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.delete-btn {
  padding: 6px 8px;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.delete-btn:hover {
  background: #fecaca;
  transform: scale(1.1);
}

.unread-indicator {
  position: absolute;
  top: 15px;
  left: 15px;
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .notifications-container {
    padding: 15px;
  }
  
  .notifications-header h1 {
    font-size: 2.2rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 10px;
  }
  
  .notifications-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-controls {
    justify-content: center;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .notification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .notification-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
