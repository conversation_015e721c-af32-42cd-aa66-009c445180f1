import React, { useState, useEffect } from 'react';
import './Dashboard.css';
import database from '../../utils/database';

const Dashboard = ({ user, onLogout, onNavigate }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // تحديث الوقت كل ثانية
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // إحصائيات من قاعدة البيانات
  const [stats, setStats] = useState({
    totalSales: 0,
    todaySales: 0,
    totalCustomers: 0,
    pendingInvoices: 0
  });

  // تحميل الإحصائيات
  useEffect(() => {
    try {
      const todayStats = database.getTodayStats();
      const monthStats = database.getMonthStats();
      const customers = database.getCustomers();

      setStats({
        totalSales: monthStats.totalSales || 0,
        todaySales: todayStats.totalSales || 0,
        totalCustomers: customers.length || 0,
        pendingInvoices: 0 // يمكن إضافة هذه الميزة لاحقاً
      });
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
    }
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatTime = (date) => {
    return date.toLocaleString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="dashboard-container">
      {/* شريط التنقل العلوي */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="dashboard-title">🧮 نظام المحاسبة السعودي</h1>
            <p className="current-time">{formatTime(currentTime)}</p>
          </div>
          <div className="header-right">
            <div className="user-info">
              <span className="user-name">مرحباً، {user.name}</span>
              <span className="user-role">{user.role}</span>
            </div>
            <button onClick={onLogout} className="logout-btn">
              🚪 تسجيل خروج
            </button>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="dashboard-main">
        {/* بطاقات الإحصائيات */}
        <section className="stats-section">
          <h2 className="section-title">📊 الإحصائيات السريعة</h2>
          <div className="stats-grid">
            <div className="stat-card sales">
              <div className="stat-icon">💰</div>
              <div className="stat-content">
                <h3>إجمالي المبيعات</h3>
                <p className="stat-value">{formatCurrency(stats.totalSales)}</p>
                <span className="stat-change positive">+12.5%</span>
              </div>
            </div>

            <div className="stat-card today">
              <div className="stat-icon">📈</div>
              <div className="stat-content">
                <h3>مبيعات اليوم</h3>
                <p className="stat-value">{formatCurrency(stats.todaySales)}</p>
                <span className="stat-change positive">+8.2%</span>
              </div>
            </div>

            <div className="stat-card customers">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <h3>إجمالي العملاء</h3>
                <p className="stat-value">{stats.totalCustomers}</p>
                <span className="stat-change positive">+5 جديد</span>
              </div>
            </div>

            <div className="stat-card invoices">
              <div className="stat-icon">📋</div>
              <div className="stat-content">
                <h3>فواتير معلقة</h3>
                <p className="stat-value">{stats.pendingInvoices}</p>
                <span className="stat-change neutral">بحاجة متابعة</span>
              </div>
            </div>
          </div>
        </section>

        {/* الإجراءات السريعة */}
        <section className="actions-section">
          <h2 className="section-title">⚡ الإجراءات السريعة</h2>
          <div className="actions-grid">
            <button
              className="action-card"
              onClick={() => onNavigate('pos')}
            >
              <div className="action-icon">🛒</div>
              <h3>نقطة البيع</h3>
              <p>إنشاء فاتورة جديدة</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('products')}
            >
              <div className="action-icon">📦</div>
              <h3>إدارة المنتجات</h3>
              <p>إضافة وتعديل المنتجات والمخزون</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('customers')}
            >
              <div className="action-icon">👤</div>
              <h3>إدارة العملاء</h3>
              <p>إضافة وتعديل بيانات العملاء</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('reports')}
            >
              <div className="action-icon">📊</div>
              <h3>التقارير</h3>
              <p>عرض التقارير المالية والإحصائيات</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('suppliers')}
            >
              <div className="action-icon">🏭</div>
              <h3>إدارة الموردين</h3>
              <p>إضافة وتعديل بيانات الموردين</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('accounting')}
            >
              <div className="action-icon">📊</div>
              <h3>المحاسبة المالية</h3>
              <p>دليل الحسابات والقيود المحاسبية</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('payments')}
            >
              <div className="action-icon">💳</div>
              <h3>إدارة المدفوعات</h3>
              <p>تتبع وإدارة جميع المدفوعات</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('inventory')}
            >
              <div className="action-icon">📦</div>
              <h3>إدارة المخزون</h3>
              <p>تتبع حركات المخزون والجرد</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('employees')}
            >
              <div className="action-icon">👥</div>
              <h3>إدارة الموظفين</h3>
              <p>إدارة بيانات الموظفين والرواتب</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('settings')}
            >
              <div className="action-icon">⚙️</div>
              <h3>إعدادات النظام</h3>
              <p>إعدادات الشركة والنظام</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('backup')}
            >
              <div className="action-icon">💾</div>
              <h3>النسخ الاحتياطي</h3>
              <p>حماية وإدارة بيانات النظام</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('notifications')}
            >
              <div className="action-icon">🔔</div>
              <h3>الإشعارات</h3>
              <p>تتبع التحديثات والتنبيهات</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('journal-entries')}
            >
              <div className="action-icon">📋</div>
              <h3>القيود اليومية</h3>
              <p>إدارة القيود المحاسبية</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('returns')}
            >
              <div className="action-icon">↩️</div>
              <h3>المرتجعات</h3>
              <p>إدارة مرتجعات المبيعات والمشتريات</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('quotations')}
            >
              <div className="action-icon">💼</div>
              <h3>عروض الأسعار</h3>
              <p>إنشاء وإدارة عروض الأسعار</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('vouchers')}
            >
              <div className="action-icon">🧾</div>
              <h3>سندات القبض والصرف</h3>
              <p>إدارة السندات النقدية</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('barcode')}
            >
              <div className="action-icon">📊</div>
              <h3>إدارة الباركود</h3>
              <p>إنشاء وطباعة الباركود</p>
            </button>

            <button
              className="action-card"
              onClick={() => onNavigate('advanced-reports')}
            >
              <div className="action-icon">📈</div>
              <h3>التقارير المتقدمة</h3>
              <p>تقارير مالية ومحاسبية شاملة</p>
            </button>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Dashboard;
