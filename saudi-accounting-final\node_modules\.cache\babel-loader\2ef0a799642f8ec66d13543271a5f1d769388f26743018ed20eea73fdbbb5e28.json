{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Login from './components/Login/Login';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport POS from './components/POS/POS';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentView, setCurrentView] = useState('home'); // 'home', 'login', 'dashboard'\n  const [user, setUser] = useState(null);\n  const [showMore, setShowMore] = useState(false);\n  const handleLoginClick = () => {\n    setCurrentView('login');\n  };\n  const handleLogin = userData => {\n    setUser(userData);\n    setCurrentView('dashboard');\n  };\n  const handleLogout = () => {\n    setUser(null);\n    setCurrentView('home');\n  };\n  const handleBackToHome = () => {\n    setCurrentView('home');\n  };\n  const handleMoreClick = () => {\n    setShowMore(!showMore);\n  };\n\n  // عرض صفحة تسجيل الدخول\n  if (currentView === 'login') {\n    return /*#__PURE__*/_jsxDEV(Login, {\n      onLogin: handleLogin,\n      onBack: handleBackToHome\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 12\n    }, this);\n  }\n\n  // عرض لوحة التحكم\n  if (currentView === 'dashboard' && user) {\n    return /*#__PURE__*/_jsxDEV(Dashboard, {\n      user: user,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 12\n    }, this);\n  }\n\n  // عرض الصفحة الرئيسية\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"main-title\",\n        children: \"\\uD83E\\uDDEE \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"main-description\",\n        children: \"\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644 \\u0644\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629 \\u0645\\u0639 \\u062F\\u0639\\u0645 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 \\u0648\\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"buttons-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleLoginClick,\n          children: \"\\uD83D\\uDD10 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: handleMoreClick,\n          children: \"\\u2139\\uFE0F \\u0645\\u0639\\u0631\\u0641\\u0629 \\u0627\\u0644\\u0645\\u0632\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"features-title\",\n          children: \"\\u2705 \\u0627\\u0644\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"features-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDCB0 \\u0646\\u0642\\u0637\\u0629 \\u0628\\u064A\\u0639 \\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDCE6 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDC65 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0645\\u0637\\u0627\\u0628\\u0642 \\u0644\\u0644\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), showMore && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          padding: '20px',\n          background: '#f6ffed',\n          borderRadius: '8px',\n          border: '1px solid #b7eb8f',\n          animation: 'fadeIn 0.5s ease-in'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#52c41a',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83D\\uDCCB \\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83C\\uDFE2 \\u0644\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 18\n            }, this), \" \\u0645\\u0646\\u0627\\u0633\\u0628 \\u0644\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0635\\u063A\\u064A\\u0631\\u0629 \\u0648\\u0627\\u0644\\u0645\\u062A\\u0648\\u0633\\u0637\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCB3 \\u0637\\u0631\\u0642 \\u0627\\u0644\\u062F\\u0641\\u0639:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 18\n            }, this), \" \\u0646\\u0642\\u062F\\u064A\\u060C \\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u060C \\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCF1 \\u0627\\u0644\\u0645\\u0646\\u0635\\u0627\\u062A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 18\n            }, this), \" \\u0648\\u064A\\u0628\\u060C \\u0645\\u0648\\u0628\\u0627\\u064A\\u0644\\u060C \\u062A\\u0627\\u0628\\u0644\\u062A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDD12 \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 18\n            }, this), \" \\u062A\\u0634\\u0641\\u064A\\u0631 SSL\\u060C \\u0646\\u0633\\u062E \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u064A\\u0648\\u0645\\u064A\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCDE \\u0627\\u0644\\u062F\\u0639\\u0645:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 18\n            }, this), \" \\u062F\\u0639\\u0645 \\u0641\\u0646\\u064A 24/7 \\u0628\\u0627\\u0644\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this), showMore && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"more-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDCCB \\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83C\\uDFE2 \\u0644\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 16\n          }, this), \" \\u0645\\u0646\\u0627\\u0633\\u0628 \\u0644\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0635\\u063A\\u064A\\u0631\\u0629 \\u0648\\u0627\\u0644\\u0645\\u062A\\u0648\\u0633\\u0637\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDCB3 \\u0637\\u0631\\u0642 \\u0627\\u0644\\u062F\\u0641\\u0639:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 16\n          }, this), \" \\u0646\\u0642\\u062F\\u064A\\u060C \\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u060C \\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDCF1 \\u0627\\u0644\\u0645\\u0646\\u0635\\u0627\\u062A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 16\n          }, this), \" \\u0648\\u064A\\u0628\\u060C \\u0645\\u0648\\u0628\\u0627\\u064A\\u0644\\u060C \\u062A\\u0627\\u0628\\u0644\\u062A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDD12 \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 16\n          }, this), \" \\u062A\\u0634\\u0641\\u064A\\u0631 SSL\\u060C \\u0646\\u0633\\u062E \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u064A\\u0648\\u0645\\u064A\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83D\\uDCDE \\u0627\\u0644\\u062F\\u0639\\u0645:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 16\n          }, this), \" \\u062F\\u0639\\u0645 \\u0641\\u0646\\u064A 24/7 \\u0628\\u0627\\u0644\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"status-title\",\n          children: \"\\uD83C\\uDF89 \\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"status-text\",\n          children: [\"\\u2705 React \\u064A\\u0639\\u0645\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 31\n          }, this), \"\\u2705 \\u0627\\u0644\\u062A\\u0635\\u0645\\u064A\\u0645 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A \\u062C\\u0627\\u0647\\u0632\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 34\n          }, this), \"\\u2705 \\u0627\\u0644\\u0623\\u0632\\u0631\\u0627\\u0631 \\u062A\\u0641\\u0627\\u0639\\u0644\\u064A\\u0629 \\u0645\\u0639 \\u062A\\u0623\\u062B\\u064A\\u0631\\u0627\\u062A \\u062C\\u0645\\u064A\\u0644\\u0629\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 47\n          }, this), \"\\u2705 CSS \\u0645\\u062A\\u0642\\u062F\\u0645 \\u0645\\u0639 \\u0631\\u0633\\u0648\\u0645 \\u0645\\u062A\\u062D\\u0631\\u0643\\u0629\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 39\n          }, this), \"\\uD83D\\uDCCB \\u062C\\u0627\\u0631\\u064A \\u062A\\u0637\\u0648\\u064A\\u0631 \\u0646\\u0638\\u0627\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ONZItljCKM5kDmXN7KIyvJu6LPU=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Dashboard", "POS", "jsxDEV", "_jsxDEV", "App", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "user", "setUser", "showMore", "setShowMore", "handleLoginClick", "handleLogin", "userData", "handleLogout", "handleBackToHome", "handleMoreClick", "onLogin", "onBack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLogout", "className", "children", "onClick", "style", "marginTop", "padding", "background", "borderRadius", "border", "animation", "color", "marginBottom", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport Login from './components/Login/Login';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport POS from './components/POS/POS';\nimport './App.css';\n\nfunction App() {\n  const [currentView, setCurrentView] = useState('home'); // 'home', 'login', 'dashboard'\n  const [user, setUser] = useState(null);\n  const [showMore, setShowMore] = useState(false);\n\n  const handleLoginClick = () => {\n    setCurrentView('login');\n  };\n\n  const handleLogin = (userData) => {\n    setUser(userData);\n    setCurrentView('dashboard');\n  };\n\n  const handleLogout = () => {\n    setUser(null);\n    setCurrentView('home');\n  };\n\n  const handleBackToHome = () => {\n    setCurrentView('home');\n  };\n\n  const handleMoreClick = () => {\n    setShowMore(!showMore);\n  };\n\n  // عرض صفحة تسجيل الدخول\n  if (currentView === 'login') {\n    return <Login onLogin={handleLogin} onBack={handleBackToHome} />;\n  }\n\n  // عرض لوحة التحكم\n  if (currentView === 'dashboard' && user) {\n    return <Dashboard user={user} onLogout={handleLogout} />;\n  }\n\n  // عرض الصفحة الرئيسية\n  return (\n    <div className=\"app\">\n      <div className=\"main-card\">\n        <h1 className=\"main-title\">\n          🧮 نظام المحاسبة السعودي\n        </h1>\n\n        <p className=\"main-description\">\n          نظام محاسبة متكامل للشركات السعودية مع دعم ضريبة القيمة المضافة والفواتير الإلكترونية\n        </p>\n\n        <div className=\"buttons-container\">\n          <button\n            className=\"btn btn-primary\"\n            onClick={handleLoginClick}\n          >\n            🔐 تسجيل الدخول\n          </button>\n          <button\n            className=\"btn btn-secondary\"\n            onClick={handleMoreClick}\n          >\n            ℹ️ معرفة المزيد\n          </button>\n        </div>\n\n        <div className=\"features-section\">\n          <h4 className=\"features-title\">✅ المميزات الرئيسية:</h4>\n          <ul className=\"features-list\">\n            <li>💰 نقطة بيع متقدمة</li>\n            <li>📦 إدارة المخزون</li>\n            <li>📊 التقارير المالية</li>\n            <li>👥 إدارة العملاء</li>\n            <li>🇸🇦 مطابق للمتطلبات السعودية</li>\n          </ul>\n        </div>\n\n        {showMore && (\n          <div style={{\n            marginTop: '20px',\n            padding: '20px',\n            background: '#f6ffed',\n            borderRadius: '8px',\n            border: '1px solid #b7eb8f',\n            animation: 'fadeIn 0.5s ease-in'\n          }}>\n            <h4 style={{ color: '#52c41a', marginBottom: '15px' }}>📋 تفاصيل إضافية:</h4>\n            <div style={{ textAlign: 'right', color: '#666' }}>\n              <p><strong>🏢 للشركات:</strong> مناسب للشركات الصغيرة والمتوسطة</p>\n              <p><strong>💳 طرق الدفع:</strong> نقدي، بطاقة ائتمان، تحويل بنكي</p>\n              <p><strong>📱 المنصات:</strong> ويب، موبايل، تابلت</p>\n              <p><strong>🔒 الأمان:</strong> تشفير SSL، نسخ احتياطية يومية</p>\n              <p><strong>📞 الدعم:</strong> دعم فني 24/7 باللغة العربية</p>\n            </div>\n          </div>\n        )}\n\n        {showMore && (\n          <div className=\"more-info\">\n            <h4>📋 تفاصيل إضافية:</h4>\n            <p><strong>🏢 للشركات:</strong> مناسب للشركات الصغيرة والمتوسطة</p>\n            <p><strong>💳 طرق الدفع:</strong> نقدي، بطاقة ائتمان، تحويل بنكي</p>\n            <p><strong>📱 المنصات:</strong> ويب، موبايل، تابلت</p>\n            <p><strong>🔒 الأمان:</strong> تشفير SSL، نسخ احتياطية يومية</p>\n            <p><strong>📞 الدعم:</strong> دعم فني 24/7 باللغة العربية</p>\n          </div>\n        )}\n\n        <div className=\"status-section\">\n          <h4 className=\"status-title\">🎉 حالة التطوير:</h4>\n          <p className=\"status-text\">\n            ✅ React يعمل بنجاح<br/>\n            ✅ التصميم العربي جاهز<br/>\n            ✅ الأزرار تفاعلية مع تأثيرات جميلة<br/>\n            ✅ CSS متقدم مع رسوم متحركة<br/>\n            📋 جاري تطوير نظام تسجيل الدخول\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,GAAG,MAAM,sBAAsB;AACtC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7BL,cAAc,CAAC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMM,WAAW,GAAIC,QAAQ,IAAK;IAChCL,OAAO,CAACK,QAAQ,CAAC;IACjBP,cAAc,CAAC,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBN,OAAO,CAAC,IAAI,CAAC;IACbF,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;IAC7BT,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BN,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;;EAED;EACA,IAAIJ,WAAW,KAAK,OAAO,EAAE;IAC3B,oBAAOH,OAAA,CAACJ,KAAK;MAACmB,OAAO,EAAEL,WAAY;MAACM,MAAM,EAAEH;IAAiB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClE;;EAEA;EACA,IAAIjB,WAAW,KAAK,WAAW,IAAIE,IAAI,EAAE;IACvC,oBAAOL,OAAA,CAACH,SAAS;MAACQ,IAAI,EAAEA,IAAK;MAACgB,QAAQ,EAAET;IAAa;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1D;;EAEA;EACA,oBACEpB,OAAA;IAAKsB,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBvB,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBvB,OAAA;QAAIsB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAE3B;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELpB,OAAA;QAAGsB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEhC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJpB,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvB,OAAA;UACEsB,SAAS,EAAC,iBAAiB;UAC3BE,OAAO,EAAEf,gBAAiB;UAAAc,QAAA,EAC3B;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpB,OAAA;UACEsB,SAAS,EAAC,mBAAmB;UAC7BE,OAAO,EAAEV,eAAgB;UAAAS,QAAA,EAC1B;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpB,OAAA;QAAKsB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BvB,OAAA;UAAIsB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDpB,OAAA;UAAIsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC3BvB,OAAA;YAAAuB,QAAA,EAAI;UAAkB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BpB,OAAA;YAAAuB,QAAA,EAAI;UAAgB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBpB,OAAA;YAAAuB,QAAA,EAAI;UAAmB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BpB,OAAA;YAAAuB,QAAA,EAAI;UAAgB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBpB,OAAA;YAAAuB,QAAA,EAAI;UAA6B;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAELb,QAAQ,iBACPP,OAAA;QAAKyB,KAAK,EAAE;UACVC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE;QACb,CAAE;QAAAR,QAAA,gBACAvB,OAAA;UAAIyB,KAAK,EAAE;YAAEO,KAAK,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAiB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpB,OAAA;UAAKyB,KAAK,EAAE;YAAES,SAAS,EAAE,OAAO;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,gBAChDvB,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gLAAgC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnEpB,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qKAA+B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpEpB,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uGAAmB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDpB,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gJAA8B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChEpB,OAAA;YAAAuB,QAAA,gBAAGvB,OAAA;cAAAuB,QAAA,EAAQ;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+HAA4B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAb,QAAQ,iBACPP,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvB,OAAA;UAAAuB,QAAA,EAAI;QAAiB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BpB,OAAA;UAAAuB,QAAA,gBAAGvB,OAAA;YAAAuB,QAAA,EAAQ;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gLAAgC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnEpB,OAAA;UAAAuB,QAAA,gBAAGvB,OAAA;YAAAuB,QAAA,EAAQ;UAAa;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qKAA+B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpEpB,OAAA;UAAAuB,QAAA,gBAAGvB,OAAA;YAAAuB,QAAA,EAAQ;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uGAAmB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtDpB,OAAA;UAAAuB,QAAA,gBAAGvB,OAAA;YAAAuB,QAAA,EAAQ;UAAU;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gJAA8B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEpB,OAAA;UAAAuB,QAAA,gBAAGvB,OAAA;YAAAuB,QAAA,EAAQ;UAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,+HAA4B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACN,eAEDpB,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvB,OAAA;UAAIsB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAgB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDpB,OAAA;UAAGsB,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,sEACP,eAAAvB,OAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,mHACF,eAAApB,OAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,uLACQ,eAAApB,OAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,wHACb,eAAApB,OAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,qKAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClB,EAAA,CAvHQD,GAAG;AAAAkC,EAAA,GAAHlC,GAAG;AAyHZ,eAAeA,GAAG;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}