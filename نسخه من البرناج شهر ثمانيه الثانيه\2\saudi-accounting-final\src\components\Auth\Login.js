import React, { useState } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Alert, 
  Space, 
  Divider,
  Checkbox,
  Row,
  Col
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  LoginOutlined, 
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import './Login.css';

const { Title, Text } = Typography;

const Login = ({ onLogin }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  // بيانات المستخدمين التجريبية
  const demoUsers = [
    {
      username: 'admin',
      password: 'admin123',
      name: 'المدير العام',
      role: 'admin',
      permissions: ['all']
    },
    {
      username: 'accountant',
      password: 'acc123',
      name: 'المحاسب الرئيسي',
      role: 'accountant',
      permissions: ['accounting', 'reports']
    },
    {
      username: 'cashier',
      password: 'cash123',
      name: 'أمين الصندوق',
      role: 'cashier',
      permissions: ['pos', 'sales']
    }
  ];

  const handleLogin = async (values) => {
    try {
      setLoading(true);
      setError('');

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      // التحقق من بيانات المستخدم
      const user = demoUsers.find(u => 
        u.username === values.username && 
        u.password === values.password
      );

      if (user) {
        // تسجيل دخول ناجح
        const userSession = {
          id: Math.random().toString(36).substr(2, 9),
          username: user.username,
          name: user.name,
          role: user.role,
          permissions: user.permissions,
          loginTime: new Date().toISOString(),
          rememberMe: rememberMe
        };
        
        // حفظ بيانات المستخدم إذا اختار "تذكرني"
        if (rememberMe) {
          localStorage.setItem('rememberedUser', values.username);
        } else {
          localStorage.removeItem('rememberedUser');
        }
        
        // حفظ جلسة المستخدم
        sessionStorage.setItem('currentUser', JSON.stringify(userSession));
        
        onLogin(userSession);
      } else {
        setError('اسم المستخدم أو كلمة المرور غير صحيحة');
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setError('حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const fillDemoCredentials = (userType) => {
    const user = demoUsers.find(u => u.role === userType);
    if (user) {
      form.setFieldsValue({
        username: user.username,
        password: user.password
      });
    }
  };

  // تحميل اسم المستخدم المحفوظ عند تحميل المكون
  React.useEffect(() => {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      form.setFieldsValue({ username: rememberedUser });
      setRememberMe(true);
    }
  }, [form]);

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay"></div>
      </div>
      
      <Row justify="center" align="middle" style={{ minHeight: '100vh', padding: '20px' }}>
        <Col xs={24} sm={20} md={16} lg={12} xl={10} xxl={8}>
          <Card className="login-card" bordered={false}>
            <div className="login-header">
              <div className="login-logo">
                <SafetyOutlined className="logo-icon" />
              </div>
              <Title level={2} className="login-title">
                نظام المحاسبة السعودي
              </Title>
              <Text className="login-subtitle">
                نظام محاسبة متكامل للشركات السعودية
              </Text>
            </div>

            <Divider />

            <Form
              form={form}
              name="login"
              onFinish={handleLogin}
              layout="vertical"
              size="large"
              className="login-form"
            >
              {error && (
                <Alert
                  message={error}
                  type="error"
                  showIcon
                  closable
                  className="login-error"
                  onClose={() => setError('')}
                />
              )}

              <Form.Item
                name="username"
                label="اسم المستخدم"
                rules={[
                  { required: true, message: 'يرجى إدخال اسم المستخدم' },
                  { min: 3, message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="أدخل اسم المستخدم"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="كلمة المرور"
                rules={[
                  { required: true, message: 'يرجى إدخال كلمة المرور' },
                  { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="أدخل كلمة المرور"
                  autoComplete="current-password"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>

              <Form.Item>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Checkbox 
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  >
                    تذكرني
                  </Checkbox>
                  <Button type="link" size="small">
                    نسيت كلمة المرور؟
                  </Button>
                </Space>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<LoginOutlined />}
                  block
                  className="login-button"
                >
                  {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                </Button>
              </Form.Item>
            </Form>

            <Divider>أو جرب الحسابات التجريبية</Divider>

            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <Button
                type="dashed"
                onClick={() => fillDemoCredentials('admin')}
                block
                className="demo-button"
              >
                👨‍💼 مدير النظام (admin / admin123)
              </Button>
              
              <Button
                type="dashed"
                onClick={() => fillDemoCredentials('accountant')}
                block
                className="demo-button"
              >
                🧮 محاسب (accountant / acc123)
              </Button>
              
              <Button
                type="dashed"
                onClick={() => fillDemoCredentials('cashier')}
                block
                className="demo-button"
              >
                💰 أمين صندوق (cashier / cash123)
              </Button>
            </Space>

            <div className="login-footer">
              <Text type="secondary" className="footer-text">
                © 2025 نظام المحاسبة السعودي. جميع الحقوق محفوظة.
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
