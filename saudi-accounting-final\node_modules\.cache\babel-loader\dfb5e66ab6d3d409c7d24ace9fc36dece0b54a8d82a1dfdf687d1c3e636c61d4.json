{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Login\\\\EnhancedLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './EnhancedLogin.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedLogin = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n  useEffect(() => {\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({\n        ...prev,\n        username: rememberedUsername\n      }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      const user = database.authenticateUser(formData.username, formData.password);\n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        database.logActivity({\n          type: 'login',\n          userId: user.id,\n          username: user.username,\n          timestamp: new Date().toISOString(),\n          details: 'تسجيل دخول ناجح'\n        });\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDemoLogin = () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"enhanced-login\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-background\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"background-pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shapes\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: \"\\uD83C\\uDFEA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Aronim EXP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-time\",\n            children: getCurrentTime()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0633\\u062C\\u0644 \\u062F\\u062E\\u0648\\u0644\\u0643 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"login-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"username\",\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  name: \"username\",\n                  value: formData.username,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-icon\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"current-password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"password-toggle\",\n                  onClick: () => setShowPassword(!showPassword),\n                  disabled: loading,\n                  children: showPassword ? '🙈' : '👁️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-options\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"checkbox-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: rememberMe,\n                  onChange: e => setRememberMe(e.target.checked),\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"checkmark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), \"\\u062A\\u0630\\u0643\\u0631\\u0646\\u064A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"login-button\",\n              disabled: loading,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"button-icon\",\n                  children: \"\\uD83D\\uDD10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divider\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0623\\u0648\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleDemoLogin,\n              className: \"demo-button\",\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), \"\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 (Demo)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"system-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDD12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0622\\u0645\\u0646 \\u0648\\u0645\\u062D\\u0645\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u062C\\u0627\\u0648\\u0628 \\u0645\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u062C\\u0647\\u0632\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"copyright\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Aronim EXP. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0646\\u0633\\u062E\\u0629 2.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0646\\u0642\\u0637\\u0629 \\u0628\\u064A\\u0639 \\u0645\\u062A\\u0637\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0628\\u064A\\u0639 \\u0633\\u0631\\u064A\\u0639 \\u0648\\u0641\\u0639\\u0627\\u0644 \\u0645\\u0639 \\u062F\\u0639\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0645\\u0641\\u0635\\u0644\\u0629 \\u0648\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u062F\\u0642\\u064A\\u0642\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u062A\\u0628\\u0639 \\u062F\\u0642\\u064A\\u0642 \\u0644\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0648\\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0630\\u0643\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062D\\u0627\\u0633\\u0628\\u064A \\u0634\\u0627\\u0645\\u0644 \\u0645\\u0639 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedLogin, \"IjvILKq9CIJnPDg9lujxmrutoIc=\");\n_c = EnhancedLogin;\nexport default EnhancedLogin;\nvar _c;\n$RefreshReg$(_c, \"EnhancedLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "onLogin", "_s", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "showPassword", "setShowPassword", "rememberMe", "setRememberMe", "rememberedUsername", "localStorage", "getItem", "prev", "usernameField", "document", "getElementById", "focus", "handleInputChange", "e", "name", "value", "target", "handleSubmit", "preventDefault", "trim", "Promise", "resolve", "setTimeout", "user", "authenticateUser", "setItem", "removeItem", "logActivity", "type", "userId", "id", "timestamp", "Date", "toISOString", "details", "console", "handleDemoLogin", "getCurrentTime", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "onChange", "placeholder", "disabled", "autoComplete", "onClick", "checked", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Login/EnhancedLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './EnhancedLogin.css';\n\nconst EnhancedLogin = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n\n  useEffect(() => {\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({ ...prev, username: rememberedUsername }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const user = database.authenticateUser(formData.username, formData.password);\n      \n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        database.logActivity({\n          type: 'login',\n          userId: user.id,\n          username: user.username,\n          timestamp: new Date().toISOString(),\n          details: 'تسجيل دخول ناجح'\n        });\n\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoLogin = () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"enhanced-login\">\n      <div className=\"login-background\">\n        <div className=\"background-pattern\"></div>\n        <div className=\"floating-shapes\">\n          <div className=\"shape shape-1\"></div>\n          <div className=\"shape shape-2\"></div>\n          <div className=\"shape shape-3\"></div>\n          <div className=\"shape shape-4\"></div>\n        </div>\n      </div>\n\n      <div className=\"login-container\">\n        <div className=\"login-card\">\n          <div className=\"login-header\">\n            <div className=\"company-logo\">\n              <div className=\"logo-icon\">🏪</div>\n              <div className=\"logo-text\">\n                <h1>Aronim EXP</h1>\n                <p>نظام المحاسبة المتطور</p>\n              </div>\n            </div>\n            <div className=\"current-time\">\n              {getCurrentTime()}\n            </div>\n          </div>\n\n          <div className=\"login-body\">\n            <div className=\"welcome-text\">\n              <h2>مرحباً بك</h2>\n              <p>سجل دخولك للوصول إلى نظام المحاسبة</p>\n            </div>\n\n            {error && (\n              <div className=\"error-message\">\n                <span className=\"error-icon\">⚠️</span>\n                {error}\n              </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"login-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"username\">اسم المستخدم</label>\n                <div className=\"input-container\">\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل اسم المستخدم\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"username\"\n                  />\n                  <span className=\"input-icon\">👤</span>\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\">كلمة المرور</label>\n                <div className=\"input-container\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل كلمة المرور\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"current-password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    disabled={loading}\n                  >\n                    {showPassword ? '🙈' : '👁️'}\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"form-options\">\n                <label className=\"checkbox-container\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    disabled={loading}\n                  />\n                  <span className=\"checkmark\"></span>\n                  تذكرني\n                </label>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"login-button\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    جاري تسجيل الدخول...\n                  </>\n                ) : (\n                  <>\n                    <span className=\"button-icon\">🔐</span>\n                    تسجيل الدخول\n                  </>\n                )}\n              </button>\n            </form>\n\n            <div className=\"demo-section\">\n              <div className=\"divider\">\n                <span>أو</span>\n              </div>\n              <button\n                type=\"button\"\n                onClick={handleDemoLogin}\n                className=\"demo-button\"\n                disabled={loading}\n              >\n                <span className=\"button-icon\">🎯</span>\n                تجربة النظام (Demo)\n              </button>\n            </div>\n          </div>\n\n          <div className=\"login-footer\">\n            <div className=\"system-info\">\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🇸🇦</span>\n                <span>متوافق مع المتطلبات السعودية</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🔒</span>\n                <span>نظام آمن ومحمي</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">📱</span>\n                <span>متجاوب مع جميع الأجهزة</span>\n              </div>\n            </div>\n            \n            <div className=\"copyright\">\n              <p>© 2024 Aronim EXP. جميع الحقوق محفوظة.</p>\n              <p>نسخة 2.0.0</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"features-sidebar\">\n          <h3>مميزات النظام</h3>\n          <div className=\"features-list\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🛒</div>\n              <div className=\"feature-content\">\n                <h4>نقطة بيع متطورة</h4>\n                <p>نظام بيع سريع وفعال مع دعم الباركود</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📊</div>\n              <div className=\"feature-content\">\n                <h4>تقارير شاملة</h4>\n                <p>تقارير مالية مفصلة وإحصائيات دقيقة</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📦</div>\n              <div className=\"feature-content\">\n                <h4>إدارة المخزون</h4>\n                <p>تتبع دقيق للمخزون والتنبيهات الذكية</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <div className=\"feature-content\">\n                <h4>محاسبة متكاملة</h4>\n                <p>نظام محاسبي شامل مع ضريبة القيمة المضافة</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMqB,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IACrE,IAAIF,kBAAkB,EAAE;MACtBX,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEb,QAAQ,EAAEU;MAAmB,CAAC,CAAC,CAAC;MAChED,aAAa,CAAC,IAAI,CAAC;IACrB;;IAEA;IACA,MAAMK,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC;IACzD,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvB,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACO,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIjB,KAAK,EAAE;MACTC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC1B,QAAQ,CAACE,QAAQ,CAACyB,IAAI,CAAC,CAAC,IAAI,CAAC3B,QAAQ,CAACG,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC1DpB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAM,IAAIqB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,IAAI,GAAGvC,QAAQ,CAACwC,gBAAgB,CAAChC,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAE5E,IAAI4B,IAAI,EAAE;QACR;QACA,IAAIrB,UAAU,EAAE;UACdG,YAAY,CAACoB,OAAO,CAAC,oBAAoB,EAAEjC,QAAQ,CAACE,QAAQ,CAAC;QAC/D,CAAC,MAAM;UACLW,YAAY,CAACqB,UAAU,CAAC,oBAAoB,CAAC;QAC/C;;QAEA;QACA1C,QAAQ,CAAC2C,WAAW,CAAC;UACnBC,IAAI,EAAE,OAAO;UACbC,MAAM,EAAEN,IAAI,CAACO,EAAE;UACfpC,QAAQ,EAAE6B,IAAI,CAAC7B,QAAQ;UACvBqC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnCC,OAAO,EAAE;QACX,CAAC,CAAC;QAEF5C,OAAO,CAACiC,IAAI,CAAC;MACf,CAAC,MAAM;QACLxB,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B3C,WAAW,CAAC;MACVC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMsC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIL,IAAI,CAAC,CAAC,CAACM,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE1D,OAAA;IAAK2D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5D,OAAA;MAAK2D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B5D,OAAA;QAAK2D,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1ChE,OAAA;QAAK2D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5D,OAAA;UAAK2D,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrChE,OAAA;UAAK2D,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrChE,OAAA;UAAK2D,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrChE,OAAA;UAAK2D,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhE,OAAA;MAAK2D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5D,OAAA;QAAK2D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5D,OAAA;UAAK2D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B5D,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChE,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5D,OAAA;gBAAA4D,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBhE,OAAA;gBAAA4D,QAAA,EAAG;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BT,cAAc,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5D,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAA4D,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBhE,OAAA;cAAA4D,QAAA,EAAG;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAELpD,KAAK,iBACJZ,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5D,OAAA;cAAM2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrCpD,KAAK;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDhE,OAAA;YAAMiE,QAAQ,EAAElC,YAAa;YAAC4B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAClD5D,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAOkE,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChE,OAAA;gBAAK2D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B5D,OAAA;kBACE0C,IAAI,EAAC,MAAM;kBACXE,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACE,QAAS;kBACzB2D,QAAQ,EAAEzC,iBAAkB;kBAC5B0C,WAAW,EAAC,8FAAmB;kBAC/BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAE3D,OAAQ;kBAClB4D,YAAY,EAAC;gBAAU;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFhE,OAAA;kBAAM2D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAOkE,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ChE,OAAA;gBAAK2D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B5D,OAAA;kBACE0C,IAAI,EAAE5B,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzC8B,EAAE,EAAC,UAAU;kBACbhB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACG,QAAS;kBACzB0D,QAAQ,EAAEzC,iBAAkB;kBAC5B0C,WAAW,EAAC,wFAAkB;kBAC9BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAE3D,OAAQ;kBAClB4D,YAAY,EAAC;gBAAkB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACFhE,OAAA;kBACE0C,IAAI,EAAC,QAAQ;kBACbiB,SAAS,EAAC,iBAAiB;kBAC3BY,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CuD,QAAQ,EAAE3D,OAAQ;kBAAAkD,QAAA,EAEjB9C,YAAY,GAAG,IAAI,GAAG;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5D,OAAA;gBAAO2D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACnC5D,OAAA;kBACE0C,IAAI,EAAC,UAAU;kBACf8B,OAAO,EAAExD,UAAW;kBACpBmD,QAAQ,EAAGxC,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACG,MAAM,CAAC0C,OAAO,CAAE;kBACjDH,QAAQ,EAAE3D;gBAAQ;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACFhE,OAAA;kBAAM2D,SAAS,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,wCAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhE,OAAA;cACE0C,IAAI,EAAC,QAAQ;cACbiB,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAE3D,OAAQ;cAAAkD,QAAA,EAEjBlD,OAAO,gBACNV,OAAA,CAAAE,SAAA;gBAAA0D,QAAA,gBACE5D,OAAA;kBAAM2D,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mGAE3C;cAAA,eAAE,CAAC,gBAEHhE,OAAA,CAAAE,SAAA;gBAAA0D,QAAA,gBACE5D,OAAA;kBAAM2D,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uEAEzC;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEPhE,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtB5D,OAAA;gBAAA4D,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACNhE,OAAA;cACE0C,IAAI,EAAC,QAAQ;cACb6B,OAAO,EAAErB,eAAgB;cACzBS,SAAS,EAAC,aAAa;cACvBU,QAAQ,EAAE3D,OAAQ;cAAAkD,QAAA,gBAElB5D,OAAA;gBAAM2D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,8EAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA;UAAK2D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B5D,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5D,OAAA;gBAAM2D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvChE,OAAA;gBAAA4D,QAAA,EAAM;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5D,OAAA;gBAAM2D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrChE,OAAA;gBAAA4D,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5D,OAAA;gBAAM2D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrChE,OAAA;gBAAA4D,QAAA,EAAM;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5D,OAAA;cAAA4D,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7ChE,OAAA;cAAA4D,QAAA,EAAG;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5D,OAAA;UAAA4D,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBhE,OAAA;UAAK2D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5D,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtChE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5D,OAAA;gBAAA4D,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBhE,OAAA;gBAAA4D,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtChE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5D,OAAA;gBAAA4D,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhE,OAAA;gBAAA4D,QAAA,EAAG;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtChE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5D,OAAA;gBAAA4D,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBhE,OAAA;gBAAA4D,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtChE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5D,OAAA;gBAAA4D,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBhE,OAAA;gBAAA4D,QAAA,EAAG;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAzSIF,aAAa;AAAAsE,EAAA,GAAbtE,aAAa;AA2SnB,eAAeA,aAAa;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}