@echo off
echo ===============================================
echo    نظام المحاسبة السعودي المتكامل
echo    Saudi Integrated Accounting System
echo ===============================================
echo.
echo الميزات المتوفرة:
echo - نقطة البيع المتقدمة
echo - إدارة المخزون الذكية
echo - الفواتير الإلكترونية
echo - التقارير المالية المتقدمة
echo - إدارة العملاء والموردين
echo - القيود المحاسبية التلقائية
echo - عروض الأسعار الاحترافية
echo - إدارة المرتجعات الشاملة
echo - سندات القبض والصرف
echo - إدارة الباركود المتقدمة
echo - النسخ الاحتياطي الآمن
echo - نظام الإشعارات الذكي
echo - إدارة الموظفين
echo.
echo ===============================================
echo 🚀 بدء تشغيل النظام...
echo.

REM إضافة Node.js للـ PATH مؤقتاً
set PATH=%PATH%;C:\Program Files\nodejs

REM التحقق من وجود Node.js
echo 🔍 التحقق من Node.js...
"C:\Program Files\nodejs\node.exe" --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير موجود
    pause
    exit /b 1
)

REM التحقق من وجود npm
echo 🔍 التحقق من npm...
"C:\Program Files\nodejs\npm.cmd" --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير موجود
    pause
    exit /b 1
)

echo ✅ Node.js و npm جاهزان
echo.

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    "C:\Program Files\nodejs\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
)

echo 🌐 بدء تشغيل الخادم...
echo سيتم فتح التطبيق في المتصفح على http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

REM تعيين متغيرات البيئة
set NODE_PATH=C:\Program Files\nodejs
set PATH=%NODE_PATH%;%PATH%

REM تشغيل التطبيق مع المسار الكامل
"%NODE_PATH%\node.exe" "%NODE_PATH%\node_modules\npm\bin\npm-cli.js" start
