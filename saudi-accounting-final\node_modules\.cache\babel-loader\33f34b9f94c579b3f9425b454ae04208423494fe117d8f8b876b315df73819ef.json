{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Barcode\\\\BarcodeManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport database from '../../utils/database';\nimport './BarcodeManager.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BarcodeManager = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [barcodeType, setBarcodeType] = useState('CODE128');\n  const [printSettings, setPrintSettings] = useState({\n    labelWidth: 50,\n    labelHeight: 25,\n    fontSize: 10,\n    showProductName: true,\n    showPrice: true,\n    showBarcode: true,\n    copies: 1\n  });\n  const [message, setMessage] = useState('');\n  const [showScanner, setShowScanner] = useState(false);\n  const [scannedCode, setScannedCode] = useState('');\n  const printRef = useRef();\n  const barcodeTypes = [{\n    value: 'CODE128',\n    label: 'CODE 128'\n  }, {\n    value: 'CODE39',\n    label: 'CODE 39'\n  }, {\n    value: 'EAN13',\n    label: 'EAN-13'\n  }, {\n    value: 'EAN8',\n    label: 'EAN-8'\n  }, {\n    value: 'UPC',\n    label: 'UPC'\n  }];\n  useEffect(() => {\n    loadProducts();\n  }, []);\n  const loadProducts = () => {\n    try {\n      const productsData = database.getProducts();\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      setMessage('خطأ في تحميل المنتجات');\n    }\n  };\n  const generateBarcode = (text, type = 'CODE128') => {\n    // محاكاة إنشاء الباركود - في التطبيق الحقيقي ستستخدم مكتبة JsBarcode\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    canvas.width = 200;\n    canvas.height = 50;\n\n    // رسم خطوط الباركود\n    ctx.fillStyle = '#000000';\n    for (let i = 0; i < 50; i++) {\n      if (Math.random() > 0.5) {\n        ctx.fillRect(i * 4, 0, 2, 40);\n      }\n    }\n\n    // إضافة النص\n    ctx.fillStyle = '#000000';\n    ctx.font = '12px Arial';\n    ctx.textAlign = 'center';\n    ctx.fillText(text, canvas.width / 2, 50);\n    return canvas.toDataURL();\n  };\n  const generateProductBarcode = productId => {\n    const product = products.find(p => p.id === productId);\n    if (!product) return '';\n\n    // إنشاء باركود فريد للمنتج\n    let barcode = product.barcode;\n    if (!barcode) {\n      // إنشاء باركود جديد إذا لم يكن موجود\n      const timestamp = Date.now().toString();\n      const productCode = product.id.toString().padStart(4, '0');\n      barcode = `${productCode}${timestamp.slice(-6)}`;\n\n      // حفظ الباركود في قاعدة البيانات\n      database.updateProduct(productId, {\n        barcode\n      });\n      loadProducts();\n    }\n    return barcode;\n  };\n  const handleProductSelect = productId => {\n    setSelectedProducts(prev => {\n      if (prev.includes(productId)) {\n        return prev.filter(id => id !== productId);\n      } else {\n        return [...prev, productId];\n      }\n    });\n  };\n  const selectAllProducts = () => {\n    const filteredProductIds = filteredProducts.map(p => p.id);\n    setSelectedProducts(filteredProductIds);\n  };\n  const clearSelection = () => {\n    setSelectedProducts([]);\n  };\n  const handlePrintSettingsChange = (setting, value) => {\n    setPrintSettings(prev => ({\n      ...prev,\n      [setting]: value\n    }));\n  };\n  const printBarcodes = () => {\n    if (selectedProducts.length === 0) {\n      setMessage('يرجى اختيار منتج واحد على الأقل للطباعة');\n      return;\n    }\n\n    // إنشاء نافذة طباعة جديدة\n    const printWindow = window.open('', '_blank');\n    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));\n    let printContent = `\n      <html>\n        <head>\n          <title>طباعة الباركود</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 0; \n              padding: 20px;\n              background: white;\n            }\n            .barcode-grid {\n              display: grid;\n              grid-template-columns: repeat(auto-fit, minmax(${printSettings.labelWidth}mm, 1fr));\n              gap: 5mm;\n              page-break-inside: avoid;\n            }\n            .barcode-label {\n              width: ${printSettings.labelWidth}mm;\n              height: ${printSettings.labelHeight}mm;\n              border: 1px solid #ccc;\n              padding: 2mm;\n              text-align: center;\n              display: flex;\n              flex-direction: column;\n              justify-content: center;\n              page-break-inside: avoid;\n              background: white;\n            }\n            .product-name {\n              font-size: ${printSettings.fontSize - 2}px;\n              font-weight: bold;\n              margin-bottom: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            .barcode-image {\n              max-width: 100%;\n              height: auto;\n              margin: 2px 0;\n            }\n            .barcode-text {\n              font-size: ${printSettings.fontSize - 3}px;\n              font-family: monospace;\n              margin: 1px 0;\n            }\n            .product-price {\n              font-size: ${printSettings.fontSize - 1}px;\n              font-weight: bold;\n              color: #d32f2f;\n              margin-top: 2px;\n            }\n            @media print {\n              body { margin: 0; }\n              .barcode-label { \n                border: 1px solid #000;\n                margin: 0;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"barcode-grid\">\n    `;\n    selectedProductsData.forEach(product => {\n      const barcode = generateProductBarcode(product.id);\n      const barcodeImage = generateBarcode(barcode, barcodeType);\n      for (let copy = 0; copy < printSettings.copies; copy++) {\n        printContent += `\n          <div class=\"barcode-label\">\n            ${printSettings.showProductName ? `<div class=\"product-name\">${product.name}</div>` : ''}\n            ${printSettings.showBarcode ? `\n              <img src=\"${barcodeImage}\" alt=\"Barcode\" class=\"barcode-image\" />\n              <div class=\"barcode-text\">${barcode}</div>\n            ` : ''}\n            ${printSettings.showPrice ? `<div class=\"product-price\">${product.price} ريال</div>` : ''}\n          </div>\n        `;\n      }\n    });\n    printContent += `\n          </div>\n        </body>\n      </html>\n    `;\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    setTimeout(() => {\n      printWindow.print();\n    }, 500);\n    setMessage(`تم إنشاء ${selectedProducts.length * printSettings.copies} ملصق باركود للطباعة`);\n    setTimeout(() => setMessage(''), 3000);\n  };\n  const scanBarcode = () => {\n    setShowScanner(true);\n    // في التطبيق الحقيقي، ستستخدم مكتبة لقراءة الباركود من الكاميرا\n    // هنا سنحاكي عملية المسح\n    setTimeout(() => {\n      const randomProduct = products[Math.floor(Math.random() * products.length)];\n      if (randomProduct) {\n        const barcode = generateProductBarcode(randomProduct.id);\n        setScannedCode(barcode);\n        setMessage(`تم مسح الباركود: ${barcode} - ${randomProduct.name}`);\n      }\n      setShowScanner(false);\n    }, 2000);\n  };\n  const searchByBarcode = () => {\n    if (!scannedCode) {\n      setMessage('لا يوجد باركود ممسوح');\n      return;\n    }\n    const product = products.find(p => p.barcode === scannedCode);\n    if (product) {\n      setSearchTerm(product.name);\n      setMessage(`تم العثور على المنتج: ${product.name}`);\n    } else {\n      setMessage('لم يتم العثور على منتج بهذا الباركود');\n    }\n  };\n  const exportBarcodes = () => {\n    if (selectedProducts.length === 0) {\n      setMessage('يرجى اختيار منتج واحد على الأقل للتصدير');\n      return;\n    }\n    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));\n    const barcodeData = selectedProductsData.map(product => ({\n      id: product.id,\n      name: product.name,\n      barcode: generateProductBarcode(product.id),\n      price: product.price,\n      category: product.category\n    }));\n    const dataStr = JSON.stringify(barcodeData, null, 2);\n    const dataBlob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `barcodes-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n    URL.revokeObjectURL(url);\n    setMessage('تم تصدير بيانات الباركود بنجاح!');\n    setTimeout(() => setMessage(''), 3000);\n  };\n  const filteredProducts = products.filter(product => {\n    var _product$barcode;\n    return product.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_product$barcode = product.barcode) === null || _product$barcode === void 0 ? void 0 : _product$barcode.includes(searchTerm));\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"barcode-manager-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"barcode-manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCA \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0637\\u0628\\u0627\\u0639\\u0629 \\u0648\\u0645\\u0633\\u062D \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('خطأ') ? 'error' : 'success'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scanner-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scanner-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: scanBarcode,\n          className: \"scan-btn\",\n          disabled: showScanner,\n          children: showScanner ? '📷 جاري المسح...' : '📷 مسح باركود'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), scannedCode && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scanned-result\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0645\\u0633\\u0648\\u062D: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: scannedCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: searchByBarcode,\n            className: \"search-scanned-btn\",\n            children: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"barcode-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0623\\u0648 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"selection-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: selectAllProducts,\n          className: \"select-all-btn\",\n          children: [\"\\u2705 \\u062A\\u062D\\u062F\\u064A\\u062F \\u0627\\u0644\\u0643\\u0644 (\", filteredProducts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearSelection,\n          className: \"clear-selection-btn\",\n          children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621 \\u0627\\u0644\\u062A\\u062D\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"selection-count\",\n          children: [\"\\u0645\\u062D\\u062F\\u062F: \", selectedProducts.length, \" \\u0645\\u0646\\u062A\\u062C\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"print-settings\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u2699\\uFE0F \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0637\\u0628\\u0627\\u0639\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: barcodeType,\n            onChange: e => setBarcodeType(e.target.value),\n            children: barcodeTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type.value,\n              children: type.label\n            }, type.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0645\\u0644\\u0635\\u0642 (\\u0645\\u0645)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: printSettings.labelWidth,\n            onChange: e => handlePrintSettingsChange('labelWidth', parseInt(e.target.value)),\n            min: \"20\",\n            max: \"100\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0627\\u0631\\u062A\\u0641\\u0627\\u0639 \\u0627\\u0644\\u0645\\u0644\\u0635\\u0642 (\\u0645\\u0645)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: printSettings.labelHeight,\n            onChange: e => handlePrintSettingsChange('labelHeight', parseInt(e.target.value)),\n            min: \"15\",\n            max: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u062D\\u062C\\u0645 \\u0627\\u0644\\u062E\\u0637\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: printSettings.fontSize,\n            onChange: e => handlePrintSettingsChange('fontSize', parseInt(e.target.value)),\n            min: \"6\",\n            max: \"16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0646\\u0633\\u062E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: printSettings.copies,\n            onChange: e => handlePrintSettingsChange('copies', parseInt(e.target.value)),\n            min: \"1\",\n            max: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group checkbox-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: printSettings.showProductName,\n              onChange: e => handlePrintSettingsChange('showProductName', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), \"\\u0639\\u0631\\u0636 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group checkbox-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: printSettings.showPrice,\n              onChange: e => handlePrintSettingsChange('showPrice', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0633\\u0639\\u0631\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-group checkbox-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: printSettings.showBarcode,\n              onChange: e => handlePrintSettingsChange('showBarcode', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"action-buttons\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: printBarcodes,\n        className: \"print-btn\",\n        disabled: selectedProducts.length === 0,\n        children: [\"\\uD83D\\uDDA8\\uFE0F \\u0637\\u0628\\u0627\\u0639\\u0629 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F (\", selectedProducts.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: exportBarcodes,\n        className: \"export-btn\",\n        disabled: selectedProducts.length === 0,\n        children: \"\\uD83D\\uDCE4 \\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products-grid\",\n      children: filteredProducts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-products\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0637\\u0627\\u0628\\u0642\\u0629 \\u0644\\u0644\\u0628\\u062D\\u062B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this) : filteredProducts.map(product => {\n        const barcode = generateProductBarcode(product.id);\n        const barcodeImage = generateBarcode(barcode, barcodeType);\n        const isSelected = selectedProducts.includes(product.id);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `product-card ${isSelected ? 'selected' : ''}`,\n          onClick: () => handleProductSelect(product.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selection-indicator\",\n              children: isSelected ? '✅' : '⭕'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0627\\u0644\\u0641\\u0626\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 22\n              }, this), \" \", product.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0627\\u0644\\u0633\\u0639\\u0631:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 22\n              }, this), \" \", product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 22\n              }, this), \" \", product.stock]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"barcode-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: barcodeImage,\n              alt: \"Barcode Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"barcode-text\",\n              children: barcode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                setSelectedProducts([product.id]);\n                printBarcodes();\n              },\n              className: \"quick-print-btn\",\n              children: \"\\uD83D\\uDDA8\\uFE0F \\u0637\\u0628\\u0627\\u0639\\u0629 \\u0633\\u0631\\u064A\\u0639\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 17\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), showScanner && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scanner-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scanner-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCF7 \\u0645\\u0633\\u062D \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scanner-animation\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"scanner-line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0648\\u062C\\u0647 \\u0627\\u0644\\u0643\\u0627\\u0645\\u064A\\u0631\\u0627 \\u0646\\u062D\\u0648 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowScanner(false),\n            className: \"cancel-scan-btn\",\n            children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 291,\n    columnNumber: 5\n  }, this);\n};\n_s(BarcodeManager, \"48VdWr7TGqFoPO5NHdV+x9TWJgA=\");\n_c = BarcodeManager;\nexport default BarcodeManager;\nvar _c;\n$RefreshReg$(_c, \"BarcodeManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "database", "jsxDEV", "_jsxDEV", "BarcodeManager", "_s", "products", "setProducts", "selectedProducts", "setSelectedProducts", "searchTerm", "setSearchTerm", "barcodeType", "setBarcodeType", "printSettings", "setPrintSettings", "labelWidth", "labelHeight", "fontSize", "showProductName", "showPrice", "showBarcode", "copies", "message", "setMessage", "showScanner", "setShowScanner", "scannedCode", "setScannedCode", "printRef", "barcodeTypes", "value", "label", "loadProducts", "productsData", "getProducts", "error", "console", "generateBarcode", "text", "type", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "fillStyle", "i", "Math", "random", "fillRect", "font", "textAlign", "fillText", "toDataURL", "generateProductBarcode", "productId", "product", "find", "p", "id", "barcode", "timestamp", "Date", "now", "toString", "productCode", "padStart", "slice", "updateProduct", "handleProductSelect", "prev", "includes", "filter", "selectAllProducts", "filteredProductIds", "filteredProducts", "map", "clearSelection", "handlePrintSettingsChange", "setting", "printBarcodes", "length", "printWindow", "window", "open", "selectedProductsData", "printContent", "for<PERSON>ach", "barcodeImage", "copy", "name", "price", "write", "close", "setTimeout", "print", "scanBarcode", "randomProduct", "floor", "searchByBarcode", "exportBarcodes", "barcodeData", "category", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "url", "URL", "createObjectURL", "link", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "_product$barcode", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "e", "target", "parseInt", "min", "max", "checked", "isSelected", "stock", "src", "alt", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Barcode/BarcodeManager.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport database from '../../utils/database';\nimport './BarcodeManager.css';\n\nconst BarcodeManager = () => {\n  const [products, setProducts] = useState([]);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [barcodeType, setBarcodeType] = useState('CODE128');\n  const [printSettings, setPrintSettings] = useState({\n    labelWidth: 50,\n    labelHeight: 25,\n    fontSize: 10,\n    showProductName: true,\n    showPrice: true,\n    showBarcode: true,\n    copies: 1\n  });\n  const [message, setMessage] = useState('');\n  const [showScanner, setShowScanner] = useState(false);\n  const [scannedCode, setScannedCode] = useState('');\n  const printRef = useRef();\n\n  const barcodeTypes = [\n    { value: 'CODE128', label: 'CODE 128' },\n    { value: 'CODE39', label: 'CODE 39' },\n    { value: 'EAN13', label: 'EAN-13' },\n    { value: 'EAN8', label: 'EAN-8' },\n    { value: 'UPC', label: 'UPC' }\n  ];\n\n  useEffect(() => {\n    loadProducts();\n  }, []);\n\n  const loadProducts = () => {\n    try {\n      const productsData = database.getProducts();\n      setProducts(productsData);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      setMessage('خطأ في تحميل المنتجات');\n    }\n  };\n\n  const generateBarcode = (text, type = 'CODE128') => {\n    // محاكاة إنشاء الباركود - في التطبيق الحقيقي ستستخدم مكتبة JsBarcode\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    canvas.width = 200;\n    canvas.height = 50;\n    \n    // رسم خطوط الباركود\n    ctx.fillStyle = '#000000';\n    for (let i = 0; i < 50; i++) {\n      if (Math.random() > 0.5) {\n        ctx.fillRect(i * 4, 0, 2, 40);\n      }\n    }\n    \n    // إضافة النص\n    ctx.fillStyle = '#000000';\n    ctx.font = '12px Arial';\n    ctx.textAlign = 'center';\n    ctx.fillText(text, canvas.width / 2, 50);\n    \n    return canvas.toDataURL();\n  };\n\n  const generateProductBarcode = (productId) => {\n    const product = products.find(p => p.id === productId);\n    if (!product) return '';\n\n    // إنشاء باركود فريد للمنتج\n    let barcode = product.barcode;\n    if (!barcode) {\n      // إنشاء باركود جديد إذا لم يكن موجود\n      const timestamp = Date.now().toString();\n      const productCode = product.id.toString().padStart(4, '0');\n      barcode = `${productCode}${timestamp.slice(-6)}`;\n      \n      // حفظ الباركود في قاعدة البيانات\n      database.updateProduct(productId, { barcode });\n      loadProducts();\n    }\n\n    return barcode;\n  };\n\n  const handleProductSelect = (productId) => {\n    setSelectedProducts(prev => {\n      if (prev.includes(productId)) {\n        return prev.filter(id => id !== productId);\n      } else {\n        return [...prev, productId];\n      }\n    });\n  };\n\n  const selectAllProducts = () => {\n    const filteredProductIds = filteredProducts.map(p => p.id);\n    setSelectedProducts(filteredProductIds);\n  };\n\n  const clearSelection = () => {\n    setSelectedProducts([]);\n  };\n\n  const handlePrintSettingsChange = (setting, value) => {\n    setPrintSettings(prev => ({\n      ...prev,\n      [setting]: value\n    }));\n  };\n\n  const printBarcodes = () => {\n    if (selectedProducts.length === 0) {\n      setMessage('يرجى اختيار منتج واحد على الأقل للطباعة');\n      return;\n    }\n\n    // إنشاء نافذة طباعة جديدة\n    const printWindow = window.open('', '_blank');\n    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));\n    \n    let printContent = `\n      <html>\n        <head>\n          <title>طباعة الباركود</title>\n          <style>\n            body { \n              font-family: Arial, sans-serif; \n              margin: 0; \n              padding: 20px;\n              background: white;\n            }\n            .barcode-grid {\n              display: grid;\n              grid-template-columns: repeat(auto-fit, minmax(${printSettings.labelWidth}mm, 1fr));\n              gap: 5mm;\n              page-break-inside: avoid;\n            }\n            .barcode-label {\n              width: ${printSettings.labelWidth}mm;\n              height: ${printSettings.labelHeight}mm;\n              border: 1px solid #ccc;\n              padding: 2mm;\n              text-align: center;\n              display: flex;\n              flex-direction: column;\n              justify-content: center;\n              page-break-inside: avoid;\n              background: white;\n            }\n            .product-name {\n              font-size: ${printSettings.fontSize - 2}px;\n              font-weight: bold;\n              margin-bottom: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            .barcode-image {\n              max-width: 100%;\n              height: auto;\n              margin: 2px 0;\n            }\n            .barcode-text {\n              font-size: ${printSettings.fontSize - 3}px;\n              font-family: monospace;\n              margin: 1px 0;\n            }\n            .product-price {\n              font-size: ${printSettings.fontSize - 1}px;\n              font-weight: bold;\n              color: #d32f2f;\n              margin-top: 2px;\n            }\n            @media print {\n              body { margin: 0; }\n              .barcode-label { \n                border: 1px solid #000;\n                margin: 0;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"barcode-grid\">\n    `;\n\n    selectedProductsData.forEach(product => {\n      const barcode = generateProductBarcode(product.id);\n      const barcodeImage = generateBarcode(barcode, barcodeType);\n      \n      for (let copy = 0; copy < printSettings.copies; copy++) {\n        printContent += `\n          <div class=\"barcode-label\">\n            ${printSettings.showProductName ? `<div class=\"product-name\">${product.name}</div>` : ''}\n            ${printSettings.showBarcode ? `\n              <img src=\"${barcodeImage}\" alt=\"Barcode\" class=\"barcode-image\" />\n              <div class=\"barcode-text\">${barcode}</div>\n            ` : ''}\n            ${printSettings.showPrice ? `<div class=\"product-price\">${product.price} ريال</div>` : ''}\n          </div>\n        `;\n      }\n    });\n\n    printContent += `\n          </div>\n        </body>\n      </html>\n    `;\n\n    printWindow.document.write(printContent);\n    printWindow.document.close();\n    \n    setTimeout(() => {\n      printWindow.print();\n    }, 500);\n\n    setMessage(`تم إنشاء ${selectedProducts.length * printSettings.copies} ملصق باركود للطباعة`);\n    setTimeout(() => setMessage(''), 3000);\n  };\n\n  const scanBarcode = () => {\n    setShowScanner(true);\n    // في التطبيق الحقيقي، ستستخدم مكتبة لقراءة الباركود من الكاميرا\n    // هنا سنحاكي عملية المسح\n    setTimeout(() => {\n      const randomProduct = products[Math.floor(Math.random() * products.length)];\n      if (randomProduct) {\n        const barcode = generateProductBarcode(randomProduct.id);\n        setScannedCode(barcode);\n        setMessage(`تم مسح الباركود: ${barcode} - ${randomProduct.name}`);\n      }\n      setShowScanner(false);\n    }, 2000);\n  };\n\n  const searchByBarcode = () => {\n    if (!scannedCode) {\n      setMessage('لا يوجد باركود ممسوح');\n      return;\n    }\n\n    const product = products.find(p => p.barcode === scannedCode);\n    if (product) {\n      setSearchTerm(product.name);\n      setMessage(`تم العثور على المنتج: ${product.name}`);\n    } else {\n      setMessage('لم يتم العثور على منتج بهذا الباركود');\n    }\n  };\n\n  const exportBarcodes = () => {\n    if (selectedProducts.length === 0) {\n      setMessage('يرجى اختيار منتج واحد على الأقل للتصدير');\n      return;\n    }\n\n    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));\n    const barcodeData = selectedProductsData.map(product => ({\n      id: product.id,\n      name: product.name,\n      barcode: generateProductBarcode(product.id),\n      price: product.price,\n      category: product.category\n    }));\n\n    const dataStr = JSON.stringify(barcodeData, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `barcodes-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n    URL.revokeObjectURL(url);\n\n    setMessage('تم تصدير بيانات الباركود بنجاح!');\n    setTimeout(() => setMessage(''), 3000);\n  };\n\n  const filteredProducts = products.filter(product =>\n    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    product.barcode?.includes(searchTerm)\n  );\n\n  return (\n    <div className=\"barcode-manager-container\">\n      <div className=\"barcode-manager-header\">\n        <h1>📊 إدارة الباركود</h1>\n        <p>إنشاء وطباعة ومسح الباركود للمنتجات</p>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>\n          {message}\n        </div>\n      )}\n\n      {/* Scanner Section */}\n      <div className=\"scanner-section\">\n        <div className=\"scanner-controls\">\n          <button onClick={scanBarcode} className=\"scan-btn\" disabled={showScanner}>\n            {showScanner ? '📷 جاري المسح...' : '📷 مسح باركود'}\n          </button>\n          {scannedCode && (\n            <div className=\"scanned-result\">\n              <span>الباركود الممسوح: <strong>{scannedCode}</strong></span>\n              <button onClick={searchByBarcode} className=\"search-scanned-btn\">\n                🔍 البحث\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Controls Section */}\n      <div className=\"barcode-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder=\"🔍 البحث في المنتجات أو الباركود...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"selection-controls\">\n          <button onClick={selectAllProducts} className=\"select-all-btn\">\n            ✅ تحديد الكل ({filteredProducts.length})\n          </button>\n          <button onClick={clearSelection} className=\"clear-selection-btn\">\n            ❌ إلغاء التحديد\n          </button>\n          <span className=\"selection-count\">\n            محدد: {selectedProducts.length} منتج\n          </span>\n        </div>\n      </div>\n\n      {/* Print Settings */}\n      <div className=\"print-settings\">\n        <h3>⚙️ إعدادات الطباعة</h3>\n        <div className=\"settings-grid\">\n          <div className=\"setting-group\">\n            <label>نوع الباركود</label>\n            <select\n              value={barcodeType}\n              onChange={(e) => setBarcodeType(e.target.value)}\n            >\n              {barcodeTypes.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"setting-group\">\n            <label>عرض الملصق (مم)</label>\n            <input\n              type=\"number\"\n              value={printSettings.labelWidth}\n              onChange={(e) => handlePrintSettingsChange('labelWidth', parseInt(e.target.value))}\n              min=\"20\"\n              max=\"100\"\n            />\n          </div>\n\n          <div className=\"setting-group\">\n            <label>ارتفاع الملصق (مم)</label>\n            <input\n              type=\"number\"\n              value={printSettings.labelHeight}\n              onChange={(e) => handlePrintSettingsChange('labelHeight', parseInt(e.target.value))}\n              min=\"15\"\n              max=\"50\"\n            />\n          </div>\n\n          <div className=\"setting-group\">\n            <label>حجم الخط</label>\n            <input\n              type=\"number\"\n              value={printSettings.fontSize}\n              onChange={(e) => handlePrintSettingsChange('fontSize', parseInt(e.target.value))}\n              min=\"6\"\n              max=\"16\"\n            />\n          </div>\n\n          <div className=\"setting-group\">\n            <label>عدد النسخ</label>\n            <input\n              type=\"number\"\n              value={printSettings.copies}\n              onChange={(e) => handlePrintSettingsChange('copies', parseInt(e.target.value))}\n              min=\"1\"\n              max=\"10\"\n            />\n          </div>\n\n          <div className=\"setting-group checkbox-group\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={printSettings.showProductName}\n                onChange={(e) => handlePrintSettingsChange('showProductName', e.target.checked)}\n              />\n              عرض اسم المنتج\n            </label>\n          </div>\n\n          <div className=\"setting-group checkbox-group\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={printSettings.showPrice}\n                onChange={(e) => handlePrintSettingsChange('showPrice', e.target.checked)}\n              />\n              عرض السعر\n            </label>\n          </div>\n\n          <div className=\"setting-group checkbox-group\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={printSettings.showBarcode}\n                onChange={(e) => handlePrintSettingsChange('showBarcode', e.target.checked)}\n              />\n              عرض الباركود\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"action-buttons\">\n        <button \n          onClick={printBarcodes} \n          className=\"print-btn\"\n          disabled={selectedProducts.length === 0}\n        >\n          🖨️ طباعة الباركود ({selectedProducts.length})\n        </button>\n        <button \n          onClick={exportBarcodes} \n          className=\"export-btn\"\n          disabled={selectedProducts.length === 0}\n        >\n          📤 تصدير البيانات\n        </button>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"products-grid\">\n        {filteredProducts.length === 0 ? (\n          <div className=\"no-products\">\n            <p>لا توجد منتجات مطابقة للبحث</p>\n          </div>\n        ) : (\n          filteredProducts.map(product => {\n            const barcode = generateProductBarcode(product.id);\n            const barcodeImage = generateBarcode(barcode, barcodeType);\n            const isSelected = selectedProducts.includes(product.id);\n\n            return (\n              <div \n                key={product.id} \n                className={`product-card ${isSelected ? 'selected' : ''}`}\n                onClick={() => handleProductSelect(product.id)}\n              >\n                <div className=\"product-header\">\n                  <h4>{product.name}</h4>\n                  <div className=\"selection-indicator\">\n                    {isSelected ? '✅' : '⭕'}\n                  </div>\n                </div>\n                \n                <div className=\"product-info\">\n                  <p><strong>الفئة:</strong> {product.category}</p>\n                  <p><strong>السعر:</strong> {product.price} ريال</p>\n                  <p><strong>المخزون:</strong> {product.stock}</p>\n                </div>\n\n                <div className=\"barcode-preview\">\n                  <img src={barcodeImage} alt=\"Barcode Preview\" />\n                  <div className=\"barcode-text\">{barcode}</div>\n                </div>\n\n                <div className=\"product-actions\">\n                  <button \n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedProducts([product.id]);\n                      printBarcodes();\n                    }}\n                    className=\"quick-print-btn\"\n                  >\n                    🖨️ طباعة سريعة\n                  </button>\n                </div>\n              </div>\n            );\n          })\n        )}\n      </div>\n\n      {/* Scanner Modal */}\n      {showScanner && (\n        <div className=\"modal-overlay\">\n          <div className=\"scanner-modal\">\n            <div className=\"scanner-content\">\n              <h3>📷 مسح الباركود</h3>\n              <div className=\"scanner-animation\">\n                <div className=\"scanner-line\"></div>\n                <p>وجه الكاميرا نحو الباركود...</p>\n              </div>\n              <button \n                onClick={() => setShowScanner(false)} \n                className=\"cancel-scan-btn\"\n              >\n                إلغاء\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default BarcodeManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC;IACjDkB,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM+B,QAAQ,GAAG7B,MAAM,CAAC,CAAC;EAEzB,MAAM8B,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAW,CAAC,EACvC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACjC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC/B;EAEDjC,SAAS,CAAC,MAAM;IACdkC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI;MACF,MAAMC,YAAY,GAAGjC,QAAQ,CAACkC,WAAW,CAAC,CAAC;MAC3C5B,WAAW,CAAC2B,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CZ,UAAU,CAAC,uBAAuB,CAAC;IACrC;EACF,CAAC;EAED,MAAMc,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,GAAG,SAAS,KAAK;IAClD;IACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCJ,MAAM,CAACK,KAAK,GAAG,GAAG;IAClBL,MAAM,CAACM,MAAM,GAAG,EAAE;;IAElB;IACAH,GAAG,CAACI,SAAS,GAAG,SAAS;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;QACvBP,GAAG,CAACQ,QAAQ,CAACH,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;MAC/B;IACF;;IAEA;IACAL,GAAG,CAACI,SAAS,GAAG,SAAS;IACzBJ,GAAG,CAACS,IAAI,GAAG,YAAY;IACvBT,GAAG,CAACU,SAAS,GAAG,QAAQ;IACxBV,GAAG,CAACW,QAAQ,CAAChB,IAAI,EAAEE,MAAM,CAACK,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;IAExC,OAAOL,MAAM,CAACe,SAAS,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMC,sBAAsB,GAAIC,SAAS,IAAK;IAC5C,MAAMC,OAAO,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,EAAE,OAAO,EAAE;;IAEvB;IACA,IAAII,OAAO,GAAGJ,OAAO,CAACI,OAAO;IAC7B,IAAI,CAACA,OAAO,EAAE;MACZ;MACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACvC,MAAMC,WAAW,GAAGT,OAAO,CAACG,EAAE,CAACK,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1DN,OAAO,GAAG,GAAGK,WAAW,GAAGJ,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEhD;MACArE,QAAQ,CAACsE,aAAa,CAACb,SAAS,EAAE;QAAEK;MAAQ,CAAC,CAAC;MAC9C9B,YAAY,CAAC,CAAC;IAChB;IAEA,OAAO8B,OAAO;EAChB,CAAC;EAED,MAAMS,mBAAmB,GAAId,SAAS,IAAK;IACzCjD,mBAAmB,CAACgE,IAAI,IAAI;MAC1B,IAAIA,IAAI,CAACC,QAAQ,CAAChB,SAAS,CAAC,EAAE;QAC5B,OAAOe,IAAI,CAACE,MAAM,CAACb,EAAE,IAAIA,EAAE,KAAKJ,SAAS,CAAC;MAC5C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGe,IAAI,EAAEf,SAAS,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,kBAAkB,GAAGC,gBAAgB,CAACC,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC;IAC1DrD,mBAAmB,CAACoE,kBAAkB,CAAC;EACzC,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BvE,mBAAmB,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMwE,yBAAyB,GAAGA,CAACC,OAAO,EAAEnD,KAAK,KAAK;IACpDhB,gBAAgB,CAAC0D,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACS,OAAO,GAAGnD;IACb,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI3E,gBAAgB,CAAC4E,MAAM,KAAK,CAAC,EAAE;MACjC5D,UAAU,CAAC,yCAAyC,CAAC;MACrD;IACF;;IAEA;IACA,MAAM6D,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7C,MAAMC,oBAAoB,GAAGlF,QAAQ,CAACqE,MAAM,CAACd,CAAC,IAAIrD,gBAAgB,CAACkE,QAAQ,CAACb,CAAC,CAACC,EAAE,CAAC,CAAC;IAElF,IAAI2B,YAAY,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D3E,aAAa,CAACE,UAAU;AACvF;AACA;AACA;AACA;AACA,uBAAuBF,aAAa,CAACE,UAAU;AAC/C,wBAAwBF,aAAa,CAACG,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BH,aAAa,CAACI,QAAQ,GAAG,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BJ,aAAa,CAACI,QAAQ,GAAG,CAAC;AACrD;AACA;AACA;AACA;AACA,2BAA2BJ,aAAa,CAACI,QAAQ,GAAG,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAEDsE,oBAAoB,CAACE,OAAO,CAAC/B,OAAO,IAAI;MACtC,MAAMI,OAAO,GAAGN,sBAAsB,CAACE,OAAO,CAACG,EAAE,CAAC;MAClD,MAAM6B,YAAY,GAAGrD,eAAe,CAACyB,OAAO,EAAEnD,WAAW,CAAC;MAE1D,KAAK,IAAIgF,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG9E,aAAa,CAACQ,MAAM,EAAEsE,IAAI,EAAE,EAAE;QACtDH,YAAY,IAAI;AACxB;AACA,cAAc3E,aAAa,CAACK,eAAe,GAAG,6BAA6BwC,OAAO,CAACkC,IAAI,QAAQ,GAAG,EAAE;AACpG,cAAc/E,aAAa,CAACO,WAAW,GAAG;AAC1C,0BAA0BsE,YAAY;AACtC,0CAA0C5B,OAAO;AACjD,aAAa,GAAG,EAAE;AAClB,cAAcjD,aAAa,CAACM,SAAS,GAAG,8BAA8BuC,OAAO,CAACmC,KAAK,aAAa,GAAG,EAAE;AACrG;AACA,SAAS;MACH;IACF,CAAC,CAAC;IAEFL,YAAY,IAAI;AACpB;AACA;AACA;AACA,KAAK;IAEDJ,WAAW,CAAC3C,QAAQ,CAACqD,KAAK,CAACN,YAAY,CAAC;IACxCJ,WAAW,CAAC3C,QAAQ,CAACsD,KAAK,CAAC,CAAC;IAE5BC,UAAU,CAAC,MAAM;MACfZ,WAAW,CAACa,KAAK,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP1E,UAAU,CAAC,YAAYhB,gBAAgB,CAAC4E,MAAM,GAAGtE,aAAa,CAACQ,MAAM,sBAAsB,CAAC;IAC5F2E,UAAU,CAAC,MAAMzE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAM2E,WAAW,GAAGA,CAAA,KAAM;IACxBzE,cAAc,CAAC,IAAI,CAAC;IACpB;IACA;IACAuE,UAAU,CAAC,MAAM;MACf,MAAMG,aAAa,GAAG9F,QAAQ,CAAC4C,IAAI,CAACmD,KAAK,CAACnD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG7C,QAAQ,CAAC8E,MAAM,CAAC,CAAC;MAC3E,IAAIgB,aAAa,EAAE;QACjB,MAAMrC,OAAO,GAAGN,sBAAsB,CAAC2C,aAAa,CAACtC,EAAE,CAAC;QACxDlC,cAAc,CAACmC,OAAO,CAAC;QACvBvC,UAAU,CAAC,oBAAoBuC,OAAO,MAAMqC,aAAa,CAACP,IAAI,EAAE,CAAC;MACnE;MACAnE,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM4E,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC3E,WAAW,EAAE;MAChBH,UAAU,CAAC,sBAAsB,CAAC;MAClC;IACF;IAEA,MAAMmC,OAAO,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,OAAO,KAAKpC,WAAW,CAAC;IAC7D,IAAIgC,OAAO,EAAE;MACXhD,aAAa,CAACgD,OAAO,CAACkC,IAAI,CAAC;MAC3BrE,UAAU,CAAC,yBAAyBmC,OAAO,CAACkC,IAAI,EAAE,CAAC;IACrD,CAAC,MAAM;MACLrE,UAAU,CAAC,sCAAsC,CAAC;IACpD;EACF,CAAC;EAED,MAAM+E,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI/F,gBAAgB,CAAC4E,MAAM,KAAK,CAAC,EAAE;MACjC5D,UAAU,CAAC,yCAAyC,CAAC;MACrD;IACF;IAEA,MAAMgE,oBAAoB,GAAGlF,QAAQ,CAACqE,MAAM,CAACd,CAAC,IAAIrD,gBAAgB,CAACkE,QAAQ,CAACb,CAAC,CAACC,EAAE,CAAC,CAAC;IAClF,MAAM0C,WAAW,GAAGhB,oBAAoB,CAACT,GAAG,CAACpB,OAAO,KAAK;MACvDG,EAAE,EAAEH,OAAO,CAACG,EAAE;MACd+B,IAAI,EAAElC,OAAO,CAACkC,IAAI;MAClB9B,OAAO,EAAEN,sBAAsB,CAACE,OAAO,CAACG,EAAE,CAAC;MAC3CgC,KAAK,EAAEnC,OAAO,CAACmC,KAAK;MACpBW,QAAQ,EAAE9C,OAAO,CAAC8C;IACpB,CAAC,CAAC,CAAC;IAEH,MAAMC,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IACpD,MAAMK,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAElE,IAAI,EAAE;IAAmB,CAAC,CAAC;IAClE,MAAMuE,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAAC;IACzC,MAAMK,IAAI,GAAGxE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCuE,IAAI,CAACC,IAAI,GAAGJ,GAAG;IACfG,IAAI,CAACE,QAAQ,GAAG,YAAY,IAAInD,IAAI,CAAC,CAAC,CAACoD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IACzEJ,IAAI,CAACK,KAAK,CAAC,CAAC;IACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAExBvF,UAAU,CAAC,iCAAiC,CAAC;IAC7CyE,UAAU,CAAC,MAAMzE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAMsD,gBAAgB,GAAGxE,QAAQ,CAACqE,MAAM,CAAChB,OAAO;IAAA,IAAA8D,gBAAA;IAAA,OAC9C9D,OAAO,CAACkC,IAAI,CAAC6B,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAAChE,UAAU,CAACgH,WAAW,CAAC,CAAC,CAAC,MAAAD,gBAAA,GAC7D9D,OAAO,CAACI,OAAO,cAAA0D,gBAAA,uBAAfA,gBAAA,CAAiB/C,QAAQ,CAAChE,UAAU,CAAC;EAAA,CACvC,CAAC;EAED,oBACEP,OAAA;IAAKwH,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCzH,OAAA;MAAKwH,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCzH,OAAA;QAAAyH,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B7H,OAAA;QAAAyH,QAAA,EAAG;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,EAELzG,OAAO,iBACNpB,OAAA;MAAKwH,SAAS,EAAE,WAAWpG,OAAO,CAACmD,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAAkD,QAAA,EACxErG;IAAO;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD7H,OAAA;MAAKwH,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BzH,OAAA;QAAKwH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzH,OAAA;UAAQ8H,OAAO,EAAE9B,WAAY;UAACwB,SAAS,EAAC,UAAU;UAACO,QAAQ,EAAEzG,WAAY;UAAAmG,QAAA,EACtEnG,WAAW,GAAG,kBAAkB,GAAG;QAAe;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EACRrG,WAAW,iBACVxB,OAAA;UAAKwH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzH,OAAA;YAAAyH,QAAA,GAAM,+FAAkB,eAAAzH,OAAA;cAAAyH,QAAA,EAASjG;YAAW;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7D7H,OAAA;YAAQ8H,OAAO,EAAE3B,eAAgB;YAACqB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7H,OAAA;MAAKwH,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzH,OAAA;QAAKwH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BzH,OAAA;UACEqC,IAAI,EAAC,MAAM;UACX2F,WAAW,EAAC,4KAAqC;UACjDpG,KAAK,EAAErB,UAAW;UAClB0H,QAAQ,EAAGC,CAAC,IAAK1H,aAAa,CAAC0H,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAE;UAC/C4F,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7H,OAAA;QAAKwH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzH,OAAA;UAAQ8H,OAAO,EAAErD,iBAAkB;UAAC+C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAAC,kEAC/C,EAAC9C,gBAAgB,CAACM,MAAM,EAAC,GACzC;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7H,OAAA;UAAQ8H,OAAO,EAAEjD,cAAe;UAAC2C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7H,OAAA;UAAMwH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,4BAC1B,EAACpH,gBAAgB,CAAC4E,MAAM,EAAC,2BACjC;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7H,OAAA;MAAKwH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzH,OAAA;QAAAyH,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B7H,OAAA;QAAKwH,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzH,OAAA;UAAKwH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzH,OAAA;YAAAyH,QAAA,EAAO;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3B7H,OAAA;YACE4B,KAAK,EAAEnB,WAAY;YACnBwH,QAAQ,EAAGC,CAAC,IAAKxH,cAAc,CAACwH,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAE;YAAA6F,QAAA,EAE/C9F,YAAY,CAACiD,GAAG,CAACvC,IAAI,iBACpBrC,OAAA;cAAyB4B,KAAK,EAAES,IAAI,CAACT,KAAM;cAAA6F,QAAA,EACxCpF,IAAI,CAACR;YAAK,GADAQ,IAAI,CAACT,KAAK;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzH,OAAA;YAAAyH,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9B7H,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbT,KAAK,EAAEjB,aAAa,CAACE,UAAW;YAChCoH,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,YAAY,EAAEsD,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC,CAAE;YACnFyG,GAAG,EAAC,IAAI;YACRC,GAAG,EAAC;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzH,OAAA;YAAAyH,QAAA,EAAO;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC7H,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbT,KAAK,EAAEjB,aAAa,CAACG,WAAY;YACjCmH,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,aAAa,EAAEsD,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC,CAAE;YACpFyG,GAAG,EAAC,IAAI;YACRC,GAAG,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzH,OAAA;YAAAyH,QAAA,EAAO;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvB7H,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbT,KAAK,EAAEjB,aAAa,CAACI,QAAS;YAC9BkH,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,UAAU,EAAEsD,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC,CAAE;YACjFyG,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzH,OAAA;YAAAyH,QAAA,EAAO;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxB7H,OAAA;YACEqC,IAAI,EAAC,QAAQ;YACbT,KAAK,EAAEjB,aAAa,CAACQ,MAAO;YAC5B8G,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,QAAQ,EAAEsD,QAAQ,CAACF,CAAC,CAACC,MAAM,CAACvG,KAAK,CAAC,CAAE;YAC/EyG,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CzH,OAAA;YAAAyH,QAAA,gBACEzH,OAAA;cACEqC,IAAI,EAAC,UAAU;cACfkG,OAAO,EAAE5H,aAAa,CAACK,eAAgB;cACvCiH,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,iBAAiB,EAAEoD,CAAC,CAACC,MAAM,CAACI,OAAO;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,8EAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CzH,OAAA;YAAAyH,QAAA,gBACEzH,OAAA;cACEqC,IAAI,EAAC,UAAU;cACfkG,OAAO,EAAE5H,aAAa,CAACM,SAAU;cACjCgH,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,WAAW,EAAEoD,CAAC,CAACC,MAAM,CAACI,OAAO;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,qDAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7H,OAAA;UAAKwH,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CzH,OAAA;YAAAyH,QAAA,gBACEzH,OAAA;cACEqC,IAAI,EAAC,UAAU;cACfkG,OAAO,EAAE5H,aAAa,CAACO,WAAY;cACnC+G,QAAQ,EAAGC,CAAC,IAAKpD,yBAAyB,CAAC,aAAa,EAAEoD,CAAC,CAACC,MAAM,CAACI,OAAO;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,uEAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7H,OAAA;MAAKwH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzH,OAAA;QACE8H,OAAO,EAAE9C,aAAc;QACvBwC,SAAS,EAAC,WAAW;QACrBO,QAAQ,EAAE1H,gBAAgB,CAAC4E,MAAM,KAAK,CAAE;QAAAwC,QAAA,GACzC,sGACqB,EAACpH,gBAAgB,CAAC4E,MAAM,EAAC,GAC/C;MAAA;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7H,OAAA;QACE8H,OAAO,EAAE1B,cAAe;QACxBoB,SAAS,EAAC,YAAY;QACtBO,QAAQ,EAAE1H,gBAAgB,CAAC4E,MAAM,KAAK,CAAE;QAAAwC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7H,OAAA;MAAKwH,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B9C,gBAAgB,CAACM,MAAM,KAAK,CAAC,gBAC5BjF,OAAA;QAAKwH,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BzH,OAAA;UAAAyH,QAAA,EAAG;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,GAENlD,gBAAgB,CAACC,GAAG,CAACpB,OAAO,IAAI;QAC9B,MAAMI,OAAO,GAAGN,sBAAsB,CAACE,OAAO,CAACG,EAAE,CAAC;QAClD,MAAM6B,YAAY,GAAGrD,eAAe,CAACyB,OAAO,EAAEnD,WAAW,CAAC;QAC1D,MAAM+H,UAAU,GAAGnI,gBAAgB,CAACkE,QAAQ,CAACf,OAAO,CAACG,EAAE,CAAC;QAExD,oBACE3D,OAAA;UAEEwH,SAAS,EAAE,gBAAgBgB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAC1DV,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAACb,OAAO,CAACG,EAAE,CAAE;UAAA8D,QAAA,gBAE/CzH,OAAA;YAAKwH,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzH,OAAA;cAAAyH,QAAA,EAAKjE,OAAO,CAACkC;YAAI;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvB7H,OAAA;cAAKwH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjCe,UAAU,GAAG,GAAG,GAAG;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7H,OAAA;YAAKwH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzH,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrE,OAAO,CAAC8C,QAAQ;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD7H,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrE,OAAO,CAACmC,KAAK,EAAC,2BAAK;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnD7H,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrE,OAAO,CAACiF,KAAK;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEN7H,OAAA;YAAKwH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzH,OAAA;cAAK0I,GAAG,EAAElD,YAAa;cAACmD,GAAG,EAAC;YAAiB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD7H,OAAA;cAAKwH,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAE7D;YAAO;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEN7H,OAAA;YAAKwH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BzH,OAAA;cACE8H,OAAO,EAAGI,CAAC,IAAK;gBACdA,CAAC,CAACU,eAAe,CAAC,CAAC;gBACnBtI,mBAAmB,CAAC,CAACkD,OAAO,CAACG,EAAE,CAAC,CAAC;gBACjCqB,aAAa,CAAC,CAAC;cACjB,CAAE;cACFwC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAjCDrE,OAAO,CAACG,EAAE;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCZ,CAAC;MAEV,CAAC;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvG,WAAW,iBACVtB,OAAA;MAAKwH,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BzH,OAAA;QAAKwH,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BzH,OAAA;UAAKwH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzH,OAAA;YAAAyH,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB7H,OAAA;YAAKwH,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzH,OAAA;cAAKwH,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC7H,OAAA;cAAAyH,QAAA,EAAG;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACN7H,OAAA;YACE8H,OAAO,EAAEA,CAAA,KAAMvG,cAAc,CAAC,KAAK,CAAE;YACrCiG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3H,EAAA,CAnhBID,cAAc;AAAA4I,EAAA,GAAd5I,cAAc;AAqhBpB,eAAeA,cAAc;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}