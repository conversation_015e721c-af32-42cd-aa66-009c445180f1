import React, { useState } from 'react';
import Login from './components/Login/Login';
import Dashboard from './components/Dashboard/Dashboard';
import POS from './components/POS/POS';
import Reports from './components/Reports/Reports';
import Products from './components/Products/Products';
import Customers from './components/Customers/Customers';
import Suppliers from './components/Suppliers/Suppliers';
import Accounting from './components/Accounting/Accounting';
import Settings from './components/Settings/Settings';
import Payments from './components/Payments/Payments';
import Inventory from './components/Inventory/Inventory';
import Employees from './components/Employees/Employees';
import Backup from './components/Backup/Backup';
import Notifications from './components/Notifications/Notifications';
import JournalEntries from './components/JournalEntries/JournalEntries';
import Returns from './components/Returns/Returns';
import BarcodeManager from './components/Barcode/BarcodeManager';
import Quotations from './components/Quotations/Quotations';
import Vouchers from './components/Vouchers/Vouchers';
import AdvancedReports from './components/AdvancedReports/AdvancedReports';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('home'); // 'home', 'login', 'dashboard', 'pos'
  const [user, setUser] = useState(null);
  const [showMore, setShowMore] = useState(false);

  const handleLoginClick = () => {
    setCurrentView('login');
  };

  const handleLogin = (userData) => {
    setUser(userData);
    setCurrentView('dashboard');
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentView('home');
  };

  const handleBackToHome = () => {
    setCurrentView('home');
  };

  const handleNavigate = (view) => {
    setCurrentView(view);
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
  };

  const handleMoreClick = () => {
    setShowMore(!showMore);
  };

  // عرض صفحة تسجيل الدخول
  if (currentView === 'login') {
    return <Login onLogin={handleLogin} onBack={handleBackToHome} />;
  }

  // عرض لوحة التحكم
  if (currentView === 'dashboard' && user) {
    return <Dashboard user={user} onLogout={handleLogout} onNavigate={handleNavigate} />;
  }

  // عرض نقطة البيع
  if (currentView === 'pos' && user) {
    return <POS user={user} onBack={handleBackToDashboard} />;
  }

  // عرض التقارير
  if (currentView === 'reports' && user) {
    return <Reports user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة المنتجات
  if (currentView === 'products' && user) {
    return <Products user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة العملاء
  if (currentView === 'customers' && user) {
    return <Customers user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة الموردين
  if (currentView === 'suppliers' && user) {
    return <Suppliers user={user} onBack={handleBackToDashboard} />;
  }

  // عرض المحاسبة المالية
  if (currentView === 'accounting' && user) {
    return <Accounting user={user} onBack={handleBackToDashboard} />;
  }

  // عرض الإعدادات
  if (currentView === 'settings' && user) {
    return <Settings user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة المدفوعات
  if (currentView === 'payments' && user) {
    return <Payments user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة المخزون
  if (currentView === 'inventory' && user) {
    return <Inventory user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة الموظفين
  if (currentView === 'employees' && user) {
    return <Employees user={user} onBack={handleBackToDashboard} />;
  }

  // عرض النسخ الاحتياطي
  if (currentView === 'backup' && user) {
    return <Backup user={user} onBack={handleBackToDashboard} />;
  }

  // عرض الإشعارات
  if (currentView === 'notifications' && user) {
    return <Notifications user={user} onBack={handleBackToDashboard} />;
  }

  // عرض القيود اليومية
  if (currentView === 'journal-entries' && user) {
    return <JournalEntries user={user} onBack={handleBackToDashboard} />;
  }

  // عرض المرتجعات
  if (currentView === 'returns' && user) {
    return <Returns user={user} onBack={handleBackToDashboard} />;
  }

  // عرض إدارة الباركود
  if (currentView === 'barcode' && user) {
    return <BarcodeManager user={user} onBack={handleBackToDashboard} />;
  }

  // عرض عروض الأسعار
  if (currentView === 'quotations' && user) {
    return <Quotations user={user} onBack={handleBackToDashboard} />;
  }

  // عرض السندات
  if (currentView === 'vouchers' && user) {
    return <Vouchers user={user} onBack={handleBackToDashboard} />;
  }

  // عرض التقارير المتقدمة
  if (currentView === 'advanced-reports' && user) {
    return <AdvancedReports user={user} onBack={handleBackToDashboard} />;
  }

  // عرض الصفحة الرئيسية
  return (
    <div className="app">
      <div className="main-card">
        <h1 className="main-title">
          🧮 نظام المحاسبة السعودي
        </h1>

        <p className="main-description">
          نظام محاسبة متكامل للشركات السعودية مع دعم ضريبة القيمة المضافة والفواتير الإلكترونية
        </p>

        <div className="buttons-container">
          <button
            className="btn btn-primary"
            onClick={handleLoginClick}
          >
            🔐 تسجيل الدخول
          </button>
          <button
            className="btn btn-secondary"
            onClick={handleMoreClick}
          >
            ℹ️ معرفة المزيد
          </button>
        </div>

        <div className="features-section">
          <h4 className="features-title">✅ المميزات الرئيسية:</h4>
          <ul className="features-list">
            <li>💰 نقطة بيع متقدمة</li>
            <li>📦 إدارة المخزون</li>
            <li>📊 التقارير المالية</li>
            <li>👥 إدارة العملاء</li>
            <li>🇸🇦 مطابق للمتطلبات السعودية</li>
          </ul>
        </div>

        {showMore && (
          <div style={{
            marginTop: '20px',
            padding: '20px',
            background: '#f6ffed',
            borderRadius: '8px',
            border: '1px solid #b7eb8f',
            animation: 'fadeIn 0.5s ease-in'
          }}>
            <h4 style={{ color: '#52c41a', marginBottom: '15px' }}>📋 تفاصيل إضافية:</h4>
            <div style={{ textAlign: 'right', color: '#666' }}>
              <p><strong>🏢 للشركات:</strong> مناسب للشركات الصغيرة والمتوسطة</p>
              <p><strong>💳 طرق الدفع:</strong> نقدي، بطاقة ائتمان، تحويل بنكي</p>
              <p><strong>📱 المنصات:</strong> ويب، موبايل، تابلت</p>
              <p><strong>🔒 الأمان:</strong> تشفير SSL، نسخ احتياطية يومية</p>
              <p><strong>📞 الدعم:</strong> دعم فني 24/7 باللغة العربية</p>
            </div>
          </div>
        )}

        {showMore && (
          <div className="more-info">
            <h4>📋 تفاصيل إضافية:</h4>
            <p><strong>🏢 للشركات:</strong> مناسب للشركات الصغيرة والمتوسطة</p>
            <p><strong>💳 طرق الدفع:</strong> نقدي، بطاقة ائتمان، تحويل بنكي</p>
            <p><strong>📱 المنصات:</strong> ويب، موبايل، تابلت</p>
            <p><strong>🔒 الأمان:</strong> تشفير SSL، نسخ احتياطية يومية</p>
            <p><strong>📞 الدعم:</strong> دعم فني 24/7 باللغة العربية</p>
          </div>
        )}

        <div className="status-section">
          <h4 className="status-title">🎉 حالة التطوير:</h4>
          <p className="status-text">
            ✅ React يعمل بنجاح<br/>
            ✅ التصميم العربي جاهز<br/>
            ✅ الأزرار تفاعلية مع تأثيرات جميلة<br/>
            ✅ CSS متقدم مع رسوم متحركة<br/>
            📋 جاري تطوير نظام تسجيل الدخول
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
