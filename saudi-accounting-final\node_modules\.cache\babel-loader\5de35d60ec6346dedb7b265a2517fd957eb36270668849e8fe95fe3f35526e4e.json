{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Reports\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Reports.css';\nimport database from '../../utils/database';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = ({\n  user,\n  onBack\n}) => {\n  _s();\n  var _todayStats$totalSale2, _todayStats$totalVAT2, _todayStats$averageIn, _monthStats$totalSale2, _monthStats$totalVAT2, _monthStats$averageIn;\n  const [activeTab, setActiveTab] = useState('daily');\n  const [todayStats, setTodayStats] = useState({});\n  const [monthStats, setMonthStats] = useState({});\n  const [recentInvoices, setRecentInvoices] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [topProducts, setTopProducts] = useState([]);\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n\n  // تحميل البيانات عند تحميل الصفحة\n  useEffect(() => {\n    loadReportsData();\n  }, [selectedDate]);\n  const loadReportsData = () => {\n    try {\n      // إحصائيات اليوم\n      const todayData = database.getTodayStats();\n      setTodayStats(todayData);\n\n      // إحصائيات الشهر\n      const monthData = database.getMonthStats();\n      setMonthStats(monthData);\n\n      // آخر الفواتير\n      const allInvoices = database.getInvoices();\n      const recent = allInvoices.slice(-10).reverse();\n      setRecentInvoices(recent);\n\n      // المنتجات منخفضة المخزون\n      const lowStock = database.getLowStockProducts(5);\n      setLowStockProducts(lowStock);\n\n      // أفضل المنتجات مبيعاً\n      const topSelling = database.getTopSellingProducts(5);\n      setTopProducts(topSelling);\n    } catch (error) {\n      console.error('خطأ في تحميل التقارير:', error);\n    }\n  };\n\n  // تصدير التقارير\n  const exportReports = () => {\n    try {\n      const data = database.exportData();\n      const blob = new Blob([JSON.stringify(data, null, 2)], {\n        type: 'application/json'\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `saudi-accounting-backup-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      alert('✅ تم تصدير البيانات بنجاح!');\n    } catch (error) {\n      console.error('خطأ في تصدير البيانات:', error);\n      alert('❌ فشل في تصدير البيانات');\n    }\n  };\n\n  // طباعة التقرير\n  const printReport = () => {\n    var _todayStats$totalSale, _todayStats$totalVAT, _monthStats$totalSale, _monthStats$totalVAT;\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>تقرير المبيعات - ${new Date().toLocaleDateString('ar-SA')}</title>\n          <style>\n            body { font-family: Arial, sans-serif; margin: 20px; }\n            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }\n            .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }\n            .stat-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }\n            table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            th { background-color: #f5f5f5; }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>تقرير المبيعات اليومي</h2>\n            <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>\n          </div>\n          \n          <div class=\"stats\">\n            <div class=\"stat-card\">\n              <h3>إحصائيات اليوم</h3>\n              <p>إجمالي المبيعات: ${((_todayStats$totalSale = todayStats.totalSales) === null || _todayStats$totalSale === void 0 ? void 0 : _todayStats$totalSale.toFixed(2)) || 0} ريال</p>\n              <p>إجمالي الضرائب: ${((_todayStats$totalVAT = todayStats.totalVAT) === null || _todayStats$totalVAT === void 0 ? void 0 : _todayStats$totalVAT.toFixed(2)) || 0} ريال</p>\n              <p>عدد الفواتير: ${todayStats.totalInvoices || 0}</p>\n            </div>\n            <div class=\"stat-card\">\n              <h3>إحصائيات الشهر</h3>\n              <p>إجمالي المبيعات: ${((_monthStats$totalSale = monthStats.totalSales) === null || _monthStats$totalSale === void 0 ? void 0 : _monthStats$totalSale.toFixed(2)) || 0} ريال</p>\n              <p>إجمالي الضرائب: ${((_monthStats$totalVAT = monthStats.totalVAT) === null || _monthStats$totalVAT === void 0 ? void 0 : _monthStats$totalVAT.toFixed(2)) || 0} ريال</p>\n              <p>عدد الفواتير: ${monthStats.totalInvoices || 0}</p>\n            </div>\n          </div>\n\n          <h3>آخر الفواتير</h3>\n          <table>\n            <thead>\n              <tr>\n                <th>رقم الفاتورة</th>\n                <th>العميل</th>\n                <th>الإجمالي</th>\n                <th>التاريخ</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${recentInvoices.map(invoice => `\n                <tr>\n                  <td>${invoice.id}</td>\n                  <td>${invoice.customer}</td>\n                  <td>${invoice.total.toFixed(2)} ريال</td>\n                  <td>${new Date(invoice.createdAt).toLocaleDateString('ar-SA')}</td>\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reports-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reports-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: \"\\u2190 \\u0627\\u0644\\u0639\\u0648\\u062F\\u0629 \\u0644\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0648\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: printReport,\n            className: \"action-btn print-btn\",\n            children: \"\\uD83D\\uDDA8\\uFE0F \\u0637\\u0628\\u0627\\u0639\\u0629 \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: exportReports,\n            className: \"action-btn export-btn\",\n            children: \"\\uD83D\\uDCE4 \\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reports-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'daily' ? 'active' : ''}`,\n        onClick: () => setActiveTab('daily'),\n        children: \"\\uD83D\\uDCC5 \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631 \\u0627\\u0644\\u064A\\u0648\\u0645\\u064A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'monthly' ? 'active' : ''}`,\n        onClick: () => setActiveTab('monthly'),\n        children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0634\\u0647\\u0631\\u064A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'products' ? 'active' : ''}`,\n        onClick: () => setActiveTab('products'),\n        children: \"\\uD83D\\uDCE6 \\u062A\\u0642\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'invoices' ? 'active' : ''}`,\n        onClick: () => setActiveTab('invoices'),\n        children: \"\\uD83E\\uDDFE \\u0633\\u062C\\u0644 \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reports-content\",\n      children: [activeTab === 'daily' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"daily-report\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card sales\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_todayStats$totalSale2 = todayStats.totalSales) === null || _todayStats$totalSale2 === void 0 ? void 0 : _todayStats$totalSale2.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card vat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83E\\uDDFE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u0627\\u0626\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_todayStats$totalVAT2 = todayStats.totalVAT) === null || _todayStats$totalVAT2 === void 0 ? void 0 : _todayStats$totalVAT2.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card invoices\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: todayStats.totalInvoices || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card average\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_todayStats$averageIn = todayStats.averageInvoice) === null || _todayStats$averageIn === void 0 ? void 0 : _todayStats$averageIn.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), activeTab === 'monthly' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"monthly-report\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card sales\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u0634\\u0647\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_monthStats$totalSale2 = monthStats.totalSales) === null || _monthStats$totalSale2 === void 0 ? void 0 : _monthStats$totalSale2.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card vat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83E\\uDDFE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u0627\\u0626\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_monthStats$totalVAT2 = monthStats.totalVAT) === null || _monthStats$totalVAT2 === void 0 ? void 0 : _monthStats$totalVAT2.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card invoices\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: monthStats.totalInvoices || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-card average\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"stat-value\",\n                children: [((_monthStats$averageIn = monthStats.averageInvoice) === null || _monthStats$averageIn === void 0 ? void 0 : _monthStats$averageIn.toFixed(2)) || 0, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), activeTab === 'products' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-report\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD25 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0628\\u064A\\u0639\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"products-list\",\n            children: topProducts.length > 0 ? topProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rank\",\n                children: [\"#\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"name\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"quantity\",\n                children: [\"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629: \", product.quantity]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"revenue\",\n                children: [product.revenue.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u26A0\\uFE0F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0646\\u062E\\u0641\\u0636\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"products-list\",\n            children: lowStockProducts.length > 0 ? lowStockProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-item low-stock\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"name\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stock\",\n                children: [\"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646: \", product.stock]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price\",\n                children: [product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062A\\u0648\\u0641\\u0631\\u0629 \\u0628\\u0643\\u0645\\u064A\\u0627\\u062A \\u0643\\u0627\\u0641\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), activeTab === 'invoices' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"invoices-report\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"invoices-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCB \\u0633\\u062C\\u0644 \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: selectedDate,\n            onChange: e => setSelectedDate(e.target.value),\n            className: \"date-filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"invoices-list\",\n          children: recentInvoices.length > 0 ? recentInvoices.map(invoice => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invoice-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"invoice-id\",\n                children: [\"#\", invoice.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"invoice-date\",\n                children: new Date(invoice.createdAt).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"customer\",\n                children: [\"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644: \", invoice.customer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"total\",\n                children: [\"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A: \", invoice.total.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"payment\",\n                children: [\"\\u0627\\u0644\\u062F\\u0641\\u0639: \", invoice.paymentMethod]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this)]\n          }, invoice.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-data\",\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"+XjSlezgnyOsfRTMcmqj67Z+mX8=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsxDEV", "_jsxDEV", "Reports", "user", "onBack", "_s", "_todayStats$totalSale2", "_todayStats$totalVAT2", "_todayStats$averageIn", "_monthStats$totalSale2", "_monthStats$totalVAT2", "_monthStats$averageIn", "activeTab", "setActiveTab", "todayStats", "setTodayStats", "monthStats", "setMonthStats", "recentInvoices", "setRecentInvoices", "lowStockProducts", "setLowStockProducts", "topProducts", "setTopProducts", "selectedDate", "setSelectedDate", "Date", "toISOString", "split", "loadReportsData", "todayData", "getTodayStats", "monthData", "getMonthStats", "allInvoices", "getInvoices", "recent", "slice", "reverse", "lowStock", "getLowStockProducts", "topSelling", "getTopSellingProducts", "error", "console", "exportReports", "data", "exportData", "blob", "Blob", "JSON", "stringify", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "alert", "printReport", "_todayStats$totalSale", "_todayStats$totalVAT", "_monthStats$totalSale", "_monthStats$totalVAT", "printWindow", "window", "open", "write", "toLocaleDateString", "totalSales", "toFixed", "totalVAT", "totalInvoices", "map", "invoice", "id", "customer", "total", "createdAt", "join", "close", "print", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "averageInvoice", "length", "product", "index", "name", "quantity", "revenue", "stock", "price", "value", "onChange", "e", "target", "paymentMethod", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Reports/Reports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Reports.css';\nimport database from '../../utils/database';\n\nconst Reports = ({ user, onBack }) => {\n  const [activeTab, setActiveTab] = useState('daily');\n  const [todayStats, setTodayStats] = useState({});\n  const [monthStats, setMonthStats] = useState({});\n  const [recentInvoices, setRecentInvoices] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [topProducts, setTopProducts] = useState([]);\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n\n  // تحميل البيانات عند تحميل الصفحة\n  useEffect(() => {\n    loadReportsData();\n  }, [selectedDate]);\n\n  const loadReportsData = () => {\n    try {\n      // إحصائيات اليوم\n      const todayData = database.getTodayStats();\n      setTodayStats(todayData);\n\n      // إحصائيات الشهر\n      const monthData = database.getMonthStats();\n      setMonthStats(monthData);\n\n      // آخر الفواتير\n      const allInvoices = database.getInvoices();\n      const recent = allInvoices.slice(-10).reverse();\n      setRecentInvoices(recent);\n\n      // المنتجات منخفضة المخزون\n      const lowStock = database.getLowStockProducts(5);\n      setLowStockProducts(lowStock);\n\n      // أفضل المنتجات مبيعاً\n      const topSelling = database.getTopSellingProducts(5);\n      setTopProducts(topSelling);\n    } catch (error) {\n      console.error('خطأ في تحميل التقارير:', error);\n    }\n  };\n\n  // تصدير التقارير\n  const exportReports = () => {\n    try {\n      const data = database.exportData();\n      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `saudi-accounting-backup-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      alert('✅ تم تصدير البيانات بنجاح!');\n    } catch (error) {\n      console.error('خطأ في تصدير البيانات:', error);\n      alert('❌ فشل في تصدير البيانات');\n    }\n  };\n\n  // طباعة التقرير\n  const printReport = () => {\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>تقرير المبيعات - ${new Date().toLocaleDateString('ar-SA')}</title>\n          <style>\n            body { font-family: Arial, sans-serif; margin: 20px; }\n            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }\n            .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }\n            .stat-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }\n            table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            th { background-color: #f5f5f5; }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>تقرير المبيعات اليومي</h2>\n            <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>\n          </div>\n          \n          <div class=\"stats\">\n            <div class=\"stat-card\">\n              <h3>إحصائيات اليوم</h3>\n              <p>إجمالي المبيعات: ${todayStats.totalSales?.toFixed(2) || 0} ريال</p>\n              <p>إجمالي الضرائب: ${todayStats.totalVAT?.toFixed(2) || 0} ريال</p>\n              <p>عدد الفواتير: ${todayStats.totalInvoices || 0}</p>\n            </div>\n            <div class=\"stat-card\">\n              <h3>إحصائيات الشهر</h3>\n              <p>إجمالي المبيعات: ${monthStats.totalSales?.toFixed(2) || 0} ريال</p>\n              <p>إجمالي الضرائب: ${monthStats.totalVAT?.toFixed(2) || 0} ريال</p>\n              <p>عدد الفواتير: ${monthStats.totalInvoices || 0}</p>\n            </div>\n          </div>\n\n          <h3>آخر الفواتير</h3>\n          <table>\n            <thead>\n              <tr>\n                <th>رقم الفاتورة</th>\n                <th>العميل</th>\n                <th>الإجمالي</th>\n                <th>التاريخ</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${recentInvoices.map(invoice => `\n                <tr>\n                  <td>${invoice.id}</td>\n                  <td>${invoice.customer}</td>\n                  <td>${invoice.total.toFixed(2)} ريال</td>\n                  <td>${new Date(invoice.createdAt).toLocaleDateString('ar-SA')}</td>\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n\n  return (\n    <div className=\"reports-container\">\n      <div className=\"reports-header\">\n        <div className=\"header-content\">\n          <button onClick={onBack} className=\"back-btn\">\n            ← العودة للوحة التحكم\n          </button>\n          <h1>📊 التقارير والإحصائيات</h1>\n          <div className=\"header-actions\">\n            <button onClick={printReport} className=\"action-btn print-btn\">\n              🖨️ طباعة التقرير\n            </button>\n            <button onClick={exportReports} className=\"action-btn export-btn\">\n              📤 تصدير البيانات\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"reports-tabs\">\n        <button \n          className={`tab-btn ${activeTab === 'daily' ? 'active' : ''}`}\n          onClick={() => setActiveTab('daily')}\n        >\n          📅 التقرير اليومي\n        </button>\n        <button \n          className={`tab-btn ${activeTab === 'monthly' ? 'active' : ''}`}\n          onClick={() => setActiveTab('monthly')}\n        >\n          📊 التقرير الشهري\n        </button>\n        <button \n          className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}\n          onClick={() => setActiveTab('products')}\n        >\n          📦 تقرير المنتجات\n        </button>\n        <button \n          className={`tab-btn ${activeTab === 'invoices' ? 'active' : ''}`}\n          onClick={() => setActiveTab('invoices')}\n        >\n          🧾 سجل الفواتير\n        </button>\n      </div>\n\n      <div className=\"reports-content\">\n        {activeTab === 'daily' && (\n          <div className=\"daily-report\">\n            <div className=\"stats-grid\">\n              <div className=\"stat-card sales\">\n                <div className=\"stat-icon\">💰</div>\n                <div className=\"stat-info\">\n                  <h3>إجمالي المبيعات اليوم</h3>\n                  <p className=\"stat-value\">{todayStats.totalSales?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card vat\">\n                <div className=\"stat-icon\">🧾</div>\n                <div className=\"stat-info\">\n                  <h3>إجمالي الضرائب</h3>\n                  <p className=\"stat-value\">{todayStats.totalVAT?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card invoices\">\n                <div className=\"stat-icon\">📋</div>\n                <div className=\"stat-info\">\n                  <h3>عدد الفواتير</h3>\n                  <p className=\"stat-value\">{todayStats.totalInvoices || 0}</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card average\">\n                <div className=\"stat-icon\">📈</div>\n                <div className=\"stat-info\">\n                  <h3>متوسط الفاتورة</h3>\n                  <p className=\"stat-value\">{todayStats.averageInvoice?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'monthly' && (\n          <div className=\"monthly-report\">\n            <div className=\"stats-grid\">\n              <div className=\"stat-card sales\">\n                <div className=\"stat-icon\">💰</div>\n                <div className=\"stat-info\">\n                  <h3>إجمالي المبيعات الشهر</h3>\n                  <p className=\"stat-value\">{monthStats.totalSales?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card vat\">\n                <div className=\"stat-icon\">🧾</div>\n                <div className=\"stat-info\">\n                  <h3>إجمالي الضرائب</h3>\n                  <p className=\"stat-value\">{monthStats.totalVAT?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card invoices\">\n                <div className=\"stat-icon\">📋</div>\n                <div className=\"stat-info\">\n                  <h3>عدد الفواتير</h3>\n                  <p className=\"stat-value\">{monthStats.totalInvoices || 0}</p>\n                </div>\n              </div>\n              \n              <div className=\"stat-card average\">\n                <div className=\"stat-icon\">📈</div>\n                <div className=\"stat-info\">\n                  <h3>متوسط الفاتورة</h3>\n                  <p className=\"stat-value\">{monthStats.averageInvoice?.toFixed(2) || 0} ريال</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'products' && (\n          <div className=\"products-report\">\n            <div className=\"report-section\">\n              <h3>🔥 أفضل المنتجات مبيعاً</h3>\n              <div className=\"products-list\">\n                {topProducts.length > 0 ? topProducts.map((product, index) => (\n                  <div key={product.id} className=\"product-item\">\n                    <span className=\"rank\">#{index + 1}</span>\n                    <span className=\"name\">{product.name}</span>\n                    <span className=\"quantity\">الكمية: {product.quantity}</span>\n                    <span className=\"revenue\">{product.revenue.toFixed(2)} ريال</span>\n                  </div>\n                )) : (\n                  <p className=\"no-data\">لا توجد بيانات مبيعات</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"report-section\">\n              <h3>⚠️ منتجات منخفضة المخزون</h3>\n              <div className=\"products-list\">\n                {lowStockProducts.length > 0 ? lowStockProducts.map(product => (\n                  <div key={product.id} className=\"product-item low-stock\">\n                    <span className=\"name\">{product.name}</span>\n                    <span className=\"stock\">المخزون: {product.stock}</span>\n                    <span className=\"price\">{product.price} ريال</span>\n                  </div>\n                )) : (\n                  <p className=\"no-data\">جميع المنتجات متوفرة بكميات كافية</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'invoices' && (\n          <div className=\"invoices-report\">\n            <div className=\"invoices-header\">\n              <h3>📋 سجل الفواتير</h3>\n              <input\n                type=\"date\"\n                value={selectedDate}\n                onChange={(e) => setSelectedDate(e.target.value)}\n                className=\"date-filter\"\n              />\n            </div>\n            \n            <div className=\"invoices-list\">\n              {recentInvoices.length > 0 ? recentInvoices.map(invoice => (\n                <div key={invoice.id} className=\"invoice-item\">\n                  <div className=\"invoice-header\">\n                    <span className=\"invoice-id\">#{invoice.id}</span>\n                    <span className=\"invoice-date\">\n                      {new Date(invoice.createdAt).toLocaleDateString('ar-SA')}\n                    </span>\n                  </div>\n                  <div className=\"invoice-details\">\n                    <span className=\"customer\">العميل: {invoice.customer}</span>\n                    <span className=\"total\">الإجمالي: {invoice.total.toFixed(2)} ريال</span>\n                    <span className=\"payment\">الدفع: {invoice.paymentMethod}</span>\n                  </div>\n                </div>\n              )) : (\n                <p className=\"no-data\">لا توجد فواتير</p>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,eAAe;AACtB,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExF;EACA9B,SAAS,CAAC,MAAM;IACd+B,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EAElB,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI;MACF;MACA,MAAMC,SAAS,GAAG/B,QAAQ,CAACgC,aAAa,CAAC,CAAC;MAC1ChB,aAAa,CAACe,SAAS,CAAC;;MAExB;MACA,MAAME,SAAS,GAAGjC,QAAQ,CAACkC,aAAa,CAAC,CAAC;MAC1ChB,aAAa,CAACe,SAAS,CAAC;;MAExB;MACA,MAAME,WAAW,GAAGnC,QAAQ,CAACoC,WAAW,CAAC,CAAC;MAC1C,MAAMC,MAAM,GAAGF,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC;MAC/CnB,iBAAiB,CAACiB,MAAM,CAAC;;MAEzB;MACA,MAAMG,QAAQ,GAAGxC,QAAQ,CAACyC,mBAAmB,CAAC,CAAC,CAAC;MAChDnB,mBAAmB,CAACkB,QAAQ,CAAC;;MAE7B;MACA,MAAME,UAAU,GAAG1C,QAAQ,CAAC2C,qBAAqB,CAAC,CAAC,CAAC;MACpDnB,cAAc,CAACkB,UAAU,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI;MACF,MAAMC,IAAI,GAAG/C,QAAQ,CAACgD,UAAU,CAAC,CAAC;MAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACL,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QAAEM,IAAI,EAAE;MAAmB,CAAC,CAAC;MACpF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;MACrC,MAAMQ,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;MACZG,CAAC,CAACI,QAAQ,GAAG,2BAA2B,IAAIlC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MACrF6B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;MAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MACxBa,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CuB,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA;IACxB,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7CF,WAAW,CAACf,QAAQ,CAACkB,KAAK,CAAC;AAC/B;AACA;AACA,oCAAoC,IAAIjD,IAAI,CAAC,CAAC,CAACkD,kBAAkB,CAAC,OAAO,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,IAAIlD,IAAI,CAAC,CAAC,CAACkD,kBAAkB,CAAC,OAAO,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA,oCAAoC,EAAAR,qBAAA,GAAAtD,UAAU,CAAC+D,UAAU,cAAAT,qBAAA,uBAArBA,qBAAA,CAAuBU,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;AAC1E,mCAAmC,EAAAT,oBAAA,GAAAvD,UAAU,CAACiE,QAAQ,cAAAV,oBAAA,uBAAnBA,oBAAA,CAAqBS,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;AACvE,iCAAiChE,UAAU,CAACkE,aAAa,IAAI,CAAC;AAC9D;AACA;AACA;AACA,oCAAoC,EAAAV,qBAAA,GAAAtD,UAAU,CAAC6D,UAAU,cAAAP,qBAAA,uBAArBA,qBAAA,CAAuBQ,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;AAC1E,mCAAmC,EAAAP,oBAAA,GAAAvD,UAAU,CAAC+D,QAAQ,cAAAR,oBAAA,uBAAnBA,oBAAA,CAAqBO,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC;AACvE,iCAAiC9D,UAAU,CAACgE,aAAa,IAAI,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB9D,cAAc,CAAC+D,GAAG,CAACC,OAAO,IAAI;AAC9C;AACA,wBAAwBA,OAAO,CAACC,EAAE;AAClC,wBAAwBD,OAAO,CAACE,QAAQ;AACxC,wBAAwBF,OAAO,CAACG,KAAK,CAACP,OAAO,CAAC,CAAC,CAAC;AAChD,wBAAwB,IAAIpD,IAAI,CAACwD,OAAO,CAACI,SAAS,CAAC,CAACV,kBAAkB,CAAC,OAAO,CAAC;AAC/E;AACA,eAAe,CAAC,CAACW,IAAI,CAAC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA,KAAK,CAAC;IACFf,WAAW,CAACf,QAAQ,CAAC+B,KAAK,CAAC,CAAC;IAC5BhB,WAAW,CAACiB,KAAK,CAAC,CAAC;EACrB,CAAC;EAED,oBACExF,OAAA;IAAKyF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC1F,OAAA;MAAKyF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B1F,OAAA;QAAKyF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1F,OAAA;UAAQ2F,OAAO,EAAExF,MAAO;UAACsF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/F,OAAA;UAAA0F,QAAA,EAAI;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChC/F,OAAA;UAAKyF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1F,OAAA;YAAQ2F,OAAO,EAAEzB,WAAY;YAACuB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/F,OAAA;YAAQ2F,OAAO,EAAE/C,aAAc;YAAC6C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAElE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/F,OAAA;MAAKyF,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1F,OAAA;QACEyF,SAAS,EAAE,WAAW9E,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC9DgF,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,OAAO,CAAE;QAAA8E,QAAA,EACtC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA;QACEyF,SAAS,EAAE,WAAW9E,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QAChEgF,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,SAAS,CAAE;QAAA8E,QAAA,EACxC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA;QACEyF,SAAS,EAAE,WAAW9E,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjEgF,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,UAAU,CAAE;QAAA8E,QAAA,EACzC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA;QACEyF,SAAS,EAAE,WAAW9E,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjEgF,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,UAAU,CAAE;QAAA8E,QAAA,EACzC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/F,OAAA;MAAKyF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7B/E,SAAS,KAAK,OAAO,iBACpBX,OAAA;QAAKyF,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B1F,OAAA;UAAKyF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1F,OAAA;YAAKyF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9B/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAArF,sBAAA,GAAAQ,UAAU,CAAC+D,UAAU,cAAAvE,sBAAA,uBAArBA,sBAAA,CAAuBwE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAApF,qBAAA,GAAAO,UAAU,CAACiE,QAAQ,cAAAxE,qBAAA,uBAAnBA,qBAAA,CAAqBuE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE7E,UAAU,CAACkE,aAAa,IAAI;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAAnF,qBAAA,GAAAM,UAAU,CAACmF,cAAc,cAAAzF,qBAAA,uBAAzBA,qBAAA,CAA2BsE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEApF,SAAS,KAAK,SAAS,iBACtBX,OAAA;QAAKyF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B1F,OAAA;UAAKyF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1F,OAAA;YAAKyF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9B/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAAlF,sBAAA,GAAAO,UAAU,CAAC6D,UAAU,cAAApE,sBAAA,uBAArBA,sBAAA,CAAuBqE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAAjF,qBAAA,GAAAM,UAAU,CAAC+D,QAAQ,cAAArE,qBAAA,uBAAnBA,qBAAA,CAAqBoE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3E,UAAU,CAACgE,aAAa,IAAI;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAKyF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnC/F,OAAA;cAAKyF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1F,OAAA;gBAAA0F,QAAA,EAAI;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvB/F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,EAAAhF,qBAAA,GAAAK,UAAU,CAACiF,cAAc,cAAAtF,qBAAA,uBAAzBA,qBAAA,CAA2BmE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEApF,SAAS,KAAK,UAAU,iBACvBX,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1F,OAAA;UAAKyF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1F,OAAA;YAAA0F,QAAA,EAAI;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC/F,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BrE,WAAW,CAAC4E,MAAM,GAAG,CAAC,GAAG5E,WAAW,CAAC2D,GAAG,CAAC,CAACkB,OAAO,EAAEC,KAAK,kBACvDnG,OAAA;cAAsByF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC5C1F,OAAA;gBAAMyF,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,GAAC,EAACS,KAAK,GAAG,CAAC;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1C/F,OAAA;gBAAMyF,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEQ,OAAO,CAACE;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C/F,OAAA;gBAAMyF,SAAS,EAAC,UAAU;gBAAAC,QAAA,GAAC,wCAAQ,EAACQ,OAAO,CAACG,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5D/F,OAAA;gBAAMyF,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAEQ,OAAO,CAACI,OAAO,CAACzB,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ1DG,OAAO,CAAChB,EAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKf,CACN,CAAC,gBACA/F,OAAA;cAAGyF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAChD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/F,OAAA;UAAKyF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1F,OAAA;YAAA0F,QAAA,EAAI;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC/F,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BvE,gBAAgB,CAAC8E,MAAM,GAAG,CAAC,GAAG9E,gBAAgB,CAAC6D,GAAG,CAACkB,OAAO,iBACzDlG,OAAA;cAAsByF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACtD1F,OAAA;gBAAMyF,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEQ,OAAO,CAACE;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C/F,OAAA;gBAAMyF,SAAS,EAAC,OAAO;gBAAAC,QAAA,GAAC,8CAAS,EAACQ,OAAO,CAACK,KAAK;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD/F,OAAA;gBAAMyF,SAAS,EAAC,OAAO;gBAAAC,QAAA,GAAEQ,OAAO,CAACM,KAAK,EAAC,2BAAK;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAH3CG,OAAO,CAAChB,EAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIf,CACN,CAAC,gBACA/F,OAAA;cAAGyF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC5D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEApF,SAAS,KAAK,UAAU,iBACvBX,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1F,OAAA;UAAKyF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B1F,OAAA;YAAA0F,QAAA,EAAI;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB/F,OAAA;YACEmD,IAAI,EAAC,MAAM;YACXsD,KAAK,EAAElF,YAAa;YACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,eAAe,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDhB,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/F,OAAA;UAAKyF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BzE,cAAc,CAACgF,MAAM,GAAG,CAAC,GAAGhF,cAAc,CAAC+D,GAAG,CAACC,OAAO,iBACrDjF,OAAA;YAAsByF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC5C1F,OAAA;cAAKyF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B1F,OAAA;gBAAMyF,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAACT,OAAO,CAACC,EAAE;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD/F,OAAA;gBAAMyF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC3B,IAAIjE,IAAI,CAACwD,OAAO,CAACI,SAAS,CAAC,CAACV,kBAAkB,CAAC,OAAO;cAAC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN/F,OAAA;cAAKyF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B1F,OAAA;gBAAMyF,SAAS,EAAC,UAAU;gBAAAC,QAAA,GAAC,wCAAQ,EAACT,OAAO,CAACE,QAAQ;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5D/F,OAAA;gBAAMyF,SAAS,EAAC,OAAO;gBAAAC,QAAA,GAAC,oDAAU,EAACT,OAAO,CAACG,KAAK,CAACP,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxE/F,OAAA;gBAAMyF,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,kCAAO,EAACT,OAAO,CAAC4B,aAAa;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA,GAXEd,OAAO,CAACC,EAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYf,CACN,CAAC,gBACA/F,OAAA;YAAGyF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACzC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAlUIH,OAAO;AAAA6G,EAAA,GAAP7G,OAAO;AAoUb,eAAeA,OAAO;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}