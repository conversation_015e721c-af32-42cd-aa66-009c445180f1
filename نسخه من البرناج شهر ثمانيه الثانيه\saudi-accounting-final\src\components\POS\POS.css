/* حاوية نقطة البيع */
.pos-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* شريط التنقل */
.pos-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  background-color: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.back-btn:hover {
  background-color: rgba(255,255,255,0.3);
  transform: translateY(-1px);
}

.pos-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.user-info {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* المحتوى الرئيسي */
.pos-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  min-height: calc(100vh - 80px);
}

/* قسم المنتجات */
.products-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-bar {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.product-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
  cursor: pointer;
}

.product-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.product-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.product-category {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 0.85rem;
}

.product-price {
  margin: 0 0 5px 0;
  color: #1890ff;
  font-weight: 600;
  font-size: 1.1rem;
}

.product-stock {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.85rem;
}

.add-to-cart-btn {
  width: 100%;
  padding: 8px 12px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
  font-weight: 500;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #73d13d, #95de64);
  transform: translateY(-1px);
}

.add-to-cart-btn:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

/* قسم السلة */
.cart-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 120px);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.cart-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.clear-cart-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s;
}

.clear-cart-btn:hover {
  background: #ff7875;
  transform: translateY(-1px);
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 15px;
  max-height: 300px;
  min-height: 200px;
}

.empty-cart {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #ddd;
}

.cart-item {
  display: grid;
  grid-template-columns: 1fr auto auto auto;
  gap: 10px;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
  border-radius: 6px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.cart-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.item-info p {
  margin: 0;
  color: #1890ff;
  font-size: 0.9rem;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qty-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s;
}

.qty-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.item-total {
  font-weight: 600;
  color: #1890ff;
  font-size: 0.9rem;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.remove-btn:hover {
  background: #fff2f0;
}

/* إجماليات السلة */
.cart-totals {
  border-top: 1px solid #e8e8e8;
  padding-top: 15px;
  margin-bottom: 15px;
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.final-total {
  font-weight: 600;
  font-size: 1.1rem;
  color: #1890ff;
  border-top: 1px solid #e8e8e8;
  padding-top: 8px;
  margin-top: 8px;
}

/* قسم طرق الدفع */
.payment-section {
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.section-subtitle {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: white;
  border: 2px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.payment-option.selected {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.payment-option input[type="radio"] {
  margin: 0;
  accent-color: #1890ff;
}

.payment-icon {
  font-size: 1.2rem;
}

.payment-option span:last-child {
  font-weight: 500;
  color: #333;
}

.payment-option.selected span:last-child {
  color: #1890ff;
  font-weight: 600;
}

/* قسم العميل */
.customer-section {
  margin-bottom: 15px;
}

.customer-btn {
  width: 100%;
  padding: 10px;
  background: #f0f8ff;
  color: #1890ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.customer-btn:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.customer-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.customer-input {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.customer-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* زر الدفع */
.checkout-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.checkout-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);
}

.checkout-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.checkout-btn.processing {
  pointer-events: none;
}

/* دوار التحميل */
.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* تحسين الاستجابة */
@media (max-width: 1200px) {
  .pos-main {
    grid-template-columns: 1fr 350px;
  }
}

@media (max-width: 768px) {
  .pos-main {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }

  .cart-section {
    order: -1;
    max-height: 50vh;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .cart-item {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: center;
  }
  
  .quantity-controls {
    justify-content: center;
  }
}
