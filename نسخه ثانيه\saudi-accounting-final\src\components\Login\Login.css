/* حاوية تسجيل الدخول */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* بطاقة تسجيل الدخول */
.login-card {
  width: 100%;
  max-width: 450px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 30px;
  animation: fadeIn 0.5s ease-out;
}

/* رأس تسجيل الدخول */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  color: #1890ff;
  font-size: 1.8rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* نموذج تسجيل الدخول */
.login-form {
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 0.95rem;
}

.form-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f9f9f9;
  color: #333;
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: #fff;
}

.form-input::placeholder {
  color: #bbb;
}

.form-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.7;
}

/* رسالة الخطأ */
.error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 10px 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  animation: shake 0.5s ease-in-out;
}

/* أزرار الإجراءات */
.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 1;
  font-family: inherit;
}

.btn-primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
  transform: translateY(-2px);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-secondary {
  background-color: transparent;
  color: #1890ff;
  border: 1px solid #1890ff;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f0f8ff;
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.btn-secondary:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* دوار التحميل */
.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

/* قسم المساعدة */
.login-help {
  background-color: #f0f8ff;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.login-help h4 {
  color: #1890ff;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1rem;
  font-weight: 600;
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-item {
  font-size: 0.9rem;
  color: #666;
}

.account-item strong {
  color: #333;
  margin-left: 5px;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* تحسين الاستجابة */
@media (max-width: 480px) {
  .login-card {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
}
