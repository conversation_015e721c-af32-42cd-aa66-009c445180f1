import React, { useState } from 'react';
import './Login.css';

const Login = ({ onLogin, onBack }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // بيانات المستخدمين المتاحة
  const users = {
    'admin': { password: 'admin123', role: 'مدير النظام', name: 'أحمد محمد' },
    'accountant': { password: 'acc123', role: 'محاسب', name: 'فاطمة علي' },
    'cashier': { password: 'cash123', role: 'أمين صندوق', name: 'محمد سعد' }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // إزالة رسالة الخطأ عند الكتابة
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1500));

    const { username, password } = formData;

    // التحقق من البيانات
    if (!username || !password) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      setIsLoading(false);
      return;
    }

    const user = users[username.toLowerCase()];
    if (!user || user.password !== password) {
      setError('اسم المستخدم أو كلمة المرور غير صحيحة');
      setIsLoading(false);
      return;
    }

    // تسجيل دخول ناجح
    setIsLoading(false);
    onLogin({
      username: username,
      name: user.name,
      role: user.role
    });
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h2 className="login-title">🔐 تسجيل الدخول</h2>
          <p className="login-subtitle">نظام المحاسبة السعودي</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username" className="form-label">
              👤 اسم المستخدم
            </label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              className="form-input"
              placeholder="أدخل اسم المستخدم"
              disabled={isLoading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              🔒 كلمة المرور
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className="form-input"
              placeholder="أدخل كلمة المرور"
              disabled={isLoading}
            />
          </div>

          {error && (
            <div className="error-message">
              ⚠️ {error}
            </div>
          )}

          <div className="form-actions">
            <button
              type="submit"
              className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="spinner"></span>
                  جاري تسجيل الدخول...
                </>
              ) : (
                '🚀 دخول'
              )}
            </button>

            <button
              type="button"
              onClick={onBack}
              className="btn btn-secondary"
              disabled={isLoading}
            >
              ↩️ رجوع
            </button>
          </div>
        </form>

        <div className="login-help">
          <h4>📋 الحسابات المتاحة:</h4>
          <div className="accounts-list">
            <div className="account-item">
              <strong>👨‍💼 مدير النظام:</strong> admin / admin123
            </div>
            <div className="account-item">
              <strong>📊 محاسب:</strong> accountant / acc123
            </div>
            <div className="account-item">
              <strong>💰 أمين صندوق:</strong> cashier / cash123
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
