import React, { useState, useEffect, useRef } from 'react';
import database from '../../utils/database';
import './BarcodeManager.css';

const BarcodeManager = () => {
  const [products, setProducts] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [barcodeType, setBarcodeType] = useState('CODE128');
  const [printSettings, setPrintSettings] = useState({
    labelWidth: 50,
    labelHeight: 25,
    fontSize: 10,
    showProductName: true,
    showPrice: true,
    showBarcode: true,
    copies: 1
  });
  const [message, setMessage] = useState('');
  const [showScanner, setShowScanner] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const printRef = useRef();

  const barcodeTypes = [
    { value: 'CODE128', label: 'CODE 128' },
    { value: 'CODE39', label: 'CODE 39' },
    { value: 'EAN13', label: 'EAN-13' },
    { value: 'EAN8', label: 'EAN-8' },
    { value: 'UPC', label: 'UPC' }
  ];

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = () => {
    try {
      const productsData = database.getProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
      setMessage('خطأ في تحميل المنتجات');
    }
  };

  const generateBarcode = (text, type = 'CODE128') => {
    // محاكاة إنشاء الباركود - في التطبيق الحقيقي ستستخدم مكتبة JsBarcode
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 200;
    canvas.height = 50;
    
    // رسم خطوط الباركود
    ctx.fillStyle = '#000000';
    for (let i = 0; i < 50; i++) {
      if (Math.random() > 0.5) {
        ctx.fillRect(i * 4, 0, 2, 40);
      }
    }
    
    // إضافة النص
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(text, canvas.width / 2, 50);
    
    return canvas.toDataURL();
  };

  const generateProductBarcode = (productId) => {
    const product = products.find(p => p.id === productId);
    if (!product) return '';

    // إنشاء باركود فريد للمنتج
    let barcode = product.barcode;
    if (!barcode) {
      // إنشاء باركود جديد إذا لم يكن موجود
      const timestamp = Date.now().toString();
      const productCode = product.id.toString().padStart(4, '0');
      barcode = `${productCode}${timestamp.slice(-6)}`;
      
      // حفظ الباركود في قاعدة البيانات
      database.updateProduct(productId, { barcode });
      loadProducts();
    }

    return barcode;
  };

  const handleProductSelect = (productId) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  const selectAllProducts = () => {
    const filteredProductIds = filteredProducts.map(p => p.id);
    setSelectedProducts(filteredProductIds);
  };

  const clearSelection = () => {
    setSelectedProducts([]);
  };

  const handlePrintSettingsChange = (setting, value) => {
    setPrintSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const printBarcodes = () => {
    if (selectedProducts.length === 0) {
      setMessage('يرجى اختيار منتج واحد على الأقل للطباعة');
      return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));
    
    let printContent = `
      <html>
        <head>
          <title>طباعة الباركود</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 0; 
              padding: 20px;
              background: white;
            }
            .barcode-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(${printSettings.labelWidth}mm, 1fr));
              gap: 5mm;
              page-break-inside: avoid;
            }
            .barcode-label {
              width: ${printSettings.labelWidth}mm;
              height: ${printSettings.labelHeight}mm;
              border: 1px solid #ccc;
              padding: 2mm;
              text-align: center;
              display: flex;
              flex-direction: column;
              justify-content: center;
              page-break-inside: avoid;
              background: white;
            }
            .product-name {
              font-size: ${printSettings.fontSize - 2}px;
              font-weight: bold;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .barcode-image {
              max-width: 100%;
              height: auto;
              margin: 2px 0;
            }
            .barcode-text {
              font-size: ${printSettings.fontSize - 3}px;
              font-family: monospace;
              margin: 1px 0;
            }
            .product-price {
              font-size: ${printSettings.fontSize - 1}px;
              font-weight: bold;
              color: #d32f2f;
              margin-top: 2px;
            }
            @media print {
              body { margin: 0; }
              .barcode-label { 
                border: 1px solid #000;
                margin: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="barcode-grid">
    `;

    selectedProductsData.forEach(product => {
      const barcode = generateProductBarcode(product.id);
      const barcodeImage = generateBarcode(barcode, barcodeType);
      
      for (let copy = 0; copy < printSettings.copies; copy++) {
        printContent += `
          <div class="barcode-label">
            ${printSettings.showProductName ? `<div class="product-name">${product.name}</div>` : ''}
            ${printSettings.showBarcode ? `
              <img src="${barcodeImage}" alt="Barcode" class="barcode-image" />
              <div class="barcode-text">${barcode}</div>
            ` : ''}
            ${printSettings.showPrice ? `<div class="product-price">${product.price} ريال</div>` : ''}
          </div>
        `;
      }
    });

    printContent += `
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    
    setTimeout(() => {
      printWindow.print();
    }, 500);

    setMessage(`تم إنشاء ${selectedProducts.length * printSettings.copies} ملصق باركود للطباعة`);
    setTimeout(() => setMessage(''), 3000);
  };

  const scanBarcode = () => {
    setShowScanner(true);
    // في التطبيق الحقيقي، ستستخدم مكتبة لقراءة الباركود من الكاميرا
    // هنا سنحاكي عملية المسح
    setTimeout(() => {
      const randomProduct = products[Math.floor(Math.random() * products.length)];
      if (randomProduct) {
        const barcode = generateProductBarcode(randomProduct.id);
        setScannedCode(barcode);
        setMessage(`تم مسح الباركود: ${barcode} - ${randomProduct.name}`);
      }
      setShowScanner(false);
    }, 2000);
  };

  const searchByBarcode = () => {
    if (!scannedCode) {
      setMessage('لا يوجد باركود ممسوح');
      return;
    }

    const product = products.find(p => p.barcode === scannedCode);
    if (product) {
      setSearchTerm(product.name);
      setMessage(`تم العثور على المنتج: ${product.name}`);
    } else {
      setMessage('لم يتم العثور على منتج بهذا الباركود');
    }
  };

  const exportBarcodes = () => {
    if (selectedProducts.length === 0) {
      setMessage('يرجى اختيار منتج واحد على الأقل للتصدير');
      return;
    }

    const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));
    const barcodeData = selectedProductsData.map(product => ({
      id: product.id,
      name: product.name,
      barcode: generateProductBarcode(product.id),
      price: product.price,
      category: product.category
    }));

    const dataStr = JSON.stringify(barcodeData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `barcodes-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    setMessage('تم تصدير بيانات الباركود بنجاح!');
    setTimeout(() => setMessage(''), 3000);
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode?.includes(searchTerm)
  );

  return (
    <div className="barcode-manager-container">
      <div className="barcode-manager-header">
        <h1>📊 إدارة الباركود</h1>
        <p>إنشاء وطباعة ومسح الباركود للمنتجات</p>
      </div>

      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* Scanner Section */}
      <div className="scanner-section">
        <div className="scanner-controls">
          <button onClick={scanBarcode} className="scan-btn" disabled={showScanner}>
            {showScanner ? '📷 جاري المسح...' : '📷 مسح باركود'}
          </button>
          {scannedCode && (
            <div className="scanned-result">
              <span>الباركود الممسوح: <strong>{scannedCode}</strong></span>
              <button onClick={searchByBarcode} className="search-scanned-btn">
                🔍 البحث
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Controls Section */}
      <div className="barcode-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="🔍 البحث في المنتجات أو الباركود..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="selection-controls">
          <button onClick={selectAllProducts} className="select-all-btn">
            ✅ تحديد الكل ({filteredProducts.length})
          </button>
          <button onClick={clearSelection} className="clear-selection-btn">
            ❌ إلغاء التحديد
          </button>
          <span className="selection-count">
            محدد: {selectedProducts.length} منتج
          </span>
        </div>
      </div>

      {/* Print Settings */}
      <div className="print-settings">
        <h3>⚙️ إعدادات الطباعة</h3>
        <div className="settings-grid">
          <div className="setting-group">
            <label>نوع الباركود</label>
            <select
              value={barcodeType}
              onChange={(e) => setBarcodeType(e.target.value)}
            >
              {barcodeTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className="setting-group">
            <label>عرض الملصق (مم)</label>
            <input
              type="number"
              value={printSettings.labelWidth}
              onChange={(e) => handlePrintSettingsChange('labelWidth', parseInt(e.target.value))}
              min="20"
              max="100"
            />
          </div>

          <div className="setting-group">
            <label>ارتفاع الملصق (مم)</label>
            <input
              type="number"
              value={printSettings.labelHeight}
              onChange={(e) => handlePrintSettingsChange('labelHeight', parseInt(e.target.value))}
              min="15"
              max="50"
            />
          </div>

          <div className="setting-group">
            <label>حجم الخط</label>
            <input
              type="number"
              value={printSettings.fontSize}
              onChange={(e) => handlePrintSettingsChange('fontSize', parseInt(e.target.value))}
              min="6"
              max="16"
            />
          </div>

          <div className="setting-group">
            <label>عدد النسخ</label>
            <input
              type="number"
              value={printSettings.copies}
              onChange={(e) => handlePrintSettingsChange('copies', parseInt(e.target.value))}
              min="1"
              max="10"
            />
          </div>

          <div className="setting-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={printSettings.showProductName}
                onChange={(e) => handlePrintSettingsChange('showProductName', e.target.checked)}
              />
              عرض اسم المنتج
            </label>
          </div>

          <div className="setting-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={printSettings.showPrice}
                onChange={(e) => handlePrintSettingsChange('showPrice', e.target.checked)}
              />
              عرض السعر
            </label>
          </div>

          <div className="setting-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={printSettings.showBarcode}
                onChange={(e) => handlePrintSettingsChange('showBarcode', e.target.checked)}
              />
              عرض الباركود
            </label>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <button 
          onClick={printBarcodes} 
          className="print-btn"
          disabled={selectedProducts.length === 0}
        >
          🖨️ طباعة الباركود ({selectedProducts.length})
        </button>
        <button 
          onClick={exportBarcodes} 
          className="export-btn"
          disabled={selectedProducts.length === 0}
        >
          📤 تصدير البيانات
        </button>
      </div>

      {/* Products Grid */}
      <div className="products-grid">
        {filteredProducts.length === 0 ? (
          <div className="no-products">
            <p>لا توجد منتجات مطابقة للبحث</p>
          </div>
        ) : (
          filteredProducts.map(product => {
            const barcode = generateProductBarcode(product.id);
            const barcodeImage = generateBarcode(barcode, barcodeType);
            const isSelected = selectedProducts.includes(product.id);

            return (
              <div 
                key={product.id} 
                className={`product-card ${isSelected ? 'selected' : ''}`}
                onClick={() => handleProductSelect(product.id)}
              >
                <div className="product-header">
                  <h4>{product.name}</h4>
                  <div className="selection-indicator">
                    {isSelected ? '✅' : '⭕'}
                  </div>
                </div>
                
                <div className="product-info">
                  <p><strong>الفئة:</strong> {product.category}</p>
                  <p><strong>السعر:</strong> {product.price} ريال</p>
                  <p><strong>المخزون:</strong> {product.stock}</p>
                </div>

                <div className="barcode-preview">
                  <img src={barcodeImage} alt="Barcode Preview" />
                  <div className="barcode-text">{barcode}</div>
                </div>

                <div className="product-actions">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedProducts([product.id]);
                      printBarcodes();
                    }}
                    className="quick-print-btn"
                  >
                    🖨️ طباعة سريعة
                  </button>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Scanner Modal */}
      {showScanner && (
        <div className="modal-overlay">
          <div className="scanner-modal">
            <div className="scanner-content">
              <h3>📷 مسح الباركود</h3>
              <div className="scanner-animation">
                <div className="scanner-line"></div>
                <p>وجه الكاميرا نحو الباركود...</p>
              </div>
              <button 
                onClick={() => setShowScanner(false)} 
                className="cancel-scan-btn"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BarcodeManager;
