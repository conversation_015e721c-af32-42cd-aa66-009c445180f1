/**
 * @typedef {Object} SpacerProps
 * @property {string} space
 */

/**
 * An empty element to add spacing manually.
 * @param {Document} document
 * @param {HTMLElement} root
 * @param {SpacerProps} props
 * @returns {void}
 */
function Spacer(document, root, props) {
  const spacer = document.createElement('div');
  spacer.style.paddingBottom = props.space;
  root.appendChild(spacer);
}

module.exports = Spacer;
