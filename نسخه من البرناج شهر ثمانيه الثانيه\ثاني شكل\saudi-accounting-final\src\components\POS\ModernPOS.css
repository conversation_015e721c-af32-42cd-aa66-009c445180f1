/* Modern POS Styles - <PERSON><PERSON><PERSON> ERP Design */

.modern-pos {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--gray-50);
  font-family: 'Cairo', sans-serif;
}

/* Header Styles */
.pos-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 100;
}

.pos-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.title-main h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.pos-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  font-weight: 400;
}

.invoice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.invoice-number {
  font-size: var(--font-size-lg);
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.current-time {
  font-size: var(--font-size-sm);
  font-family: 'Courier New', monospace;
  opacity: 0.9;
}

.pos-shortcuts {
  display: flex;
  gap: var(--spacing-md);
}

.shortcut-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.shortcut-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.shortcut-key {
  font-size: var(--font-size-sm);
  font-weight: 700;
  background: var(--white);
  color: var(--primary-color);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  min-width: 24px;
  text-align: center;
}

.shortcut-label {
  font-size: var(--font-size-xs);
  opacity: 0.9;
  white-space: nowrap;
}

/* Message Styles */
.pos-message {
  padding: var(--spacing-md) var(--spacing-xl);
  margin: 0;
  font-weight: 500;
  text-align: center;
  animation: slideDown 0.3s ease-out;
}

.pos-message.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: var(--white);
}

.pos-message.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: var(--white);
}

/* Content Layout */
.pos-content {
  display: flex;
  flex: 1;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  overflow: hidden;
}

/* Products Section */
.products-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.products-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
}

.search-section {
  display: flex;
  gap: var(--spacing-md);
  flex: 1;
}

.search-input-container {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-left: 3rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
  background: var(--white);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: var(--font-size-lg);
}

.category-filter {
  padding: var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--white);
  min-width: 150px;
}

.category-filter:focus {
  outline: none;
  border-color: var(--primary-color);
}

.products-count {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: 500;
  padding: var(--spacing-md);
  background: var(--gray-100);
  border-radius: var(--radius-md);
}

/* Products Grid */
.products-grid {
  flex: 1;
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.product-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 160px;
}

.product-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.product-card.out-of-stock {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--gray-50);
}

.product-card.low-stock {
  border-color: var(--warning-color);
  background: linear-gradient(135deg, #fff 0%, #fffbeb 100%);
}

.product-header {
  margin-bottom: var(--spacing-sm);
}

.product-name {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  display: inline-block;
}

.product-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-price {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.product-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.stock-label {
  color: var(--gray-600);
}

.stock-value {
  font-weight: 600;
  color: var(--success-color);
}

.stock-value.low {
  color: var(--warning-color);
}

.product-footer {
  margin-top: var(--spacing-sm);
}

.add-to-cart-btn {
  width: 100%;
  padding: var(--spacing-sm);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.add-to-cart-btn:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
}

/* Cart Section */
.cart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  min-width: 400px;
  max-width: 500px;
}

.cart-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-header h2 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.cart-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.cart-count {
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.clear-cart-btn {
  background: var(--danger-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-cart-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Customer Section */
.customer-section {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.customer-section label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-700);
}

.customer-select {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background: var(--white);
}

.customer-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Cart Items */
.cart-items {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  max-height: 300px;
}

.empty-cart {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--gray-500);
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-cart h3 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--gray-700);
}

.empty-cart p {
  font-size: var(--font-size-sm);
  margin: 0;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  background: var(--gray-50);
  transition: all 0.3s ease;
}

.cart-item:hover {
  background: var(--gray-100);
  border-color: var(--primary-color);
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.item-price {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
}

.item-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn.decrease {
  background: var(--gray-200);
  color: var(--gray-700);
}

.qty-btn.increase {
  background: var(--primary-color);
  color: var(--white);
}

.qty-btn:hover {
  transform: scale(1.1);
}

.quantity {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-900);
  min-width: 30px;
  text-align: center;
}

.item-total {
  font-size: var(--font-size-sm);
  font-weight: 700;
  color: var(--primary-color);
  min-width: 80px;
  text-align: right;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: var(--danger-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Discount Section */
.discount-section {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.discount-section label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-700);
}

.discount-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  text-align: center;
}

.discount-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Cart Totals */
.cart-totals {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.total-row.discount {
  color: var(--danger-color);
}

.total-row.final {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
  border-top: 2px solid var(--gray-300);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
}

/* Checkout Button */
.checkout-btn {
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.checkout-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.checkout-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Payment Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.payment-modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-xl);
}

.payment-summary {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-xl);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.amount {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.payment-methods {
  margin-bottom: var(--spacing-xl);
}

.payment-methods label {
  display: block;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  color: var(--gray-700);
}

.payment-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.payment-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: var(--white);
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.payment-option.active {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
}

.method-icon {
  font-size: var(--font-size-2xl);
}

.method-label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-align: center;
}

.cash-payment {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

.cash-payment label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--gray-700);
}

.cash-input {
  width: 100%;
  padding: var(--spacing-lg);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  text-align: center;
  font-weight: 600;
}

.cash-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.change-amount {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--success-color);
  color: var(--white);
  border-radius: var(--radius-md);
  text-align: center;
  font-weight: 600;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.cancel-btn,
.confirm-payment-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: var(--gray-300);
  color: var(--gray-700);
}

.cancel-btn:hover {
  background: var(--gray-400);
}

.confirm-payment-btn {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: var(--white);
}

.confirm-payment-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
}

.confirm-payment-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .pos-content {
    flex-direction: column;
  }
  
  .cart-section {
    max-width: none;
    min-width: auto;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .pos-header {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .pos-shortcuts {
    order: -1;
  }
  
  .pos-content {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-sm);
  }
  
  .product-card {
    height: 140px;
    padding: var(--spacing-sm);
  }
  
  .search-section {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .payment-options {
    grid-template-columns: repeat(2, 1fr);
  }
}
