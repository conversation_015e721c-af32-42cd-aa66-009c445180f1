// نظام قاعدة البيانات المحلية للنظام المحاسبي السعودي
// Local Database System for Saudi Accounting System

class LocalDatabase {
  constructor() {
    this.initializeDatabase();
  }

  // تهيئة قاعدة البيانات
  initializeDatabase() {
    // إنشاء الجداول الأساسية إذا لم تكن موجودة
    if (!localStorage.getItem('saudi_accounting_invoices')) {
      localStorage.setItem('saudi_accounting_invoices', JSON.stringify([]));
    }
    
    if (!localStorage.getItem('saudi_accounting_products')) {
      localStorage.setItem('saudi_accounting_products', JSON.stringify(this.getDefaultProducts()));
    }
    
    if (!localStorage.getItem('saudi_accounting_customers')) {
      localStorage.setItem('saudi_accounting_customers', JSON.stringify([]));
    }
    
    if (!localStorage.getItem('saudi_accounting_settings')) {
      localStorage.setItem('saudi_accounting_settings', JSON.stringify(this.getDefaultSettings()));
    }

    if (!localStorage.getItem('saudi_accounting_payments')) {
      localStorage.setItem('saudi_accounting_payments', JSON.stringify([]));
    }

    if (!localStorage.getItem('saudi_accounting_inventory_movements')) {
      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify([]));
    }

    if (!localStorage.getItem('saudi_accounting_employees')) {
      localStorage.setItem('saudi_accounting_employees', JSON.stringify([]));
    }
  }

  // المنتجات الافتراضية
  getDefaultProducts() {
    return [
      {
        id: 1,
        name: 'لابتوب Dell',
        price: 2500,
        stock: 10,
        category: 'إلكترونيات',
        barcode: '*************',
        description: 'لابتوب Dell Inspiron 15',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 2,
        name: 'ماوس لاسلكي',
        price: 85,
        stock: 25,
        category: 'إلكترونيات',
        barcode: '*************',
        description: 'ماوس لاسلكي عالي الجودة',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 3,
        name: 'كيبورد ميكانيكي',
        price: 150,
        stock: 15,
        category: 'إلكترونيات',
        barcode: '*************',
        description: 'كيبورد ميكانيكي للألعاب',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 4,
        name: 'شاشة 24 بوصة',
        price: 800,
        stock: 8,
        category: 'إلكترونيات',
        barcode: '1234567890126',
        description: 'شاشة LED 24 بوصة Full HD',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 5,
        name: 'طابعة HP',
        price: 450,
        stock: 5,
        category: 'مكتبية',
        barcode: '1234567890127',
        description: 'طابعة HP LaserJet',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 6,
        name: 'كاميرا ويب',
        price: 120,
        stock: 20,
        category: 'إلكترونيات',
        barcode: '1234567890128',
        description: 'كاميرا ويب HD 1080p',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 7,
        name: 'سماعات بلوتوث',
        price: 200,
        stock: 12,
        category: 'إلكترونيات',
        barcode: '1234567890129',
        description: 'سماعات بلوتوث لاسلكية',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      },
      {
        id: 8,
        name: 'قلم رقمي',
        price: 75,
        stock: 30,
        category: 'مكتبية',
        barcode: '1234567890130',
        description: 'قلم رقمي للرسم والكتابة',
        vatRate: 0.15,
        createdAt: new Date().toISOString()
      }
    ];
  }

  // الإعدادات الافتراضية
  getDefaultSettings() {
    return {
      companyName: 'شركة نظام المحاسبة السعودي المحدودة',
      vatNumber: '310122393500003',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966 11 123 4567',
      email: '<EMAIL>',
      vatRate: 0.15,
      currency: 'ريال سعودي',
      invoicePrefix: 'INV',
      nextInvoiceNumber: 1001,
      createdAt: new Date().toISOString()
    };
  }

  // === إدارة الفواتير ===
  
  // حفظ فاتورة جديدة
  saveInvoice(invoice) {
    try {
      const invoices = this.getInvoices();
      const newInvoice = {
        ...invoice,
        id: this.generateInvoiceId(),
        createdAt: new Date().toISOString(),
        status: 'paid'
      };
      
      invoices.push(newInvoice);
      localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));
      
      // تحديث المخزون
      this.updateStockAfterSale(invoice.items);
      
      // تحديث رقم الفاتورة التالي
      this.incrementInvoiceNumber();
      
      return newInvoice;
    } catch (error) {
      console.error('خطأ في حفظ الفاتورة:', error);
      throw new Error('فشل في حفظ الفاتورة');
    }
  }

  // جلب جميع الفواتير
  getInvoices() {
    try {
      const invoices = localStorage.getItem('saudi_accounting_invoices');
      return invoices ? JSON.parse(invoices) : [];
    } catch (error) {
      console.error('خطأ في جلب الفواتير:', error);
      return [];
    }
  }

  // جلب فاتورة بالرقم
  getInvoiceById(id) {
    const invoices = this.getInvoices();
    return invoices.find(invoice => invoice.id === id);
  }

  // جلب فواتير بتاريخ محدد
  getInvoicesByDate(date) {
    const invoices = this.getInvoices();
    return invoices.filter(invoice => {
      const invoiceDate = new Date(invoice.createdAt).toDateString();
      const searchDate = new Date(date).toDateString();
      return invoiceDate === searchDate;
    });
  }

  // === إدارة المنتجات ===
  
  // جلب جميع المنتجات
  getProducts() {
    try {
      const products = localStorage.getItem('saudi_accounting_products');
      return products ? JSON.parse(products) : this.getDefaultProducts();
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      return this.getDefaultProducts();
    }
  }

  // إضافة منتج جديد
  addProduct(product) {
    try {
      const products = this.getProducts();
      const newProduct = {
        ...product,
        id: this.generateProductId(),
        createdAt: new Date().toISOString()
      };
      
      products.push(newProduct);
      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));
      return newProduct;
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw new Error('فشل في إضافة المنتج');
    }
  }

  // تحديث منتج
  updateProduct(id, updates) {
    try {
      const products = this.getProducts();
      const index = products.findIndex(product => product.id === id);
      
      if (index !== -1) {
        products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };
        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));
        return products[index];
      }
      
      throw new Error('المنتج غير موجود');
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      throw new Error('فشل في تحديث المنتج');
    }
  }

  // حذف منتج
  deleteProduct(id) {
    try {
      const products = this.getProducts();
      const filteredProducts = products.filter(product => product.id !== id);
      localStorage.setItem('saudi_accounting_products', JSON.stringify(filteredProducts));
      return true;
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      throw new Error('فشل في حذف المنتج');
    }
  }

  // تحديث المخزون بعد البيع
  updateStockAfterSale(items) {
    try {
      const products = this.getProducts();
      
      items.forEach(item => {
        const productIndex = products.findIndex(product => product.id === item.id);
        if (productIndex !== -1) {
          products[productIndex].stock -= item.quantity;
          if (products[productIndex].stock < 0) {
            products[productIndex].stock = 0;
          }
        }
      });
      
      localStorage.setItem('saudi_accounting_products', JSON.stringify(products));
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
    }
  }

  // === إدارة العملاء ===
  
  // جلب جميع العملاء
  getCustomers() {
    try {
      const customers = localStorage.getItem('saudi_accounting_customers');
      return customers ? JSON.parse(customers) : [];
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error);
      return [];
    }
  }

  // إضافة عميل جديد
  addCustomer(customer) {
    try {
      const customers = this.getCustomers();
      const newCustomer = {
        ...customer,
        id: this.generateCustomerId(),
        createdAt: new Date().toISOString()
      };
      
      customers.push(newCustomer);
      localStorage.setItem('saudi_accounting_customers', JSON.stringify(customers));
      return newCustomer;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw new Error('فشل في إضافة العميل');
    }
  }

  // === مولدات الأرقام ===
  
  generateInvoiceId() {
    const settings = this.getSettings();
    return `${settings.invoicePrefix}-${settings.nextInvoiceNumber}`;
  }

  generateProductId() {
    const products = this.getProducts();
    return products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;
  }

  generateCustomerId() {
    const customers = this.getCustomers();
    return customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;
  }

  // === إدارة الموردين ===

  // جلب جميع الموردين
  getSuppliers() {
    try {
      const suppliers = localStorage.getItem('saudi_accounting_suppliers');
      return suppliers ? JSON.parse(suppliers) : [];
    } catch (error) {
      console.error('خطأ في جلب الموردين:', error);
      return [];
    }
  }

  // إضافة مورد جديد
  addSupplier(supplier) {
    try {
      const suppliers = this.getSuppliers();
      const newSupplier = {
        ...supplier,
        id: this.generateSupplierId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      suppliers.push(newSupplier);
      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));
      return newSupplier;
    } catch (error) {
      console.error('خطأ في إضافة المورد:', error);
      throw new Error('فشل في إضافة المورد');
    }
  }

  // تحديث مورد
  updateSupplier(id, updates) {
    try {
      const suppliers = this.getSuppliers();
      const index = suppliers.findIndex(supplier => supplier.id === id);

      if (index !== -1) {
        suppliers[index] = {
          ...suppliers[index],
          ...updates,
          updatedAt: new Date().toISOString()
        };
        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(suppliers));
        return suppliers[index];
      }

      throw new Error('المورد غير موجود');
    } catch (error) {
      console.error('خطأ في تحديث المورد:', error);
      throw new Error('فشل في تحديث المورد');
    }
  }

  // حذف مورد
  deleteSupplier(id) {
    try {
      const suppliers = this.getSuppliers();
      const filteredSuppliers = suppliers.filter(supplier => supplier.id !== id);
      localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(filteredSuppliers));
      return true;
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      throw new Error('فشل في حذف المورد');
    }
  }

  // توليد معرف مورد
  generateSupplierId() {
    const suppliers = this.getSuppliers();
    return suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 1;
  }

  // === إدارة المدفوعات ===

  // جلب جميع المدفوعات
  getPayments() {
    try {
      const payments = localStorage.getItem('saudi_accounting_payments');
      return payments ? JSON.parse(payments) : [];
    } catch (error) {
      console.error('خطأ في جلب المدفوعات:', error);
      return [];
    }
  }

  // إضافة دفعة جديدة
  addPayment(payment) {
    try {
      const payments = this.getPayments();
      const newPayment = {
        ...payment,
        id: this.generatePaymentId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      payments.push(newPayment);
      localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));

      // تحديث حالة الفاتورة إذا كانت الدفعة مكتملة
      if (payment.status === 'completed') {
        this.updateInvoicePaymentStatus(payment.invoiceId, payment.amount);
      }

      return newPayment;
    } catch (error) {
      console.error('خطأ في إضافة الدفعة:', error);
      throw new Error('فشل في إضافة الدفعة');
    }
  }

  // تحديث دفعة
  updatePayment(id, updates) {
    try {
      const payments = this.getPayments();
      const paymentIndex = payments.findIndex(p => p.id === id);

      if (paymentIndex !== -1) {
        payments[paymentIndex] = {
          ...payments[paymentIndex],
          ...updates,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('saudi_accounting_payments', JSON.stringify(payments));
        return payments[paymentIndex];
      }

      throw new Error('الدفعة غير موجودة');
    } catch (error) {
      console.error('خطأ في تحديث الدفعة:', error);
      throw new Error('فشل في تحديث الدفعة');
    }
  }

  // حذف دفعة
  deletePayment(id) {
    try {
      const payments = this.getPayments();
      const filteredPayments = payments.filter(p => p.id !== id);
      localStorage.setItem('saudi_accounting_payments', JSON.stringify(filteredPayments));
      return true;
    } catch (error) {
      console.error('خطأ في حذف الدفعة:', error);
      throw new Error('فشل في حذف الدفعة');
    }
  }

  // توليد معرف دفعة
  generatePaymentId() {
    const payments = this.getPayments();
    return payments.length > 0 ? Math.max(...payments.map(p => p.id)) + 1 : 1;
  }

  // تحديث حالة دفع الفاتورة
  updateInvoicePaymentStatus(invoiceId, paidAmount) {
    try {
      const invoices = this.getInvoices();
      const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);

      if (invoiceIndex !== -1) {
        const invoice = invoices[invoiceIndex];
        const totalPaid = (invoice.paidAmount || 0) + paidAmount;

        let paymentStatus = 'unpaid';
        if (totalPaid >= invoice.total) {
          paymentStatus = 'paid';
        } else if (totalPaid > 0) {
          paymentStatus = 'partial';
        }

        invoices[invoiceIndex] = {
          ...invoice,
          paidAmount: totalPaid,
          paymentStatus: paymentStatus,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(invoices));
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة دفع الفاتورة:', error);
    }
  }

  // جلب مدفوعات عميل معين
  getCustomerPayments(customerId) {
    try {
      const payments = this.getPayments();
      return payments.filter(payment => payment.customerId === customerId);
    } catch (error) {
      console.error('خطأ في جلب مدفوعات العميل:', error);
      return [];
    }
  }

  // جلب مدفوعات فاتورة معينة
  getInvoicePayments(invoiceId) {
    try {
      const payments = this.getPayments();
      return payments.filter(payment => payment.invoiceId === invoiceId);
    } catch (error) {
      console.error('خطأ في جلب مدفوعات الفاتورة:', error);
      return [];
    }
  }

  // === إدارة حركات المخزون ===

  // جلب جميع حركات المخزون
  getInventoryMovements() {
    try {
      const movements = localStorage.getItem('saudi_accounting_inventory_movements');
      return movements ? JSON.parse(movements) : [];
    } catch (error) {
      console.error('خطأ في جلب حركات المخزون:', error);
      return [];
    }
  }

  // إضافة حركة مخزون جديدة
  addInventoryMovement(movement) {
    try {
      const movements = this.getInventoryMovements();
      const newMovement = {
        ...movement,
        id: this.generateMovementId(),
        createdAt: new Date().toISOString()
      };

      movements.push(newMovement);
      localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(movements));

      // تحديث مخزون المنتج
      this.updateProductStock(movement.productId, movement.quantity, movement.type);

      return newMovement;
    } catch (error) {
      console.error('خطأ في إضافة حركة المخزون:', error);
      throw new Error('فشل في إضافة حركة المخزون');
    }
  }

  // تحديث مخزون المنتج
  updateProductStock(productId, quantity, movementType) {
    try {
      const products = this.getProducts();
      const productIndex = products.findIndex(p => p.id === productId);

      if (productIndex !== -1) {
        const currentStock = products[productIndex].stock || 0;
        let newStock = currentStock;

        if (movementType === 'in') {
          newStock = currentStock + quantity;
        } else if (movementType === 'out' || movementType === 'damaged' || movementType === 'expired') {
          newStock = Math.max(0, currentStock - quantity);
        } else if (movementType === 'adjustment') {
          newStock = quantity; // التعديل يحدد الكمية الجديدة مباشرة
        }

        products[productIndex] = {
          ...products[productIndex],
          stock: newStock,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('saudi_accounting_products', JSON.stringify(products));
      }
    } catch (error) {
      console.error('خطأ في تحديث مخزون المنتج:', error);
    }
  }

  // توليد معرف حركة مخزون
  generateMovementId() {
    const movements = this.getInventoryMovements();
    return movements.length > 0 ? Math.max(...movements.map(m => m.id)) + 1 : 1;
  }

  // جلب حركات مخزون منتج معين
  getProductMovements(productId) {
    try {
      const movements = this.getInventoryMovements();
      return movements.filter(movement => movement.productId === productId);
    } catch (error) {
      console.error('خطأ في جلب حركات المنتج:', error);
      return [];
    }
  }

  // جلب تقرير حركات المخزون لفترة معينة
  getInventoryMovementsByDateRange(startDate, endDate) {
    try {
      const movements = this.getInventoryMovements();
      return movements.filter(movement => {
        const movementDate = new Date(movement.date);
        return movementDate >= new Date(startDate) && movementDate <= new Date(endDate);
      });
    } catch (error) {
      console.error('خطأ في جلب حركات المخزون للفترة:', error);
      return [];
    }
  }

  // حساب قيمة المخزون الإجمالية
  calculateTotalInventoryValue() {
    try {
      const products = this.getProducts();
      return products.reduce((total, product) => {
        return total + (product.stock * product.price);
      }, 0);
    } catch (error) {
      console.error('خطأ في حساب قيمة المخزون:', error);
      return 0;
    }
  }

  // جلب المنتجات منخفضة المخزون
  getLowStockProducts() {
    try {
      const products = this.getProducts();
      return products.filter(product => product.stock <= (product.minStock || 5));
    } catch (error) {
      console.error('خطأ في جلب المنتجات منخفضة المخزون:', error);
      return [];
    }
  }

  // جلب المنتجات نافدة المخزون
  getOutOfStockProducts() {
    try {
      const products = this.getProducts();
      return products.filter(product => product.stock === 0);
    } catch (error) {
      console.error('خطأ في جلب المنتجات نافدة المخزون:', error);
      return [];
    }
  }

  // === إدارة الموظفين ===

  // جلب جميع الموظفين
  getEmployees() {
    try {
      const employees = localStorage.getItem('saudi_accounting_employees');
      return employees ? JSON.parse(employees) : [];
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      return [];
    }
  }

  // إضافة موظف جديد
  addEmployee(employee) {
    try {
      const employees = this.getEmployees();
      const newEmployee = {
        ...employee,
        id: this.generateEmployeeId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      employees.push(newEmployee);
      localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));

      return newEmployee;
    } catch (error) {
      console.error('خطأ في إضافة الموظف:', error);
      throw new Error('فشل في إضافة الموظف');
    }
  }

  // تحديث موظف
  updateEmployee(id, updates) {
    try {
      const employees = this.getEmployees();
      const employeeIndex = employees.findIndex(emp => emp.id === id);

      if (employeeIndex !== -1) {
        employees[employeeIndex] = {
          ...employees[employeeIndex],
          ...updates,
          updatedAt: new Date().toISOString()
        };

        localStorage.setItem('saudi_accounting_employees', JSON.stringify(employees));
        return employees[employeeIndex];
      }

      throw new Error('الموظف غير موجود');
    } catch (error) {
      console.error('خطأ في تحديث الموظف:', error);
      throw new Error('فشل في تحديث الموظف');
    }
  }

  // حذف موظف
  deleteEmployee(id) {
    try {
      const employees = this.getEmployees();
      const filteredEmployees = employees.filter(emp => emp.id !== id);
      localStorage.setItem('saudi_accounting_employees', JSON.stringify(filteredEmployees));
      return true;
    } catch (error) {
      console.error('خطأ في حذف الموظف:', error);
      throw new Error('فشل في حذف الموظف');
    }
  }

  // توليد معرف موظف
  generateEmployeeId() {
    const employees = this.getEmployees();
    return employees.length > 0 ? Math.max(...employees.map(emp => emp.id)) + 1 : 1;
  }

  // جلب الموظفين النشطين
  getActiveEmployees() {
    try {
      const employees = this.getEmployees();
      return employees.filter(employee => employee.status === 'active');
    } catch (error) {
      console.error('خطأ في جلب الموظفين النشطين:', error);
      return [];
    }
  }

  // جلب موظفين حسب القسم
  getEmployeesByDepartment(department) {
    try {
      const employees = this.getEmployees();
      return employees.filter(employee => employee.department === department);
    } catch (error) {
      console.error('خطأ في جلب موظفين القسم:', error);
      return [];
    }
  }

  // حساب إجمالي الرواتب
  calculateTotalSalaries() {
    try {
      const activeEmployees = this.getActiveEmployees();
      return activeEmployees.reduce((total, employee) => {
        return total + (employee.salary || 0);
      }, 0);
    } catch (error) {
      console.error('خطأ في حساب إجمالي الرواتب:', error);
      return 0;
    }
  }

  // === إدارة الإعدادات المحسنة ===

  // جلب الإعدادات الافتراضية المحسنة
  getDefaultSettings() {
    return {
      // بيانات الشركة
      companyName: 'شركة المحاسبة السعودية',
      vatNumber: '300000000000003',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966 11 123 4567',
      email: '<EMAIL>',
      website: 'www.company.com',

      // إعدادات الفواتير
      invoicePrefix: 'INV',
      nextInvoiceNumber: 1,
      vatRate: 15,
      currency: 'SAR',
      defaultPaymentTerms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
      autoSaveInvoices: true,

      // إعدادات النظام
      language: 'ar',
      timezone: 'Asia/Riyadh',
      dateFormat: 'DD/MM/YYYY',
      enableNotifications: true,
      enableDarkMode: false,
      enableAutoBackup: false,

      // إعدادات إضافية
      fiscalYearStart: '01/01',
      backupFrequency: 'weekly',
      maxInvoiceItems: 50,
      lowStockThreshold: 5,

      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  // تحديث الإعدادات
  updateSettings(newSettings) {
    try {
      const currentSettings = this.getSettings();
      const updatedSettings = {
        ...currentSettings,
        ...newSettings,
        updatedAt: new Date().toISOString()
      };

      localStorage.setItem('saudi_accounting_settings', JSON.stringify(updatedSettings));
      return updatedSettings;
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error);
      throw new Error('فشل في تحديث الإعدادات');
    }
  }

  // إعادة تعيين الإعدادات للقيم الافتراضية
  resetSettings() {
    try {
      const defaultSettings = this.getDefaultSettings();
      localStorage.setItem('saudi_accounting_settings', JSON.stringify(defaultSettings));
      return defaultSettings;
    } catch (error) {
      console.error('خطأ في إعادة تعيين الإعدادات:', error);
      throw new Error('فشل في إعادة تعيين الإعدادات');
    }
  }

  // تصدير جميع البيانات
  exportAllData() {
    try {
      const data = {
        settings: this.getSettings(),
        invoices: this.getInvoices(),
        products: this.getProducts(),
        customers: this.getCustomers(),
        suppliers: this.getSuppliers(),
        accounts: this.getAccounts(),
        journalEntries: this.getJournalEntries(),
        payments: this.getPayments(),
        inventoryMovements: this.getInventoryMovements(),
        employees: this.getEmployees(),
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };

      return data;
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      throw new Error('فشل في تصدير البيانات');
    }
  }

  // استيراد جميع البيانات
  importAllData(data) {
    try {
      if (data.settings) {
        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));
      }
      if (data.invoices) {
        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));
      }
      if (data.products) {
        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));
      }
      if (data.customers) {
        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));
      }
      if (data.suppliers) {
        localStorage.setItem('saudi_accounting_suppliers', JSON.stringify(data.suppliers));
      }
      if (data.accounts) {
        localStorage.setItem('saudi_accounting_accounts', JSON.stringify(data.accounts));
      }
      if (data.journalEntries) {
        localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(data.journalEntries));
      }
      if (data.payments) {
        localStorage.setItem('saudi_accounting_payments', JSON.stringify(data.payments));
      }
      if (data.inventoryMovements) {
        localStorage.setItem('saudi_accounting_inventory_movements', JSON.stringify(data.inventoryMovements));
      }
      if (data.employees) {
        localStorage.setItem('saudi_accounting_employees', JSON.stringify(data.employees));
      }

      return true;
    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      throw new Error('فشل في استيراد البيانات');
    }
  }

  // === المحاسبة المالية ===

  // جلب جميع الحسابات
  getAccounts() {
    try {
      const accounts = localStorage.getItem('saudi_accounting_accounts');
      return accounts ? JSON.parse(accounts) : this.getDefaultAccounts();
    } catch (error) {
      console.error('خطأ في جلب الحسابات:', error);
      return this.getDefaultAccounts();
    }
  }

  // الحسابات الافتراضية
  getDefaultAccounts() {
    return [
      { id: 1, code: '1001', name: 'النقدية', type: 'asset', balance: 0, description: 'النقدية في الصندوق' },
      { id: 2, code: '1002', name: 'البنك', type: 'asset', balance: 0, description: 'الحساب الجاري في البنك' },
      { id: 3, code: '1101', name: 'العملاء', type: 'asset', balance: 0, description: 'مدينو العملاء' },
      { id: 4, code: '1201', name: 'المخزون', type: 'asset', balance: 0, description: 'مخزون البضائع' },
      { id: 5, code: '2001', name: 'الموردون', type: 'liability', balance: 0, description: 'دائنو الموردين' },
      { id: 6, code: '2101', name: 'ضريبة القيمة المضافة', type: 'liability', balance: 0, description: 'ضريبة القيمة المضافة المستحقة' },
      { id: 7, code: '3001', name: 'رأس المال', type: 'equity', balance: 0, description: 'رأس مال المالك' },
      { id: 8, code: '4001', name: 'المبيعات', type: 'revenue', balance: 0, description: 'إيرادات المبيعات' },
      { id: 9, code: '5001', name: 'تكلفة البضاعة المباعة', type: 'expense', balance: 0, description: 'تكلفة البضائع المباعة' },
      { id: 10, code: '5101', name: 'مصاريف التشغيل', type: 'expense', balance: 0, description: 'المصاريف التشغيلية' }
    ];
  }

  // إضافة حساب جديد
  addAccount(account) {
    try {
      const accounts = this.getAccounts();
      const newAccount = {
        ...account,
        id: this.generateAccountId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      accounts.push(newAccount);
      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));
      return newAccount;
    } catch (error) {
      console.error('خطأ في إضافة الحساب:', error);
      throw new Error('فشل في إضافة الحساب');
    }
  }

  // توليد معرف حساب
  generateAccountId() {
    const accounts = this.getAccounts();
    return accounts.length > 0 ? Math.max(...accounts.map(a => a.id)) + 1 : 1;
  }

  // جلب جميع القيود المحاسبية
  getJournalEntries() {
    try {
      const entries = localStorage.getItem('saudi_accounting_journal_entries');
      return entries ? JSON.parse(entries) : [];
    } catch (error) {
      console.error('خطأ في جلب القيود:', error);
      return [];
    }
  }

  // إضافة قيد محاسبي
  addJournalEntry(entry) {
    try {
      const entries = this.getJournalEntries();
      const newEntry = {
        ...entry,
        id: this.generateJournalEntryId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      entries.push(newEntry);
      localStorage.setItem('saudi_accounting_journal_entries', JSON.stringify(entries));

      // تحديث أرصدة الحسابات
      this.updateAccountBalances(newEntry);

      return newEntry;
    } catch (error) {
      console.error('خطأ في إضافة القيد:', error);
      throw new Error('فشل في إضافة القيد');
    }
  }

  // تحديث أرصدة الحسابات
  updateAccountBalances(entry) {
    try {
      const accounts = this.getAccounts();

      entry.entries.forEach(line => {
        const accountIndex = accounts.findIndex(a => a.id === parseInt(line.accountId));
        if (accountIndex !== -1) {
          // للأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص
          // للخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص
          if (accounts[accountIndex].type === 'asset' || accounts[accountIndex].type === 'expense') {
            accounts[accountIndex].balance += (line.debit - line.credit);
          } else {
            accounts[accountIndex].balance += (line.credit - line.debit);
          }
          accounts[accountIndex].updatedAt = new Date().toISOString();
        }
      });

      localStorage.setItem('saudi_accounting_accounts', JSON.stringify(accounts));
    } catch (error) {
      console.error('خطأ في تحديث أرصدة الحسابات:', error);
    }
  }

  // توليد معرف قيد محاسبي
  generateJournalEntryId() {
    const entries = this.getJournalEntries();
    return entries.length > 0 ? Math.max(...entries.map(e => e.id)) + 1 : 1;
  }

  incrementInvoiceNumber() {
    try {
      const settings = this.getSettings();
      settings.nextInvoiceNumber += 1;
      localStorage.setItem('saudi_accounting_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('خطأ في تحديث رقم الفاتورة:', error);
    }
  }

  // === إدارة الإعدادات ===
  
  getSettings() {
    try {
      const settings = localStorage.getItem('saudi_accounting_settings');
      return settings ? JSON.parse(settings) : this.getDefaultSettings();
    } catch (error) {
      console.error('خطأ في جلب الإعدادات:', error);
      return this.getDefaultSettings();
    }
  }

  updateSettings(updates) {
    try {
      const settings = this.getSettings();
      const newSettings = { ...settings, ...updates, updatedAt: new Date().toISOString() };
      localStorage.setItem('saudi_accounting_settings', JSON.stringify(newSettings));
      return newSettings;
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error);
      throw new Error('فشل في تحديث الإعدادات');
    }
  }

  // === التقارير والإحصائيات ===
  
  // إحصائيات اليوم
  getTodayStats() {
    const today = new Date().toDateString();
    const todayInvoices = this.getInvoicesByDate(today);
    
    const totalSales = todayInvoices.reduce((sum, invoice) => sum + invoice.total, 0);
    const totalVAT = todayInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);
    const totalInvoices = todayInvoices.length;
    
    return {
      totalSales,
      totalVAT,
      totalInvoices,
      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0
    };
  }

  // إحصائيات الشهر
  getMonthStats() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const monthInvoices = this.getInvoices().filter(invoice => {
      const invoiceDate = new Date(invoice.createdAt);
      return invoiceDate.getMonth() === currentMonth && invoiceDate.getFullYear() === currentYear;
    });
    
    const totalSales = monthInvoices.reduce((sum, invoice) => sum + invoice.total, 0);
    const totalVAT = monthInvoices.reduce((sum, invoice) => sum + invoice.vat, 0);
    const totalInvoices = monthInvoices.length;
    
    return {
      totalSales,
      totalVAT,
      totalInvoices,
      averageInvoice: totalInvoices > 0 ? totalSales / totalInvoices : 0
    };
  }

  // منتجات منخفضة المخزون
  getLowStockProducts(threshold = 5) {
    const products = this.getProducts();
    return products.filter(product => product.stock <= threshold);
  }

  // أفضل المنتجات مبيعاً
  getTopSellingProducts(limit = 5) {
    const invoices = this.getInvoices();
    const productSales = {};
    
    invoices.forEach(invoice => {
      invoice.items.forEach(item => {
        if (productSales[item.id]) {
          productSales[item.id].quantity += item.quantity;
          productSales[item.id].revenue += item.price * item.quantity;
        } else {
          productSales[item.id] = {
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            revenue: item.price * item.quantity
          };
        }
      });
    });
    
    return Object.values(productSales)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, limit);
  }

  // تصدير البيانات
  exportData() {
    return {
      invoices: this.getInvoices(),
      products: this.getProducts(),
      customers: this.getCustomers(),
      suppliers: this.getSuppliers(),
      accounts: this.getAccounts(),
      journalEntries: this.getJournalEntries(),
      settings: this.getSettings(),
      exportDate: new Date().toISOString()
    };
  }

  // استيراد البيانات
  importData(data) {
    try {
      if (data.invoices) {
        localStorage.setItem('saudi_accounting_invoices', JSON.stringify(data.invoices));
      }
      if (data.products) {
        localStorage.setItem('saudi_accounting_products', JSON.stringify(data.products));
      }
      if (data.customers) {
        localStorage.setItem('saudi_accounting_customers', JSON.stringify(data.customers));
      }
      if (data.settings) {
        localStorage.setItem('saudi_accounting_settings', JSON.stringify(data.settings));
      }
      return true;
    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      throw new Error('فشل في استيراد البيانات');
    }
  }

  // مسح جميع البيانات
  clearAllData() {
    try {
      localStorage.removeItem('saudi_accounting_invoices');
      localStorage.removeItem('saudi_accounting_products');
      localStorage.removeItem('saudi_accounting_customers');
      localStorage.removeItem('saudi_accounting_settings');
      this.initializeDatabase();
      return true;
    } catch (error) {
      console.error('خطأ في مسح البيانات:', error);
      throw new Error('فشل في مسح البيانات');
    }
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
const database = new LocalDatabase();

export default database;
