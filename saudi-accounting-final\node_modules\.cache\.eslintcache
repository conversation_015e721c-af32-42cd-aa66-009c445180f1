[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12"}, {"size": 254, "mtime": *************, "results": "13", "hashOfConfig": "14"}, {"size": 6093, "mtime": *************, "results": "15", "hashOfConfig": "14"}, {"size": 8310, "mtime": *************, "results": "16", "hashOfConfig": "14"}, {"size": 7025, "mtime": *************, "results": "17", "hashOfConfig": "14"}, {"size": 4637, "mtime": *************, "results": "18", "hashOfConfig": "14"}, {"size": 25866, "mtime": *************, "results": "19", "hashOfConfig": "14"}, {"size": 22489, "mtime": *************, "results": "20", "hashOfConfig": "14"}, {"size": 13139, "mtime": *************, "results": "21", "hashOfConfig": "14"}, {"size": 21589, "mtime": *************, "results": "22", "hashOfConfig": "14"}, {"size": 22737, "mtime": *************, "results": "23", "hashOfConfig": "14"}, {"size": 23370, "mtime": *************, "results": "24", "hashOfConfig": "14"}, {"size": 29658, "mtime": *************, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["62", "63"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 7, "column": 22, "nodeType": "66", "messageId": "67", "endLine": 7, "endColumn": 35}, {"ruleId": "64", "severity": 1, "message": "68", "line": 8, "column": 17, "nodeType": "66", "messageId": "67", "endLine": 8, "endColumn": 25}, "no-unused-vars", "'setCategories' is assigned a value but never used.", "Identifier", "unusedVar", "'setUnits' is assigned a value but never used."]