[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12", "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js": "13", "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js": "14", "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js": "15", "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js": "16", "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js": "17", "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js": "18", "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js": "19", "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js": "20", "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js": "21", "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js": "22", "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js": "23", "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js": "24"}, {"size": 254, "mtime": *************, "results": "25", "hashOfConfig": "26"}, {"size": 8714, "mtime": *************, "results": "27", "hashOfConfig": "26"}, {"size": 8310, "mtime": *************, "results": "28", "hashOfConfig": "26"}, {"size": 10487, "mtime": *************, "results": "29", "hashOfConfig": "26"}, {"size": 4637, "mtime": *************, "results": "30", "hashOfConfig": "26"}, {"size": 26350, "mtime": *************, "results": "31", "hashOfConfig": "26"}, {"size": 56672, "mtime": *************, "results": "32", "hashOfConfig": "26"}, {"size": 13139, "mtime": *************, "results": "33", "hashOfConfig": "26"}, {"size": 21589, "mtime": *************, "results": "34", "hashOfConfig": "26"}, {"size": 22737, "mtime": *************, "results": "35", "hashOfConfig": "26"}, {"size": 23370, "mtime": *************, "results": "36", "hashOfConfig": "26"}, {"size": 29658, "mtime": *************, "results": "37", "hashOfConfig": "26"}, {"size": 16871, "mtime": 1754281749934, "results": "38", "hashOfConfig": "26"}, {"size": 14016, "mtime": 1754281646202, "results": "39", "hashOfConfig": "26"}, {"size": 17048, "mtime": 1754281914610, "results": "40", "hashOfConfig": "26"}, {"size": 18668, "mtime": 1754282084604, "results": "41", "hashOfConfig": "26"}, {"size": 13415, "mtime": 1754282378597, "results": "42", "hashOfConfig": "26"}, {"size": 13334, "mtime": 1754282574915, "results": "43", "hashOfConfig": "26"}, {"size": 23709, "mtime": 1754283833610, "results": "44", "hashOfConfig": "26"}, {"size": 17560, "mtime": 1754284072328, "results": "45", "hashOfConfig": "26"}, {"size": 30890, "mtime": 1754284188023, "results": "46", "hashOfConfig": "26"}, {"size": 27147, "mtime": 1754283996127, "results": "47", "hashOfConfig": "26"}, {"size": 26944, "mtime": 1754284448466, "results": "48", "hashOfConfig": "26"}, {"size": 41186, "mtime": 1754285732743, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", ["122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138"], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", ["139", "140", "141", "142"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["143", "144"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js", ["145"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js", ["146"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js", ["147"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js", ["148"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js", ["149", "150"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js", ["151", "152"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js", [], [], {"ruleId": "153", "severity": 1, "message": "154", "line": 8, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 8, "endColumn": 19}, {"ruleId": "153", "severity": 1, "message": "157", "line": 8, "column": 21, "nodeType": "155", "messageId": "156", "endLine": 8, "endColumn": 33}, {"ruleId": "153", "severity": 1, "message": "158", "line": 10, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 10, "endColumn": 26}, {"ruleId": "153", "severity": 1, "message": "159", "line": 10, "column": 28, "nodeType": "155", "messageId": "156", "endLine": 10, "endColumn": 47}, {"ruleId": "153", "severity": 1, "message": "160", "line": 11, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 11, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "161", "line": 11, "column": 26, "nodeType": "155", "messageId": "156", "endLine": 11, "endColumn": 43}, {"ruleId": "153", "severity": 1, "message": "162", "line": 19, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 19, "endColumn": 18}, {"ruleId": "153", "severity": 1, "message": "163", "line": 19, "column": 20, "nodeType": "155", "messageId": "156", "endLine": 19, "endColumn": 31}, {"ruleId": "153", "severity": 1, "message": "164", "line": 21, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 21, "endColumn": 17}, {"ruleId": "153", "severity": 1, "message": "165", "line": 21, "column": 19, "nodeType": "155", "messageId": "156", "endLine": 21, "endColumn": 29}, {"ruleId": "153", "severity": 1, "message": "166", "line": 22, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 22, "endColumn": 26}, {"ruleId": "153", "severity": 1, "message": "167", "line": 22, "column": 28, "nodeType": "155", "messageId": "156", "endLine": 22, "endColumn": 47}, {"ruleId": "153", "severity": 1, "message": "168", "line": 23, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 23, "endColumn": 22}, {"ruleId": "153", "severity": 1, "message": "169", "line": 23, "column": 24, "nodeType": "155", "messageId": "156", "endLine": 23, "endColumn": 39}, {"ruleId": "153", "severity": 1, "message": "170", "line": 24, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 24, "endColumn": 30}, {"ruleId": "153", "severity": 1, "message": "171", "line": 24, "column": 32, "nodeType": "155", "messageId": "156", "endLine": 24, "endColumn": 55}, {"ruleId": "153", "severity": 1, "message": "172", "line": 25, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 25, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "174", "line": 815, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 850, "endColumn": 4}, {"ruleId": "173", "severity": 1, "message": "177", "line": 1520, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 1542, "endColumn": 4}, {"ruleId": "173", "severity": 1, "message": "178", "line": 1572, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 1582, "endColumn": 4}, {"ruleId": "173", "severity": 1, "message": "179", "line": 1626, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 1629, "endColumn": 4}, {"ruleId": "153", "severity": 1, "message": "180", "line": 7, "column": 22, "nodeType": "155", "messageId": "156", "endLine": 7, "endColumn": 35}, {"ruleId": "153", "severity": 1, "message": "181", "line": 8, "column": 17, "nodeType": "155", "messageId": "156", "endLine": 8, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "183", "line": 23, "column": 6, "nodeType": "184", "endLine": 23, "endColumn": 8, "suggestions": "185"}, {"ruleId": "153", "severity": 1, "message": "186", "line": 217, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 217, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "187", "line": 22, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 22, "endColumn": 17}, {"ruleId": "153", "severity": 1, "message": "188", "line": 386, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 386, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "188", "line": 275, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 275, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "189", "line": 280, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 280, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "190", "line": 344, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 344, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "191", "line": 349, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 349, "endColumn": 24}, "no-unused-vars", "'customers' is assigned a value but never used.", "Identifier", "unusedVar", "'setCustomers' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", "'setSelectedCustomer' is assigned a value but never used.", "'categoryFilter' is assigned a value but never used.", "'setCategoryFilter' is assigned a value but never used.", "'discount' is assigned a value but never used.", "'setDiscount' is assigned a value but never used.", "'message' is assigned a value but never used.", "'setMessage' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'setShowPaymentModal' is assigned a value but never used.", "'cashReceived' is assigned a value but never used.", "'setCashReceived' is assigned a value but never used.", "'currentInvoiceNumber' is assigned a value but never used.", "'setCurrentInvoiceNumber' is assigned a value but never used.", "'searchInputRef' is assigned a value but never used.", "no-dupe-class-members", "Duplicate name 'getDefaultSettings'.", "MethodDefinition", "unexpected", "Duplicate name 'updateAccountBalances'.", "Duplicate name 'updateSettings'.", "Duplicate name 'getLowStockProducts'.", "'setCategories' is assigned a value but never used.", "'setUnits' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateSystemNotifications'. Either include it or remove the dependency array.", "ArrayExpression", ["192"], "'getAccountName' is assigned a value but never used.", "'printRef' is assigned a value but never used.", "'getProductName' is assigned a value but never used.", "'getInvoiceNumber' is assigned a value but never used.", "'getCustomerName' is assigned a value but never used.", "'getSupplierName' is assigned a value but never used.", {"desc": "193", "fix": "194"}, "Update the dependencies array to be: [generateSystemNotifications]", {"range": "195", "text": "196"}, [924, 926], "[generateSystemNotifications]"]