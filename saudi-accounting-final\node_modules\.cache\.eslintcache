[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12", "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js": "13", "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js": "14", "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js": "15", "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js": "16", "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js": "17", "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js": "18", "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js": "19", "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js": "20", "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js": "21", "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js": "22", "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js": "23", "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js": "24", "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js": "25", "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js": "26", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js": "27", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js": "28", "D:\\aronim\\saudi-accounting-final\\src\\utils\\testDatabase.js": "29"}, {"size": 254, "mtime": *************, "results": "30", "hashOfConfig": "31"}, {"size": 5215, "mtime": *************, "results": "32", "hashOfConfig": "31"}, {"size": 8310, "mtime": *************, "results": "33", "hashOfConfig": "31"}, {"size": 10487, "mtime": *************, "results": "34", "hashOfConfig": "31"}, {"size": 4637, "mtime": *************, "results": "35", "hashOfConfig": "31"}, {"size": 26350, "mtime": *************, "results": "36", "hashOfConfig": "31"}, {"size": 60500, "mtime": *************, "results": "37", "hashOfConfig": "31"}, {"size": 13139, "mtime": *************, "results": "38", "hashOfConfig": "31"}, {"size": 21589, "mtime": *************, "results": "39", "hashOfConfig": "31"}, {"size": 22737, "mtime": *************, "results": "40", "hashOfConfig": "31"}, {"size": 23370, "mtime": *************, "results": "41", "hashOfConfig": "31"}, {"size": 29658, "mtime": *************, "results": "42", "hashOfConfig": "31"}, {"size": 16871, "mtime": 1754281749934, "results": "43", "hashOfConfig": "31"}, {"size": 14016, "mtime": 1754281646202, "results": "44", "hashOfConfig": "31"}, {"size": 17048, "mtime": 1754281914610, "results": "45", "hashOfConfig": "31"}, {"size": 18668, "mtime": 1754282084604, "results": "46", "hashOfConfig": "31"}, {"size": 13415, "mtime": 1754282378597, "results": "47", "hashOfConfig": "31"}, {"size": 13334, "mtime": 1754282574915, "results": "48", "hashOfConfig": "31"}, {"size": 23709, "mtime": 1754283833610, "results": "49", "hashOfConfig": "31"}, {"size": 17560, "mtime": 1754284072328, "results": "50", "hashOfConfig": "31"}, {"size": 30890, "mtime": 1754284188023, "results": "51", "hashOfConfig": "31"}, {"size": 27147, "mtime": 1754283996127, "results": "52", "hashOfConfig": "31"}, {"size": 26944, "mtime": 1754284448466, "results": "53", "hashOfConfig": "31"}, {"size": 41186, "mtime": 1754285732743, "results": "54", "hashOfConfig": "31"}, {"size": 6246, "mtime": 1754289297544, "results": "55", "hashOfConfig": "31"}, {"size": 20543, "mtime": 1754290645252, "results": "56", "hashOfConfig": "31"}, {"size": 11999, "mtime": 1754292252744, "results": "57", "hashOfConfig": "31"}, {"size": 12085, "mtime": 1754293704080, "results": "58", "hashOfConfig": "31"}, {"size": 1000, "mtime": 1754293560642, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", ["147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163"], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", ["164", "165", "166", "167"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["168", "169"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js", ["170"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js", ["171"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js", ["172"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js", ["173"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js", ["174", "175"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js", ["176", "177"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js", ["178", "179"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js", ["180", "181"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\testDatabase.js", [], [], {"ruleId": "182", "severity": 1, "message": "183", "line": 8, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 8, "endColumn": 19}, {"ruleId": "182", "severity": 1, "message": "186", "line": 8, "column": 21, "nodeType": "184", "messageId": "185", "endLine": 8, "endColumn": 33}, {"ruleId": "182", "severity": 1, "message": "187", "line": 10, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 10, "endColumn": 26}, {"ruleId": "182", "severity": 1, "message": "188", "line": 10, "column": 28, "nodeType": "184", "messageId": "185", "endLine": 10, "endColumn": 47}, {"ruleId": "182", "severity": 1, "message": "189", "line": 11, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 11, "endColumn": 24}, {"ruleId": "182", "severity": 1, "message": "190", "line": 11, "column": 26, "nodeType": "184", "messageId": "185", "endLine": 11, "endColumn": 43}, {"ruleId": "182", "severity": 1, "message": "191", "line": 19, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 19, "endColumn": 18}, {"ruleId": "182", "severity": 1, "message": "192", "line": 19, "column": 20, "nodeType": "184", "messageId": "185", "endLine": 19, "endColumn": 31}, {"ruleId": "182", "severity": 1, "message": "193", "line": 21, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 21, "endColumn": 17}, {"ruleId": "182", "severity": 1, "message": "194", "line": 21, "column": 19, "nodeType": "184", "messageId": "185", "endLine": 21, "endColumn": 29}, {"ruleId": "182", "severity": 1, "message": "195", "line": 22, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 22, "endColumn": 26}, {"ruleId": "182", "severity": 1, "message": "196", "line": 22, "column": 28, "nodeType": "184", "messageId": "185", "endLine": 22, "endColumn": 47}, {"ruleId": "182", "severity": 1, "message": "197", "line": 23, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 23, "endColumn": 22}, {"ruleId": "182", "severity": 1, "message": "198", "line": 23, "column": 24, "nodeType": "184", "messageId": "185", "endLine": 23, "endColumn": 39}, {"ruleId": "182", "severity": 1, "message": "199", "line": 24, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 24, "endColumn": 30}, {"ruleId": "182", "severity": 1, "message": "200", "line": 24, "column": 32, "nodeType": "184", "messageId": "185", "endLine": 24, "endColumn": 55}, {"ruleId": "182", "severity": 1, "message": "201", "line": 25, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 25, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "203", "line": 822, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 857, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "206", "line": 1527, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 1549, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "207", "line": 1579, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 1589, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "208", "line": 1633, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 1636, "endColumn": 4}, {"ruleId": "182", "severity": 1, "message": "209", "line": 7, "column": 22, "nodeType": "184", "messageId": "185", "endLine": 7, "endColumn": 35}, {"ruleId": "182", "severity": 1, "message": "210", "line": 8, "column": 17, "nodeType": "184", "messageId": "185", "endLine": 8, "endColumn": 25}, {"ruleId": "211", "severity": 1, "message": "212", "line": 23, "column": 6, "nodeType": "213", "endLine": 23, "endColumn": 8, "suggestions": "214"}, {"ruleId": "182", "severity": 1, "message": "215", "line": 217, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 217, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "216", "line": 22, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 22, "endColumn": 17}, {"ruleId": "182", "severity": 1, "message": "217", "line": 386, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 386, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "217", "line": 275, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 275, "endColumn": 23}, {"ruleId": "182", "severity": 1, "message": "218", "line": 280, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 280, "endColumn": 25}, {"ruleId": "182", "severity": 1, "message": "219", "line": 344, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 344, "endColumn": 24}, {"ruleId": "182", "severity": 1, "message": "220", "line": 349, "column": 9, "nodeType": "184", "messageId": "185", "endLine": 349, "endColumn": 24}, {"ruleId": "221", "severity": 1, "message": "222", "line": 144, "column": 11, "nodeType": "223", "endLine": 144, "endColumn": 84}, {"ruleId": "221", "severity": 1, "message": "222", "line": 153, "column": 15, "nodeType": "223", "endLine": 161, "endColumn": 16}, {"ruleId": "182", "severity": 1, "message": "224", "line": 13, "column": 10, "nodeType": "184", "messageId": "185", "endLine": 13, "endColumn": 17}, {"ruleId": "182", "severity": 1, "message": "225", "line": 13, "column": 19, "nodeType": "184", "messageId": "185", "endLine": 13, "endColumn": 29}, "no-unused-vars", "'customers' is assigned a value but never used.", "Identifier", "unusedVar", "'setCustomers' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", "'setSelectedCustomer' is assigned a value but never used.", "'categoryFilter' is assigned a value but never used.", "'setCategoryFilter' is assigned a value but never used.", "'discount' is assigned a value but never used.", "'setDiscount' is assigned a value but never used.", "'message' is assigned a value but never used.", "'setMessage' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'setShowPaymentModal' is assigned a value but never used.", "'cashReceived' is assigned a value but never used.", "'setCashReceived' is assigned a value but never used.", "'currentInvoiceNumber' is assigned a value but never used.", "'setCurrentInvoiceNumber' is assigned a value but never used.", "'searchInputRef' is assigned a value but never used.", "no-dupe-class-members", "Duplicate name 'getDefaultSettings'.", "MethodDefinition", "unexpected", "Duplicate name 'updateAccountBalances'.", "Duplicate name 'updateSettings'.", "Duplicate name 'getLowStockProducts'.", "'setCategories' is assigned a value but never used.", "'setUnits' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateSystemNotifications'. Either include it or remove the dependency array.", "ArrayExpression", ["226"], "'getAccountName' is assigned a value but never used.", "'printRef' is assigned a value but never used.", "'getProductName' is assigned a value but never used.", "'getInvoiceNumber' is assigned a value but never used.", "'getCustomerName' is assigned a value but never used.", "'getSupplierName' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", {"desc": "227", "fix": "228"}, "Update the dependencies array to be: [generateSystemNotifications]", {"range": "229", "text": "230"}, [924, 926], "[generateSystemNotifications]"]