[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12", "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js": "13", "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js": "14", "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js": "15", "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js": "16", "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js": "17", "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js": "18", "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js": "19", "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js": "20", "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js": "21", "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js": "22", "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js": "23", "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js": "24", "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js": "25", "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js": "26", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js": "27", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js": "28"}, {"size": 254, "mtime": *************, "results": "29", "hashOfConfig": "30"}, {"size": 5215, "mtime": *************, "results": "31", "hashOfConfig": "30"}, {"size": 8310, "mtime": *************, "results": "32", "hashOfConfig": "30"}, {"size": 10487, "mtime": *************, "results": "33", "hashOfConfig": "30"}, {"size": 4637, "mtime": *************, "results": "34", "hashOfConfig": "30"}, {"size": 26350, "mtime": *************, "results": "35", "hashOfConfig": "30"}, {"size": 57445, "mtime": *************, "results": "36", "hashOfConfig": "30"}, {"size": 13139, "mtime": *************, "results": "37", "hashOfConfig": "30"}, {"size": 21589, "mtime": *************, "results": "38", "hashOfConfig": "30"}, {"size": 22737, "mtime": *************, "results": "39", "hashOfConfig": "30"}, {"size": 23370, "mtime": *************, "results": "40", "hashOfConfig": "30"}, {"size": 29658, "mtime": *************, "results": "41", "hashOfConfig": "30"}, {"size": 16871, "mtime": 1754281749934, "results": "42", "hashOfConfig": "30"}, {"size": 14016, "mtime": 1754281646202, "results": "43", "hashOfConfig": "30"}, {"size": 17048, "mtime": 1754281914610, "results": "44", "hashOfConfig": "30"}, {"size": 18668, "mtime": 1754282084604, "results": "45", "hashOfConfig": "30"}, {"size": 13415, "mtime": 1754282378597, "results": "46", "hashOfConfig": "30"}, {"size": 13334, "mtime": 1754282574915, "results": "47", "hashOfConfig": "30"}, {"size": 23709, "mtime": 1754283833610, "results": "48", "hashOfConfig": "30"}, {"size": 17560, "mtime": 1754284072328, "results": "49", "hashOfConfig": "30"}, {"size": 30890, "mtime": 1754284188023, "results": "50", "hashOfConfig": "30"}, {"size": 27147, "mtime": 1754283996127, "results": "51", "hashOfConfig": "30"}, {"size": 26944, "mtime": 1754284448466, "results": "52", "hashOfConfig": "30"}, {"size": 41186, "mtime": 1754285732743, "results": "53", "hashOfConfig": "30"}, {"size": 6246, "mtime": 1754289297544, "results": "54", "hashOfConfig": "30"}, {"size": 20543, "mtime": 1754290645252, "results": "55", "hashOfConfig": "30"}, {"size": 11999, "mtime": 1754292252744, "results": "56", "hashOfConfig": "30"}, {"size": 9988, "mtime": 1754292451876, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", ["142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158"], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", ["159", "160", "161", "162"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["163", "164"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js", ["165"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js", ["166"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js", ["167"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js", ["168"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js", ["169", "170"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js", ["171", "172"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js", ["173", "174"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js", ["175", "176"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js", [], [], {"ruleId": "177", "severity": 1, "message": "178", "line": 8, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 8, "endColumn": 19}, {"ruleId": "177", "severity": 1, "message": "181", "line": 8, "column": 21, "nodeType": "179", "messageId": "180", "endLine": 8, "endColumn": 33}, {"ruleId": "177", "severity": 1, "message": "182", "line": 10, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 10, "endColumn": 26}, {"ruleId": "177", "severity": 1, "message": "183", "line": 10, "column": 28, "nodeType": "179", "messageId": "180", "endLine": 10, "endColumn": 47}, {"ruleId": "177", "severity": 1, "message": "184", "line": 11, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 11, "endColumn": 24}, {"ruleId": "177", "severity": 1, "message": "185", "line": 11, "column": 26, "nodeType": "179", "messageId": "180", "endLine": 11, "endColumn": 43}, {"ruleId": "177", "severity": 1, "message": "186", "line": 19, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 19, "endColumn": 18}, {"ruleId": "177", "severity": 1, "message": "187", "line": 19, "column": 20, "nodeType": "179", "messageId": "180", "endLine": 19, "endColumn": 31}, {"ruleId": "177", "severity": 1, "message": "188", "line": 21, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 21, "endColumn": 17}, {"ruleId": "177", "severity": 1, "message": "189", "line": 21, "column": 19, "nodeType": "179", "messageId": "180", "endLine": 21, "endColumn": 29}, {"ruleId": "177", "severity": 1, "message": "190", "line": 22, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 22, "endColumn": 26}, {"ruleId": "177", "severity": 1, "message": "191", "line": 22, "column": 28, "nodeType": "179", "messageId": "180", "endLine": 22, "endColumn": 47}, {"ruleId": "177", "severity": 1, "message": "192", "line": 23, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 23, "endColumn": 22}, {"ruleId": "177", "severity": 1, "message": "193", "line": 23, "column": 24, "nodeType": "179", "messageId": "180", "endLine": 23, "endColumn": 39}, {"ruleId": "177", "severity": 1, "message": "194", "line": 24, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 24, "endColumn": 30}, {"ruleId": "177", "severity": 1, "message": "195", "line": 24, "column": 32, "nodeType": "179", "messageId": "180", "endLine": 24, "endColumn": 55}, {"ruleId": "177", "severity": 1, "message": "196", "line": 25, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 25, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "198", "line": 815, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 850, "endColumn": 4}, {"ruleId": "197", "severity": 1, "message": "201", "line": 1520, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 1542, "endColumn": 4}, {"ruleId": "197", "severity": 1, "message": "202", "line": 1572, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 1582, "endColumn": 4}, {"ruleId": "197", "severity": 1, "message": "203", "line": 1626, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 1629, "endColumn": 4}, {"ruleId": "177", "severity": 1, "message": "204", "line": 7, "column": 22, "nodeType": "179", "messageId": "180", "endLine": 7, "endColumn": 35}, {"ruleId": "177", "severity": 1, "message": "205", "line": 8, "column": 17, "nodeType": "179", "messageId": "180", "endLine": 8, "endColumn": 25}, {"ruleId": "206", "severity": 1, "message": "207", "line": 23, "column": 6, "nodeType": "208", "endLine": 23, "endColumn": 8, "suggestions": "209"}, {"ruleId": "177", "severity": 1, "message": "210", "line": 217, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 217, "endColumn": 23}, {"ruleId": "177", "severity": 1, "message": "211", "line": 22, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 22, "endColumn": 17}, {"ruleId": "177", "severity": 1, "message": "212", "line": 386, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 386, "endColumn": 23}, {"ruleId": "177", "severity": 1, "message": "212", "line": 275, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 275, "endColumn": 23}, {"ruleId": "177", "severity": 1, "message": "213", "line": 280, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 280, "endColumn": 25}, {"ruleId": "177", "severity": 1, "message": "214", "line": 344, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 344, "endColumn": 24}, {"ruleId": "177", "severity": 1, "message": "215", "line": 349, "column": 9, "nodeType": "179", "messageId": "180", "endLine": 349, "endColumn": 24}, {"ruleId": "216", "severity": 1, "message": "217", "line": 144, "column": 11, "nodeType": "218", "endLine": 144, "endColumn": 84}, {"ruleId": "216", "severity": 1, "message": "217", "line": 153, "column": 15, "nodeType": "218", "endLine": 161, "endColumn": 16}, {"ruleId": "177", "severity": 1, "message": "219", "line": 13, "column": 10, "nodeType": "179", "messageId": "180", "endLine": 13, "endColumn": 17}, {"ruleId": "177", "severity": 1, "message": "220", "line": 13, "column": 19, "nodeType": "179", "messageId": "180", "endLine": 13, "endColumn": 29}, "no-unused-vars", "'customers' is assigned a value but never used.", "Identifier", "unusedVar", "'setCustomers' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", "'setSelectedCustomer' is assigned a value but never used.", "'categoryFilter' is assigned a value but never used.", "'setCategoryFilter' is assigned a value but never used.", "'discount' is assigned a value but never used.", "'setDiscount' is assigned a value but never used.", "'message' is assigned a value but never used.", "'setMessage' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'setShowPaymentModal' is assigned a value but never used.", "'cashReceived' is assigned a value but never used.", "'setCashReceived' is assigned a value but never used.", "'currentInvoiceNumber' is assigned a value but never used.", "'setCurrentInvoiceNumber' is assigned a value but never used.", "'searchInputRef' is assigned a value but never used.", "no-dupe-class-members", "Duplicate name 'getDefaultSettings'.", "MethodDefinition", "unexpected", "Duplicate name 'updateAccountBalances'.", "Duplicate name 'updateSettings'.", "Duplicate name 'getLowStockProducts'.", "'setCategories' is assigned a value but never used.", "'setUnits' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateSystemNotifications'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "'getAccountName' is assigned a value but never used.", "'printRef' is assigned a value but never used.", "'getProductName' is assigned a value but never used.", "'getInvoiceNumber' is assigned a value but never used.", "'getCustomerName' is assigned a value but never used.", "'getSupplierName' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", {"desc": "222", "fix": "223"}, "Update the dependencies array to be: [generateSystemNotifications]", {"range": "224", "text": "225"}, [924, 926], "[generateSystemNotifications]"]