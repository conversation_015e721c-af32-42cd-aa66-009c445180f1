[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12", "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js": "13", "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js": "14", "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js": "15", "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js": "16", "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js": "17", "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js": "18", "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js": "19", "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js": "20", "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js": "21", "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js": "22", "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js": "23", "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js": "24", "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js": "25", "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js": "26", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js": "27", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js": "28", "D:\\aronim\\saudi-accounting-final\\src\\utils\\testDatabase.js": "29", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\ProfessionalPOS.js": "30"}, {"size": 254, "mtime": *************, "results": "31", "hashOfConfig": "32"}, {"size": 5251, "mtime": *************, "results": "33", "hashOfConfig": "32"}, {"size": 8310, "mtime": *************, "results": "34", "hashOfConfig": "32"}, {"size": 10487, "mtime": *************, "results": "35", "hashOfConfig": "32"}, {"size": 4637, "mtime": *************, "results": "36", "hashOfConfig": "32"}, {"size": 26350, "mtime": *************, "results": "37", "hashOfConfig": "32"}, {"size": 62919, "mtime": *************, "results": "38", "hashOfConfig": "32"}, {"size": 13139, "mtime": *************, "results": "39", "hashOfConfig": "32"}, {"size": 21589, "mtime": *************, "results": "40", "hashOfConfig": "32"}, {"size": 22737, "mtime": *************, "results": "41", "hashOfConfig": "32"}, {"size": 23370, "mtime": *************, "results": "42", "hashOfConfig": "32"}, {"size": 29658, "mtime": *************, "results": "43", "hashOfConfig": "32"}, {"size": 16871, "mtime": 1754281749934, "results": "44", "hashOfConfig": "32"}, {"size": 14016, "mtime": 1754281646202, "results": "45", "hashOfConfig": "32"}, {"size": 17048, "mtime": 1754281914610, "results": "46", "hashOfConfig": "32"}, {"size": 18668, "mtime": 1754282084604, "results": "47", "hashOfConfig": "32"}, {"size": 13415, "mtime": 1754282378597, "results": "48", "hashOfConfig": "32"}, {"size": 13334, "mtime": 1754282574915, "results": "49", "hashOfConfig": "32"}, {"size": 23709, "mtime": 1754283833610, "results": "50", "hashOfConfig": "32"}, {"size": 17560, "mtime": 1754284072328, "results": "51", "hashOfConfig": "32"}, {"size": 30890, "mtime": 1754284188023, "results": "52", "hashOfConfig": "32"}, {"size": 27147, "mtime": 1754283996127, "results": "53", "hashOfConfig": "32"}, {"size": 26944, "mtime": 1754284448466, "results": "54", "hashOfConfig": "32"}, {"size": 41186, "mtime": 1754285732743, "results": "55", "hashOfConfig": "32"}, {"size": 6246, "mtime": 1754289297544, "results": "56", "hashOfConfig": "32"}, {"size": 20543, "mtime": 1754290645252, "results": "57", "hashOfConfig": "32"}, {"size": 11999, "mtime": 1754292252744, "results": "58", "hashOfConfig": "32"}, {"size": 12085, "mtime": 1754293704080, "results": "59", "hashOfConfig": "32"}, {"size": 1000, "mtime": 1754293560642, "results": "60", "hashOfConfig": "32"}, {"size": 14875, "mtime": 1754294823813, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", ["152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168"], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", ["169", "170", "171", "172"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["173", "174"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js", ["175"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js", ["176"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js", ["177"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js", ["178"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js", ["179", "180"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js", ["181", "182"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Layout\\Layout.js", ["183", "184"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Invoices\\Invoices.js", ["185", "186"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\EnhancedDashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\EnhancedLogin.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\testDatabase.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\ProfessionalPOS.js", ["187"], [], {"ruleId": "188", "severity": 1, "message": "189", "line": 8, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 8, "endColumn": 19}, {"ruleId": "188", "severity": 1, "message": "192", "line": 8, "column": 21, "nodeType": "190", "messageId": "191", "endLine": 8, "endColumn": 33}, {"ruleId": "188", "severity": 1, "message": "193", "line": 10, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 10, "endColumn": 26}, {"ruleId": "188", "severity": 1, "message": "194", "line": 10, "column": 28, "nodeType": "190", "messageId": "191", "endLine": 10, "endColumn": 47}, {"ruleId": "188", "severity": 1, "message": "195", "line": 11, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 11, "endColumn": 24}, {"ruleId": "188", "severity": 1, "message": "196", "line": 11, "column": 26, "nodeType": "190", "messageId": "191", "endLine": 11, "endColumn": 43}, {"ruleId": "188", "severity": 1, "message": "197", "line": 19, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 19, "endColumn": 18}, {"ruleId": "188", "severity": 1, "message": "198", "line": 19, "column": 20, "nodeType": "190", "messageId": "191", "endLine": 19, "endColumn": 31}, {"ruleId": "188", "severity": 1, "message": "199", "line": 21, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 21, "endColumn": 17}, {"ruleId": "188", "severity": 1, "message": "200", "line": 21, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 21, "endColumn": 29}, {"ruleId": "188", "severity": 1, "message": "201", "line": 22, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 22, "endColumn": 26}, {"ruleId": "188", "severity": 1, "message": "202", "line": 22, "column": 28, "nodeType": "190", "messageId": "191", "endLine": 22, "endColumn": 47}, {"ruleId": "188", "severity": 1, "message": "203", "line": 23, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 23, "endColumn": 22}, {"ruleId": "188", "severity": 1, "message": "204", "line": 23, "column": 24, "nodeType": "190", "messageId": "191", "endLine": 23, "endColumn": 39}, {"ruleId": "188", "severity": 1, "message": "205", "line": 24, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 24, "endColumn": 30}, {"ruleId": "188", "severity": 1, "message": "206", "line": 24, "column": 32, "nodeType": "190", "messageId": "191", "endLine": 24, "endColumn": 55}, {"ruleId": "188", "severity": 1, "message": "207", "line": 25, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 25, "endColumn": 23}, {"ruleId": "208", "severity": 1, "message": "209", "line": 901, "column": 3, "nodeType": "210", "messageId": "211", "endLine": 936, "endColumn": 4}, {"ruleId": "208", "severity": 1, "message": "212", "line": 1606, "column": 3, "nodeType": "210", "messageId": "211", "endLine": 1628, "endColumn": 4}, {"ruleId": "208", "severity": 1, "message": "213", "line": 1658, "column": 3, "nodeType": "210", "messageId": "211", "endLine": 1668, "endColumn": 4}, {"ruleId": "208", "severity": 1, "message": "214", "line": 1712, "column": 3, "nodeType": "210", "messageId": "211", "endLine": 1715, "endColumn": 4}, {"ruleId": "188", "severity": 1, "message": "215", "line": 7, "column": 22, "nodeType": "190", "messageId": "191", "endLine": 7, "endColumn": 35}, {"ruleId": "188", "severity": 1, "message": "216", "line": 8, "column": 17, "nodeType": "190", "messageId": "191", "endLine": 8, "endColumn": 25}, {"ruleId": "217", "severity": 1, "message": "218", "line": 23, "column": 6, "nodeType": "219", "endLine": 23, "endColumn": 8, "suggestions": "220"}, {"ruleId": "188", "severity": 1, "message": "221", "line": 217, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 217, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "222", "line": 22, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 22, "endColumn": 17}, {"ruleId": "188", "severity": 1, "message": "223", "line": 386, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 386, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "223", "line": 275, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 275, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "224", "line": 280, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 280, "endColumn": 25}, {"ruleId": "188", "severity": 1, "message": "225", "line": 344, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 344, "endColumn": 24}, {"ruleId": "188", "severity": 1, "message": "226", "line": 349, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 349, "endColumn": 24}, {"ruleId": "227", "severity": 1, "message": "228", "line": 144, "column": 11, "nodeType": "229", "endLine": 144, "endColumn": 84}, {"ruleId": "227", "severity": 1, "message": "228", "line": 153, "column": 15, "nodeType": "229", "endLine": 161, "endColumn": 16}, {"ruleId": "188", "severity": 1, "message": "230", "line": 13, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 13, "endColumn": 17}, {"ruleId": "188", "severity": 1, "message": "231", "line": 13, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 13, "endColumn": 29}, {"ruleId": "217", "severity": 1, "message": "232", "line": 37, "column": 6, "nodeType": "219", "endLine": 37, "endColumn": 8, "suggestions": "233"}, "no-unused-vars", "'customers' is assigned a value but never used.", "Identifier", "unusedVar", "'setCustomers' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", "'setSelectedCustomer' is assigned a value but never used.", "'categoryFilter' is assigned a value but never used.", "'setCategoryFilter' is assigned a value but never used.", "'discount' is assigned a value but never used.", "'setDiscount' is assigned a value but never used.", "'message' is assigned a value but never used.", "'setMessage' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'setShowPaymentModal' is assigned a value but never used.", "'cashReceived' is assigned a value but never used.", "'setCashReceived' is assigned a value but never used.", "'currentInvoiceNumber' is assigned a value but never used.", "'setCurrentInvoiceNumber' is assigned a value but never used.", "'searchInputRef' is assigned a value but never used.", "no-dupe-class-members", "Duplicate name 'getDefaultSettings'.", "MethodDefinition", "unexpected", "Duplicate name 'updateAccountBalances'.", "Duplicate name 'updateSettings'.", "Duplicate name 'getLowStockProducts'.", "'setCategories' is assigned a value but never used.", "'setUnits' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateSystemNotifications'. Either include it or remove the dependency array.", "ArrayExpression", ["234"], "'getAccountName' is assigned a value but never used.", "'printRef' is assigned a value but never used.", "'getProductName' is assigned a value but never used.", "'getInvoiceNumber' is assigned a value but never used.", "'getCustomerName' is assigned a value but never used.", "'getSupplierName' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleCheckout'. Either include it or remove the dependency array.", ["235"], {"desc": "236", "fix": "237"}, {"desc": "238", "fix": "239"}, "Update the dependencies array to be: [generateSystemNotifications]", {"range": "240", "text": "241"}, "Update the dependencies array to be: [handleCheckout]", {"range": "242", "text": "243"}, [924, 926], "[generateSystemNotifications]", [1278, 1280], "[handleCheckout]"]