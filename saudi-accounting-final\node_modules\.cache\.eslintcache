[{"D:\\aronim\\saudi-accounting-final\\src\\index.js": "1", "D:\\aronim\\saudi-accounting-final\\src\\App.js": "2", "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js": "3", "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js": "4", "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js": "5", "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js": "6", "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js": "7", "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js": "8", "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js": "9", "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js": "10", "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js": "11", "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js": "12", "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js": "13", "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js": "14", "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js": "15", "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js": "16", "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js": "17", "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js": "18", "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js": "19", "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js": "20", "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js": "21", "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js": "22", "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js": "23", "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js": "24"}, {"size": 254, "mtime": *************, "results": "25", "hashOfConfig": "26"}, {"size": 8714, "mtime": *************, "results": "27", "hashOfConfig": "26"}, {"size": 8310, "mtime": *************, "results": "28", "hashOfConfig": "26"}, {"size": 10487, "mtime": *************, "results": "29", "hashOfConfig": "26"}, {"size": 4637, "mtime": *************, "results": "30", "hashOfConfig": "26"}, {"size": 25866, "mtime": *************, "results": "31", "hashOfConfig": "26"}, {"size": 56672, "mtime": *************, "results": "32", "hashOfConfig": "26"}, {"size": 13139, "mtime": *************, "results": "33", "hashOfConfig": "26"}, {"size": 21589, "mtime": *************, "results": "34", "hashOfConfig": "26"}, {"size": 22737, "mtime": *************, "results": "35", "hashOfConfig": "26"}, {"size": 23370, "mtime": *************, "results": "36", "hashOfConfig": "26"}, {"size": 29658, "mtime": *************, "results": "37", "hashOfConfig": "26"}, {"size": 16871, "mtime": 1754281749934, "results": "38", "hashOfConfig": "26"}, {"size": 14016, "mtime": 1754281646202, "results": "39", "hashOfConfig": "26"}, {"size": 17048, "mtime": 1754281914610, "results": "40", "hashOfConfig": "26"}, {"size": 18668, "mtime": 1754282084604, "results": "41", "hashOfConfig": "26"}, {"size": 13415, "mtime": 1754282378597, "results": "42", "hashOfConfig": "26"}, {"size": 13334, "mtime": 1754282574915, "results": "43", "hashOfConfig": "26"}, {"size": 23709, "mtime": 1754283833610, "results": "44", "hashOfConfig": "26"}, {"size": 17560, "mtime": 1754284072328, "results": "45", "hashOfConfig": "26"}, {"size": 30890, "mtime": 1754284188023, "results": "46", "hashOfConfig": "26"}, {"size": 27147, "mtime": 1754283996127, "results": "47", "hashOfConfig": "26"}, {"size": 26944, "mtime": 1754284448466, "results": "48", "hashOfConfig": "26"}, {"size": 30135, "mtime": 1754284597608, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i79zow", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\aronim\\saudi-accounting-final\\src\\index.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\App.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Auth\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Dashboard\\Dashboard.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Login\\Login.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\POS\\POS.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\utils\\database.js", ["122", "123", "124", "125"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Reports\\Reports.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Products\\Products.js", ["126", "127"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Customers\\Customers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Suppliers\\Suppliers.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Accounting\\Accounting.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Payments\\Payments.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Settings\\Settings.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Inventory\\Inventory.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Employees\\Employees.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Backup\\Backup.js", [], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Notifications\\Notifications.js", ["128"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\JournalEntries\\JournalEntries.js", ["129"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Barcode\\BarcodeManager.js", ["130"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Quotations\\Quotations.js", ["131"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Returns\\Returns.js", ["132", "133"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\Vouchers\\Vouchers.js", ["134", "135"], [], "D:\\aronim\\saudi-accounting-final\\src\\components\\AdvancedReports\\AdvancedReports.js", ["136", "137", "138", "139", "140", "141", "142", "143", "144", "145"], [], {"ruleId": "146", "severity": 1, "message": "147", "line": 815, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 850, "endColumn": 4}, {"ruleId": "146", "severity": 1, "message": "150", "line": 1520, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 1542, "endColumn": 4}, {"ruleId": "146", "severity": 1, "message": "151", "line": 1572, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 1582, "endColumn": 4}, {"ruleId": "146", "severity": 1, "message": "152", "line": 1626, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 1629, "endColumn": 4}, {"ruleId": "153", "severity": 1, "message": "154", "line": 7, "column": 22, "nodeType": "155", "messageId": "156", "endLine": 7, "endColumn": 35}, {"ruleId": "153", "severity": 1, "message": "157", "line": 8, "column": 17, "nodeType": "155", "messageId": "156", "endLine": 8, "endColumn": 25}, {"ruleId": "158", "severity": 1, "message": "159", "line": 23, "column": 6, "nodeType": "160", "endLine": 23, "endColumn": 8, "suggestions": "161"}, {"ruleId": "153", "severity": 1, "message": "162", "line": 217, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 217, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "163", "line": 22, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 22, "endColumn": 17}, {"ruleId": "153", "severity": 1, "message": "164", "line": 386, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 386, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "164", "line": 275, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 275, "endColumn": 23}, {"ruleId": "153", "severity": 1, "message": "165", "line": 280, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 280, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "166", "line": 344, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 344, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "167", "line": 349, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 349, "endColumn": 24}, {"ruleId": "153", "severity": 1, "message": "168", "line": 14, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 14, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "169", "line": 14, "column": 27, "nodeType": "155", "messageId": "156", "endLine": 14, "endColumn": 45}, {"ruleId": "170", "severity": 2, "message": "171", "line": 77, "column": 18, "nodeType": "155", "messageId": "172", "endLine": 77, "endColumn": 42}, {"ruleId": "170", "severity": 2, "message": "173", "line": 92, "column": 18, "nodeType": "155", "messageId": "172", "endLine": 92, "endColumn": 45}, {"ruleId": "170", "severity": 2, "message": "174", "line": 95, "column": 18, "nodeType": "155", "messageId": "172", "endLine": 95, "endColumn": 49}, {"ruleId": "170", "severity": 2, "message": "175", "line": 98, "column": 18, "nodeType": "155", "messageId": "172", "endLine": 98, "endColumn": 44}, {"ruleId": "170", "severity": 2, "message": "176", "line": 101, "column": 18, "nodeType": "155", "messageId": "172", "endLine": 101, "endColumn": 39}, {"ruleId": "153", "severity": 1, "message": "177", "line": 118, "column": 11, "nodeType": "155", "messageId": "156", "endLine": 118, "endColumn": 20}, {"ruleId": "153", "severity": 1, "message": "178", "line": 255, "column": 11, "nodeType": "155", "messageId": "156", "endLine": 255, "endColumn": 19}, {"ruleId": "153", "severity": 1, "message": "177", "line": 256, "column": 11, "nodeType": "155", "messageId": "156", "endLine": 256, "endColumn": 20}, "no-dupe-class-members", "Duplicate name 'getDefaultSettings'.", "MethodDefinition", "unexpected", "Duplicate name 'updateAccountBalances'.", "Duplicate name 'updateSettings'.", "Duplicate name 'getLowStockProducts'.", "no-unused-vars", "'setCategories' is assigned a value but never used.", "Identifier", "unusedVar", "'setUnits' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateSystemNotifications'. Either include it or remove the dependency array.", "ArrayExpression", ["179"], "'getAccountName' is assigned a value but never used.", "'printRef' is assigned a value but never used.", "'getProductName' is assigned a value but never used.", "'getInvoiceNumber' is assigned a value but never used.", "'getCustomerName' is assigned a value but never used.", "'getSupplierName' is assigned a value but never used.", "'selectedProduct' is assigned a value but never used.", "'setSelectedProduct' is assigned a value but never used.", "no-undef", "'generateAccountStatement' is not defined.", "undef", "'generateDetailedSalesReport' is not defined.", "'generateDetailedPurchasesReport' is not defined.", "'generateInventoryValuation' is not defined.", "'generateCustomerAging' is not defined.", "'purchases' is assigned a value but never used.", "'invoices' is assigned a value but never used.", {"desc": "180", "fix": "181"}, "Update the dependencies array to be: [generateSystemNotifications]", {"range": "182", "text": "183"}, [924, 926], "[generateSystemNotifications]"]