{"name": "workbox-recipes", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "A service worker helper library to manage common request and caching patterns", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "router", "routing"], "workbox": {"browserNamespace": "workbox.recipes", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-cacheable-response": "6.6.0", "workbox-core": "6.6.0", "workbox-expiration": "6.6.0", "workbox-precaching": "6.6.0", "workbox-routing": "6.6.0", "workbox-strategies": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}