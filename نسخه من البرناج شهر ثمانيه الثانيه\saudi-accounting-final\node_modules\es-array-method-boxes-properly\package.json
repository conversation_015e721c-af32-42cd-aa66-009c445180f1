{"name": "es-array-method-boxes-properly", "version": "1.0.0", "description": "Utility package to determine if an `Array.prototype` method properly boxes the callback's receiver and third argument.", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-array-method-boxes-properly.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-array-method-boxes-properly/issues"}, "homepage": "https://github.com/ljharb/es-array-method-boxes-properly#readme", "devDependencies": {"@ljharb/eslint-config": "^14.1.0", "eslint": "^6.4.0", "safe-publish-latest": "^1.1.3", "tape": "^4.11.0"}}