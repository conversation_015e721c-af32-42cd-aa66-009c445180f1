{"ast": null, "code": "import React,{useState,useEffect}from'react';import database from'../../utils/database';import'./EnhancedLogin.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const EnhancedLogin=_ref=>{let{onLogin}=_ref;const[formData,setFormData]=useState({username:'',password:''});const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[showPassword,setShowPassword]=useState(false);const[rememberMe,setRememberMe]=useState(false);useEffect(()=>{// Check for remembered credentials\nconst rememberedUsername=localStorage.getItem('rememberedUsername');if(rememberedUsername){setFormData(prev=>({...prev,username:rememberedUsername}));setRememberMe(true);}// Auto-focus on username field\nconst usernameField=document.getElementById('username');if(usernameField){usernameField.focus();}},[]);const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>({...prev,[name]:value}));// Clear error when user starts typing\nif(error){setError('');}};const handleSubmit=async e=>{e.preventDefault();if(!formData.username.trim()||!formData.password.trim()){setError('يرجى إدخال اسم المستخدم وكلمة المرور');return;}setLoading(true);setError('');try{// Simulate login delay for better UX\nawait new Promise(resolve=>setTimeout(resolve,1000));const user=database.authenticateUser(formData.username,formData.password);if(user){// Handle remember me\nif(rememberMe){localStorage.setItem('rememberedUsername',formData.username);}else{localStorage.removeItem('rememberedUsername');}// Log the login activity\ndatabase.logActivity({type:'login',userId:user.id,username:user.username,timestamp:new Date().toISOString(),details:'تسجيل دخول ناجح'});onLogin(user);}else{setError('اسم المستخدم أو كلمة المرور غير صحيحة');}}catch(error){console.error('خطأ في تسجيل الدخول:',error);setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');}finally{setLoading(false);}};const handleDemoLogin=()=>{setFormData({username:'admin',password:'admin123'});setError('');};const getCurrentTime=()=>{return new Date().toLocaleString('ar-SA',{weekday:'long',year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});};return/*#__PURE__*/_jsxs(\"div\",{className:\"enhanced-login\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-background\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"background-pattern\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"floating-shapes\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"shape shape-1\"}),/*#__PURE__*/_jsx(\"div\",{className:\"shape shape-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"shape shape-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"shape shape-4\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"company-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-icon\",children:\"\\uD83C\\uDFEA\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"logo-text\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Aronim EXP\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0648\\u0631\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"current-time\",children:getCurrentTime()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-text\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0633\\u062C\\u0644 \\u062F\\u062E\\u0648\\u0644\\u0643 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),error]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"login-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",children:\"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"username\",name:\"username\",value:formData.username,onChange:handleInputChange,placeholder:\"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",className:\"form-input\",disabled:loading,autoComplete:\"username\"}),/*#__PURE__*/_jsx(\"span\",{className:\"input-icon\",children:\"\\uD83D\\uDC64\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:showPassword?'text':'password',id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,placeholder:\"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",className:\"form-input\",disabled:loading,autoComplete:\"current-password\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"password-toggle\",onClick:()=>setShowPassword(!showPassword),disabled:loading,children:showPassword?'🙈':'👁️'})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-options\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:rememberMe,onChange:e=>setRememberMe(e.target.checked),disabled:loading}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),\"\\u062A\\u0630\\u0643\\u0631\\u0646\\u064A\"]})}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"login-button\",disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"loading-spinner\"}),\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"button-icon\",children:\"\\uD83D\\uDD10\"}),\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"demo-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"divider\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0623\\u0648\"})}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:handleDemoLogin,className:\"demo-button\",disabled:loading,children:[/*#__PURE__*/_jsx(\"span\",{className:\"button-icon\",children:\"\\uD83C\\uDFAF\"}),\"\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 (Demo)\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-footer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"system-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"info-icon\",children:\"\\uD83C\\uDDF8\\uD83C\\uDDE6\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"info-icon\",children:\"\\uD83D\\uDD12\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0646\\u0638\\u0627\\u0645 \\u0622\\u0645\\u0646 \\u0648\\u0645\\u062D\\u0645\\u064A\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"info-icon\",children:\"\\uD83D\\uDCF1\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0645\\u062A\\u062C\\u0627\\u0648\\u0628 \\u0645\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u062C\\u0647\\u0632\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"copyright\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2024 Aronim EXP. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0646\\u0633\\u062E\\u0629 2.0.0\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"features-sidebar\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"features-list\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83D\\uDED2\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0646\\u0642\\u0637\\u0629 \\u0628\\u064A\\u0639 \\u0645\\u062A\\u0637\\u0648\\u0631\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0646\\u0638\\u0627\\u0645 \\u0628\\u064A\\u0639 \\u0633\\u0631\\u064A\\u0639 \\u0648\\u0641\\u0639\\u0627\\u0644 \\u0645\\u0639 \\u062F\\u0639\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0645\\u0641\\u0635\\u0644\\u0629 \\u0648\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u062F\\u0642\\u064A\\u0642\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83D\\uDCE6\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u062A\\u062A\\u0628\\u0639 \\u062F\\u0642\\u064A\\u0642 \\u0644\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0648\\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0630\\u0643\\u064A\\u0629\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83D\\uDCB0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062D\\u0627\\u0633\\u0628\\u064A \\u0634\\u0627\\u0645\\u0644 \\u0645\\u0639 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\"})]})]})]})]})]})]});};export default EnhancedLogin;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "_ref", "onLogin", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "showPassword", "setShowPassword", "rememberMe", "setRememberMe", "rememberedUsername", "localStorage", "getItem", "prev", "usernameField", "document", "getElementById", "focus", "handleInputChange", "e", "name", "value", "target", "handleSubmit", "preventDefault", "trim", "Promise", "resolve", "setTimeout", "user", "authenticateUser", "setItem", "removeItem", "logActivity", "type", "userId", "id", "timestamp", "Date", "toISOString", "details", "console", "handleDemoLogin", "getCurrentTime", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "className", "children", "onSubmit", "htmlFor", "onChange", "placeholder", "disabled", "autoComplete", "onClick", "checked"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Login/EnhancedLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport './EnhancedLogin.css';\n\nconst EnhancedLogin = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n\n  useEffect(() => {\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({ ...prev, username: rememberedUsername }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const user = database.authenticateUser(formData.username, formData.password);\n      \n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        database.logActivity({\n          type: 'login',\n          userId: user.id,\n          username: user.username,\n          timestamp: new Date().toISOString(),\n          details: 'تسجيل دخول ناجح'\n        });\n\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoLogin = () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"enhanced-login\">\n      <div className=\"login-background\">\n        <div className=\"background-pattern\"></div>\n        <div className=\"floating-shapes\">\n          <div className=\"shape shape-1\"></div>\n          <div className=\"shape shape-2\"></div>\n          <div className=\"shape shape-3\"></div>\n          <div className=\"shape shape-4\"></div>\n        </div>\n      </div>\n\n      <div className=\"login-container\">\n        <div className=\"login-card\">\n          <div className=\"login-header\">\n            <div className=\"company-logo\">\n              <div className=\"logo-icon\">🏪</div>\n              <div className=\"logo-text\">\n                <h1>Aronim EXP</h1>\n                <p>نظام المحاسبة المتطور</p>\n              </div>\n            </div>\n            <div className=\"current-time\">\n              {getCurrentTime()}\n            </div>\n          </div>\n\n          <div className=\"login-body\">\n            <div className=\"welcome-text\">\n              <h2>مرحباً بك</h2>\n              <p>سجل دخولك للوصول إلى نظام المحاسبة</p>\n            </div>\n\n            {error && (\n              <div className=\"error-message\">\n                <span className=\"error-icon\">⚠️</span>\n                {error}\n              </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"login-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"username\">اسم المستخدم</label>\n                <div className=\"input-container\">\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل اسم المستخدم\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"username\"\n                  />\n                  <span className=\"input-icon\">👤</span>\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\">كلمة المرور</label>\n                <div className=\"input-container\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل كلمة المرور\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"current-password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    disabled={loading}\n                  >\n                    {showPassword ? '🙈' : '👁️'}\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"form-options\">\n                <label className=\"checkbox-container\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    disabled={loading}\n                  />\n                  <span className=\"checkmark\"></span>\n                  تذكرني\n                </label>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"login-button\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    جاري تسجيل الدخول...\n                  </>\n                ) : (\n                  <>\n                    <span className=\"button-icon\">🔐</span>\n                    تسجيل الدخول\n                  </>\n                )}\n              </button>\n            </form>\n\n            <div className=\"demo-section\">\n              <div className=\"divider\">\n                <span>أو</span>\n              </div>\n              <button\n                type=\"button\"\n                onClick={handleDemoLogin}\n                className=\"demo-button\"\n                disabled={loading}\n              >\n                <span className=\"button-icon\">🎯</span>\n                تجربة النظام (Demo)\n              </button>\n            </div>\n          </div>\n\n          <div className=\"login-footer\">\n            <div className=\"system-info\">\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🇸🇦</span>\n                <span>متوافق مع المتطلبات السعودية</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🔒</span>\n                <span>نظام آمن ومحمي</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">📱</span>\n                <span>متجاوب مع جميع الأجهزة</span>\n              </div>\n            </div>\n            \n            <div className=\"copyright\">\n              <p>© 2024 Aronim EXP. جميع الحقوق محفوظة.</p>\n              <p>نسخة 2.0.0</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"features-sidebar\">\n          <h3>مميزات النظام</h3>\n          <div className=\"features-list\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🛒</div>\n              <div className=\"feature-content\">\n                <h4>نقطة بيع متطورة</h4>\n                <p>نظام بيع سريع وفعال مع دعم الباركود</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📊</div>\n              <div className=\"feature-content\">\n                <h4>تقارير شاملة</h4>\n                <p>تقارير مالية مفصلة وإحصائيات دقيقة</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📦</div>\n              <div className=\"feature-content\">\n                <h4>إدارة المخزون</h4>\n                <p>تتبع دقيق للمخزون والتنبيهات الذكية</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <div className=\"feature-content\">\n                <h4>محاسبة متكاملة</h4>\n                <p>نظام محاسبي شامل مع ضريبة القيمة المضافة</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedLogin;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7B,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAChC,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,CACvCc,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAuB,kBAAkB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACrE,GAAIF,kBAAkB,CAAE,CACtBX,WAAW,CAACc,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEb,QAAQ,CAAEU,kBAAmB,CAAC,CAAC,CAAC,CAChED,aAAa,CAAC,IAAI,CAAC,CACrB,CAEA;AACA,KAAM,CAAAK,aAAa,CAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CACzD,GAAIF,aAAa,CAAE,CACjBA,aAAa,CAACG,KAAK,CAAC,CAAC,CACvB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCvB,WAAW,CAACc,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACO,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CAEH;AACA,GAAIjB,KAAK,CAAE,CACTC,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAG,KAAO,CAAAJ,CAAC,EAAK,CAChCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAElB,GAAI,CAAC1B,QAAQ,CAACE,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAAI,CAAC3B,QAAQ,CAACG,QAAQ,CAACwB,IAAI,CAAC,CAAC,CAAE,CAC1DpB,QAAQ,CAAC,sCAAsC,CAAC,CAChD,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF;AACA,KAAM,IAAI,CAAAqB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD,KAAM,CAAAE,IAAI,CAAGzC,QAAQ,CAAC0C,gBAAgB,CAAChC,QAAQ,CAACE,QAAQ,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CAE5E,GAAI4B,IAAI,CAAE,CACR;AACA,GAAIrB,UAAU,CAAE,CACdG,YAAY,CAACoB,OAAO,CAAC,oBAAoB,CAAEjC,QAAQ,CAACE,QAAQ,CAAC,CAC/D,CAAC,IAAM,CACLW,YAAY,CAACqB,UAAU,CAAC,oBAAoB,CAAC,CAC/C,CAEA;AACA5C,QAAQ,CAAC6C,WAAW,CAAC,CACnBC,IAAI,CAAE,OAAO,CACbC,MAAM,CAAEN,IAAI,CAACO,EAAE,CACfpC,QAAQ,CAAE6B,IAAI,CAAC7B,QAAQ,CACvBqC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,OAAO,CAAE,iBACX,CAAC,CAAC,CAEF3C,OAAO,CAACgC,IAAI,CAAC,CACf,CAAC,IAAM,CACLxB,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CACF,CAAE,MAAOD,KAAK,CAAE,CACdqC,OAAO,CAACrC,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CC,QAAQ,CAAC,qDAAqD,CAAC,CACjE,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuC,eAAe,CAAGA,CAAA,GAAM,CAC5B3C,WAAW,CAAC,CACVC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,UACZ,CAAC,CAAC,CACFI,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAAsC,cAAc,CAAGA,CAAA,GAAM,CAC3B,MAAO,IAAI,CAAAL,IAAI,CAAC,CAAC,CAACM,cAAc,CAAC,OAAO,CAAE,CACxCC,OAAO,CAAE,MAAM,CACfC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED,mBACE1D,KAAA,QAAK2D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5D,KAAA,QAAK2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9D,IAAA,QAAK6D,SAAS,CAAC,oBAAoB,CAAM,CAAC,cAC1C3D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,QAAK6D,SAAS,CAAC,eAAe,CAAM,CAAC,cACrC7D,IAAA,QAAK6D,SAAS,CAAC,eAAe,CAAM,CAAC,cACrC7D,IAAA,QAAK6D,SAAS,CAAC,eAAe,CAAM,CAAC,cACrC7D,IAAA,QAAK6D,SAAS,CAAC,eAAe,CAAM,CAAC,EAClC,CAAC,EACH,CAAC,cAEN3D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B5D,KAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC5D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9D,IAAA,OAAA8D,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB9D,IAAA,MAAA8D,QAAA,CAAG,sHAAqB,CAAG,CAAC,EACzB,CAAC,EACH,CAAC,cACN9D,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BT,cAAc,CAAC,CAAC,CACd,CAAC,EACH,CAAC,cAENnD,KAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,OAAA8D,QAAA,CAAI,mDAAS,CAAI,CAAC,cAClB9D,IAAA,MAAA8D,QAAA,CAAG,qLAAkC,CAAG,CAAC,EACtC,CAAC,CAELhD,KAAK,eACJZ,KAAA,QAAK2D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9D,IAAA,SAAM6D,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrChD,KAAK,EACH,CACN,cAEDZ,KAAA,SAAM6D,QAAQ,CAAE9B,YAAa,CAAC4B,SAAS,CAAC,YAAY,CAAAC,QAAA,eAClD5D,KAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9D,IAAA,UAAOgE,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,qEAAY,CAAO,CAAC,cAC9C5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXE,EAAE,CAAC,UAAU,CACbhB,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEvB,QAAQ,CAACE,QAAS,CACzBuD,QAAQ,CAAErC,iBAAkB,CAC5BsC,WAAW,CAAC,8FAAmB,CAC/BL,SAAS,CAAC,YAAY,CACtBM,QAAQ,CAAEvD,OAAQ,CAClBwD,YAAY,CAAC,UAAU,CACxB,CAAC,cACFpE,IAAA,SAAM6D,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACnC,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9D,IAAA,UAAOgE,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,+DAAW,CAAO,CAAC,cAC7C5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,UACE4C,IAAI,CAAE5B,YAAY,CAAG,MAAM,CAAG,UAAW,CACzC8B,EAAE,CAAC,UAAU,CACbhB,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEvB,QAAQ,CAACG,QAAS,CACzBsD,QAAQ,CAAErC,iBAAkB,CAC5BsC,WAAW,CAAC,wFAAkB,CAC9BL,SAAS,CAAC,YAAY,CACtBM,QAAQ,CAAEvD,OAAQ,CAClBwD,YAAY,CAAC,kBAAkB,CAChC,CAAC,cACFpE,IAAA,WACE4C,IAAI,CAAC,QAAQ,CACbiB,SAAS,CAAC,iBAAiB,CAC3BQ,OAAO,CAAEA,CAAA,GAAMpD,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CmD,QAAQ,CAAEvD,OAAQ,CAAAkD,QAAA,CAEjB9C,YAAY,CAAG,IAAI,CAAG,KAAK,CACtB,CAAC,EACN,CAAC,EACH,CAAC,cAENhB,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B5D,KAAA,UAAO2D,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC9D,IAAA,UACE4C,IAAI,CAAC,UAAU,CACf0B,OAAO,CAAEpD,UAAW,CACpB+C,QAAQ,CAAGpC,CAAC,EAAKV,aAAa,CAACU,CAAC,CAACG,MAAM,CAACsC,OAAO,CAAE,CACjDH,QAAQ,CAAEvD,OAAQ,CACnB,CAAC,cACFZ,IAAA,SAAM6D,SAAS,CAAC,WAAW,CAAO,CAAC,uCAErC,EAAO,CAAC,CACL,CAAC,cAEN7D,IAAA,WACE4C,IAAI,CAAC,QAAQ,CACbiB,SAAS,CAAC,cAAc,CACxBM,QAAQ,CAAEvD,OAAQ,CAAAkD,QAAA,CAEjBlD,OAAO,cACNV,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE9D,IAAA,SAAM6D,SAAS,CAAC,iBAAiB,CAAO,CAAC,kGAE3C,EAAE,CAAC,cAEH3D,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE9D,IAAA,SAAM6D,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,sEAEzC,EAAE,CACH,CACK,CAAC,EACL,CAAC,cAEP5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB9D,IAAA,SAAA8D,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CAAC,cACN5D,KAAA,WACE0C,IAAI,CAAC,QAAQ,CACbyB,OAAO,CAAEjB,eAAgB,CACzBS,SAAS,CAAC,aAAa,CACvBM,QAAQ,CAAEvD,OAAQ,CAAAkD,QAAA,eAElB9D,IAAA,SAAM6D,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,6EAEzC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5D,KAAA,QAAK2D,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9D,IAAA,SAAM6D,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACvC9D,IAAA,SAAA8D,QAAA,CAAM,2JAA4B,CAAM,CAAC,EACtC,CAAC,cACN5D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9D,IAAA,SAAM6D,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACrC9D,IAAA,SAAA8D,QAAA,CAAM,4EAAc,CAAM,CAAC,EACxB,CAAC,cACN5D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9D,IAAA,SAAM6D,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACrC9D,IAAA,SAAA8D,QAAA,CAAM,uHAAsB,CAAM,CAAC,EAChC,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9D,IAAA,MAAA8D,QAAA,CAAG,2HAAsC,CAAG,CAAC,cAC7C9D,IAAA,MAAA8D,QAAA,CAAG,gCAAU,CAAG,CAAC,EACd,CAAC,EACH,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9D,IAAA,OAAA8D,QAAA,CAAI,2EAAa,CAAI,CAAC,cACtB5D,KAAA,QAAK2D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,OAAA8D,QAAA,CAAI,kFAAe,CAAI,CAAC,cACxB9D,IAAA,MAAA8D,QAAA,CAAG,sLAAmC,CAAG,CAAC,EACvC,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,OAAA8D,QAAA,CAAI,qEAAY,CAAI,CAAC,cACrB9D,IAAA,MAAA8D,QAAA,CAAG,0LAAkC,CAAG,CAAC,EACtC,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,OAAA8D,QAAA,CAAI,2EAAa,CAAI,CAAC,cACtB9D,IAAA,MAAA8D,QAAA,CAAG,gMAAmC,CAAG,CAAC,EACvC,CAAC,EACH,CAAC,cAEN5D,KAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9D,IAAA,QAAK6D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC5D,KAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9D,IAAA,OAAA8D,QAAA,CAAI,iFAAc,CAAI,CAAC,cACvB9D,IAAA,MAAA8D,QAAA,CAAG,oNAAwC,CAAG,CAAC,EAC5C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}