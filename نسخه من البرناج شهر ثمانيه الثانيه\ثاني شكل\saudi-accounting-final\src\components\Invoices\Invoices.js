import React, { useState, useEffect } from 'react';
import database from '../../utils/database';
import './Invoices.css';

const Invoices = ({ user, onBack }) => {
  const [invoices, setInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    try {
      const invoicesData = database.getInvoices();
      const customersData = database.getCustomers();
      
      setInvoices(invoicesData);
      setCustomers(customersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      setMessage('خطأ في تحميل البيانات');
    }
  };

  const getCustomerName = (customerId) => {
    if (!customerId) return 'عميل نقدي';
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'عميل غير معروف';
  };

  const getStatusText = (status) => {
    const statusMap = {
      'paid': 'مدفوعة',
      'pending': 'معلقة',
      'cancelled': 'ملغية',
      'draft': 'مسودة'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    const colorMap = {
      'paid': 'success',
      'pending': 'warning',
      'cancelled': 'danger',
      'draft': 'secondary'
    };
    return colorMap[status] || 'secondary';
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      getCustomerName(invoice.customerId).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const invoiceDate = new Date(invoice.invoiceDate);
      const today = new Date();
      
      switch (dateFilter) {
        case 'today':
          matchesDate = invoiceDate.toDateString() === today.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = invoiceDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          matchesDate = invoiceDate >= monthAgo;
          break;
        default:
          matchesDate = true;
      }
    }
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const handleViewDetails = (invoice) => {
    setSelectedInvoice(invoice);
    setShowDetails(true);
  };

  const handlePrintInvoice = (invoice) => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>فاتورة - ${invoice.invoiceNumber}</title>
          <style>
            body { 
              font-family: 'Cairo', Arial, sans-serif; 
              margin: 20px; 
              direction: rtl; 
              font-size: 14px;
              line-height: 1.6;
            }
            .invoice-header { 
              text-align: center; 
              border-bottom: 2px solid #2563eb; 
              padding-bottom: 20px; 
              margin-bottom: 20px; 
            }
            .company-name {
              font-size: 24px;
              font-weight: bold;
              color: #2563eb;
              margin-bottom: 10px;
            }
            .invoice-details { 
              display: flex; 
              justify-content: space-between; 
              margin-bottom: 20px; 
              background: #f8fafc;
              padding: 15px;
              border-radius: 8px;
            }
            .items-table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-bottom: 20px; 
            }
            .items-table th, .items-table td { 
              border: 1px solid #e2e8f0; 
              padding: 12px 8px; 
              text-align: right; 
            }
            .items-table th { 
              background: #2563eb; 
              color: white;
              font-weight: 600;
            }
            .totals { 
              background: #f8fafc;
              padding: 20px;
              border-radius: 8px;
              margin-top: 20px; 
            }
            .total-row { 
              display: flex; 
              justify-content: space-between; 
              margin: 8px 0; 
              padding: 5px 0;
            }
            .final-total { 
              font-weight: bold; 
              font-size: 18px; 
              border-top: 2px solid #2563eb; 
              padding-top: 15px; 
              margin-top: 15px;
              color: #2563eb;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #e2e8f0;
              color: #64748b;
            }
            @media print { 
              body { margin: 0; } 
            }
          </style>
        </head>
        <body>
          <div class="invoice-header">
            <div class="company-name">شركة العرونيم للتجارة</div>
            <p>الرياض - المملكة العربية السعودية</p>
            <p>هاتف: +966 11 123 4567 | بريد إلكتروني: <EMAIL></p>
            <p>الرقم الضريبي: 123456789012345</p>
          </div>

          <div class="invoice-details">
            <div>
              <p><strong>رقم الفاتورة:</strong> ${invoice.invoiceNumber}</p>
              <p><strong>التاريخ:</strong> ${new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')}</p>
              <p><strong>الوقت:</strong> ${new Date(invoice.invoiceDate).toLocaleTimeString('ar-SA')}</p>
            </div>
            <div>
              <p><strong>العميل:</strong> ${getCustomerName(invoice.customerId)}</p>
              <p><strong>الكاشير:</strong> ${invoice.cashierName || 'غير محدد'}</p>
              <p><strong>الحالة:</strong> ${getStatusText(invoice.status)}</p>
            </div>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th>الصنف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map(item => `
                <tr>
                  <td>${item.name}</td>
                  <td>${item.quantity}</td>
                  <td>${item.price.toFixed(2)} ريال</td>
                  <td>${item.total.toFixed(2)} ريال</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="totals">
            <div class="total-row">
              <span>المجموع الفرعي:</span>
              <span>${invoice.subtotal.toFixed(2)} ريال</span>
            </div>
            ${invoice.discountAmount > 0 ? `
              <div class="total-row">
                <span>الخصم:</span>
                <span>-${invoice.discountAmount.toFixed(2)} ريال</span>
              </div>
            ` : ''}
            <div class="total-row">
              <span>ضريبة القيمة المضافة (15%):</span>
              <span>${invoice.vatAmount.toFixed(2)} ريال</span>
            </div>
            <div class="total-row final-total">
              <span>الإجمالي النهائي:</span>
              <span>${invoice.total.toFixed(2)} ريال</span>
            </div>
          </div>

          <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام Aronim EXP</p>
          </div>
        </body>
      </html>
    `;

    printWindow.document.documentElement.innerHTML = printContent;
    setTimeout(() => printWindow.print(), 500);
  };

  const handleUpdateStatus = (invoiceId, newStatus) => {
    try {
      database.updateInvoice(invoiceId, { status: newStatus });
      loadData();
      setMessage(`✅ تم تحديث حالة الفاتورة إلى ${getStatusText(newStatus)}`);
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في تحديث الفاتورة:', error);
      setMessage('❌ خطأ في تحديث الفاتورة');
      setTimeout(() => setMessage(''), 3000);
    }
  };

  const getTotalAmount = () => {
    return filteredInvoices.reduce((sum, invoice) => sum + invoice.total, 0);
  };

  const getInvoiceStats = () => {
    const stats = {
      total: filteredInvoices.length,
      paid: filteredInvoices.filter(inv => inv.status === 'paid').length,
      pending: filteredInvoices.filter(inv => inv.status === 'pending').length,
      cancelled: filteredInvoices.filter(inv => inv.status === 'cancelled').length
    };
    return stats;
  };

  const stats = getInvoiceStats();

  return (
    <div className="invoices-container">
      <div className="invoices-header">
        <div className="header-top">
          <h1>📋 إدارة الفواتير</h1>
          <button onClick={onBack} className="btn btn-secondary">
            ← العودة
          </button>
        </div>

        {message && (
          <div className={`alert ${message.includes('❌') ? 'alert-error' : 'alert-success'}`}>
            {message}
          </div>
        )}

        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">📊</div>
            <div className="stat-info">
              <div className="stat-value">{stats.total}</div>
              <div className="stat-label">إجمالي الفواتير</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-info">
              <div className="stat-value">{stats.paid}</div>
              <div className="stat-label">مدفوعة</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">⏳</div>
            <div className="stat-info">
              <div className="stat-value">{stats.pending}</div>
              <div className="stat-label">معلقة</div>
            </div>
          </div>
          <div className="stat-card danger">
            <div className="stat-icon">❌</div>
            <div className="stat-info">
              <div className="stat-value">{stats.cancelled}</div>
              <div className="stat-label">ملغية</div>
            </div>
          </div>
        </div>

        <div className="filters-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث برقم الفاتورة أو اسم العميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="filters-row">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">جميع الحالات</option>
              <option value="paid">مدفوعة</option>
              <option value="pending">معلقة</option>
              <option value="cancelled">ملغية</option>
              <option value="draft">مسودة</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">جميع التواريخ</option>
              <option value="today">اليوم</option>
              <option value="week">آخر أسبوع</option>
              <option value="month">آخر شهر</option>
            </select>
          </div>
        </div>
      </div>

      <div className="invoices-content">
        {filteredInvoices.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📋</div>
            <h3>لا توجد فواتير</h3>
            <p>لم يتم العثور على فواتير تطابق معايير البحث</p>
          </div>
        ) : (
          <>
            <div className="invoices-summary">
              <div className="summary-item">
                <span>عدد الفواتير: {filteredInvoices.length}</span>
                <span>إجمالي المبلغ: {getTotalAmount().toFixed(2)} ريال</span>
              </div>
            </div>

            <div className="invoices-table-container">
              <table className="invoices-table">
                <thead>
                  <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInvoices.map(invoice => (
                    <tr key={invoice.id}>
                      <td className="invoice-number">{invoice.invoiceNumber}</td>
                      <td>{new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')}</td>
                      <td>{getCustomerName(invoice.customerId)}</td>
                      <td className="amount">{invoice.total.toFixed(2)} ريال</td>
                      <td>
                        <span className={`status-badge ${getStatusColor(invoice.status)}`}>
                          {getStatusText(invoice.status)}
                        </span>
                      </td>
                      <td>
                        <div className="actions-buttons">
                          <button
                            onClick={() => handleViewDetails(invoice)}
                            className="btn btn-sm btn-primary"
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          <button
                            onClick={() => handlePrintInvoice(invoice)}
                            className="btn btn-sm btn-secondary"
                            title="طباعة"
                          >
                            🖨️
                          </button>
                          {invoice.status !== 'paid' && (
                            <button
                              onClick={() => handleUpdateStatus(invoice.id, 'paid')}
                              className="btn btn-sm btn-success"
                              title="تحديد كمدفوعة"
                            >
                              ✅
                            </button>
                          )}
                          {invoice.status !== 'cancelled' && (
                            <button
                              onClick={() => handleUpdateStatus(invoice.id, 'cancelled')}
                              className="btn btn-sm btn-danger"
                              title="إلغاء"
                            >
                              ❌
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>

      {/* Invoice Details Modal */}
      {showDetails && selectedInvoice && (
        <div className="modal-overlay">
          <div className="invoice-details-modal">
            <div className="modal-header">
              <h3>تفاصيل الفاتورة - {selectedInvoice.invoiceNumber}</h3>
              <button onClick={() => setShowDetails(false)} className="close-btn">
                ✕
              </button>
            </div>

            <div className="modal-body">
              <div className="invoice-info-grid">
                <div className="info-item">
                  <label>رقم الفاتورة:</label>
                  <span>{selectedInvoice.invoiceNumber}</span>
                </div>
                <div className="info-item">
                  <label>التاريخ:</label>
                  <span>{new Date(selectedInvoice.invoiceDate).toLocaleDateString('ar-SA')}</span>
                </div>
                <div className="info-item">
                  <label>العميل:</label>
                  <span>{getCustomerName(selectedInvoice.customerId)}</span>
                </div>
                <div className="info-item">
                  <label>الكاشير:</label>
                  <span>{selectedInvoice.cashierName || 'غير محدد'}</span>
                </div>
                <div className="info-item">
                  <label>الحالة:</label>
                  <span className={`status-badge ${getStatusColor(selectedInvoice.status)}`}>
                    {getStatusText(selectedInvoice.status)}
                  </span>
                </div>
                <div className="info-item">
                  <label>طريقة الدفع:</label>
                  <span>{selectedInvoice.paymentMethod || 'نقدي'}</span>
                </div>
              </div>

              <div className="items-section">
                <h4>الأصناف:</h4>
                <table className="items-table">
                  <thead>
                    <tr>
                      <th>الصنف</th>
                      <th>الكمية</th>
                      <th>السعر</th>
                      <th>الإجمالي</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedInvoice.items.map((item, index) => (
                      <tr key={index}>
                        <td>{item.name}</td>
                        <td>{item.quantity}</td>
                        <td>{item.price.toFixed(2)} ريال</td>
                        <td>{item.total.toFixed(2)} ريال</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="totals-section">
                <div className="total-row">
                  <span>المجموع الفرعي:</span>
                  <span>{selectedInvoice.subtotal.toFixed(2)} ريال</span>
                </div>
                {selectedInvoice.discountAmount > 0 && (
                  <div className="total-row">
                    <span>الخصم:</span>
                    <span>-{selectedInvoice.discountAmount.toFixed(2)} ريال</span>
                  </div>
                )}
                <div className="total-row">
                  <span>ضريبة القيمة المضافة:</span>
                  <span>{selectedInvoice.vatAmount.toFixed(2)} ريال</span>
                </div>
                <div className="total-row final-total">
                  <span>الإجمالي النهائي:</span>
                  <span>{selectedInvoice.total.toFixed(2)} ريال</span>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button
                onClick={() => handlePrintInvoice(selectedInvoice)}
                className="btn btn-primary"
              >
                🖨️ طباعة الفاتورة
              </button>
              <button
                onClick={() => setShowDetails(false)}
                className="btn btn-secondary"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Invoices;
