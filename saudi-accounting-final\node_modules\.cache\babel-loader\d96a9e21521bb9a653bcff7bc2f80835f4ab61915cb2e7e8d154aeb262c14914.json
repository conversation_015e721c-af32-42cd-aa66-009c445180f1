{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\POS\\\\POS.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './POS.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst POS = ({\n  user,\n  onBack\n}) => {\n  _s();\n  const [cart, setCart] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [customer, setCustomer] = useState({\n    name: '',\n    phone: '',\n    taxNumber: ''\n  });\n  const [showCustomerForm, setShowCustomerForm] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState('cash'); // 'cash', 'card', 'transfer'\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // منتجات وهمية للتجربة\n  useEffect(() => {\n    const sampleProducts = [{\n      id: 1,\n      name: 'لابتوب HP',\n      price: 2500,\n      stock: 10,\n      barcode: '*********',\n      category: 'إلكترونيات'\n    }, {\n      id: 2,\n      name: 'ماوس لاسلكي',\n      price: 85,\n      stock: 25,\n      barcode: '*********',\n      category: 'إلكترونيات'\n    }, {\n      id: 3,\n      name: 'كيبورد ميكانيكي',\n      price: 150,\n      stock: 15,\n      barcode: '*********',\n      category: 'إلكترونيات'\n    }, {\n      id: 4,\n      name: 'شاشة 24 بوصة',\n      price: 800,\n      stock: 8,\n      barcode: '*********',\n      category: 'إلكترونيات'\n    }, {\n      id: 5,\n      name: 'طابعة ليزر',\n      price: 450,\n      stock: 5,\n      barcode: '*********',\n      category: 'مكتبية'\n    }, {\n      id: 6,\n      name: 'ورق A4',\n      price: 25,\n      stock: 100,\n      barcode: '654987321',\n      category: 'مكتبية'\n    }, {\n      id: 7,\n      name: 'قلم حبر جاف',\n      price: 3,\n      stock: 200,\n      barcode: '147258369',\n      category: 'مكتبية'\n    }, {\n      id: 8,\n      name: 'دفتر ملاحظات',\n      price: 15,\n      stock: 50,\n      barcode: '963852741',\n      category: 'مكتبية'\n    }];\n    setProducts(sampleProducts);\n  }, []);\n\n  // إضافة منتج للسلة\n  const addToCart = product => {\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity < product.stock) {\n        setCart(cart.map(item => item.id === product.id ? {\n          ...item,\n          quantity: item.quantity + 1\n        } : item));\n      } else {\n        alert('⚠️ لا توجد كمية كافية في المخزون');\n      }\n    } else {\n      setCart([...cart, {\n        ...product,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // تحديث كمية المنتج\n  const updateQuantity = (id, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(id);\n      return;\n    }\n    const product = products.find(p => p.id === id);\n    if (newQuantity > product.stock) {\n      alert('⚠️ لا توجد كمية كافية في المخزون');\n      return;\n    }\n    setCart(cart.map(item => item.id === id ? {\n      ...item,\n      quantity: newQuantity\n    } : item));\n  };\n\n  // حذف منتج من السلة\n  const removeFromCart = id => {\n    setCart(cart.filter(item => item.id !== id));\n  };\n\n  // حساب الإجماليات\n  const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);\n  const vatRate = 0.15; // 15% ضريبة القيمة المضافة\n  const vatAmount = subtotal * vatRate;\n  const total = subtotal + vatAmount;\n\n  // تصفية المنتجات حسب البحث\n  const filteredProducts = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.barcode.includes(searchTerm));\n\n  // معالجة الدفع\n  const processPayment = async () => {\n    if (cart.length === 0) {\n      alert('⚠️ السلة فارغة');\n      return;\n    }\n    setIsProcessing(true);\n\n    // محاكاة معالجة الدفع\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // إنشاء الفاتورة\n    const invoice = {\n      id: Date.now(),\n      date: new Date().toISOString(),\n      dateArabic: new Date().toLocaleString('ar-SA', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      }),\n      customer: customer.name || 'عميل نقدي',\n      items: cart,\n      subtotal: subtotal,\n      vat: vatAmount,\n      total: total,\n      cashier: user.name,\n      paymentMethod: paymentMethod === 'cash' ? 'نقدي' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'تحويل بنكي'\n    };\n\n    // طباعة الفاتورة (محاكاة)\n    printInvoice(invoice);\n\n    // تفريغ السلة\n    setCart([]);\n    setCustomer({\n      name: '',\n      phone: '',\n      taxNumber: ''\n    });\n    setShowCustomerForm(false);\n    setIsProcessing(false);\n    alert('✅ تم إتمام البيع بنجاح!');\n  };\n\n  // إنشاء QR Code للفاتورة (حسب المعايير السعودية)\n  const generateQRCode = invoice => {\n    // الرقم الضريبي السعودي (15 رقم)\n    const vatNumber = \"310122393500003\";\n\n    // تنسيق التاريخ حسب المعايير السعودية (ISO 8601)\n    const invoiceDate = new Date();\n    const formattedDate = invoiceDate.toISOString();\n\n    // البيانات المطلوبة للـ QR Code السعودي حسب المعايير الرسمية\n    const qrData = {\n      // 1. اسم البائع\n      sellerName: \"شركة نظام المحاسبة السعودي المحدودة\",\n      // 2. الرقم الضريبي للبائع (15 رقم)\n      vatRegistrationNumber: vatNumber,\n      // 3. الطابع الزمني للفاتورة (ISO 8601)\n      invoiceTimestamp: formattedDate,\n      // 4. إجمالي الفاتورة شامل الضريبة\n      invoiceTotal: parseFloat(invoice.total.toFixed(2)),\n      // 5. إجمالي ضريبة القيمة المضافة\n      vatTotal: parseFloat(invoice.vat.toFixed(2))\n    };\n\n    // تحويل البيانات إلى تنسيق TLV (Tag-Length-Value) حسب المعايير السعودية\n    const createTLV = (tag, value) => {\n      const valueStr = value.toString();\n      const length = valueStr.length;\n      return String.fromCharCode(tag) + String.fromCharCode(length) + valueStr;\n    };\n\n    // إنشاء QR Code حسب المعايير السعودية\n    const qrString = createTLV(1, qrData.sellerName) + createTLV(2, qrData.vatRegistrationNumber) + createTLV(3, qrData.invoiceTimestamp) + createTLV(4, qrData.invoiceTotal) + createTLV(5, qrData.vatTotal);\n\n    // تحويل إلى Base64 للـ QR Code\n    const base64QR = btoa(qrString);\n\n    // إنشاء QR Code باستخدام خدمة مجانية\n    return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(base64QR)}`;\n  };\n\n  // طباعة الفاتورة المحسنة\n  const printInvoice = invoice => {\n    const qrCodeUrl = generateQRCode(invoice);\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>فاتورة ضريبية رقم ${invoice.id}</title>\n          <style>\n            body {\n              font-family: 'Arial', sans-serif;\n              margin: 20px;\n              line-height: 1.6;\n              color: #333;\n            }\n            .header {\n              text-align: center;\n              border-bottom: 3px solid #1890ff;\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 {\n              color: #1890ff;\n              margin: 0;\n              font-size: 1.8em;\n            }\n            .header h2 {\n              color: #666;\n              margin: 5px 0;\n              font-size: 1.2em;\n            }\n            .company-info {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              margin-bottom: 20px;\n              border-right: 4px solid #1890ff;\n            }\n            .invoice-details {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 20px;\n              margin: 20px 0;\n            }\n            .invoice-details div {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n            }\n            .invoice-details p {\n              margin: 8px 0;\n              font-size: 1em;\n            }\n            .items-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 20px 0;\n              box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            }\n            .items-table th {\n              background: linear-gradient(135deg, #1890ff, #40a9ff);\n              color: white;\n              padding: 12px 8px;\n              text-align: right;\n              font-weight: 600;\n            }\n            .items-table td {\n              border: 1px solid #e8e8e8;\n              padding: 10px 8px;\n              text-align: right;\n            }\n            .items-table tbody tr:nth-child(even) {\n              background-color: #f9f9f9;\n            }\n            .totals {\n              background: #f8f9fa;\n              padding: 20px;\n              border-radius: 8px;\n              margin-top: 20px;\n              border-right: 4px solid #52c41a;\n            }\n            .total-line {\n              display: flex;\n              justify-content: space-between;\n              margin: 8px 0;\n              font-size: 1.1em;\n            }\n            .vat-line {\n              color: #fa8c16;\n              font-weight: 600;\n            }\n            .final-total {\n              font-weight: bold;\n              font-size: 1.3em;\n              border-top: 2px solid #1890ff;\n              padding-top: 10px;\n              margin-top: 10px;\n              color: #1890ff;\n            }\n            .footer {\n              margin-top: 30px;\n              display: grid;\n              grid-template-columns: 1fr auto;\n              gap: 20px;\n              align-items: center;\n            }\n            .footer-text {\n              text-align: right;\n              color: #666;\n            }\n            .qr-section {\n              text-align: center;\n              border: 2px solid #e8e8e8;\n              padding: 15px;\n              border-radius: 8px;\n              background: white;\n            }\n            .qr-section h4 {\n              margin: 0 0 10px 0;\n              color: #1890ff;\n              font-size: 0.9em;\n            }\n            .tax-info {\n              background: #e6f7ff;\n              border: 1px solid #91d5ff;\n              padding: 15px;\n              border-radius: 8px;\n              margin: 20px 0;\n            }\n            .tax-info h3 {\n              color: #1890ff;\n              margin: 0 0 10px 0;\n              font-size: 1.1em;\n            }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>فاتورة ضريبية مبسطة</h2>\n          </div>\n\n          <div class=\"company-info\">\n            <h3 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات الشركة</h3>\n            <p><strong>اسم الشركة:</strong> شركة نظام المحاسبة السعودي المحدودة</p>\n            <p><strong>الرقم الضريبي:</strong> 310122393500003</p>\n            <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>\n            <p><strong>الهاتف:</strong> +966 11 123 4567</p>\n            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>\n          </div>\n\n          <div class=\"invoice-details\">\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">تفاصيل الفاتورة</h4>\n              <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>\n              <p><strong>تاريخ الإصدار:</strong> ${invoice.dateArabic}</p>\n              <p><strong>نوع الفاتورة:</strong> فاتورة ضريبية مبسطة</p>\n              <p><strong>حالة الفاتورة:</strong> مدفوعة</p>\n            </div>\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات العملية</h4>\n              <p><strong>العميل:</strong> ${invoice.customer}</p>\n              <p><strong>أمين الصندوق:</strong> ${invoice.cashier}</p>\n              <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod}</p>\n              <p><strong>رقم المرجع:</strong> ${invoice.id}-PAY</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th style=\"width: 40%\">وصف السلعة/الخدمة</th>\n                <th style=\"width: 15%\">السعر الوحدة</th>\n                <th style=\"width: 10%\">الكمية</th>\n                <th style=\"width: 15%\">الإجمالي قبل الضريبة</th>\n                <th style=\"width: 10%\">معدل الضريبة</th>\n                <th style=\"width: 10%\">مبلغ الضريبة</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${invoice.items.map(item => {\n      const itemSubtotal = item.price * item.quantity;\n      const itemVat = itemSubtotal * 0.15;\n      return `\n                  <tr>\n                    <td>${item.name}</td>\n                    <td>${item.price.toFixed(2)} ريال</td>\n                    <td>${item.quantity}</td>\n                    <td>${itemSubtotal.toFixed(2)} ريال</td>\n                    <td>15%</td>\n                    <td>${itemVat.toFixed(2)} ريال</td>\n                  </tr>\n                `;\n    }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"tax-info\">\n            <h3>📊 ملخص الضرائب</h3>\n            <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px;\">\n              <div>\n                <p><strong>إجمالي السلع والخدمات الخاضعة للضريبة:</strong></p>\n                <p style=\"color: #1890ff; font-size: 1.1em;\">${invoice.subtotal.toFixed(2)} ريال</p>\n              </div>\n              <div>\n                <p><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></p>\n                <p style=\"color: #fa8c16; font-size: 1.1em;\">${invoice.vat.toFixed(2)} ريال</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"totals\">\n            <div class=\"total-line\">\n              <span>المجموع الفرعي (قبل الضريبة):</span>\n              <span>${invoice.subtotal.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line vat-line\">\n              <span>ضريبة القيمة المضافة (15%):</span>\n              <span>${invoice.vat.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line final-total\">\n              <span>الإجمالي النهائي (شامل الضريبة):</span>\n              <span>${invoice.total.toFixed(2)} ريال</span>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <div class=\"footer-text\">\n              <h4 style=\"color: #1890ff; margin: 0 0 10px 0;\">شكراً لتعاملكم معنا</h4>\n              <p style=\"margin: 5px 0;\">هذه فاتورة ضريبية صادرة إلكترونياً</p>\n              <p style=\"margin: 5px 0;\">تم إنشاؤها بواسطة نظام المحاسبة السعودي المعتمد</p>\n              <p style=\"margin: 5px 0; font-size: 0.9em;\">للاستفسارات: <EMAIL></p>\n            </div>\n            <div class=\"qr-section\">\n              <h4>🔍 رمز الاستجابة السريعة</h4>\n              <img src=\"${qrCodeUrl}\" alt=\"QR Code\" style=\"max-width: 120px; height: auto;\">\n              <p style=\"font-size: 0.8em; margin: 5px 0 0 0; color: #666;\">امسح للتحقق من الفاتورة</p>\n            </div>\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 0.85em; color: #666;\">\n            <p style=\"margin: 0;\">هذا المستند تم إنشاؤه إلكترونياً ولا يتطلب توقيع أو ختم</p>\n            <p style=\"margin: 5px 0 0 0;\">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pos-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"pos-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: \"\\u21A9\\uFE0F \\u0631\\u062C\\u0648\\u0639 \\u0644\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDED2 \\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [user.name, \" - \", user.role]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pos-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0645\\u0646\\u062A\\u062C \\u0623\\u0648 \\u0628\\u0627\\u0631\\u0643\\u0648\\u062F...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-category\",\n                children: product.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-price\",\n                children: [product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-stock\",\n                children: [\"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646: \", product.stock]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => addToCart(product),\n              className: \"add-to-cart-btn\",\n              disabled: product.stock === 0,\n              children: product.stock === 0 ? 'نفد المخزون' : 'إضافة للسلة'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDED2 \\u0627\\u0644\\u0633\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCart([]),\n            className: \"clear-cart-btn\",\n            children: \"\\uD83D\\uDDD1\\uFE0F \\u062A\\u0641\\u0631\\u064A\\u063A \\u0627\\u0644\\u0633\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items\",\n          children: cart.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"empty-cart\",\n            children: \"\\u0627\\u0644\\u0633\\u0644\\u0629 \\u0641\\u0627\\u0631\\u063A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this) : cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [item.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quantity-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateQuantity(item.id, item.quantity - 1),\n                className: \"qty-btn\",\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"quantity\",\n                children: item.quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateQuantity(item.id, item.quantity + 1),\n                className: \"qty-btn\",\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-total\",\n              children: [(item.price * item.quantity).toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => removeFromCart(item.id),\n              className: \"remove-btn\",\n              children: \"\\u274C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-totals\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [subtotal.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [vatAmount.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line final-total\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [total.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"customer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCustomerForm(!showCustomerForm),\n              className: \"customer-btn\",\n              children: [\"\\uD83D\\uDC64 \", showCustomerForm ? 'إخفاء' : 'إضافة', \" \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), showCustomerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"customer-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\",\n                value: customer.name,\n                onChange: e => setCustomer({\n                  ...customer,\n                  name: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\",\n                value: customer.phone,\n                onChange: e => setCustomer({\n                  ...customer,\n                  phone: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: customer.taxNumber,\n                onChange: e => setCustomer({\n                  ...customer,\n                  taxNumber: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processPayment,\n            disabled: isProcessing,\n            className: `checkout-btn ${isProcessing ? 'processing' : ''}`,\n            children: isProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629...\"]\n            }, void 0, true) : '💳 إتمام البيع وطباعة الفاتورة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 431,\n    columnNumber: 5\n  }, this);\n};\n_s(POS, \"lMoiW+xoud7PhRyoXZ7SDpkG3K4=\");\n_c = POS;\nexport default POS;\nvar _c;\n$RefreshReg$(_c, \"POS\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "POS", "user", "onBack", "_s", "cart", "setCart", "products", "setProducts", "searchTerm", "setSearchTerm", "customer", "setCustomer", "name", "phone", "taxNumber", "showCustomerForm", "setShowCustomerForm", "paymentMethod", "setPaymentMethod", "isProcessing", "setIsProcessing", "sampleProducts", "id", "price", "stock", "barcode", "category", "addToCart", "product", "existingItem", "find", "item", "quantity", "map", "alert", "updateQuantity", "newQuantity", "removeFromCart", "p", "filter", "subtotal", "reduce", "sum", "vatRate", "vatAmount", "total", "filteredProducts", "toLowerCase", "includes", "processPayment", "length", "Promise", "resolve", "setTimeout", "invoice", "Date", "now", "date", "toISOString", "dateArabic", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "items", "vat", "cashier", "printInvoice", "generateQRCode", "vatNumber", "invoiceDate", "formattedDate", "qrData", "sellerName", "vatRegistrationNumber", "invoiceTimestamp", "invoiceTotal", "parseFloat", "toFixed", "vatTotal", "createTLV", "tag", "value", "valueStr", "toString", "String", "fromCharCode", "qrString", "base64QR", "btoa", "encodeURIComponent", "qrCodeUrl", "printWindow", "window", "open", "document", "write", "itemSubtotal", "itemVat", "join", "close", "print", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "placeholder", "onChange", "e", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/POS/POS.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './POS.css';\n\nconst POS = ({ user, onBack }) => {\n  const [cart, setCart] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [customer, setCustomer] = useState({\n    name: '',\n    phone: '',\n    taxNumber: ''\n  });\n  const [showCustomerForm, setShowCustomerForm] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState('cash'); // 'cash', 'card', 'transfer'\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // منتجات وهمية للتجربة\n  useEffect(() => {\n    const sampleProducts = [\n      { id: 1, name: 'لابتوب HP', price: 2500, stock: 10, barcode: '*********', category: 'إلكترونيات' },\n      { id: 2, name: 'ماوس لاسلكي', price: 85, stock: 25, barcode: '*********', category: 'إلكترونيات' },\n      { id: 3, name: 'كيبورد ميكانيكي', price: 150, stock: 15, barcode: '*********', category: 'إلكترونيات' },\n      { id: 4, name: 'شاشة 24 بوصة', price: 800, stock: 8, barcode: '*********', category: 'إلكترونيات' },\n      { id: 5, name: 'طابعة ليزر', price: 450, stock: 5, barcode: '*********', category: 'مكتبية' },\n      { id: 6, name: 'ورق A4', price: 25, stock: 100, barcode: '654987321', category: 'مكتبية' },\n      { id: 7, name: 'قلم حبر جاف', price: 3, stock: 200, barcode: '147258369', category: 'مكتبية' },\n      { id: 8, name: 'دفتر ملاحظات', price: 15, stock: 50, barcode: '963852741', category: 'مكتبية' }\n    ];\n    setProducts(sampleProducts);\n  }, []);\n\n  // إضافة منتج للسلة\n  const addToCart = (product) => {\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity < product.stock) {\n        setCart(cart.map(item =>\n          item.id === product.id\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        ));\n      } else {\n        alert('⚠️ لا توجد كمية كافية في المخزون');\n      }\n    } else {\n      setCart([...cart, { ...product, quantity: 1 }]);\n    }\n  };\n\n  // تحديث كمية المنتج\n  const updateQuantity = (id, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(id);\n      return;\n    }\n    \n    const product = products.find(p => p.id === id);\n    if (newQuantity > product.stock) {\n      alert('⚠️ لا توجد كمية كافية في المخزون');\n      return;\n    }\n\n    setCart(cart.map(item =>\n      item.id === id ? { ...item, quantity: newQuantity } : item\n    ));\n  };\n\n  // حذف منتج من السلة\n  const removeFromCart = (id) => {\n    setCart(cart.filter(item => item.id !== id));\n  };\n\n  // حساب الإجماليات\n  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n  const vatRate = 0.15; // 15% ضريبة القيمة المضافة\n  const vatAmount = subtotal * vatRate;\n  const total = subtotal + vatAmount;\n\n  // تصفية المنتجات حسب البحث\n  const filteredProducts = products.filter(product =>\n    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    product.barcode.includes(searchTerm)\n  );\n\n  // معالجة الدفع\n  const processPayment = async () => {\n    if (cart.length === 0) {\n      alert('⚠️ السلة فارغة');\n      return;\n    }\n\n    setIsProcessing(true);\n    \n    // محاكاة معالجة الدفع\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // إنشاء الفاتورة\n    const invoice = {\n      id: Date.now(),\n      date: new Date().toISOString(),\n      dateArabic: new Date().toLocaleString('ar-SA', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      }),\n      customer: customer.name || 'عميل نقدي',\n      items: cart,\n      subtotal: subtotal,\n      vat: vatAmount,\n      total: total,\n      cashier: user.name,\n      paymentMethod: paymentMethod === 'cash' ? 'نقدي' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'تحويل بنكي'\n    };\n\n    // طباعة الفاتورة (محاكاة)\n    printInvoice(invoice);\n\n    // تفريغ السلة\n    setCart([]);\n    setCustomer({ name: '', phone: '', taxNumber: '' });\n    setShowCustomerForm(false);\n    setIsProcessing(false);\n\n    alert('✅ تم إتمام البيع بنجاح!');\n  };\n\n  // إنشاء QR Code للفاتورة (حسب المعايير السعودية)\n  const generateQRCode = (invoice) => {\n    // الرقم الضريبي السعودي (15 رقم)\n    const vatNumber = \"310122393500003\";\n\n    // تنسيق التاريخ حسب المعايير السعودية (ISO 8601)\n    const invoiceDate = new Date();\n    const formattedDate = invoiceDate.toISOString();\n\n    // البيانات المطلوبة للـ QR Code السعودي حسب المعايير الرسمية\n    const qrData = {\n      // 1. اسم البائع\n      sellerName: \"شركة نظام المحاسبة السعودي المحدودة\",\n      // 2. الرقم الضريبي للبائع (15 رقم)\n      vatRegistrationNumber: vatNumber,\n      // 3. الطابع الزمني للفاتورة (ISO 8601)\n      invoiceTimestamp: formattedDate,\n      // 4. إجمالي الفاتورة شامل الضريبة\n      invoiceTotal: parseFloat(invoice.total.toFixed(2)),\n      // 5. إجمالي ضريبة القيمة المضافة\n      vatTotal: parseFloat(invoice.vat.toFixed(2))\n    };\n\n    // تحويل البيانات إلى تنسيق TLV (Tag-Length-Value) حسب المعايير السعودية\n    const createTLV = (tag, value) => {\n      const valueStr = value.toString();\n      const length = valueStr.length;\n      return String.fromCharCode(tag) + String.fromCharCode(length) + valueStr;\n    };\n\n    // إنشاء QR Code حسب المعايير السعودية\n    const qrString =\n      createTLV(1, qrData.sellerName) +\n      createTLV(2, qrData.vatRegistrationNumber) +\n      createTLV(3, qrData.invoiceTimestamp) +\n      createTLV(4, qrData.invoiceTotal) +\n      createTLV(5, qrData.vatTotal);\n\n    // تحويل إلى Base64 للـ QR Code\n    const base64QR = btoa(qrString);\n\n    // إنشاء QR Code باستخدام خدمة مجانية\n    return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(base64QR)}`;\n  };\n\n  // طباعة الفاتورة المحسنة\n  const printInvoice = (invoice) => {\n    const qrCodeUrl = generateQRCode(invoice);\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>فاتورة ضريبية رقم ${invoice.id}</title>\n          <style>\n            body {\n              font-family: 'Arial', sans-serif;\n              margin: 20px;\n              line-height: 1.6;\n              color: #333;\n            }\n            .header {\n              text-align: center;\n              border-bottom: 3px solid #1890ff;\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 {\n              color: #1890ff;\n              margin: 0;\n              font-size: 1.8em;\n            }\n            .header h2 {\n              color: #666;\n              margin: 5px 0;\n              font-size: 1.2em;\n            }\n            .company-info {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              margin-bottom: 20px;\n              border-right: 4px solid #1890ff;\n            }\n            .invoice-details {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 20px;\n              margin: 20px 0;\n            }\n            .invoice-details div {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n            }\n            .invoice-details p {\n              margin: 8px 0;\n              font-size: 1em;\n            }\n            .items-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 20px 0;\n              box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            }\n            .items-table th {\n              background: linear-gradient(135deg, #1890ff, #40a9ff);\n              color: white;\n              padding: 12px 8px;\n              text-align: right;\n              font-weight: 600;\n            }\n            .items-table td {\n              border: 1px solid #e8e8e8;\n              padding: 10px 8px;\n              text-align: right;\n            }\n            .items-table tbody tr:nth-child(even) {\n              background-color: #f9f9f9;\n            }\n            .totals {\n              background: #f8f9fa;\n              padding: 20px;\n              border-radius: 8px;\n              margin-top: 20px;\n              border-right: 4px solid #52c41a;\n            }\n            .total-line {\n              display: flex;\n              justify-content: space-between;\n              margin: 8px 0;\n              font-size: 1.1em;\n            }\n            .vat-line {\n              color: #fa8c16;\n              font-weight: 600;\n            }\n            .final-total {\n              font-weight: bold;\n              font-size: 1.3em;\n              border-top: 2px solid #1890ff;\n              padding-top: 10px;\n              margin-top: 10px;\n              color: #1890ff;\n            }\n            .footer {\n              margin-top: 30px;\n              display: grid;\n              grid-template-columns: 1fr auto;\n              gap: 20px;\n              align-items: center;\n            }\n            .footer-text {\n              text-align: right;\n              color: #666;\n            }\n            .qr-section {\n              text-align: center;\n              border: 2px solid #e8e8e8;\n              padding: 15px;\n              border-radius: 8px;\n              background: white;\n            }\n            .qr-section h4 {\n              margin: 0 0 10px 0;\n              color: #1890ff;\n              font-size: 0.9em;\n            }\n            .tax-info {\n              background: #e6f7ff;\n              border: 1px solid #91d5ff;\n              padding: 15px;\n              border-radius: 8px;\n              margin: 20px 0;\n            }\n            .tax-info h3 {\n              color: #1890ff;\n              margin: 0 0 10px 0;\n              font-size: 1.1em;\n            }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>فاتورة ضريبية مبسطة</h2>\n          </div>\n\n          <div class=\"company-info\">\n            <h3 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات الشركة</h3>\n            <p><strong>اسم الشركة:</strong> شركة نظام المحاسبة السعودي المحدودة</p>\n            <p><strong>الرقم الضريبي:</strong> 310122393500003</p>\n            <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>\n            <p><strong>الهاتف:</strong> +966 11 123 4567</p>\n            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>\n          </div>\n\n          <div class=\"invoice-details\">\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">تفاصيل الفاتورة</h4>\n              <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>\n              <p><strong>تاريخ الإصدار:</strong> ${invoice.dateArabic}</p>\n              <p><strong>نوع الفاتورة:</strong> فاتورة ضريبية مبسطة</p>\n              <p><strong>حالة الفاتورة:</strong> مدفوعة</p>\n            </div>\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات العملية</h4>\n              <p><strong>العميل:</strong> ${invoice.customer}</p>\n              <p><strong>أمين الصندوق:</strong> ${invoice.cashier}</p>\n              <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod}</p>\n              <p><strong>رقم المرجع:</strong> ${invoice.id}-PAY</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th style=\"width: 40%\">وصف السلعة/الخدمة</th>\n                <th style=\"width: 15%\">السعر الوحدة</th>\n                <th style=\"width: 10%\">الكمية</th>\n                <th style=\"width: 15%\">الإجمالي قبل الضريبة</th>\n                <th style=\"width: 10%\">معدل الضريبة</th>\n                <th style=\"width: 10%\">مبلغ الضريبة</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${invoice.items.map(item => {\n                const itemSubtotal = item.price * item.quantity;\n                const itemVat = itemSubtotal * 0.15;\n                return `\n                  <tr>\n                    <td>${item.name}</td>\n                    <td>${item.price.toFixed(2)} ريال</td>\n                    <td>${item.quantity}</td>\n                    <td>${itemSubtotal.toFixed(2)} ريال</td>\n                    <td>15%</td>\n                    <td>${itemVat.toFixed(2)} ريال</td>\n                  </tr>\n                `;\n              }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"tax-info\">\n            <h3>📊 ملخص الضرائب</h3>\n            <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px;\">\n              <div>\n                <p><strong>إجمالي السلع والخدمات الخاضعة للضريبة:</strong></p>\n                <p style=\"color: #1890ff; font-size: 1.1em;\">${invoice.subtotal.toFixed(2)} ريال</p>\n              </div>\n              <div>\n                <p><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></p>\n                <p style=\"color: #fa8c16; font-size: 1.1em;\">${invoice.vat.toFixed(2)} ريال</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"totals\">\n            <div class=\"total-line\">\n              <span>المجموع الفرعي (قبل الضريبة):</span>\n              <span>${invoice.subtotal.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line vat-line\">\n              <span>ضريبة القيمة المضافة (15%):</span>\n              <span>${invoice.vat.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line final-total\">\n              <span>الإجمالي النهائي (شامل الضريبة):</span>\n              <span>${invoice.total.toFixed(2)} ريال</span>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <div class=\"footer-text\">\n              <h4 style=\"color: #1890ff; margin: 0 0 10px 0;\">شكراً لتعاملكم معنا</h4>\n              <p style=\"margin: 5px 0;\">هذه فاتورة ضريبية صادرة إلكترونياً</p>\n              <p style=\"margin: 5px 0;\">تم إنشاؤها بواسطة نظام المحاسبة السعودي المعتمد</p>\n              <p style=\"margin: 5px 0; font-size: 0.9em;\">للاستفسارات: <EMAIL></p>\n            </div>\n            <div class=\"qr-section\">\n              <h4>🔍 رمز الاستجابة السريعة</h4>\n              <img src=\"${qrCodeUrl}\" alt=\"QR Code\" style=\"max-width: 120px; height: auto;\">\n              <p style=\"font-size: 0.8em; margin: 5px 0 0 0; color: #666;\">امسح للتحقق من الفاتورة</p>\n            </div>\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 0.85em; color: #666;\">\n            <p style=\"margin: 0;\">هذا المستند تم إنشاؤه إلكترونياً ولا يتطلب توقيع أو ختم</p>\n            <p style=\"margin: 5px 0 0 0;\">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n\n  return (\n    <div className=\"pos-container\">\n      {/* شريط التنقل */}\n      <header className=\"pos-header\">\n        <div className=\"header-content\">\n          <button onClick={onBack} className=\"back-btn\">\n            ↩️ رجوع للوحة التحكم\n          </button>\n          <h1>🛒 نقطة البيع</h1>\n          <div className=\"user-info\">\n            <span>{user.name} - {user.role}</span>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"pos-main\">\n        {/* قسم المنتجات */}\n        <div className=\"products-section\">\n          <div className=\"search-bar\">\n            <input\n              type=\"text\"\n              placeholder=\"🔍 البحث عن منتج أو باركود...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          <div className=\"products-grid\">\n            {filteredProducts.map(product => (\n              <div key={product.id} className=\"product-card\">\n                <div className=\"product-info\">\n                  <h3>{product.name}</h3>\n                  <p className=\"product-category\">{product.category}</p>\n                  <p className=\"product-price\">{product.price} ريال</p>\n                  <p className=\"product-stock\">المخزون: {product.stock}</p>\n                </div>\n                <button\n                  onClick={() => addToCart(product)}\n                  className=\"add-to-cart-btn\"\n                  disabled={product.stock === 0}\n                >\n                  {product.stock === 0 ? 'نفد المخزون' : 'إضافة للسلة'}\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* قسم السلة والدفع */}\n        <div className=\"cart-section\">\n          <div className=\"cart-header\">\n            <h2>🛒 السلة</h2>\n            {cart.length > 0 && (\n              <button\n                onClick={() => setCart([])}\n                className=\"clear-cart-btn\"\n              >\n                🗑️ تفريغ السلة\n              </button>\n            )}\n          </div>\n\n          <div className=\"cart-items\">\n            {cart.length === 0 ? (\n              <p className=\"empty-cart\">السلة فارغة</p>\n            ) : (\n              cart.map(item => (\n                <div key={item.id} className=\"cart-item\">\n                  <div className=\"item-info\">\n                    <h4>{item.name}</h4>\n                    <p>{item.price} ريال</p>\n                  </div>\n                  <div className=\"quantity-controls\">\n                    <button\n                      onClick={() => updateQuantity(item.id, item.quantity - 1)}\n                      className=\"qty-btn\"\n                    >\n                      -\n                    </button>\n                    <span className=\"quantity\">{item.quantity}</span>\n                    <button\n                      onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                      className=\"qty-btn\"\n                    >\n                      +\n                    </button>\n                  </div>\n                  <div className=\"item-total\">\n                    {(item.price * item.quantity).toFixed(2)} ريال\n                  </div>\n                  <button\n                    onClick={() => removeFromCart(item.id)}\n                    className=\"remove-btn\"\n                  >\n                    ❌\n                  </button>\n                </div>\n              ))\n            )}\n          </div>\n\n          {cart.length > 0 && (\n            <>\n              <div className=\"cart-totals\">\n                <div className=\"total-line\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{subtotal.toFixed(2)} ريال</span>\n                </div>\n                <div className=\"total-line\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{vatAmount.toFixed(2)} ريال</span>\n                </div>\n                <div className=\"total-line final-total\">\n                  <span>الإجمالي:</span>\n                  <span>{total.toFixed(2)} ريال</span>\n                </div>\n              </div>\n\n              <div className=\"customer-section\">\n                <button\n                  onClick={() => setShowCustomerForm(!showCustomerForm)}\n                  className=\"customer-btn\"\n                >\n                  👤 {showCustomerForm ? 'إخفاء' : 'إضافة'} بيانات العميل\n                </button>\n\n                {showCustomerForm && (\n                  <div className=\"customer-form\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"اسم العميل\"\n                      value={customer.name}\n                      onChange={(e) => setCustomer({...customer, name: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"رقم الهاتف\"\n                      value={customer.phone}\n                      onChange={(e) => setCustomer({...customer, phone: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"الرقم الضريبي (اختياري)\"\n                      value={customer.taxNumber}\n                      onChange={(e) => setCustomer({...customer, taxNumber: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                  </div>\n                )}\n              </div>\n\n              <button\n                onClick={processPayment}\n                disabled={isProcessing}\n                className={`checkout-btn ${isProcessing ? 'processing' : ''}`}\n              >\n                {isProcessing ? (\n                  <>\n                    <span className=\"spinner\"></span>\n                    جاري المعالجة...\n                  </>\n                ) : (\n                  '💳 إتمام البيع وطباعة الفاتورة'\n                )}\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default POS;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,MAAMC,GAAG,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0B,cAAc,GAAG,CACrB;MAAEC,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,WAAW;MAAEW,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAa,CAAC,EAClG;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,aAAa;MAAEW,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAa,CAAC,EAClG;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,iBAAiB;MAAEW,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAa,CAAC,EACvG;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,cAAc;MAAEW,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAa,CAAC,EACnG;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,YAAY;MAAEW,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAS,CAAC,EAC7F;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,QAAQ;MAAEW,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAS,CAAC,EAC1F;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,aAAa;MAAEW,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAS,CAAC,EAC9F;MAAEJ,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE,cAAc;MAAEW,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAChG;IACDnB,WAAW,CAACc,cAAc,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,SAAS,GAAIC,OAAO,IAAK;IAC7B,MAAMC,YAAY,GAAGzB,IAAI,CAAC0B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,CAAC;IAC9D,IAAIO,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACG,QAAQ,GAAGJ,OAAO,CAACJ,KAAK,EAAE;QACzCnB,OAAO,CAACD,IAAI,CAAC6B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,GAClB;UAAE,GAAGS,IAAI;UAAEC,QAAQ,EAAED,IAAI,CAACC,QAAQ,GAAG;QAAE,CAAC,GACxCD,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLG,KAAK,CAAC,kCAAkC,CAAC;MAC3C;IACF,CAAC,MAAM;MACL7B,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGwB,OAAO;QAAEI,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAGA,CAACb,EAAE,EAAEc,WAAW,KAAK;IAC1C,IAAIA,WAAW,IAAI,CAAC,EAAE;MACpBC,cAAc,CAACf,EAAE,CAAC;MAClB;IACF;IAEA,MAAMM,OAAO,GAAGtB,QAAQ,CAACwB,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKA,EAAE,CAAC;IAC/C,IAAIc,WAAW,GAAGR,OAAO,CAACJ,KAAK,EAAE;MAC/BU,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACF;IAEA7B,OAAO,CAACD,IAAI,CAAC6B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACT,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGS,IAAI;MAAEC,QAAQ,EAAEI;IAAY,CAAC,GAAGL,IACxD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMM,cAAc,GAAIf,EAAE,IAAK;IAC7BjB,OAAO,CAACD,IAAI,CAACmC,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACT,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMkB,QAAQ,GAAGpC,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAIX,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAS,EAAE,CAAC,CAAC;EAClF,MAAMW,OAAO,GAAG,IAAI,CAAC,CAAC;EACtB,MAAMC,SAAS,GAAGJ,QAAQ,GAAGG,OAAO;EACpC,MAAME,KAAK,GAAGL,QAAQ,GAAGI,SAAS;;EAElC;EACA,MAAME,gBAAgB,GAAGxC,QAAQ,CAACiC,MAAM,CAACX,OAAO,IAC9CA,OAAO,CAAChB,IAAI,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,UAAU,CAACuC,WAAW,CAAC,CAAC,CAAC,IAC7DnB,OAAO,CAACH,OAAO,CAACuB,QAAQ,CAACxC,UAAU,CACrC,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI7C,IAAI,CAAC8C,MAAM,KAAK,CAAC,EAAE;MACrBhB,KAAK,CAAC,gBAAgB,CAAC;MACvB;IACF;IAEAd,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,MAAM,IAAI+B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,OAAO,GAAG;MACdhC,EAAE,EAAEiC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,OAAO,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;MACFxD,QAAQ,EAAEA,QAAQ,CAACE,IAAI,IAAI,WAAW;MACtCuD,KAAK,EAAE/D,IAAI;MACXoC,QAAQ,EAAEA,QAAQ;MAClB4B,GAAG,EAAExB,SAAS;MACdC,KAAK,EAAEA,KAAK;MACZwB,OAAO,EAAEpE,IAAI,CAACW,IAAI;MAClBK,aAAa,EAAEA,aAAa,KAAK,MAAM,GAAG,MAAM,GAAGA,aAAa,KAAK,MAAM,GAAG,cAAc,GAAG;IACjG,CAAC;;IAED;IACAqD,YAAY,CAAChB,OAAO,CAAC;;IAErB;IACAjD,OAAO,CAAC,EAAE,CAAC;IACXM,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;IACnDE,mBAAmB,CAAC,KAAK,CAAC;IAC1BI,eAAe,CAAC,KAAK,CAAC;IAEtBc,KAAK,CAAC,yBAAyB,CAAC;EAClC,CAAC;;EAED;EACA,MAAMqC,cAAc,GAAIjB,OAAO,IAAK;IAClC;IACA,MAAMkB,SAAS,GAAG,iBAAiB;;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAIlB,IAAI,CAAC,CAAC;IAC9B,MAAMmB,aAAa,GAAGD,WAAW,CAACf,WAAW,CAAC,CAAC;;IAE/C;IACA,MAAMiB,MAAM,GAAG;MACb;MACAC,UAAU,EAAE,qCAAqC;MACjD;MACAC,qBAAqB,EAAEL,SAAS;MAChC;MACAM,gBAAgB,EAAEJ,aAAa;MAC/B;MACAK,YAAY,EAAEC,UAAU,CAAC1B,OAAO,CAACT,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,CAAC;MAClD;MACAC,QAAQ,EAAEF,UAAU,CAAC1B,OAAO,CAACc,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED;IACA,MAAME,SAAS,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;MAChC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;MACjC,MAAMrC,MAAM,GAAGoC,QAAQ,CAACpC,MAAM;MAC9B,OAAOsC,MAAM,CAACC,YAAY,CAACL,GAAG,CAAC,GAAGI,MAAM,CAACC,YAAY,CAACvC,MAAM,CAAC,GAAGoC,QAAQ;IAC1E,CAAC;;IAED;IACA,MAAMI,QAAQ,GACZP,SAAS,CAAC,CAAC,EAAER,MAAM,CAACC,UAAU,CAAC,GAC/BO,SAAS,CAAC,CAAC,EAAER,MAAM,CAACE,qBAAqB,CAAC,GAC1CM,SAAS,CAAC,CAAC,EAAER,MAAM,CAACG,gBAAgB,CAAC,GACrCK,SAAS,CAAC,CAAC,EAAER,MAAM,CAACI,YAAY,CAAC,GACjCI,SAAS,CAAC,CAAC,EAAER,MAAM,CAACO,QAAQ,CAAC;;IAE/B;IACA,MAAMS,QAAQ,GAAGC,IAAI,CAACF,QAAQ,CAAC;;IAE/B;IACA,OAAO,iEAAiEG,kBAAkB,CAACF,QAAQ,CAAC,EAAE;EACxG,CAAC;;EAED;EACA,MAAMrB,YAAY,GAAIhB,OAAO,IAAK;IAChC,MAAMwC,SAAS,GAAGvB,cAAc,CAACjB,OAAO,CAAC;IACzC,MAAMyC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7CF,WAAW,CAACG,QAAQ,CAACC,KAAK,CAAC;AAC/B;AACA;AACA,qCAAqC7C,OAAO,CAAChC,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkDgC,OAAO,CAAChC,EAAE;AAC5D,mDAAmDgC,OAAO,CAACK,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA,4CAA4CL,OAAO,CAAC5C,QAAQ;AAC5D,kDAAkD4C,OAAO,CAACe,OAAO;AACjE,iDAAiDf,OAAO,CAACrC,aAAa;AACtE,gDAAgDqC,OAAO,CAAChC,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBgC,OAAO,CAACa,KAAK,CAAClC,GAAG,CAACF,IAAI,IAAI;MAC1B,MAAMqE,YAAY,GAAGrE,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAQ;MAC/C,MAAMqE,OAAO,GAAGD,YAAY,GAAG,IAAI;MACnC,OAAO;AACvB;AACA,0BAA0BrE,IAAI,CAACnB,IAAI;AACnC,0BAA0BmB,IAAI,CAACR,KAAK,CAAC0D,OAAO,CAAC,CAAC,CAAC;AAC/C,0BAA0BlD,IAAI,CAACC,QAAQ;AACvC,0BAA0BoE,YAAY,CAACnB,OAAO,CAAC,CAAC,CAAC;AACjD;AACA,0BAA0BoB,OAAO,CAACpB,OAAO,CAAC,CAAC,CAAC;AAC5C;AACA,iBAAiB;IACH,CAAC,CAAC,CAACqB,IAAI,CAAC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+DhD,OAAO,CAACd,QAAQ,CAACyC,OAAO,CAAC,CAAC,CAAC;AAC1F;AACA;AACA;AACA,+DAA+D3B,OAAO,CAACc,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB3B,OAAO,CAACd,QAAQ,CAACyC,OAAO,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA,sBAAsB3B,OAAO,CAACc,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC;AAC5C;AACA;AACA;AACA,sBAAsB3B,OAAO,CAACT,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0Ba,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,IAAIvC,IAAI,CAAC,CAAC,CAACK,cAAc,CAAC,OAAO,CAAC;AAC7F;AACA;AACA;AACA,KAAK,CAAC;IACFmC,WAAW,CAACG,QAAQ,CAACK,KAAK,CAAC,CAAC;IAC5BR,WAAW,CAACS,KAAK,CAAC,CAAC;EACrB,CAAC;EAED,oBACE3G,OAAA;IAAK4G,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B7G,OAAA;MAAQ4G,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5B7G,OAAA;QAAK4G,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7G,OAAA;UAAQ8G,OAAO,EAAEzG,MAAO;UAACuG,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA;UAAA6G,QAAA,EAAI;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBlH,OAAA;UAAK4G,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7G,OAAA;YAAA6G,QAAA,GAAOzG,IAAI,CAACW,IAAI,EAAC,KAAG,EAACX,IAAI,CAAC+G,IAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETlH,OAAA;MAAK4G,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEvB7G,OAAA;QAAK4G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7G,OAAA;UAAK4G,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB7G,OAAA;YACEoH,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wIAA+B;YAC3C7B,KAAK,EAAE7E,UAAW;YAClB2G,QAAQ,EAAGC,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAE;YAC/CoB,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlH,OAAA;UAAK4G,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B5D,gBAAgB,CAACb,GAAG,CAACL,OAAO,iBAC3B/B,OAAA;YAAsB4G,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC5C7G,OAAA;cAAK4G,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7G,OAAA;gBAAA6G,QAAA,EAAK9E,OAAO,CAAChB;cAAI;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBlH,OAAA;gBAAG4G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAE9E,OAAO,CAACF;cAAQ;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDlH,OAAA;gBAAG4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAE9E,OAAO,CAACL,KAAK,EAAC,2BAAK;cAAA;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDlH,OAAA;gBAAG4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,8CAAS,EAAC9E,OAAO,CAACJ,KAAK;cAAA;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNlH,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAMhF,SAAS,CAACC,OAAO,CAAE;cAClC6E,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAE1F,OAAO,CAACJ,KAAK,KAAK,CAAE;cAAAkF,QAAA,EAE7B9E,OAAO,CAACJ,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG;YAAa;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA,GAbDnF,OAAO,CAACN,EAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlH,OAAA;QAAK4G,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7G,OAAA;UAAK4G,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7G,OAAA;YAAA6G,QAAA,EAAI;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChB3G,IAAI,CAAC8C,MAAM,GAAG,CAAC,iBACdrD,OAAA;YACE8G,OAAO,EAAEA,CAAA,KAAMtG,OAAO,CAAC,EAAE,CAAE;YAC3BoG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlH,OAAA;UAAK4G,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBtG,IAAI,CAAC8C,MAAM,KAAK,CAAC,gBAChBrD,OAAA;YAAG4G,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAEzC3G,IAAI,CAAC6B,GAAG,CAACF,IAAI,iBACXlC,OAAA;YAAmB4G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtC7G,OAAA;cAAK4G,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7G,OAAA;gBAAA6G,QAAA,EAAK3E,IAAI,CAACnB;cAAI;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBlH,OAAA;gBAAA6G,QAAA,GAAI3E,IAAI,CAACR,KAAK,EAAC,2BAAK;cAAA;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNlH,OAAA;cAAK4G,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7G,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAACJ,IAAI,CAACT,EAAE,EAAES,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAE;gBAC1DyE,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlH,OAAA;gBAAM4G,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAE3E,IAAI,CAACC;cAAQ;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDlH,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAACJ,IAAI,CAACT,EAAE,EAAES,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAE;gBAC1DyE,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlH,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,GACxB,CAAC3E,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAQ,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAC,2BAC3C;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlH,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAMtE,cAAc,CAACN,IAAI,CAACT,EAAE,CAAE;cACvCmF,SAAS,EAAC,YAAY;cAAAC,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GA5BDhF,IAAI,CAACT,EAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BZ,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL3G,IAAI,CAAC8C,MAAM,GAAG,CAAC,iBACdrD,OAAA,CAAAE,SAAA;UAAA2G,QAAA,gBACE7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B7G,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BlH,OAAA;gBAAA6G,QAAA,GAAOlE,QAAQ,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACNlH,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAM;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxClH,OAAA;gBAAA6G,QAAA,GAAO9D,SAAS,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNlH,OAAA;cAAK4G,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7G,OAAA;gBAAA6G,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtBlH,OAAA;gBAAA6G,QAAA,GAAO7D,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlH,OAAA;YAAK4G,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B7G,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAM3F,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtD0F,SAAS,EAAC,cAAc;cAAAC,QAAA,GACzB,eACI,EAAC3F,gBAAgB,GAAG,OAAO,GAAG,OAAO,EAAC,4EAC3C;YAAA;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERhG,gBAAgB,iBACflB,OAAA;cAAK4G,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7G,OAAA;gBACEoH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,yDAAY;gBACxB7B,KAAK,EAAE3E,QAAQ,CAACE,IAAK;gBACrBuG,QAAQ,EAAGC,CAAC,IAAKzG,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEE,IAAI,EAAEwG,CAAC,CAACC,MAAM,CAAChC;gBAAK,CAAC,CAAE;gBAClEoB,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFlH,OAAA;gBACEoH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,yDAAY;gBACxB7B,KAAK,EAAE3E,QAAQ,CAACG,KAAM;gBACtBsG,QAAQ,EAAGC,CAAC,IAAKzG,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEG,KAAK,EAAEuG,CAAC,CAACC,MAAM,CAAChC;gBAAK,CAAC,CAAE;gBACnEoB,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFlH,OAAA;gBACEoH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,wHAAyB;gBACrC7B,KAAK,EAAE3E,QAAQ,CAACI,SAAU;gBAC1BqG,QAAQ,EAAGC,CAAC,IAAKzG,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEI,SAAS,EAAEsG,CAAC,CAACC,MAAM,CAAChC;gBAAK,CAAC,CAAE;gBACvEoB,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlH,OAAA;YACE8G,OAAO,EAAE1D,cAAe;YACxBqE,QAAQ,EAAEnG,YAAa;YACvBsF,SAAS,EAAE,gBAAgBtF,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;YAAAuF,QAAA,EAE7DvF,YAAY,gBACXtB,OAAA,CAAAE,SAAA;cAAA2G,QAAA,gBACE7G,OAAA;gBAAM4G,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gFAEnC;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5G,EAAA,CAxlBIH,GAAG;AAAAuH,EAAA,GAAHvH,GAAG;AA0lBT,eAAeA,GAAG;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}