{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\Login\\\\EnhancedLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport { testDatabase } from '../../utils/testDatabase';\nimport './EnhancedLogin.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EnhancedLogin = ({\n  onLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n  useEffect(() => {\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({\n        ...prev,\n        username: rememberedUsername\n      }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 500));\n      console.log('محاولة تسجيل الدخول:', formData.username);\n      const user = database.authenticateUser(formData.username, formData.password);\n      console.log('نتيجة المصادقة:', user);\n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        try {\n          database.logActivity({\n            type: 'login',\n            userId: user.id,\n            username: user.username,\n            timestamp: new Date().toISOString(),\n            details: 'تسجيل دخول ناجح'\n          });\n        } catch (logError) {\n          console.warn('خطأ في تسجيل النشاط:', logError);\n        }\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDemoLogin = async () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n\n    // تسجيل دخول تلقائي\n    setTimeout(() => {\n      const form = document.querySelector('.login-form');\n      if (form) {\n        form.dispatchEvent(new Event('submit', {\n          bubbles: true,\n          cancelable: true\n        }));\n      }\n    }, 100);\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"enhanced-login\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-background\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"background-pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shapes\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shape shape-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: \"\\uD83C\\uDFEA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Aronim EXP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-time\",\n            children: getCurrentTime()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0633\\u062C\\u0644 \\u062F\\u062E\\u0648\\u0644\\u0643 \\u0644\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0625\\u0644\\u0649 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"login-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"username\",\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  name: \"username\",\n                  value: formData.username,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-icon\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n                  className: \"form-input\",\n                  disabled: loading,\n                  autoComplete: \"current-password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"password-toggle\",\n                  onClick: () => setShowPassword(!showPassword),\n                  disabled: loading,\n                  children: showPassword ? '🙈' : '👁️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-options\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"checkbox-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: rememberMe,\n                  onChange: e => setRememberMe(e.target.checked),\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"checkmark\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), \"\\u062A\\u0630\\u0643\\u0631\\u0646\\u064A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"login-button\",\n              disabled: loading,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"button-icon\",\n                  children: \"\\uD83D\\uDD10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divider\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0623\\u0648\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleDemoLogin,\n              className: \"demo-button\",\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), \"\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 (Demo)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"system-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDD12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0622\\u0645\\u0646 \\u0648\\u0645\\u062D\\u0645\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0645\\u062A\\u062C\\u0627\\u0648\\u0628 \\u0645\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u062C\\u0647\\u0632\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"copyright\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Aronim EXP. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0646\\u0633\\u062E\\u0629 2.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0646\\u0642\\u0637\\u0629 \\u0628\\u064A\\u0639 \\u0645\\u062A\\u0637\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0628\\u064A\\u0639 \\u0633\\u0631\\u064A\\u0639 \\u0648\\u0641\\u0639\\u0627\\u0644 \\u0645\\u0639 \\u062F\\u0639\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0645\\u0641\\u0635\\u0644\\u0629 \\u0648\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u062F\\u0642\\u064A\\u0642\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u062A\\u062A\\u0628\\u0639 \\u062F\\u0642\\u064A\\u0642 \\u0644\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0648\\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0630\\u0643\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062D\\u0627\\u0633\\u0628\\u064A \\u0634\\u0627\\u0645\\u0644 \\u0645\\u0639 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedLogin, \"IjvILKq9CIJnPDg9lujxmrutoIc=\");\n_c = EnhancedLogin;\nexport default EnhancedLogin;\nvar _c;\n$RefreshReg$(_c, \"EnhancedLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "database", "testDatabase", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "onLogin", "_s", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "showPassword", "setShowPassword", "rememberMe", "setRememberMe", "rememberedUsername", "localStorage", "getItem", "prev", "usernameField", "document", "getElementById", "focus", "handleInputChange", "e", "name", "value", "target", "handleSubmit", "preventDefault", "trim", "Promise", "resolve", "setTimeout", "console", "log", "user", "authenticateUser", "setItem", "removeItem", "logActivity", "type", "userId", "id", "timestamp", "Date", "toISOString", "details", "logError", "warn", "handleDemoLogin", "form", "querySelector", "dispatchEvent", "Event", "bubbles", "cancelable", "getCurrentTime", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "onChange", "placeholder", "disabled", "autoComplete", "onClick", "checked", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/Login/EnhancedLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport database from '../../utils/database';\nimport { testDatabase } from '../../utils/testDatabase';\nimport './EnhancedLogin.css';\n\nconst EnhancedLogin = ({ onLogin }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n\n  useEffect(() => {\n    // Check for remembered credentials\n    const rememberedUsername = localStorage.getItem('rememberedUsername');\n    if (rememberedUsername) {\n      setFormData(prev => ({ ...prev, username: rememberedUsername }));\n      setRememberMe(true);\n    }\n\n    // Auto-focus on username field\n    const usernameField = document.getElementById('username');\n    if (usernameField) {\n      usernameField.focus();\n    }\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username.trim() || !formData.password.trim()) {\n      setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate login delay for better UX\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      console.log('محاولة تسجيل الدخول:', formData.username);\n\n      const user = database.authenticateUser(formData.username, formData.password);\n\n      console.log('نتيجة المصادقة:', user);\n\n      if (user) {\n        // Handle remember me\n        if (rememberMe) {\n          localStorage.setItem('rememberedUsername', formData.username);\n        } else {\n          localStorage.removeItem('rememberedUsername');\n        }\n\n        // Log the login activity\n        try {\n          database.logActivity({\n            type: 'login',\n            userId: user.id,\n            username: user.username,\n            timestamp: new Date().toISOString(),\n            details: 'تسجيل دخول ناجح'\n          });\n        } catch (logError) {\n          console.warn('خطأ في تسجيل النشاط:', logError);\n        }\n\n        onLogin(user);\n      } else {\n        setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n      }\n    } catch (error) {\n      console.error('خطأ في تسجيل الدخول:', error);\n      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoLogin = async () => {\n    setFormData({\n      username: 'admin',\n      password: 'admin123'\n    });\n    setError('');\n\n    // تسجيل دخول تلقائي\n    setTimeout(() => {\n      const form = document.querySelector('.login-form');\n      if (form) {\n        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));\n      }\n    }, 100);\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('ar-SA', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"enhanced-login\">\n      <div className=\"login-background\">\n        <div className=\"background-pattern\"></div>\n        <div className=\"floating-shapes\">\n          <div className=\"shape shape-1\"></div>\n          <div className=\"shape shape-2\"></div>\n          <div className=\"shape shape-3\"></div>\n          <div className=\"shape shape-4\"></div>\n        </div>\n      </div>\n\n      <div className=\"login-container\">\n        <div className=\"login-card\">\n          <div className=\"login-header\">\n            <div className=\"company-logo\">\n              <div className=\"logo-icon\">🏪</div>\n              <div className=\"logo-text\">\n                <h1>Aronim EXP</h1>\n                <p>نظام المحاسبة المتطور</p>\n              </div>\n            </div>\n            <div className=\"current-time\">\n              {getCurrentTime()}\n            </div>\n          </div>\n\n          <div className=\"login-body\">\n            <div className=\"welcome-text\">\n              <h2>مرحباً بك</h2>\n              <p>سجل دخولك للوصول إلى نظام المحاسبة</p>\n            </div>\n\n            {error && (\n              <div className=\"error-message\">\n                <span className=\"error-icon\">⚠️</span>\n                {error}\n              </div>\n            )}\n\n            <form onSubmit={handleSubmit} className=\"login-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"username\">اسم المستخدم</label>\n                <div className=\"input-container\">\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل اسم المستخدم\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"username\"\n                  />\n                  <span className=\"input-icon\">👤</span>\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\">كلمة المرور</label>\n                <div className=\"input-container\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"أدخل كلمة المرور\"\n                    className=\"form-input\"\n                    disabled={loading}\n                    autoComplete=\"current-password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    disabled={loading}\n                  >\n                    {showPassword ? '🙈' : '👁️'}\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"form-options\">\n                <label className=\"checkbox-container\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    disabled={loading}\n                  />\n                  <span className=\"checkmark\"></span>\n                  تذكرني\n                </label>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"login-button\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    جاري تسجيل الدخول...\n                  </>\n                ) : (\n                  <>\n                    <span className=\"button-icon\">🔐</span>\n                    تسجيل الدخول\n                  </>\n                )}\n              </button>\n            </form>\n\n            <div className=\"demo-section\">\n              <div className=\"divider\">\n                <span>أو</span>\n              </div>\n              <button\n                type=\"button\"\n                onClick={handleDemoLogin}\n                className=\"demo-button\"\n                disabled={loading}\n              >\n                <span className=\"button-icon\">🎯</span>\n                تجربة النظام (Demo)\n              </button>\n            </div>\n          </div>\n\n          <div className=\"login-footer\">\n            <div className=\"system-info\">\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🇸🇦</span>\n                <span>متوافق مع المتطلبات السعودية</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">🔒</span>\n                <span>نظام آمن ومحمي</span>\n              </div>\n              <div className=\"info-item\">\n                <span className=\"info-icon\">📱</span>\n                <span>متجاوب مع جميع الأجهزة</span>\n              </div>\n            </div>\n            \n            <div className=\"copyright\">\n              <p>© 2024 Aronim EXP. جميع الحقوق محفوظة.</p>\n              <p>نسخة 2.0.0</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"features-sidebar\">\n          <h3>مميزات النظام</h3>\n          <div className=\"features-list\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🛒</div>\n              <div className=\"feature-content\">\n                <h4>نقطة بيع متطورة</h4>\n                <p>نظام بيع سريع وفعال مع دعم الباركود</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📊</div>\n              <div className=\"feature-content\">\n                <h4>تقارير شاملة</h4>\n                <p>تقارير مالية مفصلة وإحصائيات دقيقة</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📦</div>\n              <div className=\"feature-content\">\n                <h4>إدارة المخزون</h4>\n                <p>تتبع دقيق للمخزون والتنبيهات الذكية</p>\n              </div>\n            </div>\n            \n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <div className=\"feature-content\">\n                <h4>محاسبة متكاملة</h4>\n                <p>نظام محاسبي شامل مع ضريبة القيمة المضافة</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMsB,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IACrE,IAAIF,kBAAkB,EAAE;MACtBX,WAAW,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEb,QAAQ,EAAEU;MAAmB,CAAC,CAAC,CAAC;MAChED,aAAa,CAAC,IAAI,CAAC;IACrB;;IAEA;IACA,MAAMK,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC;IACzD,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvB,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACO,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIjB,KAAK,EAAE;MACTC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC1B,QAAQ,CAACE,QAAQ,CAACyB,IAAI,CAAC,CAAC,IAAI,CAAC3B,QAAQ,CAACG,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC1DpB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAM,IAAIqB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtDE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhC,QAAQ,CAACE,QAAQ,CAAC;MAEtD,MAAM+B,IAAI,GAAG1C,QAAQ,CAAC2C,gBAAgB,CAAClC,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAE5E4B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,IAAI,CAAC;MAEpC,IAAIA,IAAI,EAAE;QACR;QACA,IAAIvB,UAAU,EAAE;UACdG,YAAY,CAACsB,OAAO,CAAC,oBAAoB,EAAEnC,QAAQ,CAACE,QAAQ,CAAC;QAC/D,CAAC,MAAM;UACLW,YAAY,CAACuB,UAAU,CAAC,oBAAoB,CAAC;QAC/C;;QAEA;QACA,IAAI;UACF7C,QAAQ,CAAC8C,WAAW,CAAC;YACnBC,IAAI,EAAE,OAAO;YACbC,MAAM,EAAEN,IAAI,CAACO,EAAE;YACftC,QAAQ,EAAE+B,IAAI,CAAC/B,QAAQ;YACvBuC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACnCC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOC,QAAQ,EAAE;UACjBd,OAAO,CAACe,IAAI,CAAC,sBAAsB,EAAED,QAAQ,CAAC;QAChD;QAEA/C,OAAO,CAACmC,IAAI,CAAC;MACf,CAAC,MAAM;QACL1B,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC9C,WAAW,CAAC;MACVC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFI,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACAuB,UAAU,CAAC,MAAM;MACf,MAAMkB,IAAI,GAAG/B,QAAQ,CAACgC,aAAa,CAAC,aAAa,CAAC;MAClD,IAAID,IAAI,EAAE;QACRA,IAAI,CAACE,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,EAAE;UAAEC,OAAO,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC,CAAC;MAC9E;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIZ,IAAI,CAAC,CAAC,CAACa,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEnE,OAAA;IAAKoE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BrE,OAAA;MAAKoE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BrE,OAAA;QAAKoE,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CzE,OAAA;QAAKoE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrE,OAAA;UAAKoE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrCzE,OAAA;UAAKoE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrCzE,OAAA;UAAKoE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrCzE,OAAA;UAAKoE,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzE,OAAA;MAAKoE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BrE,OAAA;QAAKoE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCzE,OAAA;cAAKoE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrE,OAAA;gBAAAqE,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBzE,OAAA;gBAAAqE,QAAA,EAAG;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BT,cAAc,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAAqE,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBzE,OAAA;cAAAqE,QAAA,EAAG;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAEL7D,KAAK,iBACJZ,OAAA;YAAKoE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrE,OAAA;cAAMoE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrC7D,KAAK;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDzE,OAAA;YAAM0E,QAAQ,EAAE3C,YAAa;YAACqC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAClDrE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrE,OAAA;gBAAO2E,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CzE,OAAA;gBAAKoE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrE,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXE,EAAE,EAAC,UAAU;kBACblB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACE,QAAS;kBACzBoE,QAAQ,EAAElD,iBAAkB;kBAC5BmD,WAAW,EAAC,8FAAmB;kBAC/BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAEpE,OAAQ;kBAClBqE,YAAY,EAAC;gBAAU;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFzE,OAAA;kBAAMoE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzE,OAAA;cAAKoE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrE,OAAA;gBAAO2E,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7CzE,OAAA;gBAAKoE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrE,OAAA;kBACE4C,IAAI,EAAE9B,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCgC,EAAE,EAAC,UAAU;kBACblB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACG,QAAS;kBACzBmE,QAAQ,EAAElD,iBAAkB;kBAC5BmD,WAAW,EAAC,wFAAkB;kBAC9BT,SAAS,EAAC,YAAY;kBACtBU,QAAQ,EAAEpE,OAAQ;kBAClBqE,YAAY,EAAC;gBAAkB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACFzE,OAAA;kBACE4C,IAAI,EAAC,QAAQ;kBACbwB,SAAS,EAAC,iBAAiB;kBAC3BY,OAAO,EAAEA,CAAA,KAAMjE,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CgE,QAAQ,EAAEpE,OAAQ;kBAAA2D,QAAA,EAEjBvD,YAAY,GAAG,IAAI,GAAG;gBAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrE,OAAA;gBAAOoE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACnCrE,OAAA;kBACE4C,IAAI,EAAC,UAAU;kBACfqC,OAAO,EAAEjE,UAAW;kBACpB4D,QAAQ,EAAGjD,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACG,MAAM,CAACmD,OAAO,CAAE;kBACjDH,QAAQ,EAAEpE;gBAAQ;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACFzE,OAAA;kBAAMoE,SAAS,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,wCAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENzE,OAAA;cACE4C,IAAI,EAAC,QAAQ;cACbwB,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEpE,OAAQ;cAAA2D,QAAA,EAEjB3D,OAAO,gBACNV,OAAA,CAAAE,SAAA;gBAAAmE,QAAA,gBACErE,OAAA;kBAAMoE,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,mGAE3C;cAAA,eAAE,CAAC,gBAEHzE,OAAA,CAAAE,SAAA;gBAAAmE,QAAA,gBACErE,OAAA;kBAAMoE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uEAEzC;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEPzE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBrE,OAAA;gBAAAqE,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACNzE,OAAA;cACE4C,IAAI,EAAC,QAAQ;cACboC,OAAO,EAAE3B,eAAgB;cACzBe,SAAS,EAAC,aAAa;cACvBU,QAAQ,EAAEpE,OAAQ;cAAA2D,QAAA,gBAElBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,8EAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrE,OAAA;YAAKoE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrE,OAAA;cAAKoE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrE,OAAA;gBAAMoE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCzE,OAAA;gBAAAqE,QAAA,EAAM;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrE,OAAA;gBAAMoE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCzE,OAAA;gBAAAqE,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrE,OAAA;gBAAMoE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCzE,OAAA;gBAAAqE,QAAA,EAAM;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrE,OAAA;cAAAqE,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7CzE,OAAA;cAAAqE,QAAA,EAAG;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAKoE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrE,OAAA;UAAAqE,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBzE,OAAA;UAAKoE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BrE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCzE,OAAA;cAAKoE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BrE,OAAA;gBAAAqE,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBzE,OAAA;gBAAAqE,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCzE,OAAA;cAAKoE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BrE,OAAA;gBAAAqE,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBzE,OAAA;gBAAAqE,QAAA,EAAG;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCzE,OAAA;cAAKoE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BrE,OAAA;gBAAAqE,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBzE,OAAA;gBAAAqE,QAAA,EAAG;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCzE,OAAA;cAAKoE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BrE,OAAA;gBAAAqE,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBzE,OAAA;gBAAAqE,QAAA,EAAG;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CAzTIF,aAAa;AAAA+E,EAAA,GAAb/E,aAAa;AA2TnB,eAAeA,aAAa;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}