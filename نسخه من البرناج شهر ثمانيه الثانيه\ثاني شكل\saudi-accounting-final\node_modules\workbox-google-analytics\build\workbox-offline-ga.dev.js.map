{"version": 3, "file": "workbox-offline-ga.dev.js", "sources": ["../_version.js", "../utils/constants.js", "../initialize.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:google-analytics:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const QUEUE_NAME = 'workbox-google-analytics';\nexport const MAX_RETENTION_TIME = 60 * 48; // Two days in minutes\nexport const GOOGLE_ANALYTICS_HOST = 'www.google-analytics.com';\nexport const GTM_HOST = 'www.googletagmanager.com';\nexport const ANALYTICS_JS_PATH = '/analytics.js';\nexport const GTAG_JS_PATH = '/gtag/js';\nexport const GTM_JS_PATH = '/gtm.js';\nexport const COLLECT_DEFAULT_PATH = '/collect';\n// This RegExp matches all known Measurement Protocol single-hit collect\n// endpoints. Most of the time the default path (/collect) is used, but\n// occasionally an experimental endpoint is used when testing new features,\n// (e.g. /r/collect or /j/collect)\nexport const COLLECT_PATHS_REGEX = /^\\/(\\w+\\/)?collect/;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { BackgroundSyncPlugin } from 'workbox-background-sync/BackgroundSyncPlugin.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { Router } from 'workbox-routing/Router.js';\nimport { NetworkFirst } from 'workbox-strategies/NetworkFirst.js';\nimport { NetworkOnly } from 'workbox-strategies/NetworkOnly.js';\nimport { QUEUE_NAME, MAX_RETENTION_TIME, GOOGLE_ANALYTICS_HOST, GTM_HOST, ANALYTICS_JS_PATH, GTAG_JS_PATH, GTM_JS_PATH, COLLECT_PATHS_REGEX, } from './utils/constants.js';\nimport './_version.js';\n/**\n * Creates the requestWillDequeue callback to be used with the background\n * sync plugin. The callback takes the failed request and adds the\n * `qt` param based on the current time, as well as applies any other\n * user-defined hit modifications.\n *\n * @param {Object} config See {@link workbox-google-analytics.initialize}.\n * @return {Function} The requestWillDequeue callback function.\n *\n * @private\n */\nconst createOnSyncCallback = (config) => {\n    return async ({ queue }) => {\n        let entry;\n        while ((entry = await queue.shiftRequest())) {\n            const { request, timestamp } = entry;\n            const url = new URL(request.url);\n            try {\n                // Measurement protocol requests can set their payload parameters in\n                // either the URL query string (for GET requests) or the POST body.\n                const params = request.method === 'POST'\n                    ? new URLSearchParams(await request.clone().text())\n                    : url.searchParams;\n                // Calculate the qt param, accounting for the fact that an existing\n                // qt param may be present and should be updated rather than replaced.\n                const originalHitTime = timestamp - (Number(params.get('qt')) || 0);\n                const queueTime = Date.now() - originalHitTime;\n                // Set the qt param prior to applying hitFilter or parameterOverrides.\n                params.set('qt', String(queueTime));\n                // Apply `parameterOverrides`, if set.\n                if (config.parameterOverrides) {\n                    for (const param of Object.keys(config.parameterOverrides)) {\n                        const value = config.parameterOverrides[param];\n                        params.set(param, value);\n                    }\n                }\n                // Apply `hitFilter`, if set.\n                if (typeof config.hitFilter === 'function') {\n                    config.hitFilter.call(null, params);\n                }\n                // Retry the fetch. Ignore URL search params from the URL as they're\n                // now in the post body.\n                await fetch(new Request(url.origin + url.pathname, {\n                    body: params.toString(),\n                    method: 'POST',\n                    mode: 'cors',\n                    credentials: 'omit',\n                    headers: { 'Content-Type': 'text/plain' },\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(url.href)}' ` + `has been replayed`);\n                }\n            }\n            catch (err) {\n                await queue.unshiftRequest(entry);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(url.href)}' ` +\n                        `failed to replay, putting it back in the queue.`);\n                }\n                throw err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`All Google Analytics request successfully replayed; ` +\n                `the queue is now empty!`);\n        }\n    };\n};\n/**\n * Creates GET and POST routes to catch failed Measurement Protocol hits.\n *\n * @param {BackgroundSyncPlugin} bgSyncPlugin\n * @return {Array<Route>} The created routes.\n *\n * @private\n */\nconst createCollectRoutes = (bgSyncPlugin) => {\n    const match = ({ url }) => url.hostname === GOOGLE_ANALYTICS_HOST &&\n        COLLECT_PATHS_REGEX.test(url.pathname);\n    const handler = new NetworkOnly({\n        plugins: [bgSyncPlugin],\n    });\n    return [new Route(match, handler, 'GET'), new Route(match, handler, 'POST')];\n};\n/**\n * Creates a route with a network first strategy for the analytics.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createAnalyticsJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GOOGLE_ANALYTICS_HOST &&\n        url.pathname === ANALYTICS_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * Creates a route with a network first strategy for the gtag.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createGtagJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GTM_HOST && url.pathname === GTAG_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * Creates a route with a network first strategy for the gtm.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createGtmJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GTM_HOST && url.pathname === GTM_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * @param {Object=} [options]\n * @param {Object} [options.cacheName] The cache name to store and retrieve\n *     analytics.js. Defaults to the cache names provided by `workbox-core`.\n * @param {Object} [options.parameterOverrides]\n *     [Measurement Protocol parameters](https://developers.google.com/analytics/devguides/collection/protocol/v1/parameters),\n *     expressed as key/value pairs, to be added to replayed Google Analytics\n *     requests. This can be used to, e.g., set a custom dimension indicating\n *     that the request was replayed.\n * @param {Function} [options.hitFilter] A function that allows you to modify\n *     the hit parameters prior to replaying\n *     the hit. The function is invoked with the original hit's URLSearchParams\n *     object as its only argument.\n *\n * @memberof workbox-google-analytics\n */\nconst initialize = (options = {}) => {\n    const cacheName = cacheNames.getGoogleAnalyticsName(options.cacheName);\n    const bgSyncPlugin = new BackgroundSyncPlugin(QUEUE_NAME, {\n        maxRetentionTime: MAX_RETENTION_TIME,\n        onSync: createOnSyncCallback(options),\n    });\n    const routes = [\n        createGtmJsRoute(cacheName),\n        createAnalyticsJsRoute(cacheName),\n        createGtagJsRoute(cacheName),\n        ...createCollectRoutes(bgSyncPlugin),\n    ];\n    const router = new Router();\n    for (const route of routes) {\n        router.registerRoute(route);\n    }\n    router.addFetchListener();\n};\nexport { initialize };\n"], "names": ["self", "_", "e", "QUEUE_NAME", "MAX_RETENTION_TIME", "GOOGLE_ANALYTICS_HOST", "GTM_HOST", "ANALYTICS_JS_PATH", "GTAG_JS_PATH", "GTM_JS_PATH", "COLLECT_PATHS_REGEX", "createOnSyncCallback", "config", "queue", "entry", "shiftRequest", "request", "timestamp", "url", "URL", "params", "method", "URLSearchParams", "clone", "text", "searchParams", "originalHitTime", "Number", "get", "queueTime", "Date", "now", "set", "String", "parameterOverrides", "param", "Object", "keys", "value", "hitFilter", "call", "fetch", "Request", "origin", "pathname", "body", "toString", "mode", "credentials", "headers", "process", "logger", "log", "getFriendlyURL", "href", "err", "unshiftRequest", "createCollectRoutes", "bgSyncPlugin", "match", "hostname", "test", "handler", "NetworkOnly", "plugins", "Route", "createAnalyticsJsRoute", "cacheName", "NetworkFirst", "createGtagJsRoute", "createGtmJsRoute", "initialize", "options", "cacheNames", "getGoogleAnalyticsName", "BackgroundSyncPlugin", "maxRetentionTime", "onSync", "routes", "router", "Router", "route", "registerRoute", "addFetchListener"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,gCAAD,CAAJ,IAA0CC,CAAC,EAA3C;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMC,UAAU,GAAG,0BAAnB;IACA,MAAMC,kBAAkB,GAAG,KAAK,EAAhC;;IACA,MAAMC,qBAAqB,GAAG,0BAA9B;IACA,MAAMC,QAAQ,GAAG,0BAAjB;IACA,MAAMC,iBAAiB,GAAG,eAA1B;IACA,MAAMC,YAAY,GAAG,UAArB;IACA,MAAMC,WAAW,GAAG,SAApB;IAGP;IACA;IACA;;IACO,MAAMC,mBAAmB,GAAG,oBAA5B;;ICpBP;IACA;AACA;IACA;IACA;IACA;IACA;IAWA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMC,oBAAoB,GAAIC,MAAD,IAAY;IACrC,SAAO,OAAO;IAAEC,IAAAA;IAAF,GAAP,KAAqB;IACxB,QAAIC,KAAJ;;IACA,WAAQA,KAAK,GAAG,MAAMD,KAAK,CAACE,YAAN,EAAtB,EAA6C;IACzC,YAAM;IAAEC,QAAAA,OAAF;IAAWC,QAAAA;IAAX,UAAyBH,KAA/B;IACA,YAAMI,GAAG,GAAG,IAAIC,GAAJ,CAAQH,OAAO,CAACE,GAAhB,CAAZ;;IACA,UAAI;IACA;IACA;IACA,cAAME,MAAM,GAAGJ,OAAO,CAACK,MAAR,KAAmB,MAAnB,GACT,IAAIC,eAAJ,CAAoB,MAAMN,OAAO,CAACO,KAAR,GAAgBC,IAAhB,EAA1B,CADS,GAETN,GAAG,CAACO,YAFV,CAHA;IAOA;;IACA,cAAMC,eAAe,GAAGT,SAAS,IAAIU,MAAM,CAACP,MAAM,CAACQ,GAAP,CAAW,IAAX,CAAD,CAAN,IAA4B,CAAhC,CAAjC;IACA,cAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,KAAaL,eAA/B,CATA;;IAWAN,QAAAA,MAAM,CAACY,GAAP,CAAW,IAAX,EAAiBC,MAAM,CAACJ,SAAD,CAAvB,EAXA;;IAaA,YAAIjB,MAAM,CAACsB,kBAAX,EAA+B;IAC3B,eAAK,MAAMC,KAAX,IAAoBC,MAAM,CAACC,IAAP,CAAYzB,MAAM,CAACsB,kBAAnB,CAApB,EAA4D;IACxD,kBAAMI,KAAK,GAAG1B,MAAM,CAACsB,kBAAP,CAA0BC,KAA1B,CAAd;IACAf,YAAAA,MAAM,CAACY,GAAP,CAAWG,KAAX,EAAkBG,KAAlB;IACH;IACJ,SAlBD;;;IAoBA,YAAI,OAAO1B,MAAM,CAAC2B,SAAd,KAA4B,UAAhC,EAA4C;IACxC3B,UAAAA,MAAM,CAAC2B,SAAP,CAAiBC,IAAjB,CAAsB,IAAtB,EAA4BpB,MAA5B;IACH,SAtBD;IAwBA;;;IACA,cAAMqB,KAAK,CAAC,IAAIC,OAAJ,CAAYxB,GAAG,CAACyB,MAAJ,GAAazB,GAAG,CAAC0B,QAA7B,EAAuC;IAC/CC,UAAAA,IAAI,EAAEzB,MAAM,CAAC0B,QAAP,EADyC;IAE/CzB,UAAAA,MAAM,EAAE,MAFuC;IAG/C0B,UAAAA,IAAI,EAAE,MAHyC;IAI/CC,UAAAA,WAAW,EAAE,MAJkC;IAK/CC,UAAAA,OAAO,EAAE;IAAE,4BAAgB;IAAlB;IALsC,SAAvC,CAAD,CAAX;;IAOA,YAAIC,KAAA,KAAyB,YAA7B,EAA2C;IACvCC,UAAAA,gBAAM,CAACC,GAAP,CAAY,gBAAeC,gCAAc,CAACnC,GAAG,CAACoC,IAAL,CAAW,IAAzC,GAAgD,mBAA3D;IACH;IACJ,OAnCD,CAoCA,OAAOC,GAAP,EAAY;IACR,cAAM1C,KAAK,CAAC2C,cAAN,CAAqB1C,KAArB,CAAN;;IACA,QAA2C;IACvCqC,UAAAA,gBAAM,CAACC,GAAP,CAAY,gBAAeC,gCAAc,CAACnC,GAAG,CAACoC,IAAL,CAAW,IAAzC,GACN,iDADL;IAEH;;IACD,cAAMC,GAAN;IACH;IACJ;;IACD,IAA2C;IACvCJ,MAAAA,gBAAM,CAACC,GAAP,CAAY,sDAAD,GACN,yBADL;IAEH;IACJ,GAtDD;IAuDH,CAxDD;IAyDA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMK,mBAAmB,GAAIC,YAAD,IAAkB;IAC1C,QAAMC,KAAK,GAAG,CAAC;IAAEzC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAAC0C,QAAJ,KAAiBvD,qBAAjB,IACvBK,mBAAmB,CAACmD,IAApB,CAAyB3C,GAAG,CAAC0B,QAA7B,CADJ;;IAEA,QAAMkB,OAAO,GAAG,IAAIC,0BAAJ,CAAgB;IAC5BC,IAAAA,OAAO,EAAE,CAACN,YAAD;IADmB,GAAhB,CAAhB;IAGA,SAAO,CAAC,IAAIO,cAAJ,CAAUN,KAAV,EAAiBG,OAAjB,EAA0B,KAA1B,CAAD,EAAmC,IAAIG,cAAJ,CAAUN,KAAV,EAAiBG,OAAjB,EAA0B,MAA1B,CAAnC,CAAP;IACH,CAPD;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMI,sBAAsB,GAAIC,SAAD,IAAe;IAC1C,QAAMR,KAAK,GAAG,CAAC;IAAEzC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAAC0C,QAAJ,KAAiBvD,qBAAjB,IACvBa,GAAG,CAAC0B,QAAJ,KAAiBrC,iBADrB;;IAEA,QAAMuD,OAAO,GAAG,IAAIM,4BAAJ,CAAiB;IAAED,IAAAA;IAAF,GAAjB,CAAhB;IACA,SAAO,IAAIF,cAAJ,CAAUN,KAAV,EAAiBG,OAAjB,EAA0B,KAA1B,CAAP;IACH,CALD;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMO,iBAAiB,GAAIF,SAAD,IAAe;IACrC,QAAMR,KAAK,GAAG,CAAC;IAAEzC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAAC0C,QAAJ,KAAiBtD,QAAjB,IAA6BY,GAAG,CAAC0B,QAAJ,KAAiBpC,YAAzE;;IACA,QAAMsD,OAAO,GAAG,IAAIM,4BAAJ,CAAiB;IAAED,IAAAA;IAAF,GAAjB,CAAhB;IACA,SAAO,IAAIF,cAAJ,CAAUN,KAAV,EAAiBG,OAAjB,EAA0B,KAA1B,CAAP;IACH,CAJD;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMQ,gBAAgB,GAAIH,SAAD,IAAe;IACpC,QAAMR,KAAK,GAAG,CAAC;IAAEzC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAAC0C,QAAJ,KAAiBtD,QAAjB,IAA6BY,GAAG,CAAC0B,QAAJ,KAAiBnC,WAAzE;;IACA,QAAMqD,OAAO,GAAG,IAAIM,4BAAJ,CAAiB;IAAED,IAAAA;IAAF,GAAjB,CAAhB;IACA,SAAO,IAAIF,cAAJ,CAAUN,KAAV,EAAiBG,OAAjB,EAA0B,KAA1B,CAAP;IACH,CAJD;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;UACMS,UAAU,GAAG,CAACC,OAAO,GAAG,EAAX,KAAkB;IACjC,QAAML,SAAS,GAAGM,wBAAU,CAACC,sBAAX,CAAkCF,OAAO,CAACL,SAA1C,CAAlB;IACA,QAAMT,YAAY,GAAG,IAAIiB,4CAAJ,CAAyBxE,UAAzB,EAAqC;IACtDyE,IAAAA,gBAAgB,EAAExE,kBADoC;IAEtDyE,IAAAA,MAAM,EAAElE,oBAAoB,CAAC6D,OAAD;IAF0B,GAArC,CAArB;IAIA,QAAMM,MAAM,GAAG,CACXR,gBAAgB,CAACH,SAAD,CADL,EAEXD,sBAAsB,CAACC,SAAD,CAFX,EAGXE,iBAAiB,CAACF,SAAD,CAHN,EAIX,GAAGV,mBAAmB,CAACC,YAAD,CAJX,CAAf;IAMA,QAAMqB,MAAM,GAAG,IAAIC,gBAAJ,EAAf;;IACA,OAAK,MAAMC,KAAX,IAAoBH,MAApB,EAA4B;IACxBC,IAAAA,MAAM,CAACG,aAAP,CAAqBD,KAArB;IACH;;IACDF,EAAAA,MAAM,CAACI,gBAAP;IACH;;;;;;;;;;"}