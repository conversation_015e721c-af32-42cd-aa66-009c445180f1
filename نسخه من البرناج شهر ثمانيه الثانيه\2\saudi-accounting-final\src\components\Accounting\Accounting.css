.accounting-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.accounting-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.header-content h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.back-btn, .add-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.add-btn {
  background: #4CAF50;
  color: white;
}

.add-btn.secondary {
  background: #FF9800;
}

.add-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.add-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.accounting-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card.asset {
  border-left: 4px solid #4CAF50;
}

.stat-card.liability {
  border-left: 4px solid #f44336;
}

.stat-card.equity {
  border-left: 4px solid #2196F3;
}

.stat-card.revenue {
  border-left: 4px solid #FF9800;
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.stat-info p {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.accounting-tabs {
  background: white;
  border-radius: 15px;
  padding: 10px;
  margin-bottom: 30px;
  display: flex;
  gap: 5px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 10px;
  background: transparent;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.tab-btn.active {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.tab-content {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  min-height: 500px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.accounts-count, .entries-count, .trial-date {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.accounts-table-container, .trial-balance-table {
  overflow-x: auto;
}

.accounts-table, .trial-balance-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.accounts-table th, .trial-balance-table th {
  background: #f8f9fa;
  color: #333;
  padding: 15px;
  text-align: right;
  font-weight: 600;
  border-bottom: 2px solid #e0e0e0;
}

.accounts-table td, .trial-balance-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.accounts-table tr:hover, .trial-balance-table tr:hover {
  background: #f8f9fa;
}

.account-type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 11px;
  font-weight: 600;
}

.positive {
  color: #4CAF50;
  font-weight: 600;
}

.negative {
  color: #f44336;
  font-weight: 600;
}

.journal-entries {
  max-height: 600px;
  overflow-y: auto;
}

.journal-entry {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  margin-bottom: 20px;
  overflow: hidden;
}

.entry-header {
  background: #f8f9fa;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.entry-info strong {
  color: #333;
  margin-left: 15px;
}

.entry-date {
  color: #666;
  font-size: 14px;
}

.entry-reference {
  color: #666;
  font-size: 14px;
}

.entry-description {
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  font-style: italic;
  color: #555;
}

.entry-lines table {
  width: 100%;
  border-collapse: collapse;
}

.entry-lines th {
  background: #f8f9fa;
  padding: 10px 15px;
  text-align: right;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.entry-lines td {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.debit {
  color: #d32f2f;
  font-weight: 600;
  text-align: center;
}

.credit {
  color: #388e3c;
  font-weight: 600;
  text-align: center;
}

.ledger-accounts {
  max-height: 600px;
  overflow-y: auto;
}

.ledger-account {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.account-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.account-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.account-type {
  background: #2196F3;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.ledger-table {
  width: 100%;
  border-collapse: collapse;
}

.ledger-table th {
  background: #f8f9fa;
  padding: 12px 15px;
  text-align: right;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.ledger-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.opening-balance {
  background: #fff3e0;
  font-weight: 600;
}

.coming-soon {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.coming-soon h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
  color: #999;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 18px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

.modal.large {
  max-width: 1200px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 30px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196F3;
  background: white;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.entry-lines-section {
  margin-top: 30px;
}

.entry-lines-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.entry-lines-table {
  overflow-x: auto;
  margin-bottom: 20px;
}

.entry-lines-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.entry-lines-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: right;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.entry-lines-table td {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.entry-lines-table input,
.entry-lines-table select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.remove-line-btn {
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 12px;
}

.remove-line-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-line-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 20px;
}

.totals-row {
  background: #f8f9fa;
  font-weight: 600;
}

.total-debit, .total-credit {
  text-align: center;
  font-weight: 700;
}

.balance-warning {
  background: #fff3cd;
  color: #856404;
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid #ffeaa7;
  margin-top: 10px;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: #f8f9fa;
  border-radius: 0 0 20px 20px;
}

.cancel-btn, .save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn {
  background: #2196F3;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #1976D2;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .accounting-container {
    padding: 15px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .accounting-stats {
    grid-template-columns: 1fr;
  }
  
  .accounting-tabs {
    flex-direction: column;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .modal {
    width: 95%;
    margin: 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .entry-lines-table {
    font-size: 12px;
  }
}
