{"ast": null, "code": "var _jsxFileName = \"D:\\\\aronim\\\\saudi-accounting-final\\\\src\\\\components\\\\POS\\\\POS.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './POS.css';\nimport database from '../../utils/database';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst POS = ({\n  user,\n  onBack\n}) => {\n  _s();\n  const [cart, setCart] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [customer, setCustomer] = useState({\n    name: '',\n    phone: '',\n    taxNumber: ''\n  });\n  const [showCustomerForm, setShowCustomerForm] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState('cash');\n  const [discount, setDiscount] = useState(0);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [message, setMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [cashReceived, setCashReceived] = useState('');\n  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');\n  const searchInputRef = useRef(null);\n\n  // تحميل المنتجات من قاعدة البيانات\n  useEffect(() => {\n    try {\n      const dbProducts = database.getProducts();\n      setProducts(dbProducts);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      // في حالة الخطأ، استخدم منتجات افتراضية\n      const fallbackProducts = [{\n        id: 1,\n        name: 'لابتوب HP',\n        price: 2500,\n        stock: 10,\n        barcode: '123456789',\n        category: 'إلكترونيات'\n      }, {\n        id: 2,\n        name: 'ماوس لاسلكي',\n        price: 85,\n        stock: 25,\n        barcode: '987654321',\n        category: 'إلكترونيات'\n      }];\n      setProducts(fallbackProducts);\n    }\n  }, []);\n\n  // إضافة منتج للسلة\n  const addToCart = product => {\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity < product.stock) {\n        setCart(cart.map(item => item.id === product.id ? {\n          ...item,\n          quantity: item.quantity + 1\n        } : item));\n      } else {\n        alert('⚠️ لا توجد كمية كافية في المخزون');\n      }\n    } else {\n      setCart([...cart, {\n        ...product,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // تحديث كمية المنتج\n  const updateQuantity = (id, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(id);\n      return;\n    }\n    const product = products.find(p => p.id === id);\n    if (newQuantity > product.stock) {\n      alert('⚠️ لا توجد كمية كافية في المخزون');\n      return;\n    }\n    setCart(cart.map(item => item.id === id ? {\n      ...item,\n      quantity: newQuantity\n    } : item));\n  };\n\n  // حذف منتج من السلة\n  const removeFromCart = id => {\n    setCart(cart.filter(item => item.id !== id));\n  };\n\n  // حساب الإجماليات\n  const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);\n  const vatRate = 0.15; // 15% ضريبة القيمة المضافة\n  const vatAmount = subtotal * vatRate;\n  const total = subtotal + vatAmount;\n\n  // تصفية المنتجات حسب البحث\n  const filteredProducts = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.barcode.includes(searchTerm));\n\n  // معالجة الدفع\n  const processPayment = async () => {\n    if (cart.length === 0) {\n      alert('⚠️ السلة فارغة');\n      return;\n    }\n    setIsProcessing(true);\n    try {\n      // محاكاة معالجة الدفع\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // إنشاء بيانات الفاتورة\n      const invoiceData = {\n        customer: customer.name || 'عميل نقدي',\n        customerPhone: customer.phone || '',\n        customerTaxNumber: customer.taxNumber || '',\n        items: cart,\n        subtotal: subtotal,\n        vat: vatAmount,\n        total: total,\n        cashier: user.name,\n        paymentMethod: paymentMethod === 'cash' ? 'نقدي' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'تحويل بنكي'\n      };\n\n      // حفظ الفاتورة في قاعدة البيانات\n      const savedInvoice = database.saveInvoice(invoiceData);\n\n      // إضافة التاريخ المنسق للطباعة\n      const invoiceForPrint = {\n        ...savedInvoice,\n        dateArabic: new Date(savedInvoice.createdAt).toLocaleString('ar-SA', {\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit'\n        })\n      };\n\n      // طباعة الفاتورة\n      printInvoice(invoiceForPrint);\n\n      // تحديث المنتجات لعكس المخزون الجديد\n      const updatedProducts = database.getProducts();\n      setProducts(updatedProducts);\n\n      // تفريغ السلة\n      setCart([]);\n      setCustomer({\n        name: '',\n        phone: '',\n        taxNumber: ''\n      });\n      setShowCustomerForm(false);\n      setIsProcessing(false);\n      alert(`✅ تم إتمام البيع بنجاح!\\nرقم الفاتورة: ${savedInvoice.id}`);\n    } catch (error) {\n      console.error('خطأ في معالجة البيع:', error);\n      setIsProcessing(false);\n      alert('❌ حدث خطأ في معالجة البيع. يرجى المحاولة مرة أخرى.');\n    }\n  };\n\n  // إنشاء QR Code للفاتورة (حسب المعايير السعودية)\n  const generateQRCode = invoice => {\n    // الرقم الضريبي السعودي (15 رقم)\n    const vatNumber = \"***************\";\n\n    // تنسيق التاريخ حسب المعايير السعودية (ISO 8601)\n    const invoiceDate = new Date();\n    const formattedDate = invoiceDate.toISOString();\n\n    // البيانات المطلوبة للـ QR Code السعودي (بالإنجليزية لتجنب مشاكل التشفير)\n    const qrData = {\n      // 1. اسم البائع\n      sellerName: \"Saudi Accounting System Co. Ltd\",\n      // 2. الرقم الضريبي للبائع (15 رقم)\n      vatRegistrationNumber: vatNumber,\n      // 3. الطابع الزمني للفاتورة (ISO 8601)\n      invoiceTimestamp: formattedDate,\n      // 4. إجمالي الفاتورة شامل الضريبة\n      invoiceTotal: parseFloat(invoice.total.toFixed(2)),\n      // 5. إجمالي ضريبة القيمة المضافة\n      vatTotal: parseFloat(invoice.vat.toFixed(2))\n    };\n\n    // تحويل البيانات إلى تنسيق TLV (Tag-Length-Value) حسب المعايير السعودية\n    const createTLV = (tag, value) => {\n      const valueStr = value.toString();\n      const length = valueStr.length;\n      return String.fromCharCode(tag) + String.fromCharCode(length) + valueStr;\n    };\n\n    // إنشاء QR Code حسب المعايير السعودية (بالإنجليزية)\n    const qrString = createTLV(1, qrData.sellerName) + createTLV(2, qrData.vatRegistrationNumber) + createTLV(3, qrData.invoiceTimestamp) + createTLV(4, qrData.invoiceTotal) + createTLV(5, qrData.vatTotal);\n\n    // تحويل إلى Base64 بطريقة آمنة\n    let base64QR;\n    try {\n      // تحويل النص إلى UTF-8 bytes ثم إلى Base64\n      const utf8Bytes = new TextEncoder().encode(qrString);\n      const binaryString = Array.from(utf8Bytes, byte => String.fromCharCode(byte)).join('');\n      base64QR = btoa(binaryString);\n    } catch (error) {\n      // في حالة فشل التشفير، استخدم نص بسيط\n      const simpleData = `Company:${qrData.sellerName}|VAT:${qrData.vatRegistrationNumber}|Date:${qrData.invoiceTimestamp}|Total:${qrData.invoiceTotal}|VATAmount:${qrData.vatTotal}`;\n      base64QR = btoa(simpleData);\n    }\n\n    // إنشاء QR Code باستخدام خدمة مجانية\n    return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(base64QR)}`;\n  };\n\n  // طباعة الفاتورة المحسنة\n  const printInvoice = invoice => {\n    const qrCodeUrl = generateQRCode(invoice);\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>فاتورة ضريبية رقم ${invoice.id}</title>\n          <style>\n            body {\n              font-family: 'Arial', sans-serif;\n              margin: 20px;\n              line-height: 1.6;\n              color: #333;\n            }\n            .header {\n              text-align: center;\n              border-bottom: 3px solid #1890ff;\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 {\n              color: #1890ff;\n              margin: 0;\n              font-size: 1.8em;\n            }\n            .header h2 {\n              color: #666;\n              margin: 5px 0;\n              font-size: 1.2em;\n            }\n            .company-info {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              margin-bottom: 20px;\n              border-right: 4px solid #1890ff;\n            }\n            .invoice-details {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 20px;\n              margin: 20px 0;\n            }\n            .invoice-details div {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n            }\n            .invoice-details p {\n              margin: 8px 0;\n              font-size: 1em;\n            }\n            .items-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 20px 0;\n              box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            }\n            .items-table th {\n              background: linear-gradient(135deg, #1890ff, #40a9ff);\n              color: white;\n              padding: 12px 8px;\n              text-align: right;\n              font-weight: 600;\n            }\n            .items-table td {\n              border: 1px solid #e8e8e8;\n              padding: 10px 8px;\n              text-align: right;\n            }\n            .items-table tbody tr:nth-child(even) {\n              background-color: #f9f9f9;\n            }\n            .totals {\n              background: #f8f9fa;\n              padding: 20px;\n              border-radius: 8px;\n              margin-top: 20px;\n              border-right: 4px solid #52c41a;\n            }\n            .total-line {\n              display: flex;\n              justify-content: space-between;\n              margin: 8px 0;\n              font-size: 1.1em;\n            }\n            .vat-line {\n              color: #fa8c16;\n              font-weight: 600;\n            }\n            .final-total {\n              font-weight: bold;\n              font-size: 1.3em;\n              border-top: 2px solid #1890ff;\n              padding-top: 10px;\n              margin-top: 10px;\n              color: #1890ff;\n            }\n            .footer {\n              margin-top: 30px;\n              display: grid;\n              grid-template-columns: 1fr auto;\n              gap: 20px;\n              align-items: center;\n            }\n            .footer-text {\n              text-align: right;\n              color: #666;\n            }\n            .qr-section {\n              text-align: center;\n              border: 2px solid #e8e8e8;\n              padding: 15px;\n              border-radius: 8px;\n              background: white;\n            }\n            .qr-section h4 {\n              margin: 0 0 10px 0;\n              color: #1890ff;\n              font-size: 0.9em;\n            }\n            .tax-info {\n              background: #e6f7ff;\n              border: 1px solid #91d5ff;\n              padding: 15px;\n              border-radius: 8px;\n              margin: 20px 0;\n            }\n            .tax-info h3 {\n              color: #1890ff;\n              margin: 0 0 10px 0;\n              font-size: 1.1em;\n            }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>فاتورة ضريبية مبسطة</h2>\n          </div>\n\n          <div class=\"company-info\">\n            <h3 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات الشركة</h3>\n            <p><strong>اسم الشركة:</strong> شركة نظام المحاسبة السعودي المحدودة</p>\n            <p><strong>الرقم الضريبي:</strong> ***************</p>\n            <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>\n            <p><strong>الهاتف:</strong> +966 11 123 4567</p>\n            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>\n          </div>\n\n          <div class=\"invoice-details\">\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">تفاصيل الفاتورة</h4>\n              <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>\n              <p><strong>تاريخ الإصدار:</strong> ${invoice.dateArabic}</p>\n              <p><strong>نوع الفاتورة:</strong> فاتورة ضريبية مبسطة</p>\n              <p><strong>حالة الفاتورة:</strong> مدفوعة</p>\n            </div>\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات العملية</h4>\n              <p><strong>العميل:</strong> ${invoice.customer}</p>\n              <p><strong>أمين الصندوق:</strong> ${invoice.cashier}</p>\n              <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod}</p>\n              <p><strong>رقم المرجع:</strong> ${invoice.id}-PAY</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th style=\"width: 40%\">وصف السلعة/الخدمة</th>\n                <th style=\"width: 15%\">السعر الوحدة</th>\n                <th style=\"width: 10%\">الكمية</th>\n                <th style=\"width: 15%\">الإجمالي قبل الضريبة</th>\n                <th style=\"width: 10%\">معدل الضريبة</th>\n                <th style=\"width: 10%\">مبلغ الضريبة</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${invoice.items.map(item => {\n      const itemSubtotal = item.price * item.quantity;\n      const itemVat = itemSubtotal * 0.15;\n      return `\n                  <tr>\n                    <td>${item.name}</td>\n                    <td>${item.price.toFixed(2)} ريال</td>\n                    <td>${item.quantity}</td>\n                    <td>${itemSubtotal.toFixed(2)} ريال</td>\n                    <td>15%</td>\n                    <td>${itemVat.toFixed(2)} ريال</td>\n                  </tr>\n                `;\n    }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"tax-info\">\n            <h3>📊 ملخص الضرائب</h3>\n            <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px;\">\n              <div>\n                <p><strong>إجمالي السلع والخدمات الخاضعة للضريبة:</strong></p>\n                <p style=\"color: #1890ff; font-size: 1.1em;\">${invoice.subtotal.toFixed(2)} ريال</p>\n              </div>\n              <div>\n                <p><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></p>\n                <p style=\"color: #fa8c16; font-size: 1.1em;\">${invoice.vat.toFixed(2)} ريال</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"totals\">\n            <div class=\"total-line\">\n              <span>المجموع الفرعي (قبل الضريبة):</span>\n              <span>${invoice.subtotal.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line vat-line\">\n              <span>ضريبة القيمة المضافة (15%):</span>\n              <span>${invoice.vat.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line final-total\">\n              <span>الإجمالي النهائي (شامل الضريبة):</span>\n              <span>${invoice.total.toFixed(2)} ريال</span>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <div class=\"footer-text\">\n              <h4 style=\"color: #1890ff; margin: 0 0 10px 0;\">شكراً لتعاملكم معنا</h4>\n              <p style=\"margin: 5px 0;\">هذه فاتورة ضريبية صادرة إلكترونياً</p>\n              <p style=\"margin: 5px 0;\">تم إنشاؤها بواسطة نظام المحاسبة السعودي المعتمد</p>\n              <p style=\"margin: 5px 0; font-size: 0.9em;\">للاستفسارات: <EMAIL></p>\n            </div>\n            <div class=\"qr-section\">\n              <h4>🔍 رمز الاستجابة السريعة</h4>\n              <img src=\"${qrCodeUrl}\" alt=\"QR Code\" style=\"max-width: 120px; height: auto;\">\n              <p style=\"font-size: 0.8em; margin: 5px 0 0 0; color: #666;\">امسح للتحقق من الفاتورة</p>\n            </div>\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 0.85em; color: #666;\">\n            <p style=\"margin: 0;\">هذا المستند تم إنشاؤه إلكترونياً ولا يتطلب توقيع أو ختم</p>\n            <p style=\"margin: 5px 0 0 0;\">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pos-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"pos-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: \"\\u21A9\\uFE0F \\u0631\\u062C\\u0648\\u0639 \\u0644\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDED2 \\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [user.name, \" - \", user.role]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pos-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0645\\u0646\\u062A\\u062C \\u0623\\u0648 \\u0628\\u0627\\u0631\\u0643\\u0648\\u062F...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-category\",\n                children: product.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-price\",\n                children: [product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-stock\",\n                children: [\"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646: \", product.stock]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => addToCart(product),\n              className: \"add-to-cart-btn\",\n              disabled: product.stock === 0,\n              children: product.stock === 0 ? 'نفد المخزون' : 'إضافة للسلة'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDED2 \\u0627\\u0644\\u0633\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCart([]),\n            className: \"clear-cart-btn\",\n            children: \"\\uD83D\\uDDD1\\uFE0F \\u062A\\u0641\\u0631\\u064A\\u063A \\u0627\\u0644\\u0633\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items\",\n          children: cart.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"empty-cart\",\n            children: \"\\u0627\\u0644\\u0633\\u0644\\u0629 \\u0641\\u0627\\u0631\\u063A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this) : cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [item.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quantity-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateQuantity(item.id, item.quantity - 1),\n                className: \"qty-btn\",\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"quantity\",\n                children: item.quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => updateQuantity(item.id, item.quantity + 1),\n                className: \"qty-btn\",\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-total\",\n              children: [(item.price * item.quantity).toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => removeFromCart(item.id),\n              className: \"remove-btn\",\n              children: \"\\u274C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-totals\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [subtotal.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [vatAmount.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"total-line final-total\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [total.toFixed(2), \" \\u0631\\u064A\\u0627\\u0644\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"section-subtitle\",\n              children: \"\\uD83D\\uDCB3 \\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-methods\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: `payment-option ${paymentMethod === 'cash' ? 'selected' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"paymentMethod\",\n                  value: \"cash\",\n                  checked: paymentMethod === 'cash',\n                  onChange: e => setPaymentMethod(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-icon\",\n                  children: \"\\uD83D\\uDCB5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0646\\u0642\\u062F\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: `payment-option ${paymentMethod === 'card' ? 'selected' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"paymentMethod\",\n                  value: \"card\",\n                  checked: paymentMethod === 'card',\n                  onChange: e => setPaymentMethod(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-icon\",\n                  children: \"\\uD83D\\uDCB3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: `payment-option ${paymentMethod === 'transfer' ? 'selected' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"paymentMethod\",\n                  value: \"transfer\",\n                  checked: paymentMethod === 'transfer',\n                  onChange: e => setPaymentMethod(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-icon\",\n                  children: \"\\uD83C\\uDFE6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"customer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCustomerForm(!showCustomerForm),\n              className: \"customer-btn\",\n              children: [\"\\uD83D\\uDC64 \", showCustomerForm ? 'إخفاء' : 'إضافة', \" \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), showCustomerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"customer-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\",\n                value: customer.name,\n                onChange: e => setCustomer({\n                  ...customer,\n                  name: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\",\n                value: customer.phone,\n                onChange: e => setCustomer({\n                  ...customer,\n                  phone: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: customer.taxNumber,\n                onChange: e => setCustomer({\n                  ...customer,\n                  taxNumber: e.target.value\n                }),\n                className: \"customer-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: processPayment,\n            disabled: isProcessing,\n            className: `checkout-btn ${isProcessing ? 'processing' : ''}`,\n            children: isProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 21\n              }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629...\"]\n            }, void 0, true) : '💳 إتمام البيع وطباعة الفاتورة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 470,\n    columnNumber: 5\n  }, this);\n};\n_s(POS, \"vyb6Y0DtdSxDESGQA4GKooIjKN0=\");\n_c = POS;\nexport default POS;\nvar _c;\n$RefreshReg$(_c, \"POS\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "database", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "POS", "user", "onBack", "_s", "cart", "setCart", "products", "setProducts", "customers", "setCustomers", "searchTerm", "setSearchTerm", "selectedCustomer", "setSelectedCustomer", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "customer", "setCustomer", "name", "phone", "taxNumber", "showCustomerForm", "setShowCustomerForm", "paymentMethod", "setPaymentMethod", "discount", "setDiscount", "isProcessing", "setIsProcessing", "message", "setMessage", "showPaymentModal", "setShowPaymentModal", "cashReceived", "setCashReceived", "currentInvoiceNumber", "setCurrentInvoiceNumber", "searchInputRef", "dbProducts", "getProducts", "error", "console", "fallbackProducts", "id", "price", "stock", "barcode", "category", "addToCart", "product", "existingItem", "find", "item", "quantity", "map", "alert", "updateQuantity", "newQuantity", "removeFromCart", "p", "filter", "subtotal", "reduce", "sum", "vatRate", "vatAmount", "total", "filteredProducts", "toLowerCase", "includes", "processPayment", "length", "Promise", "resolve", "setTimeout", "invoiceData", "customerPhone", "customerTaxNumber", "items", "vat", "cashier", "savedInvoice", "saveInvoice", "invoiceForPrint", "dateArabic", "Date", "createdAt", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "printInvoice", "updatedProducts", "generateQRCode", "invoice", "vatNumber", "invoiceDate", "formattedDate", "toISOString", "qrData", "sellerName", "vatRegistrationNumber", "invoiceTimestamp", "invoiceTotal", "parseFloat", "toFixed", "vatTotal", "createTLV", "tag", "value", "valueStr", "toString", "String", "fromCharCode", "qrString", "base64QR", "utf8Bytes", "TextEncoder", "encode", "binaryString", "Array", "from", "byte", "join", "btoa", "simpleData", "encodeURIComponent", "qrCodeUrl", "printWindow", "window", "open", "document", "write", "itemSubtotal", "itemVat", "close", "print", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "placeholder", "onChange", "e", "target", "disabled", "checked", "_c", "$RefreshReg$"], "sources": ["D:/aronim/saudi-accounting-final/src/components/POS/POS.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './POS.css';\nimport database from '../../utils/database';\n\nconst POS = ({ user, onBack }) => {\n  const [cart, setCart] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [customers, setCustomers] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [customer, setCustomer] = useState({\n    name: '',\n    phone: '',\n    taxNumber: ''\n  });\n  const [showCustomerForm, setShowCustomerForm] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState('cash');\n  const [discount, setDiscount] = useState(0);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [message, setMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [cashReceived, setCashReceived] = useState('');\n  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState('');\n  const searchInputRef = useRef(null);\n\n  // تحميل المنتجات من قاعدة البيانات\n  useEffect(() => {\n    try {\n      const dbProducts = database.getProducts();\n      setProducts(dbProducts);\n    } catch (error) {\n      console.error('خطأ في تحميل المنتجات:', error);\n      // في حالة الخطأ، استخدم منتجات افتراضية\n      const fallbackProducts = [\n        { id: 1, name: 'لابتوب HP', price: 2500, stock: 10, barcode: '123456789', category: 'إلكترونيات' },\n        { id: 2, name: 'ماوس لاسلكي', price: 85, stock: 25, barcode: '987654321', category: 'إلكترونيات' }\n      ];\n      setProducts(fallbackProducts);\n    }\n  }, []);\n\n  // إضافة منتج للسلة\n  const addToCart = (product) => {\n    const existingItem = cart.find(item => item.id === product.id);\n    if (existingItem) {\n      if (existingItem.quantity < product.stock) {\n        setCart(cart.map(item =>\n          item.id === product.id\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        ));\n      } else {\n        alert('⚠️ لا توجد كمية كافية في المخزون');\n      }\n    } else {\n      setCart([...cart, { ...product, quantity: 1 }]);\n    }\n  };\n\n  // تحديث كمية المنتج\n  const updateQuantity = (id, newQuantity) => {\n    if (newQuantity <= 0) {\n      removeFromCart(id);\n      return;\n    }\n    \n    const product = products.find(p => p.id === id);\n    if (newQuantity > product.stock) {\n      alert('⚠️ لا توجد كمية كافية في المخزون');\n      return;\n    }\n\n    setCart(cart.map(item =>\n      item.id === id ? { ...item, quantity: newQuantity } : item\n    ));\n  };\n\n  // حذف منتج من السلة\n  const removeFromCart = (id) => {\n    setCart(cart.filter(item => item.id !== id));\n  };\n\n  // حساب الإجماليات\n  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n  const vatRate = 0.15; // 15% ضريبة القيمة المضافة\n  const vatAmount = subtotal * vatRate;\n  const total = subtotal + vatAmount;\n\n  // تصفية المنتجات حسب البحث\n  const filteredProducts = products.filter(product =>\n    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    product.barcode.includes(searchTerm)\n  );\n\n  // معالجة الدفع\n  const processPayment = async () => {\n    if (cart.length === 0) {\n      alert('⚠️ السلة فارغة');\n      return;\n    }\n\n    setIsProcessing(true);\n\n    try {\n      // محاكاة معالجة الدفع\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // إنشاء بيانات الفاتورة\n      const invoiceData = {\n        customer: customer.name || 'عميل نقدي',\n        customerPhone: customer.phone || '',\n        customerTaxNumber: customer.taxNumber || '',\n        items: cart,\n        subtotal: subtotal,\n        vat: vatAmount,\n        total: total,\n        cashier: user.name,\n        paymentMethod: paymentMethod === 'cash' ? 'نقدي' : paymentMethod === 'card' ? 'بطاقة ائتمان' : 'تحويل بنكي'\n      };\n\n      // حفظ الفاتورة في قاعدة البيانات\n      const savedInvoice = database.saveInvoice(invoiceData);\n\n      // إضافة التاريخ المنسق للطباعة\n      const invoiceForPrint = {\n        ...savedInvoice,\n        dateArabic: new Date(savedInvoice.createdAt).toLocaleString('ar-SA', {\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit'\n        })\n      };\n\n      // طباعة الفاتورة\n      printInvoice(invoiceForPrint);\n\n      // تحديث المنتجات لعكس المخزون الجديد\n      const updatedProducts = database.getProducts();\n      setProducts(updatedProducts);\n\n      // تفريغ السلة\n      setCart([]);\n      setCustomer({ name: '', phone: '', taxNumber: '' });\n      setShowCustomerForm(false);\n      setIsProcessing(false);\n\n      alert(`✅ تم إتمام البيع بنجاح!\\nرقم الفاتورة: ${savedInvoice.id}`);\n    } catch (error) {\n      console.error('خطأ في معالجة البيع:', error);\n      setIsProcessing(false);\n      alert('❌ حدث خطأ في معالجة البيع. يرجى المحاولة مرة أخرى.');\n    }\n  };\n\n  // إنشاء QR Code للفاتورة (حسب المعايير السعودية)\n  const generateQRCode = (invoice) => {\n    // الرقم الضريبي السعودي (15 رقم)\n    const vatNumber = \"***************\";\n\n    // تنسيق التاريخ حسب المعايير السعودية (ISO 8601)\n    const invoiceDate = new Date();\n    const formattedDate = invoiceDate.toISOString();\n\n    // البيانات المطلوبة للـ QR Code السعودي (بالإنجليزية لتجنب مشاكل التشفير)\n    const qrData = {\n      // 1. اسم البائع\n      sellerName: \"Saudi Accounting System Co. Ltd\",\n      // 2. الرقم الضريبي للبائع (15 رقم)\n      vatRegistrationNumber: vatNumber,\n      // 3. الطابع الزمني للفاتورة (ISO 8601)\n      invoiceTimestamp: formattedDate,\n      // 4. إجمالي الفاتورة شامل الضريبة\n      invoiceTotal: parseFloat(invoice.total.toFixed(2)),\n      // 5. إجمالي ضريبة القيمة المضافة\n      vatTotal: parseFloat(invoice.vat.toFixed(2))\n    };\n\n    // تحويل البيانات إلى تنسيق TLV (Tag-Length-Value) حسب المعايير السعودية\n    const createTLV = (tag, value) => {\n      const valueStr = value.toString();\n      const length = valueStr.length;\n      return String.fromCharCode(tag) + String.fromCharCode(length) + valueStr;\n    };\n\n    // إنشاء QR Code حسب المعايير السعودية (بالإنجليزية)\n    const qrString =\n      createTLV(1, qrData.sellerName) +\n      createTLV(2, qrData.vatRegistrationNumber) +\n      createTLV(3, qrData.invoiceTimestamp) +\n      createTLV(4, qrData.invoiceTotal) +\n      createTLV(5, qrData.vatTotal);\n\n    // تحويل إلى Base64 بطريقة آمنة\n    let base64QR;\n    try {\n      // تحويل النص إلى UTF-8 bytes ثم إلى Base64\n      const utf8Bytes = new TextEncoder().encode(qrString);\n      const binaryString = Array.from(utf8Bytes, byte => String.fromCharCode(byte)).join('');\n      base64QR = btoa(binaryString);\n    } catch (error) {\n      // في حالة فشل التشفير، استخدم نص بسيط\n      const simpleData = `Company:${qrData.sellerName}|VAT:${qrData.vatRegistrationNumber}|Date:${qrData.invoiceTimestamp}|Total:${qrData.invoiceTotal}|VATAmount:${qrData.vatTotal}`;\n      base64QR = btoa(simpleData);\n    }\n\n    // إنشاء QR Code باستخدام خدمة مجانية\n    return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(base64QR)}`;\n  };\n\n  // طباعة الفاتورة المحسنة\n  const printInvoice = (invoice) => {\n    const qrCodeUrl = generateQRCode(invoice);\n    const printWindow = window.open('', '_blank');\n    printWindow.document.write(`\n      <html dir=\"rtl\">\n        <head>\n          <title>فاتورة ضريبية رقم ${invoice.id}</title>\n          <style>\n            body {\n              font-family: 'Arial', sans-serif;\n              margin: 20px;\n              line-height: 1.6;\n              color: #333;\n            }\n            .header {\n              text-align: center;\n              border-bottom: 3px solid #1890ff;\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 {\n              color: #1890ff;\n              margin: 0;\n              font-size: 1.8em;\n            }\n            .header h2 {\n              color: #666;\n              margin: 5px 0;\n              font-size: 1.2em;\n            }\n            .company-info {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              margin-bottom: 20px;\n              border-right: 4px solid #1890ff;\n            }\n            .invoice-details {\n              display: grid;\n              grid-template-columns: 1fr 1fr;\n              gap: 20px;\n              margin: 20px 0;\n            }\n            .invoice-details div {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n            }\n            .invoice-details p {\n              margin: 8px 0;\n              font-size: 1em;\n            }\n            .items-table {\n              width: 100%;\n              border-collapse: collapse;\n              margin: 20px 0;\n              box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            }\n            .items-table th {\n              background: linear-gradient(135deg, #1890ff, #40a9ff);\n              color: white;\n              padding: 12px 8px;\n              text-align: right;\n              font-weight: 600;\n            }\n            .items-table td {\n              border: 1px solid #e8e8e8;\n              padding: 10px 8px;\n              text-align: right;\n            }\n            .items-table tbody tr:nth-child(even) {\n              background-color: #f9f9f9;\n            }\n            .totals {\n              background: #f8f9fa;\n              padding: 20px;\n              border-radius: 8px;\n              margin-top: 20px;\n              border-right: 4px solid #52c41a;\n            }\n            .total-line {\n              display: flex;\n              justify-content: space-between;\n              margin: 8px 0;\n              font-size: 1.1em;\n            }\n            .vat-line {\n              color: #fa8c16;\n              font-weight: 600;\n            }\n            .final-total {\n              font-weight: bold;\n              font-size: 1.3em;\n              border-top: 2px solid #1890ff;\n              padding-top: 10px;\n              margin-top: 10px;\n              color: #1890ff;\n            }\n            .footer {\n              margin-top: 30px;\n              display: grid;\n              grid-template-columns: 1fr auto;\n              gap: 20px;\n              align-items: center;\n            }\n            .footer-text {\n              text-align: right;\n              color: #666;\n            }\n            .qr-section {\n              text-align: center;\n              border: 2px solid #e8e8e8;\n              padding: 15px;\n              border-radius: 8px;\n              background: white;\n            }\n            .qr-section h4 {\n              margin: 0 0 10px 0;\n              color: #1890ff;\n              font-size: 0.9em;\n            }\n            .tax-info {\n              background: #e6f7ff;\n              border: 1px solid #91d5ff;\n              padding: 15px;\n              border-radius: 8px;\n              margin: 20px 0;\n            }\n            .tax-info h3 {\n              color: #1890ff;\n              margin: 0 0 10px 0;\n              font-size: 1.1em;\n            }\n            @media print {\n              body { margin: 0; }\n              .no-print { display: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>🧮 نظام المحاسبة السعودي</h1>\n            <h2>فاتورة ضريبية مبسطة</h2>\n          </div>\n\n          <div class=\"company-info\">\n            <h3 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات الشركة</h3>\n            <p><strong>اسم الشركة:</strong> شركة نظام المحاسبة السعودي المحدودة</p>\n            <p><strong>الرقم الضريبي:</strong> ***************</p>\n            <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>\n            <p><strong>الهاتف:</strong> +966 11 123 4567</p>\n            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>\n          </div>\n\n          <div class=\"invoice-details\">\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">تفاصيل الفاتورة</h4>\n              <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>\n              <p><strong>تاريخ الإصدار:</strong> ${invoice.dateArabic}</p>\n              <p><strong>نوع الفاتورة:</strong> فاتورة ضريبية مبسطة</p>\n              <p><strong>حالة الفاتورة:</strong> مدفوعة</p>\n            </div>\n            <div>\n              <h4 style=\"margin: 0 0 10px 0; color: #1890ff;\">بيانات العملية</h4>\n              <p><strong>العميل:</strong> ${invoice.customer}</p>\n              <p><strong>أمين الصندوق:</strong> ${invoice.cashier}</p>\n              <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod}</p>\n              <p><strong>رقم المرجع:</strong> ${invoice.id}-PAY</p>\n            </div>\n          </div>\n\n          <table class=\"items-table\">\n            <thead>\n              <tr>\n                <th style=\"width: 40%\">وصف السلعة/الخدمة</th>\n                <th style=\"width: 15%\">السعر الوحدة</th>\n                <th style=\"width: 10%\">الكمية</th>\n                <th style=\"width: 15%\">الإجمالي قبل الضريبة</th>\n                <th style=\"width: 10%\">معدل الضريبة</th>\n                <th style=\"width: 10%\">مبلغ الضريبة</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${invoice.items.map(item => {\n                const itemSubtotal = item.price * item.quantity;\n                const itemVat = itemSubtotal * 0.15;\n                return `\n                  <tr>\n                    <td>${item.name}</td>\n                    <td>${item.price.toFixed(2)} ريال</td>\n                    <td>${item.quantity}</td>\n                    <td>${itemSubtotal.toFixed(2)} ريال</td>\n                    <td>15%</td>\n                    <td>${itemVat.toFixed(2)} ريال</td>\n                  </tr>\n                `;\n              }).join('')}\n            </tbody>\n          </table>\n\n          <div class=\"tax-info\">\n            <h3>📊 ملخص الضرائب</h3>\n            <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px;\">\n              <div>\n                <p><strong>إجمالي السلع والخدمات الخاضعة للضريبة:</strong></p>\n                <p style=\"color: #1890ff; font-size: 1.1em;\">${invoice.subtotal.toFixed(2)} ريال</p>\n              </div>\n              <div>\n                <p><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></p>\n                <p style=\"color: #fa8c16; font-size: 1.1em;\">${invoice.vat.toFixed(2)} ريال</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"totals\">\n            <div class=\"total-line\">\n              <span>المجموع الفرعي (قبل الضريبة):</span>\n              <span>${invoice.subtotal.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line vat-line\">\n              <span>ضريبة القيمة المضافة (15%):</span>\n              <span>${invoice.vat.toFixed(2)} ريال</span>\n            </div>\n            <div class=\"total-line final-total\">\n              <span>الإجمالي النهائي (شامل الضريبة):</span>\n              <span>${invoice.total.toFixed(2)} ريال</span>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <div class=\"footer-text\">\n              <h4 style=\"color: #1890ff; margin: 0 0 10px 0;\">شكراً لتعاملكم معنا</h4>\n              <p style=\"margin: 5px 0;\">هذه فاتورة ضريبية صادرة إلكترونياً</p>\n              <p style=\"margin: 5px 0;\">تم إنشاؤها بواسطة نظام المحاسبة السعودي المعتمد</p>\n              <p style=\"margin: 5px 0; font-size: 0.9em;\">للاستفسارات: <EMAIL></p>\n            </div>\n            <div class=\"qr-section\">\n              <h4>🔍 رمز الاستجابة السريعة</h4>\n              <img src=\"${qrCodeUrl}\" alt=\"QR Code\" style=\"max-width: 120px; height: auto;\">\n              <p style=\"font-size: 0.8em; margin: 5px 0 0 0; color: #666;\">امسح للتحقق من الفاتورة</p>\n            </div>\n          </div>\n\n          <div style=\"margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 0.85em; color: #666;\">\n            <p style=\"margin: 0;\">هذا المستند تم إنشاؤه إلكترونياً ولا يتطلب توقيع أو ختم</p>\n            <p style=\"margin: 5px 0 0 0;\">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>\n          </div>\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.print();\n  };\n\n  return (\n    <div className=\"pos-container\">\n      {/* شريط التنقل */}\n      <header className=\"pos-header\">\n        <div className=\"header-content\">\n          <button onClick={onBack} className=\"back-btn\">\n            ↩️ رجوع للوحة التحكم\n          </button>\n          <h1>🛒 نقطة البيع</h1>\n          <div className=\"user-info\">\n            <span>{user.name} - {user.role}</span>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"pos-main\">\n        {/* قسم المنتجات */}\n        <div className=\"products-section\">\n          <div className=\"search-bar\">\n            <input\n              type=\"text\"\n              placeholder=\"🔍 البحث عن منتج أو باركود...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          <div className=\"products-grid\">\n            {filteredProducts.map(product => (\n              <div key={product.id} className=\"product-card\">\n                <div className=\"product-info\">\n                  <h3>{product.name}</h3>\n                  <p className=\"product-category\">{product.category}</p>\n                  <p className=\"product-price\">{product.price} ريال</p>\n                  <p className=\"product-stock\">المخزون: {product.stock}</p>\n                </div>\n                <button\n                  onClick={() => addToCart(product)}\n                  className=\"add-to-cart-btn\"\n                  disabled={product.stock === 0}\n                >\n                  {product.stock === 0 ? 'نفد المخزون' : 'إضافة للسلة'}\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* قسم السلة والدفع */}\n        <div className=\"cart-section\">\n          <div className=\"cart-header\">\n            <h2>🛒 السلة</h2>\n            {cart.length > 0 && (\n              <button\n                onClick={() => setCart([])}\n                className=\"clear-cart-btn\"\n              >\n                🗑️ تفريغ السلة\n              </button>\n            )}\n          </div>\n\n          <div className=\"cart-items\">\n            {cart.length === 0 ? (\n              <p className=\"empty-cart\">السلة فارغة</p>\n            ) : (\n              cart.map(item => (\n                <div key={item.id} className=\"cart-item\">\n                  <div className=\"item-info\">\n                    <h4>{item.name}</h4>\n                    <p>{item.price} ريال</p>\n                  </div>\n                  <div className=\"quantity-controls\">\n                    <button\n                      onClick={() => updateQuantity(item.id, item.quantity - 1)}\n                      className=\"qty-btn\"\n                    >\n                      -\n                    </button>\n                    <span className=\"quantity\">{item.quantity}</span>\n                    <button\n                      onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                      className=\"qty-btn\"\n                    >\n                      +\n                    </button>\n                  </div>\n                  <div className=\"item-total\">\n                    {(item.price * item.quantity).toFixed(2)} ريال\n                  </div>\n                  <button\n                    onClick={() => removeFromCart(item.id)}\n                    className=\"remove-btn\"\n                  >\n                    ❌\n                  </button>\n                </div>\n              ))\n            )}\n          </div>\n\n          {cart.length > 0 && (\n            <>\n              <div className=\"cart-totals\">\n                <div className=\"total-line\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{subtotal.toFixed(2)} ريال</span>\n                </div>\n                <div className=\"total-line\">\n                  <span>ضريبة القيمة المضافة (15%):</span>\n                  <span>{vatAmount.toFixed(2)} ريال</span>\n                </div>\n                <div className=\"total-line final-total\">\n                  <span>الإجمالي:</span>\n                  <span>{total.toFixed(2)} ريال</span>\n                </div>\n              </div>\n\n              <div className=\"payment-section\">\n                <h4 className=\"section-subtitle\">💳 طريقة الدفع</h4>\n                <div className=\"payment-methods\">\n                  <label className={`payment-option ${paymentMethod === 'cash' ? 'selected' : ''}`}>\n                    <input\n                      type=\"radio\"\n                      name=\"paymentMethod\"\n                      value=\"cash\"\n                      checked={paymentMethod === 'cash'}\n                      onChange={(e) => setPaymentMethod(e.target.value)}\n                    />\n                    <span className=\"payment-icon\">💵</span>\n                    <span>نقدي</span>\n                  </label>\n                  <label className={`payment-option ${paymentMethod === 'card' ? 'selected' : ''}`}>\n                    <input\n                      type=\"radio\"\n                      name=\"paymentMethod\"\n                      value=\"card\"\n                      checked={paymentMethod === 'card'}\n                      onChange={(e) => setPaymentMethod(e.target.value)}\n                    />\n                    <span className=\"payment-icon\">💳</span>\n                    <span>بطاقة ائتمان</span>\n                  </label>\n                  <label className={`payment-option ${paymentMethod === 'transfer' ? 'selected' : ''}`}>\n                    <input\n                      type=\"radio\"\n                      name=\"paymentMethod\"\n                      value=\"transfer\"\n                      checked={paymentMethod === 'transfer'}\n                      onChange={(e) => setPaymentMethod(e.target.value)}\n                    />\n                    <span className=\"payment-icon\">🏦</span>\n                    <span>تحويل بنكي</span>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"customer-section\">\n                <button\n                  onClick={() => setShowCustomerForm(!showCustomerForm)}\n                  className=\"customer-btn\"\n                >\n                  👤 {showCustomerForm ? 'إخفاء' : 'إضافة'} بيانات العميل\n                </button>\n\n                {showCustomerForm && (\n                  <div className=\"customer-form\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"اسم العميل\"\n                      value={customer.name}\n                      onChange={(e) => setCustomer({...customer, name: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"رقم الهاتف\"\n                      value={customer.phone}\n                      onChange={(e) => setCustomer({...customer, phone: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"الرقم الضريبي (اختياري)\"\n                      value={customer.taxNumber}\n                      onChange={(e) => setCustomer({...customer, taxNumber: e.target.value})}\n                      className=\"customer-input\"\n                    />\n                  </div>\n                )}\n              </div>\n\n              <button\n                onClick={processPayment}\n                disabled={isProcessing}\n                className={`checkout-btn ${isProcessing ? 'processing' : ''}`}\n              >\n                {isProcessing ? (\n                  <>\n                    <span className=\"spinner\"></span>\n                    جاري المعالجة...\n                  </>\n                ) : (\n                  '💳 إتمام البيع وطباعة الفاتورة'\n                )}\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default POS;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,GAAG,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM6C,cAAc,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACd,IAAI;MACF,MAAM6C,UAAU,GAAG3C,QAAQ,CAAC4C,WAAW,CAAC,CAAC;MACzChC,WAAW,CAAC+B,UAAU,CAAC;IACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACA,MAAME,gBAAgB,GAAG,CACvB;QAAEC,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE,WAAW;QAAE0B,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAa,CAAC,EAClG;QAAEJ,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE,aAAa;QAAE0B,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAa,CAAC,CACnG;MACDxC,WAAW,CAACmC,gBAAgB,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,SAAS,GAAIC,OAAO,IAAK;IAC7B,MAAMC,YAAY,GAAG9C,IAAI,CAAC+C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,CAAC;IAC9D,IAAIO,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACG,QAAQ,GAAGJ,OAAO,CAACJ,KAAK,EAAE;QACzCxC,OAAO,CAACD,IAAI,CAACkD,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACT,EAAE,KAAKM,OAAO,CAACN,EAAE,GAClB;UAAE,GAAGS,IAAI;UAAEC,QAAQ,EAAED,IAAI,CAACC,QAAQ,GAAG;QAAE,CAAC,GACxCD,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLG,KAAK,CAAC,kCAAkC,CAAC;MAC3C;IACF,CAAC,MAAM;MACLlD,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAG6C,OAAO;QAAEI,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAGA,CAACb,EAAE,EAAEc,WAAW,KAAK;IAC1C,IAAIA,WAAW,IAAI,CAAC,EAAE;MACpBC,cAAc,CAACf,EAAE,CAAC;MAClB;IACF;IAEA,MAAMM,OAAO,GAAG3C,QAAQ,CAAC6C,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKA,EAAE,CAAC;IAC/C,IAAIc,WAAW,GAAGR,OAAO,CAACJ,KAAK,EAAE;MAC/BU,KAAK,CAAC,kCAAkC,CAAC;MACzC;IACF;IAEAlD,OAAO,CAACD,IAAI,CAACkD,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACT,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGS,IAAI;MAAEC,QAAQ,EAAEI;IAAY,CAAC,GAAGL,IACxD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMM,cAAc,GAAIf,EAAE,IAAK;IAC7BtC,OAAO,CAACD,IAAI,CAACwD,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACT,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMkB,QAAQ,GAAGzD,IAAI,CAAC0D,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAIX,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAS,EAAE,CAAC,CAAC;EAClF,MAAMW,OAAO,GAAG,IAAI,CAAC,CAAC;EACtB,MAAMC,SAAS,GAAGJ,QAAQ,GAAGG,OAAO;EACpC,MAAME,KAAK,GAAGL,QAAQ,GAAGI,SAAS;;EAElC;EACA,MAAME,gBAAgB,GAAG7D,QAAQ,CAACsD,MAAM,CAACX,OAAO,IAC9CA,OAAO,CAAC/B,IAAI,CAACkD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3D,UAAU,CAAC0D,WAAW,CAAC,CAAC,CAAC,IAC7DnB,OAAO,CAACH,OAAO,CAACuB,QAAQ,CAAC3D,UAAU,CACrC,CAAC;;EAED;EACA,MAAM4D,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIlE,IAAI,CAACmE,MAAM,KAAK,CAAC,EAAE;MACrBhB,KAAK,CAAC,gBAAgB,CAAC;MACvB;IACF;IAEA3B,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM,IAAI4C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,WAAW,GAAG;QAClB3D,QAAQ,EAAEA,QAAQ,CAACE,IAAI,IAAI,WAAW;QACtC0D,aAAa,EAAE5D,QAAQ,CAACG,KAAK,IAAI,EAAE;QACnC0D,iBAAiB,EAAE7D,QAAQ,CAACI,SAAS,IAAI,EAAE;QAC3C0D,KAAK,EAAE1E,IAAI;QACXyD,QAAQ,EAAEA,QAAQ;QAClBkB,GAAG,EAAEd,SAAS;QACdC,KAAK,EAAEA,KAAK;QACZc,OAAO,EAAE/E,IAAI,CAACiB,IAAI;QAClBK,aAAa,EAAEA,aAAa,KAAK,MAAM,GAAG,MAAM,GAAGA,aAAa,KAAK,MAAM,GAAG,cAAc,GAAG;MACjG,CAAC;;MAED;MACA,MAAM0D,YAAY,GAAGtF,QAAQ,CAACuF,WAAW,CAACP,WAAW,CAAC;;MAEtD;MACA,MAAMQ,eAAe,GAAG;QACtB,GAAGF,YAAY;QACfG,UAAU,EAAE,IAAIC,IAAI,CAACJ,YAAY,CAACK,SAAS,CAAC,CAACC,cAAc,CAAC,OAAO,EAAE;UACnEC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,MAAM;UACbC,GAAG,EAAE,SAAS;UACdC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC;;MAED;MACAC,YAAY,CAACX,eAAe,CAAC;;MAE7B;MACA,MAAMY,eAAe,GAAGpG,QAAQ,CAAC4C,WAAW,CAAC,CAAC;MAC9ChC,WAAW,CAACwF,eAAe,CAAC;;MAE5B;MACA1F,OAAO,CAAC,EAAE,CAAC;MACXY,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MACnDE,mBAAmB,CAAC,KAAK,CAAC;MAC1BM,eAAe,CAAC,KAAK,CAAC;MAEtB2B,KAAK,CAAC,0CAA0C0B,YAAY,CAACtC,EAAE,EAAE,CAAC;IACpE,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CZ,eAAe,CAAC,KAAK,CAAC;MACtB2B,KAAK,CAAC,oDAAoD,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAIC,OAAO,IAAK;IAClC;IACA,MAAMC,SAAS,GAAG,iBAAiB;;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAId,IAAI,CAAC,CAAC;IAC9B,MAAMe,aAAa,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC;;IAE/C;IACA,MAAMC,MAAM,GAAG;MACb;MACAC,UAAU,EAAE,iCAAiC;MAC7C;MACAC,qBAAqB,EAAEN,SAAS;MAChC;MACAO,gBAAgB,EAAEL,aAAa;MAC/B;MACAM,YAAY,EAAEC,UAAU,CAACV,OAAO,CAAC/B,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC,CAAC;MAClD;MACAC,QAAQ,EAAEF,UAAU,CAACV,OAAO,CAAClB,GAAG,CAAC6B,OAAO,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED;IACA,MAAME,SAAS,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;MAChC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;MACjC,MAAM3C,MAAM,GAAG0C,QAAQ,CAAC1C,MAAM;MAC9B,OAAO4C,MAAM,CAACC,YAAY,CAACL,GAAG,CAAC,GAAGI,MAAM,CAACC,YAAY,CAAC7C,MAAM,CAAC,GAAG0C,QAAQ;IAC1E,CAAC;;IAED;IACA,MAAMI,QAAQ,GACZP,SAAS,CAAC,CAAC,EAAER,MAAM,CAACC,UAAU,CAAC,GAC/BO,SAAS,CAAC,CAAC,EAAER,MAAM,CAACE,qBAAqB,CAAC,GAC1CM,SAAS,CAAC,CAAC,EAAER,MAAM,CAACG,gBAAgB,CAAC,GACrCK,SAAS,CAAC,CAAC,EAAER,MAAM,CAACI,YAAY,CAAC,GACjCI,SAAS,CAAC,CAAC,EAAER,MAAM,CAACO,QAAQ,CAAC;;IAE/B;IACA,IAAIS,QAAQ;IACZ,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,QAAQ,CAAC;MACpD,MAAMK,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACL,SAAS,EAAEM,IAAI,IAAIV,MAAM,CAACC,YAAY,CAACS,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;MACtFR,QAAQ,GAAGS,IAAI,CAACL,YAAY,CAAC;IAC/B,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACd;MACA,MAAMwF,UAAU,GAAG,WAAW1B,MAAM,CAACC,UAAU,QAAQD,MAAM,CAACE,qBAAqB,SAASF,MAAM,CAACG,gBAAgB,UAAUH,MAAM,CAACI,YAAY,cAAcJ,MAAM,CAACO,QAAQ,EAAE;MAC/KS,QAAQ,GAAGS,IAAI,CAACC,UAAU,CAAC;IAC7B;;IAEA;IACA,OAAO,iEAAiEC,kBAAkB,CAACX,QAAQ,CAAC,EAAE;EACxG,CAAC;;EAED;EACA,MAAMxB,YAAY,GAAIG,OAAO,IAAK;IAChC,MAAMiC,SAAS,GAAGlC,cAAc,CAACC,OAAO,CAAC;IACzC,MAAMkC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7CF,WAAW,CAACG,QAAQ,CAACC,KAAK,CAAC;AAC/B;AACA;AACA,qCAAqCtC,OAAO,CAACtD,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkDsD,OAAO,CAACtD,EAAE;AAC5D,mDAAmDsD,OAAO,CAACb,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA,4CAA4Ca,OAAO,CAACjF,QAAQ;AAC5D,kDAAkDiF,OAAO,CAACjB,OAAO;AACjE,iDAAiDiB,OAAO,CAAC1E,aAAa;AACtE,gDAAgD0E,OAAO,CAACtD,EAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBsD,OAAO,CAACnB,KAAK,CAACxB,GAAG,CAACF,IAAI,IAAI;MAC1B,MAAMoF,YAAY,GAAGpF,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAQ;MAC/C,MAAMoF,OAAO,GAAGD,YAAY,GAAG,IAAI;MACnC,OAAO;AACvB;AACA,0BAA0BpF,IAAI,CAAClC,IAAI;AACnC,0BAA0BkC,IAAI,CAACR,KAAK,CAACgE,OAAO,CAAC,CAAC,CAAC;AAC/C,0BAA0BxD,IAAI,CAACC,QAAQ;AACvC,0BAA0BmF,YAAY,CAAC5B,OAAO,CAAC,CAAC,CAAC;AACjD;AACA,0BAA0B6B,OAAO,CAAC7B,OAAO,CAAC,CAAC,CAAC;AAC5C;AACA,iBAAiB;IACH,CAAC,CAAC,CAACkB,IAAI,CAAC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D7B,OAAO,CAACpC,QAAQ,CAAC+C,OAAO,CAAC,CAAC,CAAC;AAC1F;AACA;AACA;AACA,+DAA+DX,OAAO,CAAClB,GAAG,CAAC6B,OAAO,CAAC,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBX,OAAO,CAACpC,QAAQ,CAAC+C,OAAO,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA,sBAAsBX,OAAO,CAAClB,GAAG,CAAC6B,OAAO,CAAC,CAAC,CAAC;AAC5C;AACA;AACA;AACA,sBAAsBX,OAAO,CAAC/B,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0BsB,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,IAAI7C,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;AAC7F;AACA;AACA;AACA,KAAK,CAAC;IACF4C,WAAW,CAACG,QAAQ,CAACI,KAAK,CAAC,CAAC;IAC5BP,WAAW,CAACQ,KAAK,CAAC,CAAC;EACrB,CAAC;EAED,oBACE9I,OAAA;IAAK+I,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhJ,OAAA;MAAQ+I,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BhJ,OAAA;QAAK+I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhJ,OAAA;UAAQiJ,OAAO,EAAE5I,MAAO;UAAC0I,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAE9C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrJ,OAAA;UAAAgJ,QAAA,EAAI;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBrJ,OAAA;UAAK+I,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBhJ,OAAA;YAAAgJ,QAAA,GAAO5I,IAAI,CAACiB,IAAI,EAAC,KAAG,EAACjB,IAAI,CAACkJ,IAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETrJ,OAAA;MAAK+I,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEvBhJ,OAAA;QAAK+I,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhJ,OAAA;UAAK+I,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBhJ,OAAA;YACEuJ,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wIAA+B;YAC3CrC,KAAK,EAAEtG,UAAW;YAClB4I,QAAQ,EAAGC,CAAC,IAAK5I,aAAa,CAAC4I,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;YAC/C4B,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrJ,OAAA;UAAK+I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B1E,gBAAgB,CAACb,GAAG,CAACL,OAAO,iBAC3BpD,OAAA;YAAsB+I,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC5ChJ,OAAA;cAAK+I,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhJ,OAAA;gBAAAgJ,QAAA,EAAK5F,OAAO,CAAC/B;cAAI;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBrJ,OAAA;gBAAG+I,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAE5F,OAAO,CAACF;cAAQ;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDrJ,OAAA;gBAAG+I,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAE5F,OAAO,CAACL,KAAK,EAAC,2BAAK;cAAA;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDrJ,OAAA;gBAAG+I,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,8CAAS,EAAC5F,OAAO,CAACJ,KAAK;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNrJ,OAAA;cACEiJ,OAAO,EAAEA,CAAA,KAAM9F,SAAS,CAACC,OAAO,CAAE;cAClC2F,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAExG,OAAO,CAACJ,KAAK,KAAK,CAAE;cAAAgG,QAAA,EAE7B5F,OAAO,CAACJ,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG;YAAa;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA,GAbDjG,OAAO,CAACN,EAAE;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrJ,OAAA;QAAK+I,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhJ,OAAA;UAAK+I,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhJ,OAAA;YAAAgJ,QAAA,EAAI;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChB9I,IAAI,CAACmE,MAAM,GAAG,CAAC,iBACd1E,OAAA;YACEiJ,OAAO,EAAEA,CAAA,KAAMzI,OAAO,CAAC,EAAE,CAAE;YAC3BuI,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrJ,OAAA;UAAK+I,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBzI,IAAI,CAACmE,MAAM,KAAK,CAAC,gBAChB1E,OAAA;YAAG+I,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAEzC9I,IAAI,CAACkD,GAAG,CAACF,IAAI,iBACXvD,OAAA;YAAmB+I,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtChJ,OAAA;cAAK+I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhJ,OAAA;gBAAAgJ,QAAA,EAAKzF,IAAI,CAAClC;cAAI;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBrJ,OAAA;gBAAAgJ,QAAA,GAAIzF,IAAI,CAACR,KAAK,EAAC,2BAAK;cAAA;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNrJ,OAAA;cAAK+I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChJ,OAAA;gBACEiJ,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACJ,IAAI,CAACT,EAAE,EAAES,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAE;gBAC1DuF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrJ,OAAA;gBAAM+I,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEzF,IAAI,CAACC;cAAQ;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDrJ,OAAA;gBACEiJ,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACJ,IAAI,CAACT,EAAE,EAAES,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAE;gBAC1DuF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACpB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrJ,OAAA;cAAK+I,SAAS,EAAC,YAAY;cAAAC,QAAA,GACxB,CAACzF,IAAI,CAACR,KAAK,GAAGQ,IAAI,CAACC,QAAQ,EAAEuD,OAAO,CAAC,CAAC,CAAC,EAAC,2BAC3C;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrJ,OAAA;cACEiJ,OAAO,EAAEA,CAAA,KAAMpF,cAAc,CAACN,IAAI,CAACT,EAAE,CAAE;cACvCiG,SAAS,EAAC,YAAY;cAAAC,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GA5BD9F,IAAI,CAACT,EAAE;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BZ,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL9I,IAAI,CAACmE,MAAM,GAAG,CAAC,iBACd1E,OAAA,CAAAE,SAAA;UAAA8I,QAAA,gBACEhJ,OAAA;YAAK+I,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhJ,OAAA;cAAK+I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhJ,OAAA;gBAAAgJ,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BrJ,OAAA;gBAAAgJ,QAAA,GAAOhF,QAAQ,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACNrJ,OAAA;cAAK+I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhJ,OAAA;gBAAAgJ,QAAA,EAAM;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCrJ,OAAA;gBAAAgJ,QAAA,GAAO5E,SAAS,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNrJ,OAAA;cAAK+I,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrChJ,OAAA;gBAAAgJ,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtBrJ,OAAA;gBAAAgJ,QAAA,GAAO3E,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC,EAAC,2BAAK;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrJ,OAAA;YAAK+I,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BhJ,OAAA;cAAI+I,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDrJ,OAAA;cAAK+I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhJ,OAAA;gBAAO+I,SAAS,EAAE,kBAAkBrH,aAAa,KAAK,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAAsH,QAAA,gBAC/EhJ,OAAA;kBACEuJ,IAAI,EAAC,OAAO;kBACZlI,IAAI,EAAC,eAAe;kBACpB8F,KAAK,EAAC,MAAM;kBACZ0C,OAAO,EAAEnI,aAAa,KAAK,MAAO;kBAClC+H,QAAQ,EAAGC,CAAC,IAAK/H,gBAAgB,CAAC+H,CAAC,CAACC,MAAM,CAACxC,KAAK;gBAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFrJ,OAAA;kBAAM+I,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCrJ,OAAA;kBAAAgJ,QAAA,EAAM;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACRrJ,OAAA;gBAAO+I,SAAS,EAAE,kBAAkBrH,aAAa,KAAK,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAAsH,QAAA,gBAC/EhJ,OAAA;kBACEuJ,IAAI,EAAC,OAAO;kBACZlI,IAAI,EAAC,eAAe;kBACpB8F,KAAK,EAAC,MAAM;kBACZ0C,OAAO,EAAEnI,aAAa,KAAK,MAAO;kBAClC+H,QAAQ,EAAGC,CAAC,IAAK/H,gBAAgB,CAAC+H,CAAC,CAACC,MAAM,CAACxC,KAAK;gBAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFrJ,OAAA;kBAAM+I,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCrJ,OAAA;kBAAAgJ,QAAA,EAAM;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACRrJ,OAAA;gBAAO+I,SAAS,EAAE,kBAAkBrH,aAAa,KAAK,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAAsH,QAAA,gBACnFhJ,OAAA;kBACEuJ,IAAI,EAAC,OAAO;kBACZlI,IAAI,EAAC,eAAe;kBACpB8F,KAAK,EAAC,UAAU;kBAChB0C,OAAO,EAAEnI,aAAa,KAAK,UAAW;kBACtC+H,QAAQ,EAAGC,CAAC,IAAK/H,gBAAgB,CAAC+H,CAAC,CAACC,MAAM,CAACxC,KAAK;gBAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFrJ,OAAA;kBAAM+I,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCrJ,OAAA;kBAAAgJ,QAAA,EAAM;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrJ,OAAA;YAAK+I,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhJ,OAAA;cACEiJ,OAAO,EAAEA,CAAA,KAAMxH,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtDuH,SAAS,EAAC,cAAc;cAAAC,QAAA,GACzB,eACI,EAACxH,gBAAgB,GAAG,OAAO,GAAG,OAAO,EAAC,4EAC3C;YAAA;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER7H,gBAAgB,iBACfxB,OAAA;cAAK+I,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhJ,OAAA;gBACEuJ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,yDAAY;gBACxBrC,KAAK,EAAEhG,QAAQ,CAACE,IAAK;gBACrBoI,QAAQ,EAAGC,CAAC,IAAKtI,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEE,IAAI,EAAEqI,CAAC,CAACC,MAAM,CAACxC;gBAAK,CAAC,CAAE;gBAClE4B,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFrJ,OAAA;gBACEuJ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,yDAAY;gBACxBrC,KAAK,EAAEhG,QAAQ,CAACG,KAAM;gBACtBmI,QAAQ,EAAGC,CAAC,IAAKtI,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEG,KAAK,EAAEoI,CAAC,CAACC,MAAM,CAACxC;gBAAK,CAAC,CAAE;gBACnE4B,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFrJ,OAAA;gBACEuJ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,wHAAyB;gBACrCrC,KAAK,EAAEhG,QAAQ,CAACI,SAAU;gBAC1BkI,QAAQ,EAAGC,CAAC,IAAKtI,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEI,SAAS,EAAEmI,CAAC,CAACC,MAAM,CAACxC;gBAAK,CAAC,CAAE;gBACvE4B,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrJ,OAAA;YACEiJ,OAAO,EAAExE,cAAe;YACxBmF,QAAQ,EAAE9H,YAAa;YACvBiH,SAAS,EAAE,gBAAgBjH,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;YAAAkH,QAAA,EAE7DlH,YAAY,gBACX9B,OAAA,CAAAE,SAAA;cAAA8I,QAAA,gBACEhJ,OAAA;gBAAM+I,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gFAEnC;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/I,EAAA,CArqBIH,GAAG;AAAA2J,EAAA,GAAH3J,GAAG;AAuqBT,eAAeA,GAAG;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}