/* حاوية لوحة التحكم */
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* شريط التنقل العلوي */
.dashboard-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.dashboard-title {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.current-time {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
}

.user-role {
  font-size: 0.85rem;
  opacity: 0.8;
}

.logout-btn {
  background-color: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background-color: rgba(255,255,255,0.3);
  transform: translateY(-1px);
}

/* المحتوى الرئيسي */
.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.section-title {
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
}

/* قسم الإحصائيات */
.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
}

.stat-content h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-value {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.stat-change {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-change.positive {
  background-color: #f6ffed;
  color: #52c41a;
}

.stat-change.neutral {
  background-color: #fff7e6;
  color: #fa8c16;
}

/* قسم الإجراءات */
.actions-section {
  margin-bottom: 40px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  border: none;
  border-radius: 12px;
  padding: 25px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s;
  cursor: pointer;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  background: linear-gradient(135deg, #f0f8ff, #ffffff);
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 5px;
}

.action-card h3 {
  margin: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.action-card p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-right {
    flex-direction: column;
    gap: 10px;
  }

  .dashboard-main {
    padding: 20px 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-title {
    font-size: 1.2rem;
  }
}

/* الشريط الجانبي */
.dashboard-sider {
  background: linear-gradient(180deg, #001529 0%, #002140 100%);
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.logo-section {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-icon {
  font-size: 2.5rem;
  margin-left: 12px;
}

.logo-text {
  flex: 1;
}

.user-section {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  margin: 16px;
  border-radius: 8px;
}

.user-info {
  margin-right: 12px;
  flex: 1;
}

.user-info .ant-typography {
  display: block;
  line-height: 1.2;
}

.menu-section {
  padding: 0 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 0;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-item.active {
  background: #1890ff;
  color: white;
}

.menu-item .anticon {
  margin-left: 12px;
  font-size: 16px;
}

/* الهيدر */
.dashboard-header {
  background: white;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 250px;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-dropdown:hover {
  background: #f5f5f5;
}

/* المحتوى الرئيسي */
.dashboard-content {
  margin-right: 250px;
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* بطاقات الإحصائيات */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card .ant-card-body {
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.stat-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  color: white;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* ألوان البطاقات */
.sales-card {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.today-card {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.customers-card {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.products-card {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

/* بطاقة الأنشطة */
.activity-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-card .ant-card-head {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
}

.activity-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  font-size: 18px;
  flex-shrink: 0;
}

.activity-icon.sales {
  background: rgba(24, 144, 255, 0.1);
}

.activity-icon.customer {
  background: rgba(82, 196, 26, 0.1);
}

.activity-icon.warning {
  background: rgba(250, 173, 20, 0.1);
}

.activity-content {
  flex: 1;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

/* بطاقة الإجراءات السريعة */
.quick-actions-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions-card .ant-card-head {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
}

.quick-actions-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.quick-actions-card .ant-btn {
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-actions-card .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.quick-actions-card .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.quick-actions-card .ant-btn:not(.ant-btn-primary) {
  border: 2px solid #f0f0f0;
  color: #595959;
}

.quick-actions-card .ant-btn:not(.ant-btn-primary):hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

/* شاشة التحميل */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  direction: rtl;
}

.loading-content {
  text-align: center;
  padding: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 24px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-content h2 {
  margin-bottom: 16px;
  font-size: 1.8rem;
  font-weight: 600;
}

.loading-content p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* تحسين الاستجابة */
@media (max-width: 1200px) {
  .dashboard-sider {
    position: relative;
    width: 100% !important;
    height: auto;
  }
  
  .dashboard-header {
    margin-right: 0;
  }
  
  .dashboard-content {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 16px;
  }
  
  .stat-card .ant-card-body {
    padding: 16px;
  }
  
  .logo-section {
    padding: 16px;
  }
  
  .user-section {
    margin: 12px;
    padding: 16px;
  }
  
  .dashboard-header {
    padding: 0 16px;
  }
  
  .header-left .ant-typography {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .stats-row .ant-col {
    margin-bottom: 16px;
  }
  
  .stat-card .ant-card-body {
    padding: 12px;
  }
  
  .activity-item {
    padding: 12px 0;
  }
  
  .activity-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .quick-actions-card .ant-btn {
    height: 40px;
  }
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.activity-card {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

.quick-actions-card {
  animation: slideInRight 0.6s ease-out 0.4s both;
}
