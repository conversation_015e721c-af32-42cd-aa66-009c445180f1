/* التطبيق الرئيسي */
.app {
  text-align: center;
  padding: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  direction: rtl;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* العنوان الرئيسي */
.main-title {
  color: #1890ff;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  animation: fadeInDown 0.8s ease-out;
}

/* الوصف الرئيسي */
.main-description {
  font-size: 18px;
  margin-bottom: 30px;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 30px;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* حاوية الأزرار */
.buttons-container {
  margin-bottom: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* الأزرار الأساسية */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  height: 48px;
  justify-content: center;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn:active {
  transform: translateY(0);
}

/* الزر الأساسي */
.btn-primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border: 2px solid transparent;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4);
}

.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* الزر الثانوي */
.btn-secondary {
  background: transparent;
  color: #1890ff;
  border: 2px solid #1890ff;
}

.btn-secondary:hover {
  background: #1890ff;
  color: white;
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);
}

/* حالة التحميل */
.btn.loading {
  pointer-events: none;
}

/* دوار التحميل */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* قسم المميزات */
.features-section {
  background: #f0f2f5;
  padding: 20px;
  border-radius: 8px;
  text-align: right;
  margin-bottom: 30px;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.features-title {
  margin-bottom: 15px;
  color: #262626;
  font-size: 1.2rem;
  font-weight: 600;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  margin-bottom: 8px;
  font-size: 16px;
  color: #555;
  padding: 5px 0;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.features-list li:last-child {
  border-bottom: none;
}

/* قسم المعلومات الإضافية */
.more-info {
  margin-top: 20px;
  padding: 20px;
  background: #f6ffed;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
  animation: slideDown 0.5s ease-out;
  text-align: right;
}

.more-info h4 {
  color: #52c41a;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.more-info p {
  margin-bottom: 10px;
  color: #666;
  line-height: 1.5;
}

.more-info strong {
  color: #262626;
}

/* قسم حالة التطوير */
.status-section {
  margin-top: 30px;
  padding: 20px;
  background: #e6f7ff;
  border-radius: 8px;
  border: 1px solid #91d5ff;
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.status-title {
  color: #1890ff;
  margin-bottom: 10px;
  font-size: 1.1rem;
  font-weight: 600;
}

.status-text {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* الحاوية الرئيسية للبطاقة */
.main-card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  background: white;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  animation: fadeIn 0.8s ease-out;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); max-height: 0; }
  to { opacity: 1; transform: translateY(0); max-height: 500px; }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .app {
    padding: 20px;
  }

  .main-card {
    padding: 20px;
    margin: 0 10px;
  }

  .main-title {
    font-size: 2rem;
  }

  .buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 15px;
  }

  .main-card {
    padding: 15px;
  }

  .main-title {
    font-size: 1.8rem;
  }

  .main-description {
    font-size: 16px;
  }
}
